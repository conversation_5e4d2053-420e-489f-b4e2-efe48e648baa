apiVersion: v1
kind: ConfigMap
metadata:
  name: scp-mps-api
  namespace: prod-environment
data:
  application.properties: |
    spring.application.name = scp-mps-api
    spring.profiles.active = jdbc,cloud,redis
    metadata.name = scp-mps-api
    metadata.namespace = prod-environment
    ##########################################
    jinkosolar.env = prod
    endpoints.health.enabled=true
    spring.main.allow-bean-definition-overriding=true
    ##########################################
    spring.datasource.druid.url = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    spring.redis.password = JK_Redis_3680
    spring.redis.cluster.nodes =
    spring.redis.sentinel.master = mymaster
    spring.redis.sentinel.nodes = redis-ha-announce-0.prod-environment:26379,redis-ha-announce-1.prod-environment:26379,redis-ha-announce-2.prod-environment:26379
    ###########################################
    security.oauth2.client.client-id = scp-mps-api
    security.oauth2.client.client-secret = 624fb786074683000610ea33
    oauth.url = http://oauth.jinkosolar.com
    security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
    security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
    security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
    security.oauth2.resource.preferTokenInfo = false
    security.oauth2.client.scope = read,write,trust
    ###########################################
    entity.jpa.package = com.jinkosolar.scp.mps.domain
    entity.scan.package = com.jinkosolar.scp.mps.domain
    scan.base.package = com.jinkosolar.scp
    ###########################################
    jinkosolar.jip.url = http://*************:8000/JIP
    jinkosolar.jip.clientId = APP112
    jinkosolar.jip.jwtKey = SCP-5WN8DAK0
    jinkosolar.jip.jwtSecret = ENC(hF1qaO07ZijZ6nVkARsjRLqapEg0/DMxdXvistz+HC5sgtd3UnhdfotZlNhSYh+XzcOOfT/I3DQ=)
    jinkosolar.jip.version = 2.0.0
    ###########################################
    jinkosolar.asprova.url = http://************:8081
    asprova.db.url = **********************************************************
    asprova.db.user = sa
    asprova.db.password = ENC(JhjzVXGmVnKzwkqeIKbhtt4dvvHjgVim)
    ###########################################
    rocketmq.name-server = mq-namesrv-0.mq-namesrv.prod-environment.svc.cluster.local:20901
    rocketmq.producer.group=scp-mps-api