apiVersion: v1
kind: ConfigMap
metadata:
  name: scp-mps-api
  namespace: prod2-environment
data:
  application.properties: |
    spring.application.name = scp-mps-api
    spring.profiles.active = jdbc,cloud,redis
    metadata.name = scp-mps-api
    metadata.namespace = prod2-environment
    ##########################################
    jinkosolar.env = prod2
    endpoints.health.enabled=true
    spring.main.allow-bean-definition-overriding=true
    ##########################################
    spring.datasource.druid.url = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    spring.redis.password = JK_Redis_3680
    spring.redis.cluster.nodes =
    spring.redis.sentinel.master = mymaster
    spring.redis.sentinel.nodes = redis-ha-announce-0.prod2-environment:26379,redis-ha-announce-1.prod2-environment:26379,redis-ha-announce-2.prod2-environment:26379
    ###########################################
    security.oauth2.client.client-id = scp-mps-api
    security.oauth2.client.client-secret = 624fb786074683000610ea33
    oauth.url = http://oauth.jinkosolar.com
    security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
    security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
    security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
    security.oauth2.resource.preferTokenInfo = false
    security.oauth2.client.scope = read,write,trust
    ###########################################
    entity.jpa.package = com.jinkosolar.scp.mps.domain
    entity.scan.package = com.jinkosolar.scp.mps.domain
    scan.base.package = com.jinkosolar.scp
    ###########################################
    jinkosolar.jip.url=http://jip.jinkosolar.com/JIP
    jinkosolar.jip.clientId=APP112
    jinkosolar.jip.jwtKey=SCP-0KDILDCS
    jinkosolar.jip.jwtSecret=50cb4147-9ebf-4d27-964c-10e1584d74e5
    ###########################################
    jinkosolar.asprova.url = http://*************:80
    asprova.db.url = jdbc:sqlserver://*************:1433;database=AsprovaDB
    asprova.db.user = AsprovaUser
    asprova.db.password = A1s@p#r$o%v^a&U*ser
    ###########################################
    rocketmq.name-server = mq-namesrv-0.mq-namesrv.prod2-environment.svc.cluster.local:20901
    rocketmq.producer.group=scp-mps-api
    env.gateway.url = https://scp2-gateway.jinkosolar.com
    env.page.url = https://scp.jinkosolar.com
    spring.jpa.show-sql = false
    
    swagger.enable = false
    jinkosolar.file.fileUrl = https://scp2-gateway.jinkosolar.com/scp-system-api/file/download/
