apiVersion: v1
kind: ConfigMap
metadata:
  name: scp-mps-api
  namespace: sit-environment
data:
  application.properties: |
    spring.application.name = scp-mps-api
    spring.profiles.active = jdbc,cloud,redis
    metadata.name = scp-mps-api
    metadata.namespace = sit-environment
    ##########################################
    endpoints.health.enabled=true
    spring.main.allow-bean-definition-overriding=true
    ##########################################
    spring.jpa.show-sql = true
    spring.datasource.druid.url = ENC(vzMs2pxE7zY3iSPBDuXzc97jYKksPRnCoRBn3do+BdM/IICKqtEfSg+TtO3QSLUnoGZWWgNj8EK8WYg7JrMSnjl4aNT4vc6IH/tAX/vgFqLU+NadQzHMvORB5yRlkKscAT4ySybHnGzmfxPmjmMgMoLCUP2uZLXmeexjH06vMrM9q0Idv9XnWY00jgg9fuXpJC8Mal3x5YkAbcxjJPYNINT8Ro+wow28QuIIRjkiw6RwwpHyYQdvI7CrfIqhZbg0xjsIHiOIVZxlUhltV0F2gZkgZ0c7gVdQIxZYJBgDFgjZgYlMIxA1p++gHDnOh2OcGc5jd6VKruDmIi+2UhKy4LLbfvVGKKDqOtSzVdCaTCbZIZhGZXgItAzEfS7Lvb8jKxahAGrNX59lHUah9wod1qtybIKuDsnw+0LvEIXOJVGI/BjW4QZgSB4+9QU555nmrMX4uu61+zvudC3TGMatCVPwT8qRTPmQMyGnHEkqA/t5k8wyZqMZidmNChzuEqhs0Tz19LaJHyDkjtpkELLAMZaUgnntXrp1uN57NORvQajJfUnQbJ1TUYgEdQUoW2mb/1B3Un84TRmgIapzDykYOQ==)
    spring.redis.password = ENC(7IspwP4f27nP1MniJVGBtY9sckQ/FC+6aZArfSlRbsQ=)
    spring.redis.cluster.nodes =
    spring.redis.sentinel.master = mymaster
    spring.redis.sentinel.nodes = redis-ha-old-announce-0.sit-environment:26379,redis-ha-old-announce-1.sit-environment:26379,redis-ha-old-announce-2.sit-environment:26379
    ###########################################
    security.oauth2.client.client-id = scp-mps-api
    security.oauth2.client.client-secret = 624fb786074683000610ea33
    oauth.url = http://oauth.jinkosolar.com
    security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
    security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
    security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
    security.oauth2.resource.preferTokenInfo = false
    security.oauth2.client.scope = read,write,trust
    
    jinkosolar.env = sit
    entity.jpa.package = com.jinkosolar.scp.mps.domain
    entity.scan.package = com.jinkosolar.scp.mps.domain
    scan.base.package = com.jinkosolar.scp
    ###########################################
    jinkosolar.jip.url = http://*************:8000/JIP
    jinkosolar.jip.clientId = APP112
    jinkosolar.jip.jwtKey = SCP-5WN8DAK0
    jinkosolar.jip.jwtSecret = ENC(/BU42D4B1tTCW6azGLIpHe1vzr5wrKWq1/CGGYeqNQ9pi75f0tvCDuTtCi2FXHCH5GdgT/yiHtQ=)
    jinkosolar.jip.version = 2.0.0
    ###########################################
    jinkosolar.asprova.url = http://************:8081
    asprova.db.url = jdbc:sqlserver://************:1433;database=JK-Asprova_test;useSSL=false;serverTimezone=UTC
    asprova.db.user = sa
    asprova.db.password = ENC(fKa7HhVvcqB98Kw1E7dOnbofL0JmIBp0)
    ###########################################
    rocketmq.name-server = ************:32003
    rocketmq.producer.group=scp-mps-api
    ###########################################
    oss.io.accessKey=ENC(/NGyYwjV3hnobdQDb9nac01nGS06VurU1Mxp55M0orMmKnOiU/MMRQ==)
    oss.io.bucket= scp-test
    oss.io.endpoint= http://************:9001
    oss.io.secretKey= ENC(Q7e4D2w5/T/mS5RAA6lieOhOcTngfz5eFyZcgOpeYl4R1FXJjZvwgQdMNToVqM/2LMMxieeoGIsp2vykdl1ijA==)
    oss.io.allowFiles= .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,.doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.aac,.mp4
    oss.io.expireSecond= 60
    env.gateway.url = https://scp-gateway-sit.jinkosolar.com
