apiVersion: v1
kind: ConfigMap
metadata:
  name: scp-mps-api
  namespace: uat-environment
data:
  application.properties: |
    spring.application.name = scp-mps-api
    spring.profiles.active = jdbc,cloud,redis
    metadata.name = scp-mps-api
    metadata.namespace = uat-environment
    ##########################################
    endpoints.health.enabled=true
    spring.main.allow-bean-definition-overriding=true
    ##########################################
    spring.datasource.druid.url = ENC(QHQGm+7b63I6htnQLNv8BDJsWDQsSSLlNJLCnnwMQwCWNHxo9tVP0ULXfRXKobb0XjvZXc7zkWMZR4O/KmAs6vcX9oGPSS7jUX9ngGUZ+ZfQxId6J1qBFA0QelFCeGl8RjBkMKtD6SM/33o0wwtvMWWh3Bfb/q9KNvG9oflxrCcYQgOiA47vZH2agoh/FJ88wX7Ncoj0/Oke/vmrASVFZDi0lb/2Ua7892+o2i0e3D5yxhHxmIynXcj4caMO8Xnz8GjVe8RsPurp7QTNztPDkhJBOBrv+BUITGbBlM7wxNP3zl6XkkCgo3NaeFE9uiZTIh2Oic4jPNU0ODF+yqWU8h1e+mOssiTUNkLy3yJ8chVCupX4UvUxtcPXCBrgMW+PEHuF7PYo58FuTvCMGbYXFIsc/lfDNF9nFidA25u7v/S+hrVggduLYj4M8iscAKMw2el8G0FDVaG7gb3JfipYaP/yEmJpf5UFCNgD34juOUIAJzf1Dt6T7Dp6bvUI3A8A4pthdYKf90ElaMAF7P3uxU+kpedUJGFgom1Yl42U3Jr5u+51l4hf2A==)
    spring.redis.password = ENC(u4HidmCG3NDfLnLmk0fzf1WSRcpEstV1pfgtoG7hPFc=)
    spring.redis.cluster.nodes =
    spring.redis.sentinel.master = mymaster
    spring.redis.sentinel.nodes = redis-ha-announce-0.uat-environment:26379,redis-ha-announce-1.uat-environment:26379,redis-ha-announce-2.uat-environment:26379
    ###########################################
    security.oauth2.client.client-id = scp-mps-api
    security.oauth2.client.client-secret = 624fb786074683000610ea33
    oauth.url = http://oauth.jinkosolar.com
    security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
    security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
    security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
    security.oauth2.resource.preferTokenInfo = false
    security.oauth2.client.scope = read,write,trust
    jinkosolar.env = uat
    ##########################################
    entity.jpa.package = com.jinkosolar.scp.mps.domain
    entity.scan.package = com.jinkosolar.scp.mps.domain
    scan.base.package = com.jinkosolar.scp
    ###########################################
    jinkosolar.jip.url = http://*************:8000/JIP
    jinkosolar.jip.clientId = APP112
    jinkosolar.jip.jwtKey = SCP-5WN8DAK0
    jinkosolar.jip.jwtSecret = ENC(rpcM7usPOfWjGaGLBZm0Kt0oaVqYMCW2VvI2WAKTWpzkYWkvhcegW6SGn4g5vNK/YHFWI77YEuo=)
    jinkosolar.jip.version = 2.0.0
    ###########################################
    jinkosolar.asprova.url = http://************:8081
    asprova.db.url = ******************************************************************************************
    asprova.db.user = sa
    asprova.db.password = ENC(9HykMPuHsRqFgW3MC+JNhJLCBaJp6Pem)
    ###########################################
    rocketmq.name-server = ************:32003
    rocketmq.producer.group=scp-mps-api
    ###########################################
    oss.io.accessKey= ENC(L/obM7E702CTOj69qc83KRjfAmX3WRuJvz50UX3UnJho4NZ5OS1I5g==)
    oss.io.bucket= scp-test
    oss.io.endpoint= http://************:9001
    oss.io.secretKey= ENC(U8gK6wsYOxlNf4wJ7RM/GqRP1zGGsliAzu38v9FU0+EiBvIm4aMhyxdCZ4AuY80QK3RCrMvPG7sTnNb5z3hpiQ==)
    oss.io.allowFiles= .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,.doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.aac,.mp4
    oss.io.expireSecond= 60
