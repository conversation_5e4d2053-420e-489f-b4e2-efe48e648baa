package com.jinkosolar.scp.mps.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * REST API应用微服务
 */
@SpringBootApplication(scanBasePackages = {"com.jinkosolar.scp.mps", "com.ibm.dpf.common.config", "com.ibm.dpf.gateway", "com.ibm.scp.common.api", "com.jinkosolar.scp.jip"},
        exclude = {CacheAutoConfiguration.class,
                MongoAutoConfiguration.class,
                MongoRepositoriesAutoConfiguration.class,
                MongoDataAutoConfiguration.class,
                FreeMarkerAutoConfiguration.class,
                RabbitAutoConfiguration.class})
@EnableDiscoveryClient
@EnableHystrix
@EnableSwagger2
@EnableAsync
@RefreshScope
@EnableJpaAuditing(auditorAwareRef = "userAuditorAware")
@EnableFeignClients(basePackages = {"com.jinkosolar.scp.mps", "com.ibm.scp.common.api.component", "com.jinkosolar.scp.jip"})
public class MpsApiApplication {
    /**
     * 应用入口
     *
     * @param args
     */
    public static void main(String[] args) {
        SpringApplication.run(MpsApiApplication.class, args);
    }
}
