package com.jinkosolar.scp.mps.api.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.ibm.scp.common.api.component.cache.caffeine.CustomeCaffeineCacheManager;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/11
 */
//@Configuration
public class CaffeineCacheConfig {
    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("cacheManager")
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CustomeCaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(3, TimeUnit.MINUTES)
                // 初始的缓存空间大小
                .initialCapacity(100)
                .recordStats()
                // 缓存的最大条数
                .maximumSize(100000));
        return cacheManager;
    }

}
