package com.jinkosolar.scp.mps.api.config;

import com.ibm.scp.common.api.configure.ExceptionTranslator;
import com.ibm.scp.common.api.util.BizException;
import com.ibm.scp.common.api.util.Message;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.format.DateTimeParseException;

@Slf4j
@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
@RestControllerAdvice
public class DateTimeParseExceptionHandler{


    @ExceptionHandler(DateTimeParseException.class)
    public ResponseEntity<Results<Message>> handException(DateTimeParseException e) {
        log.warn("【DateTimeParseException】handleException", e);
        return Results.createFailRes(MessageHelper.getMessage("解析异常,日期格式错误"+e.getParsedString()).getDesc());
    }


}
