package com.jinkosolar.scp.mps.api.config;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.util.MyThreadLocal;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class FeignConfiguration {

    @Value("${scp.super.token:e342W3WeJH423rr}")
    public String superToken;

    @Bean
    public RequestInterceptor requestInterceptor() {
        return new FeignBasicAuthRequestInterceptor();
    }

    /**
     * Feign请求拦截器
     *
     * <AUTHOR>
     * @create 2017-11-10 17:25
     **/
    public class FeignBasicAuthRequestInterceptor implements RequestInterceptor {

        @Override
        public void apply(RequestTemplate template) {
            log.debug("1111========开始透传相关信息========：", JSONUtil.toJsonStr(MyThreadLocal.get()));
            template.header("lang", MyThreadLocal.get().getLang());
            // 默认给token
            if (StringUtils.isBlank(MyThreadLocal.get().getToken())) {
                template.header("accesstoken", superToken);
            } else {
                template.header("accesstoken", MyThreadLocal.get().getToken());
            }
            template.header("applicationId", MyThreadLocal.get().getApplicationId());
            log.debug("1111========透传完成========");
        }
    }
}
