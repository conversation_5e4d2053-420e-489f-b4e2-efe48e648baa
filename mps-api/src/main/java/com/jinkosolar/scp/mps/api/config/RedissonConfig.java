package com.jinkosolar.scp.mps.api.config;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@Configuration
@RefreshScope
@Slf4j
public class RedissonConfig {

    @Value("${spring.redis.cluster.nodes:#{null}}")
    public String clusterAddress;

    @Value("${spring.redis.host:#{null}}")
    public String singleAddress;

    @Value("${spring.redis.port:#{null}}")
    public String port;

    @Value("${spring.redis.password:#{null}}")
    public String password;

    @Value("${spring.redis.sentinel.nodes:#{null}}")
    public String sentinelNodes;

    @Value("${spring.redis.sentinel.master:#{null}}")
    public String sentinelMaster;

    @Bean(destroyMethod = "shutdown")
    public RedissonClient redisson() throws IOException {
        Config config = new Config();
        //集群模式配置
        if (StringUtils.isNotBlank(clusterAddress)) {
            List<String> nodes = Arrays.asList(clusterAddress.split(","));

            List<String> clusterNodes = new ArrayList<>();
            for (int i = 0; i < nodes.size(); i++) {
                clusterNodes.add("redis://" + nodes.get(i));
            }
            ClusterServersConfig serverConfig = config.useClusterServers()
                    .addNodeAddress(clusterNodes.toArray(new String[clusterNodes.size()]));
//            serverConfig.setSubscriptionConnectionMinimumIdleSize(5);
//            serverConfig.setSubscriptionConnectionPoolSize(250);
//            serverConfig.setSlaveConnectionMinimumIdleSize(160);
//            serverConfig.setSlaveConnectionPoolSize(320);
//            serverConfig.setMasterConnectionMinimumIdleSize(160);
//            serverConfig.setMasterConnectionPoolSize(320);
//            serverConfig.setSubscriptionsPerConnection(25);

            if (!StringUtils.isEmpty(password)) {
                serverConfig.setPassword(password);
            }
        } else if (StringUtils.isNotBlank(sentinelNodes)) {
            //哨兵模式配置
            String[] nodeStr = sentinelNodes.split(",");
            List<String> newNodes = new ArrayList(nodeStr.length);
            Arrays.stream(nodeStr).forEach((index) -> newNodes.add(
                    index.startsWith("redis://") ? index : "redis://" + index));

            SentinelServersConfig serverConfig = config.useSentinelServers()
                    .addSentinelAddress(newNodes.toArray(new String[0]))
                    .setMasterName(sentinelMaster)
                    .setReadMode(ReadMode.SLAVE);
//            serverConfig.setSubscriptionConnectionMinimumIdleSize(5);
//            serverConfig.setSubscriptionConnectionPoolSize(250);
//            serverConfig.setSlaveConnectionMinimumIdleSize(160);
//            serverConfig.setSlaveConnectionPoolSize(320);
//            serverConfig.setMasterConnectionMinimumIdleSize(160);
//            serverConfig.setMasterConnectionPoolSize(320);
//            serverConfig.setSubscriptionsPerConnection(25);

            if (StringUtils.isNotBlank(password)) {
                serverConfig.setPassword(password);
            }
        } else {
            //单节点配置
            String address = "redis://" + singleAddress + ":" + port;
            SingleServerConfig serverConfig = config.useSingleServer();
            serverConfig.setAddress(address);
//            serverConfig.setSubscriptionConnectionMinimumIdleSize(3);
//            serverConfig.setSubscriptionConnectionPoolSize(150);
//            serverConfig.setConnectionMinimumIdleSize(96);
//            serverConfig.setSubscriptionsPerConnection(15);
//            serverConfig.setConnectionPoolSize(192);

            if (!StringUtils.isEmpty(password)) {
                serverConfig.setPassword(password);
            }
        }

        log.info("【MRP-API.RedissonConfig】config：", JSONUtil.toJsonStr(config));
        return Redisson.create(config);
    }
}
