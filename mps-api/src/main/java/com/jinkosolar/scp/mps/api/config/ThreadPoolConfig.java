package com.jinkosolar.scp.mps.api.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.*;

/**
 * 配置默认线程池，后期修改
 *
 * <AUTHOR>
 * @date 2022/1/7
 */
@Configuration
public class ThreadPoolConfig {
    @Bean(name = "threadPoolExecutor") // 将方法返回的对象注册为Spring应用上下文中的一个Bean
    @Primary
    // 创建一个自定义的线程池（主要用于管理和执行后台任务）
    public ExecutorService threadPoolExecutor() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() + 1,
                Runtime.getRuntime().availableProcessors() + 1,
                60L,
                TimeUnit.SECONDS,
                // 工作队列
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                // 线程工厂，用于创建新线程
                Executors.defaultThreadFactory(),
                // 在任务太多处理不过来时，新任务将被拒绝并抛出异常
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("asyncTaskExecutor")
    public ExecutorService asyncTaskExecutor() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 2,
                Runtime.getRuntime().availableProcessors() * 2,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
//    @Qualifier("mrpThreadPool")
    public ExecutorService mrpThreadPool() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                1,
                1,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("verifyThreadPool")
    public ExecutorService verifyThreadPool() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                1,
                1,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("cellDeliveryThreadPool")
    public ExecutorService cellDeliveryThreadPool() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() + 1,
                Runtime.getRuntime().availableProcessors() + 1,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("batchDBExecutor")
    public ExecutorService batchDBExecutor() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 2,
                Runtime.getRuntime().availableProcessors() * 2,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }

    @Bean
    @Qualifier("mpsPrePublishExecutor")
    public ExecutorService mpsPrePublishExecutor() {
        return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(
                10,
                10,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(Integer.MAX_VALUE),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy()
        ));
    }
}
