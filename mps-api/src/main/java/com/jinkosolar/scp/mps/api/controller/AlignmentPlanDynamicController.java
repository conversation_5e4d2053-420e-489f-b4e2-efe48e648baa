package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.AlignmentPlanDynamicDTO;
import com.jinkosolar.scp.mps.domain.query.AlignmentPlanDynamicQuery;
import com.jinkosolar.scp.mps.service.AlignmentPlanDynamicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 定线规划动态列表相关操作控制层
 * 
 * <AUTHOR> 2024-07-15 13:45:38
 */
@RequestMapping(value = "/alignment-plan-dynamic")
@RestController
@Api(value = "alignmentPlanDynamic", tags = "定线规划动态列表相关操作控制层")
public class AlignmentPlanDynamicController extends BaseController {
    @Autowired
    private AlignmentPlanDynamicService alignmentPlanDynamicService;

    @PostMapping("/page")
    @ApiOperation(value = "定线规划动态列表分页查询")
    public ResponseEntity<Results<Page<Map<String, Object>>>> page(@RequestBody AlignmentPlanDynamicQuery query) {
        return Results.createSuccessRes(alignmentPlanDynamicService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "定线规划动态列表详情")
    public ResponseEntity<Results<AlignmentPlanDynamicDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(alignmentPlanDynamicService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增定线规划动态列表")
    public ResponseEntity<Results<AlignmentPlanDynamicDTO>> insert(@RequestBody AlignmentPlanDynamicDTO alignmentPlanDynamicDTO) {
        validObject(alignmentPlanDynamicDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(alignmentPlanDynamicService.saveOrUpdate(alignmentPlanDynamicDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新定线规划动态列表")
    public ResponseEntity<Results<AlignmentPlanDynamicDTO>> update(@RequestBody AlignmentPlanDynamicDTO alignmentPlanDynamicDTO) {
        validObject(alignmentPlanDynamicDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(alignmentPlanDynamicService.saveOrUpdate(alignmentPlanDynamicDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除定线规划动态列表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        alignmentPlanDynamicService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出定线规划动态列表")
    @PostMapping("/export")
    public void export(@RequestBody AlignmentPlanDynamicQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        alignmentPlanDynamicService.export(query, response);
    }

    @ApiOperation(value = "导入定线规划动态列表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = alignmentPlanDynamicService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}