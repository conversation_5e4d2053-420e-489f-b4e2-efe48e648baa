
package com.jinkosolar.scp.mps.api.controller;
import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;

import com.jinkosolar.scp.mps.domain.dto.ApsModuleLossDTO;
import com.jinkosolar.scp.mps.domain.query.ApsModuleLossQuery;
import com.jinkosolar.scp.mps.service.ApsModuleLossService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 切换损失相关操作控制层
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
@RequestMapping(value = "/module-loss")
@RestController
@Api(value = "ApsModuleLoss", tags = "切换损失相关操作控制层")
public class ApsModuleLossController extends BaseController {
    @Autowired
    private ApsModuleLossService apsModuleLossService;

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions() {
        return Results.createSuccessRes(apsModuleLossService.versions());
    }

    @PostMapping("/page")
    @ApiOperation(value = "查询切换损失列表", notes = "查询切换损失列表")
    public ResponseEntity<Results<Page<ApsModuleLossDTO>>> page(@RequestBody ApsModuleLossQuery query) {
        return Results.createSuccessRes(apsModuleLossService.page(query));
    }
    @PostMapping("/list")
    @ApiOperation(value = "查询切换损失列表", notes = "查询切换损失列表")
    public ResponseEntity<Results<List<ApsModuleLossDTO>>> list(@RequestBody ApsModuleLossQuery query) {
        return Results.createSuccessRes(apsModuleLossService.list(query));
    }
    @PostMapping("/detail")
    @ApiOperation(value = "查询切换损失详情", notes = "查询切换损失详情")
    @ApiImplicitParam(name = "id", value = "主键")
    public ResponseEntity<Results<ApsModuleLossDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(apsModuleLossService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/save")
    @ApiOperation(value = "保存切换损失信息", notes = "保存切换损失信息")
    public ResponseEntity<Results<ApsModuleLossDTO>> save(
            @Validated(ValidGroups.Insert.class) @RequestBody ApsModuleLossDTO ApsModuleLossDTO) throws Exception {
        return Results.createSuccessRes(apsModuleLossService.saveOrUpdate(ApsModuleLossDTO));
    }
    @PostMapping("/submit")
    @ApiOperation(value = "提交切换损失信息", notes = "提交切换损失信息")
    public ResponseEntity<Results<ApsModuleLossDTO>> submit(
            @Validated(ValidGroups.Update.class) @RequestBody ApsModuleLossDTO ApsModuleLossDTO) throws Exception {
        return Results.createSuccessRes(apsModuleLossService.saveOrUpdate(ApsModuleLossDTO));
    }
    @PostMapping("/delete")
    @ApiOperation(value = "批量物理删除切换损失信息", notes = "批量物理删除切换损失信息")
    @ApiImplicitParam(name = "ids", value = "主键集合")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        apsModuleLossService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }
    @ApiOperation(value = "导出切换损失数据", notes = "导出切换损失数据")
    @PostMapping("/export")
    public void export(@RequestBody ApsModuleLossQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        apsModuleLossService.export(query, response);
    }
    @ApiOperation(value = "导入切换损失数据", notes = "导入切换损失数据")
    @PostMapping("/import")
    public ResponseEntity<Results<Object>> importFile(@RequestPart("excelPara") ExcelPara excelPara, @RequestPart("file") MultipartFile file) {
        ImportResultDTO importResultDTO = apsModuleLossService.importData(file, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}
