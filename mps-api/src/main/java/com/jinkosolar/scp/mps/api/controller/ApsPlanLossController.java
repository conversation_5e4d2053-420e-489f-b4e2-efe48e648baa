
package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ApsPlanLossDTO;
import com.jinkosolar.scp.mps.domain.query.ApsPlanLossQuery;
import com.jinkosolar.scp.mps.service.ApsPlanLossService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户相关操作控制层
 */
@RequestMapping(value = "/plan-loss")
@RestController
@Api(value = "ApsPlanLoss", tags = "计划损失相关操作控制层")
public class ApsPlanLossController extends BaseController {

    @Resource
    private ApsPlanLossService apsPlanLossService;

    @PostMapping("/page")
    @ApiOperation(value = "查询计划损失列表", notes = "查询计划损失列表")
    public ResponseEntity<Results<Page<ApsPlanLossDTO>>> page(@RequestBody ApsPlanLossQuery query) {
//        query.setPageSize(Integer.MAX_VALUE);
        return Results.createSuccessRes(apsPlanLossService.page(query));
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询计划损失列表", notes = "查询计划损失列表")
    public ResponseEntity<Results<List<ApsPlanLossDTO>>> list(@RequestBody ApsPlanLossQuery query) {
        return Results.createSuccessRes(apsPlanLossService.list(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询计划损失详情", notes = "查询计划损失详情")
    @ApiImplicitParam(name = "id", value = "主键")
    public ResponseEntity<Results<ApsPlanLossDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(apsPlanLossService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存计划损失信息", notes = "保存计划损失信息")
    public ResponseEntity<Results<ApsPlanLossDTO>> save(
            @Validated(ValidGroups.Insert.class) @RequestBody ApsPlanLossDTO ApsPlanLossDTO) throws Exception {
        return Results.createSuccessRes(apsPlanLossService.saveOrUpdate(ApsPlanLossDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量物理删除计划损失信息", notes = "批量物理删除计划损失信息")
    @ApiImplicitParam(name = "ids", value = "主键集合")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        apsPlanLossService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出计划损失数据", notes = "导出计划损失数据")
    @PostMapping("/export")
    public void export(@RequestBody ApsPlanLossQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        apsPlanLossService.export(query, response);
    }

    @ApiOperation(value = "导入计划损失数据", notes = "导入计划损失数据")
    @PostMapping("/import")
    public ResponseEntity<Results<Object>> importFile(@RequestPart("excelPara") ExcelPara excelPara
            , @RequestPart("file") MultipartFile file,@RequestPart(value = "confirmFlag",required = false) Boolean confirmFlag) {
        ImportResultDTO importResult = apsPlanLossService.importData(file, excelPara,confirmFlag);
        if (importResult.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResult);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResult.getFailMessages()));
    }
}
