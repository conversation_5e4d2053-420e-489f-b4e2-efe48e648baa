package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.AssignBasePriorityDTO;
import com.jinkosolar.scp.mps.domain.query.AssignBasePriorityQuery;
import com.jinkosolar.scp.mps.service.AssignBasePriorityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分配基地优先级基础表相关操作控制层
 * 
 * <AUTHOR> 2024-08-15 15:07:35
 */
@RequestMapping(value = "/assign-base-priority")
@RestController
@Api(value = "assignBasePriority", tags = "分配基地优先级基础表相关操作控制层")
@RequiredArgsConstructor  
public class AssignBasePriorityController extends BaseController {    
    private final AssignBasePriorityService assignBasePriorityService; 

    @PostMapping("/page")
    @ApiOperation(value = "分配基地优先级基础表分页查询")
    public ResponseEntity<Results<Page<AssignBasePriorityDTO>>> page(@RequestBody AssignBasePriorityQuery query) {
        return Results.createSuccessRes(assignBasePriorityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "分配基地优先级基础表详情")
    public ResponseEntity<Results<AssignBasePriorityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(assignBasePriorityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增分配基地优先级基础表")
    public ResponseEntity<Results<Void>> insert(@RequestBody AssignBasePriorityDTO assignBasePriorityDTO) {
        validObject(assignBasePriorityDTO, ValidGroups.Insert.class);
        assignBasePriorityService.saveOrUpdate(assignBasePriorityDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新分配基地优先级基础表")
    public ResponseEntity<Results<Void>> update(@RequestBody AssignBasePriorityDTO assignBasePriorityDTO) {
        validObject(assignBasePriorityDTO, ValidGroups.Update.class);
        assignBasePriorityService.saveOrUpdate(assignBasePriorityDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除分配基地优先级基础表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        assignBasePriorityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出分配基地优先级基础表")
    @PostMapping("/export")
    public void export(@RequestBody AssignBasePriorityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        assignBasePriorityService.export(query, response);
    }

    @ApiOperation(value = "导入分配基地优先级基础表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = assignBasePriorityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}