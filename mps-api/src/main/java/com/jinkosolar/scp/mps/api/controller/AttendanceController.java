package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.AttendanceDTO;
import com.jinkosolar.scp.mps.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.query.AttendanceQuery;
import com.jinkosolar.scp.mps.domain.util.Constant;
import com.jinkosolar.scp.mps.service.AttendanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 相关操作控制层
 *
 * <AUTHOR> 2024-05-10 09:30:10
 */
@RequestMapping(value = "/attendance")
@RestController
@Api(value = "attendance", tags = "相关操作控制层")
public class AttendanceController extends BaseController {
    @Autowired
    private AttendanceService attendanceService;
    @Autowired
    private SyncTableUtils syncTableUtils;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<Results<Page<AttendanceDTO>>> page(@RequestBody AttendanceQuery query) {
        return Results.createSuccessRes(attendanceService.page(query));
    }

    @PostMapping("/list")
    @ApiOperation(value = "列表查询")
    public ResponseEntity<Results<List<CommonOption>>> list(@RequestBody AttendanceQuery query) {
        return Results.createSuccessRes(attendanceService.list(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "详情")
    public ResponseEntity<Results<AttendanceDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(attendanceService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增")
    public ResponseEntity<Results<AttendanceDTO>> insert(@RequestBody AttendanceDTO attendanceDTO) {
        validObject(attendanceDTO, ValidGroups.Insert.class);
        AttendanceDTO result =attendanceService.saveOrUpdate(attendanceDTO);
                //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    public ResponseEntity<Results<AttendanceDTO>> update(@RequestBody AttendanceDTO attendanceDTO) {
        validObject(attendanceDTO, ValidGroups.Update.class);
        AttendanceDTO result = attendanceService.saveOrUpdate(attendanceDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        attendanceService.logicDeleteByIds(ids);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    public void export(@RequestBody AttendanceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        attendanceService.export(query, response);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara,
                                                               @RequestPart("businessDepartment") String businessDepartment) {
        ImportResultDTO importResultDTO = attendanceService.importData(multipartFile, excelPara,businessDepartment);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    public void syncToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> list = new ArrayList<>();
        list.add(Constant.MPS_ATTENDANCE);
        syncTableDTO.setLovCodes(list);
        syncTableUtils.syncTables(syncTableDTO);
    }
}
