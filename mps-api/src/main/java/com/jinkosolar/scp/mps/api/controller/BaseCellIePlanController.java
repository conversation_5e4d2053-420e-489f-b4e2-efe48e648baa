package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.jinkosolar.scp.mps.domain.dto.MyImportResultDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.ibm.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jinkosolar.scp.mps.domain.query.BaseCellIePlanQuery;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.jinkosolar.scp.mps.domain.save.BaseCellIePlanSaveDTO;
import com.jinkosolar.scp.mps.domain.entity.BaseCellIePlan;
import com.jinkosolar.scp.mps.domain.dto.BaseCellIePlanDTO;
import com.jinkosolar.scp.mps.service.BaseCellIePlanService;
import com.ibm.scp.common.api.util.Results;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * [说明]IE更新月度日计划表 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-12-11
 */
@RestController
@RequestMapping("/base-cell-ie-plan")
@RequiredArgsConstructor
@Api(value = "base-cell-ie-plan", tags = "IE更新月度日计划表操作")
public class BaseCellIePlanController {
    private final BaseCellIePlanService baseCellIePlanService;




    @PostMapping("/dateReport/list")
    @ApiOperation(value = "IE导入查询报表", notes = "IE导入查询报表")
    public ResponseEntity<Results<List<BaseCellIePlanDTO>>> queryDateReport(@RequestBody BaseCellIePlanQuery query) {
        return Results.createSuccessRes(baseCellIePlanService.queryDateReport(query));
    }



    @PostMapping("/dateReport/export")
    @ApiOperation(value = "IE导入查询报表导出")
    public void exportDateReport(@RequestBody BaseCellIePlanQuery query, HttpServletResponse response) {
        baseCellIePlanService.exportDateReport(query, response);
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "IE更新月度日计划表分页列表", notes = "获得IE更新月度日计划表分页列表")
    public ResponseEntity<Results<Page<BaseCellIePlanDTO>>> queryByPage(@RequestBody BaseCellIePlanQuery query) {
        return Results.createSuccessRes(baseCellIePlanService.queryByPage(query));
    }




    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BaseCellIePlanDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(baseCellIePlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BaseCellIePlanDTO>> save(@Valid @RequestBody BaseCellIePlanSaveDTO saveDTO) {
        return Results.createSuccessRes(baseCellIePlanService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        baseCellIePlanService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BaseCellIePlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        baseCellIePlanService.export(query, response);
    }

    @ApiOperation(value = "导入IE更新月度日计划表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestParam("file") MultipartFile multipartFile) {

        MyImportResultDTO importResultDTO = baseCellIePlanService.importData(multipartFile);
        if (importResultDTO.getImportResultDTO().getFailMessages().isEmpty()) {
            if(StringUtils.isNotBlank(importResultDTO.getWarnMessage())){
                return Results.createFailRes(importResultDTO.getWarnMessage());
            }
            return Results.createSuccessRes(importResultDTO.getImportResultDTO());
        }
        return Results.createFailRes(JSONUtil.toJsonStr(importResultDTO.getImportResultDTO().getFailMessages()));
    }

    @ApiOperation(value = "同步aps")
    @PostMapping("/syncTable")
    public ResponseEntity<Results<Object>> syncTable(@RequestBody BaseCellIePlanQuery query) {
        baseCellIePlanService.syncTable();
        return Results.createSuccessRes(Results.SUCCESS_CODE);
    }

    /**
     * 根据条件查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/findIePlanByCondition")
    @ApiOperation(value = "根据条件查询", notes = "根据条件查询")
    public ResponseEntity<Results<List<BaseCellIePlanDTO>>> findIePlanByCondition(@RequestBody BaseCellIePlanQuery query) {
        return Results.createSuccessRes(baseCellIePlanService.findIePlanByCondition(query));
    }

}
