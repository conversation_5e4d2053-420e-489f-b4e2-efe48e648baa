package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.dpf.gateway.domain.Results;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BaseCellOutputMonthDTO;
import com.jinkosolar.scp.mps.domain.dto.CellForecastDTO;
import com.jinkosolar.scp.mps.domain.query.BaseCellOutputMonthQuery;
import com.jinkosolar.scp.mps.service.BaseCellOutputMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基地电池产出汇总月表相关操作控制层
 *
 * <AUTHOR> 2024-04-24 13:41:40
 */
@RequestMapping(value = "/base-cell-output-month")
@RestController
@Api(value = "baseCellOutputMonth", tags = "基地电池产出汇总月表相关操作控制层")
public class BaseCellOutputMonthController extends BaseController {
    @Autowired
    private BaseCellOutputMonthService baseCellOutputMonthService;

    @PostMapping("/page")
    @ApiOperation(value = "基地电池产出汇总月表分页查询")
    public ResponseEntity<Results<Page<BaseCellOutputMonthDTO>>> page(@RequestBody BaseCellOutputMonthQuery query) {
        return Results.createSuccessRes(baseCellOutputMonthService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "基地电池产出汇总月表详情")
    public ResponseEntity<Results<BaseCellOutputMonthDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(baseCellOutputMonthService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增基地电池产出汇总月表")
    public ResponseEntity<Results<BaseCellOutputMonthDTO>> insert(@RequestBody BaseCellOutputMonthDTO baseCellOutputMonthDTO) {
        validObject(baseCellOutputMonthDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(baseCellOutputMonthService.saveOrUpdate(baseCellOutputMonthDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新基地电池产出汇总月表")
    public ResponseEntity<Results<BaseCellOutputMonthDTO>> update(@RequestBody BaseCellOutputMonthDTO baseCellOutputMonthDTO) {
        validObject(baseCellOutputMonthDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(baseCellOutputMonthService.saveOrUpdate(baseCellOutputMonthDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除基地电池产出汇总月表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        baseCellOutputMonthService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出基地电池产出汇总月表")
    @PostMapping("/export")
    public void export(@RequestBody BaseCellOutputMonthQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        baseCellOutputMonthService.export(query, response);
    }

    @ApiOperation(value = "导入基地电池产出汇总月表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestParam("file") MultipartFile multipartFile) {


        ImportResultDTO importResultDTO = baseCellOutputMonthService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return   Results.createSuccessRes(importResultDTO);
        }

        return Results.createFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));

    }

    @ApiOperation(value = "电池产出预测报表查询")
    @PostMapping("/queryAllbBaseCellOutputMonthList")
    public ResponseEntity<com.ibm.scp.common.api.util.Results<List<BaseCellOutputMonthDTO>>> queryAllbBaseCellOutputMonthList() {
        return com.ibm.scp.common.api.util.Results.createSuccessRes(baseCellOutputMonthService.queryAllbBaseCellOutputMonthList());
    }
}
