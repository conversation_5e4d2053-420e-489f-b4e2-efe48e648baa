package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BaseCellOutputMonthDTO;
import com.jinkosolar.scp.mps.domain.dto.BaseCellOutputYearDTO;
import com.jinkosolar.scp.mps.domain.query.BaseCellOutputMonthQuery;
import com.jinkosolar.scp.mps.domain.query.BaseCellOutputYearQuery;
import com.jinkosolar.scp.mps.service.BaseCellOutputMonthService;
import com.jinkosolar.scp.mps.service.BaseCellOutputYearService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基地电池产出汇总年表相关操作控制层
 * 
 * <AUTHOR> 2024-04-24 13:41:40
 */
@RequestMapping(value = "/base-cell-output-year")
@RestController
@Api(value = "baseCellOutputYear", tags = "基地电池产出汇总年表相关操作控制层")
public class BaseCellOutputYearController extends BaseController {
    @Autowired
    private BaseCellOutputYearService baseCellOutputYearService;

    @PostMapping("/page")
    @ApiOperation(value = "基地电池产出汇总月表分页查询")
    public ResponseEntity<Results<Page<BaseCellOutputYearDTO>>> page(@RequestBody BaseCellOutputYearQuery query) {
        return Results.createSuccessRes(baseCellOutputYearService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "基地电池产出汇总月表详情")
    public ResponseEntity<Results<BaseCellOutputYearDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(baseCellOutputYearService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增基地电池产出汇总月表")
    public ResponseEntity<Results<BaseCellOutputYearDTO>> insert(@RequestBody BaseCellOutputYearDTO baseCellOutputMonthDTO) {
        validObject(baseCellOutputMonthDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(baseCellOutputYearService.saveOrUpdate(baseCellOutputMonthDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新基地电池产出汇总月表")
    public ResponseEntity<Results<BaseCellOutputYearDTO>> update(@RequestBody BaseCellOutputYearDTO baseCellOutputYearDTO) {
        validObject(baseCellOutputYearDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(baseCellOutputYearService.saveOrUpdate(baseCellOutputYearDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除基地电池产出汇总月表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        baseCellOutputYearService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出基地电池产出汇总月表")
    @PostMapping("/export")
    public void export(@RequestBody BaseCellOutputYearQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        baseCellOutputYearService.export(query, response);
    }

    @ApiOperation(value = "导入基地电池产出汇总月表")
    @PostMapping("/import")    
    public ResponseEntity<Results<String>> importFile(@RequestParam("file") MultipartFile multipartFile) {
        return Results.createSuccessRes(baseCellOutputYearService.importData(multipartFile));
    }
}