package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BaseGradeCapacityDTO;
import com.jinkosolar.scp.mps.service.BaseGradeCapacityService;
import com.jinkosolar.scp.mps.domain.query.BaseGradeCapacityQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产能数据表相关操作控制层
 * 
 * <AUTHOR> 2024-07-01 10:43:15
 */
@RequestMapping(value = "/base-grade-capacity")
@RestController
@Api(value = "baseGradeCapacity", tags = "产能数据表相关操作控制层")
public class BaseGradeCapacityController extends BaseController {
    @Autowired
    private BaseGradeCapacityService baseGradeCapacityService;

    @PostMapping("/page")
    @ApiOperation(value = "产能数据表分页查询")
    public ResponseEntity<Results<Page<Map<String, Object>>>> page(@RequestBody BaseGradeCapacityQuery query) {
        return Results.createSuccessRes(baseGradeCapacityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产能数据表详情")
    public ResponseEntity<Results<BaseGradeCapacityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(baseGradeCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产能数据表")
    public ResponseEntity<Results<BaseGradeCapacityDTO>> insert(@RequestBody BaseGradeCapacityDTO baseGradeCapacityDTO) {
        validObject(baseGradeCapacityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(baseGradeCapacityService.saveOrUpdate(baseGradeCapacityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产能数据表")
    public ResponseEntity<Results<BaseGradeCapacityDTO>> update(@RequestBody BaseGradeCapacityDTO baseGradeCapacityDTO) {
        validObject(baseGradeCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(baseGradeCapacityService.saveOrUpdate(baseGradeCapacityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产能数据表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        baseGradeCapacityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产能数据表")
    @PostMapping("/export")
    public void export(@RequestBody BaseGradeCapacityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        baseGradeCapacityService.export(query, response);
    }

    @ApiOperation(value = "导入产能数据表")
    @PostMapping("/import/{type}")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @PathVariable("type") Integer type,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = baseGradeCapacityService.importData(multipartFile,type, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions/{type}")
    @ApiOperation(value = "查询版本号")
    @ApiParam(name = "type", value = "版型类型,1：爬坡，2：实验", required = true)
    public ResponseEntity<Results<List<String>>> versions(@PathVariable("type") Integer type) {
        return Results.createSuccessRes(baseGradeCapacityService.versions(type));
    }

}