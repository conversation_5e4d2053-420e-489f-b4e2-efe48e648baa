package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryAgingConversionDTO;
import com.jinkosolar.scp.mps.domain.query.BatteryAgingConversionQuery;
import com.jinkosolar.scp.mps.service.BatteryAgingConversionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新老水位转换规则相关操作控制层
 * 
 * <AUTHOR> 2024-12-19
 */
@RequestMapping(value = "/battery-aging-conversion")
@RestController
@Api(value = "batteryAgingConversion", tags = "新老水位转换规则相关操作控制层")
@RequiredArgsConstructor
public class BatteryAgingConversionController extends BaseController {
    private final BatteryAgingConversionService batteryAgingConversionService;

    @PostMapping("/page")
    @ApiOperation(value = "新老水位转换规则分页查询")
    public ResponseEntity<Results<Page<BatteryAgingConversionDTO>>> page(@RequestBody BatteryAgingConversionQuery query) {
        return Results.createSuccessRes(batteryAgingConversionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "新老水位转换规则详情")
    public ResponseEntity<Results<BatteryAgingConversionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryAgingConversionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增新老水位转换规则")
    public ResponseEntity<Results<Void>> insert(@RequestBody BatteryAgingConversionDTO batteryAgingConversionDTO) {
        validObject(batteryAgingConversionDTO, ValidGroups.Insert.class);
        batteryAgingConversionService.saveOrUpdate(batteryAgingConversionDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新新老水位转换规则")
    public ResponseEntity<Results<Void>> update(@RequestBody BatteryAgingConversionDTO batteryAgingConversionDTO) {
        validObject(batteryAgingConversionDTO, ValidGroups.Update.class);
        batteryAgingConversionService.saveOrUpdate(batteryAgingConversionDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除新老水位转换规则")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        batteryAgingConversionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出新老水位转换规则")
    @PostMapping("/export")
    public void export(@RequestBody BatteryAgingConversionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryAgingConversionService.export(query, response);
    }

    @ApiOperation(value = "导入新老水位转换规则")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultDTO importResultDTO = batteryAgingConversionService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
} 