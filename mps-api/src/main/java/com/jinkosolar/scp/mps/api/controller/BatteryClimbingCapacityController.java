package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryClimbingCapacityDTO;
import com.jinkosolar.scp.mps.domain.query.BatteryClimbingCapacityQuery;
import com.jinkosolar.scp.mps.service.BatteryClimbingCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 电池-IE爬坡产能相关操作控制层
 *
 * <AUTHOR> ${datetimeNow}
 */
@RequestMapping(value = "/battery-climbing-capacity")
@RestController
@Api(value = "batteryClimbingCapacity", tags = "电池-IE爬坡产能相关操作控制层")
public class BatteryClimbingCapacityController extends BaseController {
    @Autowired
    private BatteryClimbingCapacityService batteryClimbingCapacityService;

    @PostMapping("/page")
    @ApiOperation(value = "电池-IE爬坡产能分页查询")
    public ResponseEntity<Results<Page<Map<String, Object>>>> page(@RequestBody BatteryClimbingCapacityQuery query) {
        return Results.createSuccessRes(batteryClimbingCapacityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池-IE爬坡产能详情")
    public ResponseEntity<Results<BatteryClimbingCapacityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryClimbingCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池-IE爬坡产能")
    public ResponseEntity<Results<BatteryClimbingCapacityDTO>> insert(@RequestBody BatteryClimbingCapacityDTO batteryClimbingCapacityDTO) {
        validObject(batteryClimbingCapacityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(batteryClimbingCapacityService.saveOrUpdate(batteryClimbingCapacityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池-IE爬坡产能")
    public ResponseEntity<Results<BatteryClimbingCapacityDTO>> update(@RequestBody BatteryClimbingCapacityDTO batteryClimbingCapacityDTO) {
        validObject(batteryClimbingCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryClimbingCapacityService.saveOrUpdate(batteryClimbingCapacityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池-IE爬坡产能")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        batteryClimbingCapacityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池-IE爬坡产能")
    @PostMapping("/export")
    public void export(@RequestBody BatteryClimbingCapacityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryClimbingCapacityService.export(query, response);
    }

    @ApiOperation(value = "导入电池-IE爬坡产能")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = batteryClimbingCapacityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/convertAsprova")
    @ApiOperation(value = "转换为Asprova所需格式数据并保存")
    public ResponseEntity<Results<Object>> convertAsprova() {
        batteryClimbingCapacityService.convertAsprova();
        batteryClimbingCapacityService.syncApsTable();
        return Results.createSuccessRes();
    }
}
