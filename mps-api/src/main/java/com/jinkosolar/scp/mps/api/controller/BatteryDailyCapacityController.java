package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryDailyCapacityDTO;
import com.jinkosolar.scp.mps.domain.query.BatteryDailyCapacityQuery;
import com.jinkosolar.scp.mps.service.BatteryDailyCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池-IE日产能相关操作控制层
 *
 * <AUTHOR> ${datetimeNow}
 */
@RequestMapping(value = "/battery-daily-capacity")
@RestController
@Api(value = "batteryDailyCapacity", tags = "电池-IE日产能相关操作控制层")
public class BatteryDailyCapacityController extends BaseController {
    @Autowired
    private BatteryDailyCapacityService batteryDailyCapacityService;

    @PostMapping("/page")
    @ApiOperation(value = "电池-IE日产能分页查询")
    public ResponseEntity<Results<Page<BatteryDailyCapacityDTO>>> page(@RequestBody BatteryDailyCapacityQuery query) {
        return Results.createSuccessRes(batteryDailyCapacityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池-IE日产能详情")
    public ResponseEntity<Results<BatteryDailyCapacityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryDailyCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池-IE日产能")
    public ResponseEntity<Results<BatteryDailyCapacityDTO>> insert(@RequestBody BatteryDailyCapacityDTO batteryDailyCapacityDTO) {
        validObject(batteryDailyCapacityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(batteryDailyCapacityService.saveOrUpdate(batteryDailyCapacityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池-IE日产能")
    public ResponseEntity<Results<BatteryDailyCapacityDTO>> update(@RequestBody BatteryDailyCapacityDTO batteryDailyCapacityDTO) {
        validObject(batteryDailyCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryDailyCapacityService.saveOrUpdate(batteryDailyCapacityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池-IE日产能")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        batteryDailyCapacityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池-IE日产能")
    @PostMapping("/export")
    public void export(@RequestBody BatteryDailyCapacityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryDailyCapacityService.export(query, response);
    }

    @ApiOperation(value = "导入电池-IE日产能")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = batteryDailyCapacityService.importData(multipartFile, excelPara);
        // 同步基础数据到APS
        batteryDailyCapacityService.syncApsTable();
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}
