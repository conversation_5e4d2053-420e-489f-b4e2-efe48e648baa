package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryDeliveryPlanDayDTO;
import com.jinkosolar.scp.mps.domain.dto.BatteryDeliveryPlanMonthDTO;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.query.BatteryDeliveryPlanDayQuery;
import com.jinkosolar.scp.mps.domain.query.BatteryDeliveryPlanMonthQuery;
import com.jinkosolar.scp.mps.service.BatteryDeliveryPlanDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 外购电池日到货计划相关操作控制层
 *
 * <AUTHOR> 2024-07-15 10:37:50
 */
@RequestMapping(value = "/battery-delivery-plan-day")
@RestController
@Api(value = "batteryDeliveryPlanDay", tags = "外购电池日到货计划相关操作控制层")
public class BatteryDeliveryPlanDayController extends BaseController {
    @Autowired
    private BatteryDeliveryPlanDayService batteryDeliveryPlanDayService;

    @PostMapping("/page")
    @ApiOperation(value = "外购电池日到货计划分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody BatteryDeliveryPlanDayQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "外购电池日到货计划详情")
    public ResponseEntity<Results<BatteryDeliveryPlanDayDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryDeliveryPlanDayService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增外购电池日到货计划")
    public ResponseEntity<Results<BatteryDeliveryPlanDayDTO>> save(@RequestBody BatteryDeliveryPlanDayDTO batteryDeliveryPlanDayDTO) {
        validObject(batteryDeliveryPlanDayDTO, ValidGroups.Insert.class, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryDeliveryPlanDayService.saveOrUpdate(batteryDeliveryPlanDayDTO));
    }


    @PostMapping("/delete")
    @ApiOperation(value = "批量删除外购电池日到货计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody List<BatteryDeliveryPlanDayDTO> batteryDeliveryPlanDayDTOList) {
        batteryDeliveryPlanDayService.deleteByGroupCondition(batteryDeliveryPlanDayDTOList);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出外购电池日到货计划")
    @PostMapping("/export")
    public void export(@RequestBody BatteryDeliveryPlanDayQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryDeliveryPlanDayService.export(query, response);
    }

    @ApiOperation(value = "导入外购电池日到货计划")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = batteryDeliveryPlanDayService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/publish")
    @ApiOperation(value = "发布外购电池日到货计划")
    public ResponseEntity<Results<Object>> publish() {
        batteryDeliveryPlanDayService.publish();
        return Results.createSuccessRes();
    }

    @PostMapping("/cellTypes")
    @ApiOperation(value = "电池片类型下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> cellTypeList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.cellTypeList(query.getLatestFlag()));
    }

    @PostMapping("/primaryGateNumbers")
    @ApiOperation(value = "主栅数下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> primaryGateNumberList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.primaryGateNumberList(query.getLatestFlag()));
    }

    @PostMapping("/areas")
    @ApiOperation(value = "到货区域下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> areaList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.areaList(query.getLatestFlag()));
    }

    @PostMapping("/vendorBrands")
    @ApiOperation(value = "品牌下拉列表")
    public ResponseEntity<Results<List<String>>> vendorBrandList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.vendorBrandList(query.getLatestFlag()));
    }

    @PostMapping("/domesticOverseas")
    @ApiOperation(value = "排产区域下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> domesticOverseaList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.domesticOverseaList(query.getLatestFlag()));
    }

    @PostMapping("/packageTypes")
    @ApiOperation(value = "包装方式下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> packageTypeList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.packageTypeList(query.getLatestFlag()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "版本号下拉列表")
    public ResponseEntity<Results<List<String>>> versionList() {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.versionList());
    }


    @PostMapping("/queryAllBatteryDeliveryPlanDay")
    @ApiOperation(value = "外购到货计划-按天")
    public ResponseEntity<Results<List<BatteryDeliveryPlanDayDTO>>> queryAllBatteryDeliveryPlanDay() {
        return Results.createSuccessRes(batteryDeliveryPlanDayService.queryAllBatteryDeliveryPlanDay());
    }
}