package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryDeliveryPlanMonthDTO;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.query.BatteryDeliveryPlanMonthQuery;
import com.jinkosolar.scp.mps.service.BatteryDeliveryPlanMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外购电池月到货计划相关操作控制层
 *
 * <AUTHOR> 2024-07-15 10:37:50
 */
@RequestMapping(value = "/battery-delivery-plan-month")
@RestController
@Api(value = "batteryDeliveryPlanMonth", tags = "外购电池月到货计划相关操作控制层")
public class BatteryDeliveryPlanMonthController extends BaseController {
    @Autowired
    private BatteryDeliveryPlanMonthService batteryDeliveryPlanMonthService;

    @PostMapping("/page")
    @ApiOperation(value = "外购电池月到货计划分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "外购电池月到货计划详情")
    public ResponseEntity<Results<BatteryDeliveryPlanMonthDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增外购电池月到货计划")
    public ResponseEntity<Results<BatteryDeliveryPlanMonthDTO>> save(@RequestBody BatteryDeliveryPlanMonthDTO batteryDeliveryPlanMonthDTO) {
        validObject(batteryDeliveryPlanMonthDTO, ValidGroups.Insert.class, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.saveOrUpdate(batteryDeliveryPlanMonthDTO));
    }


    @PostMapping("/delete")
    @ApiOperation(value = "批量删除外购电池月到货计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody List<BatteryDeliveryPlanMonthDTO> deliveryPlanMonthDTOList) {
        batteryDeliveryPlanMonthService.deleteByGroupCondition(deliveryPlanMonthDTOList);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出外购电池月到货计划")
    @PostMapping("/export")
    public void export(@RequestBody BatteryDeliveryPlanMonthQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryDeliveryPlanMonthService.export(query, response);
    }

    @ApiOperation(value = "导入外购电池月到货计划")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = batteryDeliveryPlanMonthService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/publish")
    @ApiOperation(value = "发布外购电池月到货计划")
    public ResponseEntity<Results<Object>> publish() {
        batteryDeliveryPlanMonthService.publish();
        return Results.createSuccessRes();
    }

    @PostMapping("/cellTypes")
    @ApiOperation(value = "电池片类型下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> cellTypeList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.cellTypeList(query.getLatestFlag()));
    }

    @PostMapping("/primaryGateNumbers")
    @ApiOperation(value = "主栅数下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> primaryGateNumberList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.primaryGateNumberList(query.getLatestFlag()));
    }

    @PostMapping("/areas")
    @ApiOperation(value = "到货区域下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> areaList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.areaList(query.getLatestFlag()));
    }

    @PostMapping("/vendorBrands")
    @ApiOperation(value = "品牌下拉列表")
    public ResponseEntity<Results<List<String>>> vendorBrandList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.vendorBrandList(query.getLatestFlag()));
    }

    @PostMapping("/domesticOverseas")
    @ApiOperation(value = "排产区域下拉列表")
    public ResponseEntity<Results<List<LovLineDTO>>> domesticOverseaList(@RequestBody BatteryDeliveryPlanMonthQuery query) {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.domesticOverseaList(query.getLatestFlag()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "品牌下拉列表")
    public ResponseEntity<Results<List<String>>> versionList() {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.versionList());
    }


    @PostMapping("/queryAllBatteryDeliveryPlanMonth")
    @ApiOperation(value = "外购到货计划-按月")
    public ResponseEntity<Results<List<BatteryDeliveryPlanMonthDTO>>> queryAllBatteryDeliveryPlanMonth() {
        return Results.createSuccessRes(batteryDeliveryPlanMonthService.queryAllBatteryDeliveryPlanMonth());
    }

}