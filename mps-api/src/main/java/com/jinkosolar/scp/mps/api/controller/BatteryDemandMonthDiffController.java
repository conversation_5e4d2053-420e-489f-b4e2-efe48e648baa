package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.BatteryDemandMonthDTO;
import com.jinkosolar.scp.mps.domain.dto.BatteryDemandMonthDiffDTO;
import com.jinkosolar.scp.mps.domain.query.BatteryDemandMonthDiffQuery;
import com.jinkosolar.scp.mps.service.BatteryDemandMonthDiffService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * [说明]IE更新月度日计划表 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-12-11
 */
@RestController
@RequestMapping("/battery_demand_diff")
@RequiredArgsConstructor
@Api(value = "batteryDemandMonthDiff", tags = "IE经管数据差异查看报表")
public class BatteryDemandMonthDiffController {


    private final BatteryDemandMonthDiffService batteryDemandMonthDiffService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "IE经管数据差异列表", notes = "获得IE经管数据差异列表")
    public ResponseEntity<Results<List<BatteryDemandMonthDTO>>> queryList(@RequestBody BatteryDemandMonthDiffQuery query) {
        return Results.createSuccessRes(batteryDemandMonthDiffService.queryList(query));
    }



    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryDemandMonthDiffQuery query, HttpServletResponse response) {

        batteryDemandMonthDiffService.export(query, response);
    }

}
