package com.jinkosolar.scp.mps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ibm.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jinkosolar.scp.mps.domain.query.BatteryEfficiencyLevelQuery;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.jinkosolar.scp.mps.domain.save.BatteryEfficiencyLevelSaveDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryEfficiencyLevel;
import com.jinkosolar.scp.mps.domain.dto.BatteryEfficiencyLevelDTO;
import com.jinkosolar.scp.mps.service.BatteryEfficiencyLevelService;
import com.ibm.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 电池启用效率档位 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-02 10:22:51
 */
@RestController
@RequestMapping("/battery-efficiency-level")
@RequiredArgsConstructor
@Api(value = "battery-efficiency-level", tags = "电池启用效率档位操作")
public class BatteryEfficiencyLevelController {
    private final BatteryEfficiencyLevelService batteryEfficiencyLevelService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池启用效率档位分页列表", notes = "获得电池启用效率档位分页列表")
    public ResponseEntity<Results<Page<BatteryEfficiencyLevelDTO>>> queryByPage(@RequestBody BatteryEfficiencyLevelQuery query) {
        return Results.createSuccessRes(batteryEfficiencyLevelService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<BatteryEfficiencyLevelDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(batteryEfficiencyLevelService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<BatteryEfficiencyLevelDTO>> save(@Valid @RequestBody BatteryEfficiencyLevelSaveDTO saveDTO) {
        return Results.createSuccessRes(batteryEfficiencyLevelService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        batteryEfficiencyLevelService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody BatteryEfficiencyLevelQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            batteryEfficiencyLevelService.export(query, response);
    }
}
