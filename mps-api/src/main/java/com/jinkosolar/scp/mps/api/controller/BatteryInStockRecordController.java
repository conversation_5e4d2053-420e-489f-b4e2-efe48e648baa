package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mrp.InStockFactoryRecordRequest;
import com.jinkosolar.scp.jip.api.dto.mrp.InstockFactoryRecordResponse;
import com.jinkosolar.scp.jip.api.service.InStockFactoryRecordService;
import com.jinkosolar.scp.mps.domain.dto.BatteryInStockRecordDTO;
import com.jinkosolar.scp.mps.domain.query.BatteryInStockRecordQuery;
import com.jinkosolar.scp.mps.service.BatteryInStockRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ERP采购入库记录相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 10:12:57
 */
@RequestMapping(value = "/battery-in-stock-record")
@RestController
@Api(value = "batteryInStockRecord", tags = "ERP采购入库记录相关操作控制层")
public class BatteryInStockRecordController extends BaseController {
    @Autowired
    private BatteryInStockRecordService batteryInStockRecordService;

    @PostMapping("/page")
    @ApiOperation(value = "ERP采购入库记录分页查询")
    public ResponseEntity<Results<Page<BatteryInStockRecordDTO>>> page(@RequestBody BatteryInStockRecordQuery query) {
        return Results.createSuccessRes(batteryInStockRecordService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "ERP采购入库记录详情")
    public ResponseEntity<Results<BatteryInStockRecordDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryInStockRecordService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增ERP采购入库记录")
    public ResponseEntity<Results<BatteryInStockRecordDTO>> insert(@RequestBody BatteryInStockRecordDTO batteryInStockRecordDTO) {
        validObject(batteryInStockRecordDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(batteryInStockRecordService.saveOrUpdate(batteryInStockRecordDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ERP采购入库记录")
    public ResponseEntity<Results<BatteryInStockRecordDTO>> update(@RequestBody BatteryInStockRecordDTO batteryInStockRecordDTO) {
        validObject(batteryInStockRecordDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryInStockRecordService.saveOrUpdate(batteryInStockRecordDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除ERP采购入库记录")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        batteryInStockRecordService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出ERP采购入库记录")
    @PostMapping("/export")
    public void export(@RequestBody BatteryInStockRecordQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryInStockRecordService.export(query, response);
    }

    @ApiOperation(value = "导入ERP采购入库记录")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = batteryInStockRecordService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}