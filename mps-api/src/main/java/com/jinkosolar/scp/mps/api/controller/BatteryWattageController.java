package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryWattageDTO;
import com.jinkosolar.scp.mps.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.dto.SingleModuleWattageDTO;
import com.jinkosolar.scp.mps.domain.query.BatteryWattageQuery;
import com.jinkosolar.scp.mps.service.BatteryWattageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池单片瓦数表相关操作控制层
 * 
 * <AUTHOR> 2024-07-22 14:35:33
 */
@RequestMapping(value = "/battery-wattage")
@RestController
@Api(value = "batteryWattage", tags = "电池单片瓦数表相关操作控制层")
public class BatteryWattageController extends BaseController {    
    private final BatteryWattageService batteryWattageService;

    public BatteryWattageController(BatteryWattageService batteryWattageService) {
        this.batteryWattageService = batteryWattageService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "电池单片瓦数表分页查询")
    public ResponseEntity<Results<Page<BatteryWattageDTO>>> page(@RequestBody BatteryWattageQuery query) {
        return Results.createSuccessRes(batteryWattageService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池单片瓦数表详情")
    public ResponseEntity<Results<BatteryWattageDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(batteryWattageService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池单片瓦数表")
    public ResponseEntity<Results<BatteryWattageDTO>> insert(@RequestBody BatteryWattageDTO batteryWattageDTO) {
        validObject(batteryWattageDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(batteryWattageService.saveOrUpdate(batteryWattageDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池单片瓦数表")
    public ResponseEntity<Results<BatteryWattageDTO>> update(@RequestBody BatteryWattageDTO batteryWattageDTO) {
        validObject(batteryWattageDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryWattageService.saveOrUpdate(batteryWattageDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池单片瓦数表")
    public ResponseEntity<Results<Object>> delete(@RequestBody  List<BatteryWattageDTO> batteryWattageDTO) {
        validObject(batteryWattageDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(batteryWattageService.deleteByDto((List<BatteryWattageDTO>) batteryWattageDTO));
    }

    @ApiOperation(value = "导出电池单片瓦数表")
    @PostMapping("/export")
    public void export(@RequestBody BatteryWattageQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        batteryWattageService.export(query, response);
    }

    @ApiOperation(value = "导入电池单片瓦数表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = batteryWattageService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody BatteryWattageQuery query) {
        return Results.createSuccessRes(batteryWattageService.findVersion(query));
    }

    @PostMapping("/years")
    @ApiOperation(value = "查询年份")
    public ResponseEntity<Results<List<String>>> years() {
        return Results.createSuccessRes(batteryWattageService.findYear());
    }

    @PostMapping("/planProduct")
    @ApiOperation(value = "查询电池产品")
    public ResponseEntity<Results<List<CommonOption>>> Product() {
        return Results.createSuccessRes(batteryWattageService.findProduct());
    }

}