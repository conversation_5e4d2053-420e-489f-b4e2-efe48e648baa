package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CapacityPlanCompareDTO;
import com.jinkosolar.scp.mps.domain.query.CapacityPlanCompareQuery;
import com.jinkosolar.scp.mps.service.CapacityPlanCompareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件产能与规划对比汇总相关操作控制层
 *
 * <AUTHOR> 2024-04-19 10:39:46
 */
@RequestMapping(value = "/capacity-plan-compare")
@RestController
@Api(value = "capacityPlanCompare", tags = "组件产能与规划对比汇总相关操作控制层")
public class CapacityPlanCompareController extends BaseController {

    @Autowired
    private CapacityPlanCompareService capacityPlanCompareService;

    @PostMapping("/page")
    @ApiOperation(value = "组件产能与规划对比汇总分页查询")
    public ResponseEntity<Results<Page<CapacityPlanCompareDTO>>> page(@RequestBody CapacityPlanCompareQuery query) {
        return Results.createSuccessRes(capacityPlanCompareService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件产能与规划对比汇总详情")
    public ResponseEntity<Results<CapacityPlanCompareDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(capacityPlanCompareService.getById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件产能与规划对比汇总")
    public ResponseEntity<Results<CapacityPlanCompareDTO>> insert(@RequestBody CapacityPlanCompareDTO capacityPlanCompareDTO) {
        validObject(capacityPlanCompareDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(capacityPlanCompareService.saveOrUpdate(capacityPlanCompareDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件产能与规划对比汇总")
    public ResponseEntity<Results<CapacityPlanCompareDTO>> update(@RequestBody CapacityPlanCompareDTO capacityPlanCompareDTO) {
        validObject(capacityPlanCompareDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(capacityPlanCompareService.saveOrUpdate(capacityPlanCompareDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件产能与规划对比汇总")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        capacityPlanCompareService.deleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件产能与规划对比汇总")
    @PostMapping("/export")
    public void export(@RequestBody CapacityPlanCompareQuery query, HttpServletResponse response) {
        capacityPlanCompareService.export(query, response);
    }

    @ApiOperation(value = "计算")
    @PostMapping("/calculate")
    public ResponseEntity<Results<Object>> calculate(@RequestBody CapacityPlanCompareQuery query) {
        capacityPlanCompareService.calculate(query);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "查询报表")
    @PostMapping("/query")
    public ResponseEntity<Results<Object>> query(@RequestBody CapacityPlanCompareQuery query) {
        return Results.createSuccessRes(capacityPlanCompareService.query(query));
    }

}