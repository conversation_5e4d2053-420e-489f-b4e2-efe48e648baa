package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellFineDTO;
import com.jinkosolar.scp.mps.domain.entity.CellFine;
import com.jinkosolar.scp.mps.domain.query.CellFineQuery;
import com.jinkosolar.scp.mps.domain.save.CellFineSaveDTO;
import com.jinkosolar.scp.mps.service.CellFineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池良率表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@RestController
@RequestMapping("/cell-fine")
@RequiredArgsConstructor
@Api(value = "cell-fine", tags = "电池良率表操作")
public class CellFineController {
    private final CellFineService cellFineService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池良率表分页列表", notes = "获得电池良率表分页列表")
    public ResponseEntity<Results<Page<CellFineDTO>>> queryByPage(@RequestBody CellFineQuery query) {
        return Results.createSuccessRes(cellFineService.queryByPage(query));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryList")
    @ApiOperation(value = "电池良率表列表", notes = "获得电池良率表页列表")
    public ResponseEntity<Results<List<CellFine>>> queryList(@RequestBody CellFineQuery query) {
        return Results.createSuccessRes(cellFineService.queryList(query));
    }

    /**
     * 查询
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表", notes = "列表")
    public ResponseEntity<Results<List<CellFineDTO>>> list(@RequestBody CellFineQuery query) {
        return Results.createSuccessRes(cellFineService.list(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellFineDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellFineService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellFineDTO>> save(@Valid @RequestBody CellFineSaveDTO saveDTO) {
        return Results.createSuccessRes(cellFineService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellFineService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellFineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellFineService.export(query, response);
    }

    /**
     * 读取第第三方接口电池类型和电池良率的接口后进行数据整合
     *
     * @return
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<Object>> importData() {
        cellFineService.importData();
        return Results.createSuccessRes();
    }
}
