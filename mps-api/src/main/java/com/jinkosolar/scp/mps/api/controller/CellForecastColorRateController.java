package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellForecastColorRateDTO;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.query.CellForecastColorRateQuery;
import com.jinkosolar.scp.mps.service.CellForecastColorRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池产出预测颜色占比相关操作控制层
 * 
 * <AUTHOR> 2025-01-13 18:52:28
 */
@RequestMapping(value = "/cell-forecast-color-rate")
@RestController
@Api(value = "cellForecastColorRate", tags = "电池产出预测颜色占比相关操作控制层")
@RequiredArgsConstructor  
public class CellForecastColorRateController extends BaseController {    
    private final CellForecastColorRateService cellForecastColorRateService; 

    @PostMapping("/page")
    @ApiOperation(value = "电池产出预测颜色占比分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody CellForecastColorRateQuery query) {
        return Results.createSuccessRes(cellForecastColorRateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池产出预测颜色占比详情")
    public ResponseEntity<Results<CellForecastColorRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellForecastColorRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池产出预测颜色占比")
    public ResponseEntity<Results<Void>> insert(@RequestBody CellForecastColorRateDTO cellForecastColorRateDTO) {
        validObject(cellForecastColorRateDTO, ValidGroups.Insert.class);
        cellForecastColorRateService.saveOrUpdate(cellForecastColorRateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池产出预测颜色占比")
    public ResponseEntity<Results<Void>> update(@RequestBody CellForecastColorRateDTO cellForecastColorRateDTO) {
        validObject(cellForecastColorRateDTO, ValidGroups.Update.class);
        cellForecastColorRateService.saveOrUpdate(cellForecastColorRateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池产出预测颜色占比")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellForecastColorRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池产出预测颜色占比")
    @PostMapping("/export")
    public void export(@RequestBody CellForecastColorRateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellForecastColorRateService.export(query, response);
    }

    @ApiOperation(value = "导入电池产出预测颜色占比")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellForecastColorRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}