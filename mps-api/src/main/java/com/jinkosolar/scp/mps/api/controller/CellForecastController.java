package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellForecastCompareReportDTO;
import com.jinkosolar.scp.mps.domain.dto.CellForecastDTO;
import com.jinkosolar.scp.mps.domain.dto.CellForecastVersionCompareReportDTO;
import com.jinkosolar.scp.mps.domain.query.CellForecastQuery;
import com.jinkosolar.scp.mps.domain.query.CellForecastVersionCompareQuery;
import com.jinkosolar.scp.mps.service.CellForecastService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池产出预测相关操作控制层
 * 
 * <AUTHOR> 2024-07-03 17:10:11
 */
@RequestMapping(value = "/cell-forecast")
@RestController
@Api(value = "cellForecast", tags = "电池产出预测相关操作控制层")
public class CellForecastController extends BaseController {
    @Autowired
    private CellForecastService cellForecastService;

    @PostMapping("/page")
    @ApiOperation(value = "电池产出预测分页查询")
    public ResponseEntity<Results<Page<CellForecastDTO>>> page(@RequestBody CellForecastQuery query) {
        return Results.createSuccessRes(cellForecastService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池产出预测详情")
    public ResponseEntity<Results<CellForecastDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellForecastService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池产出预测")
    public ResponseEntity<Results<CellForecastDTO>> insert(@RequestBody CellForecastDTO cellForecastDTO) {
        validObject(cellForecastDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellForecastService.saveOrUpdate(cellForecastDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池产出预测")
    public ResponseEntity<Results<CellForecastDTO>> update(@RequestBody CellForecastDTO cellForecastDTO) {
        validObject(cellForecastDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellForecastService.saveOrUpdate(cellForecastDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池产出预测")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellForecastService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池产出预测")
    @PostMapping("/export")
    public void export(@RequestBody CellForecastQuery query, HttpServletResponse response) throws Exception {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellForecastService.exportReport(query, response);
    }

    @ApiOperation(value = "导入电池产出预测")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellForecastService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "电池产出预测计算")
    @PostMapping("/calculate")
    public ResponseEntity<Results<List<CellForecastDTO>>> calculate(@RequestBody CellForecastQuery query) {
        cellForecastService.cellForecast(query);
        return Results.createSuccessRes(Collections.emptyList());
    }

    @ApiOperation(value = "电池产出预测需发布的列表")
    @PostMapping("/getPublishCellForecastList")
    public ResponseEntity<Results<List<CellForecastDTO>>> getPublishCellForecastList(@RequestBody CellForecastQuery query) {
        return Results.createSuccessRes(cellForecastService.getPublishCellForecastList(query));
    }

    @ApiOperation(value = "电池产出预测发布-根据factoryIdList")
    @PostMapping("/publishCellForecastByFactoryIdList")
    public ResponseEntity<Results<Object>> publishCellForecastByFactoryIdList(@RequestBody CellForecastQuery query) {
        cellForecastService.publishCellForecastByFactoryIdList(query);
        return Results.createSuccessRes(Results.SUCCESS_CODE);
    }

    @ApiOperation(value = "电池产出预测保存")
    @PostMapping("/cell/forecast")
    public ResponseEntity<Results<List<CellForecastDTO>>> batteryForecast(@RequestBody CellForecastQuery query) {
        return Results.createSuccessRes(cellForecastService.cellForecast(query));
    }

    @ApiOperation(value = "电池产出预测报表查询")
    @PostMapping("/list")
    public ResponseEntity<Results<List<CellForecastDTO>>> list(@RequestBody CellForecastQuery query) {
        return Results.createSuccessRes(cellForecastService.list(query));
    }

    @ApiOperation(value = "电池产出预测报表查询")
    @PostMapping("/queryAllSupplierList")
    public ResponseEntity<Results<List<String>>> queryAllSupplierList() {
        return Results.createSuccessRes(cellForecastService.queryAllSupplierList());
    }

    @ApiOperation(value = "电池产出预测报表查询")
    @PostMapping("/queryAllCellForecastList")
    public ResponseEntity<Results<List<CellForecastDTO>>> queryAllCellForecastList() {
        return Results.createSuccessRes(cellForecastService.queryAllCellForecastList());
    }

    @ApiOperation(value = "PN型电池产出对比")
    @PostMapping("/pn/compare")
    public ResponseEntity<Results<List<CellForecastCompareReportDTO>>> comparePNReport(@RequestBody CellForecastQuery query) {
        return Results.createSuccessRes(cellForecastService.comparePNReport(query));
    }

    @ApiOperation(value = "PN型电池产出对比导出")
    @PostMapping("/pn/compare/export")
    public void exportComparePNReport(@RequestBody CellForecastQuery query,HttpServletResponse response) {
         cellForecastService.exportComparePNReport(query,response);
    }

    @ApiOperation(value = "电池产出预测版本列表")
    @PostMapping("/versions")
    public ResponseEntity<Results<List<String>>> queryVersionList() {
        return Results.createSuccessRes(cellForecastService.queryVersionList());
    }

    @ApiOperation(value = "电池产出预测版本对比报表")
    @PostMapping("/version/compare")
    public ResponseEntity<Results<List<CellForecastVersionCompareReportDTO>>> versionCompareReport(@RequestBody CellForecastVersionCompareQuery query) {
        return Results.createSuccessRes(cellForecastService.versionCompareReport(query));
    }

    @ApiOperation(value = "电池产出预测版本对比报表导出")
    @PostMapping("/version/compare/export")
    public void exportVersionCompareReport(@RequestBody CellForecastVersionCompareQuery query,HttpServletResponse response) {
        cellForecastService.exportVersionCompareReport(query,response);
    }
}