package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanALowDTO;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanDTO;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanQuery;
import com.jinkosolar.scp.mps.domain.save.CellInstockPlanSaveDTO;
import com.jinkosolar.scp.mps.service.CellInstockPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 入库计划表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
@RestController
@RequestMapping("/cell-instock-plan")
@RequiredArgsConstructor
@Api(value = "cell-instock-plan", tags = "入库计划表操作")
public class CellInstockPlanController {
    private final CellInstockPlanService cellInstockPlanService;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "入库计划表分页列表", notes = "获得入库计划表分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanDTO>>> queryByPage(@RequestBody CellInstockPlanQuery query) {
        return Results.createSuccessRes(cellInstockPlanService.queryByPage(query));
    }


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page/a")
    @ApiOperation(value = "入库计划表a-分页列表", notes = "获得入库计划表a-分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanALowDTO>>> queryAByPage(@RequestBody CellInstockPlanQuery query) {
        return Results.createSuccessRes(cellInstockPlanService.queryAByPage(query));
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellInstockPlanDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellInstockPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellInstockPlanDTO>> save(@Valid @RequestBody CellInstockPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(cellInstockPlanService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellInstockPlanService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellInstockPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellInstockPlanService.export(query, response);
    }
}
