package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanTotalDTO;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanTotalQuery;
import com.jinkosolar.scp.mps.domain.save.CellInstockPlanTotalSaveDTO;
import com.jinkosolar.scp.mps.service.CellInstockPlanTotalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 入库计划汇总表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@RestController
@RequestMapping("/cell-instock-plan-total")
@RequiredArgsConstructor
@Api(value = "入库计划汇总表操作", tags = "入库计划汇总表操作")
public class CellInstockPlanTotalController {
    private final CellInstockPlanTotalService cellInstockPlanTotalService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "入库计划汇总表分页列表", notes = "获得入库计划汇总表分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanTotalDTO>>> queryByPage(@RequestBody CellInstockPlanTotalQuery query) {
        return Results.createSuccessRes(cellInstockPlanTotalService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellInstockPlanTotalDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellInstockPlanTotalService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellInstockPlanTotalDTO>> save(@Valid @RequestBody CellInstockPlanTotalSaveDTO saveDTO) {
        return Results.createSuccessRes(cellInstockPlanTotalService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellInstockPlanTotalService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellInstockPlanTotalQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanTotalService.export(query, response);
    }

    /**
     * 入库确认
     *
     * @param query
     * @return
     */
    @PostMapping("/confirm")
    @ApiOperation(value = "入库确认", notes = "入库确认")
    public ResponseEntity<Results<Object>> confirm(@RequestBody CellInstockPlanTotalQuery query) {
        setCellInstockPlanTotalQueryNull(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanTotalService.confirm(query);
        return Results.createSuccessRes();
    }

    /**
     * 邮件发送
     *
     * @param query
     * @return
     */
    @PostMapping("/email")
    @ApiOperation(value = "邮件发送", notes = "发送邮件")
    public ResponseEntity<Results<Object>> email(@RequestBody CellInstockPlanTotalQuery query) {
        setCellInstockPlanTotalQueryNull(query);
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanTotalService.email(query);
        return Results.createSuccessRes();
    }

    private void setCellInstockPlanTotalQueryNull(CellInstockPlanTotalQuery query) {
        if (query != null) {
            query.setCellsType(null);
            query.setBasePlace(null);
            query.setWorkshop(null);
            query.setCellsTypeId(null);
            query.setBasePlaceId(null);
            query.setWorkshopId(null);
        }

    }
}
