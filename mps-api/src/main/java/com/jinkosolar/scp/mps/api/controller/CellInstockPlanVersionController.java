package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanVersionQueryDto;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanVersionQuery;
import com.jinkosolar.scp.mps.domain.save.CellInstockPlanVersionSaveDTO;
import com.jinkosolar.scp.mps.service.CellInstockPlanVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 入库计划版本管理表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@RestController
@RequestMapping("/cell-instock-plan-version")
@RequiredArgsConstructor
@Api(value = "cell-instock-plan-version", tags = "入库计划版本管理表操作")
public class CellInstockPlanVersionController {
    private final CellInstockPlanVersionService cellInstockPlanVersionService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    // @PostMapping("/page")
    @ApiOperation(value = "入库计划版本管理表分页列表", notes = "获得入库计划版本管理表分页列表")
    public ResponseEntity<Results<Page<CellInstockPlanVersionDTO>>> queryByPage(@RequestBody CellInstockPlanVersionQuery query) {
        return Results.createSuccessRes(cellInstockPlanVersionService.queryByPage(query));
    }

    /**
     * 查看入库执行状态
     *
     * @param query 筛选条件
     * @return 查看投产执行状态
     */
    @PostMapping("/query")
    @ApiOperation(value = "查看入库执行状态", notes = "查看入库执行状态")
    public ResponseEntity<Results<CellInstockPlanVersionDTO>> query(@RequestBody CellInstockPlanVersionQueryDto query) {
        CellInstockPlanVersionDTO dto = cellInstockPlanVersionService.query(query);
        return Results.createSuccessRes(dto);

    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    //  @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellInstockPlanVersionDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellInstockPlanVersionService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    //  @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellInstockPlanVersionDTO>> save(@Valid @RequestBody CellInstockPlanVersionSaveDTO saveDTO) {
        return Results.createSuccessRes(cellInstockPlanVersionService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    //   @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellInstockPlanVersionService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    // @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellInstockPlanVersionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellInstockPlanVersionService.export(query, response);
    }
}
