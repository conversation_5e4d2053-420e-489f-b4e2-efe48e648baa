package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellMaterialProductAttributeDTO;
import com.jinkosolar.scp.mps.domain.query.CellMaterialProductAttributeQuery;
import com.jinkosolar.scp.mps.service.CellMaterialProductAttributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池产品主推物料属性值相关操作控制层
 * 
 * <AUTHOR> 2024-05-29 10:35:32
 */
@RequestMapping(value = "/cell-material-product-attribute")
@RestController
@Api(value = "cellMaterialProductAttribute", tags = "电池产品主推物料属性值相关操作控制层")
public class CellMaterialProductAttributeController extends BaseController {
    @Autowired
    private CellMaterialProductAttributeService cellMaterialProductAttributeService;

    @PostMapping("/page")
    @ApiOperation(value = "电池产品主推物料属性值分页查询")
    public ResponseEntity<Results<Page<CellMaterialProductAttributeDTO>>> page(@RequestBody CellMaterialProductAttributeQuery query) {
        return Results.createSuccessRes(cellMaterialProductAttributeService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池产品主推物料属性值详情")
    public ResponseEntity<Results<CellMaterialProductAttributeDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellMaterialProductAttributeService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池产品主推物料属性值")
    public ResponseEntity<Results<CellMaterialProductAttributeDTO>> insert(@RequestBody CellMaterialProductAttributeDTO cellMaterialProductAttributeDTO) {
        validObject(cellMaterialProductAttributeDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellMaterialProductAttributeService.saveOrUpdate(cellMaterialProductAttributeDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池产品主推物料属性值")
    public ResponseEntity<Results<CellMaterialProductAttributeDTO>> update(@RequestBody CellMaterialProductAttributeDTO cellMaterialProductAttributeDTO) {
        validObject(cellMaterialProductAttributeDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellMaterialProductAttributeService.saveOrUpdate(cellMaterialProductAttributeDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池产品主推物料属性值")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellMaterialProductAttributeService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池产品主推物料属性值")
    @PostMapping("/export")
    public void export(@RequestBody CellMaterialProductAttributeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellMaterialProductAttributeService.export(query, response);
    }

    @ApiOperation(value = "导入电池产品主推物料属性值")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellMaterialProductAttributeService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}