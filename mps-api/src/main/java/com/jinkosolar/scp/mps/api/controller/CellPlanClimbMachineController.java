package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbMachineDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanClimbMachineQuery;
import com.jinkosolar.scp.mps.service.CellPlanClimbMachineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新基地机台切换爬坡表相关操作控制层
 * 
 * <AUTHOR> 2024-07-23 15:02:25
 */
@RequestMapping(value = "/cell-plan-climb-machine")
@RestController
@Api(value = "cellPlanClimbMachine", tags = "新基地机台切换爬坡表相关操作控制层")
public class CellPlanClimbMachineController extends BaseController {    
    private final CellPlanClimbMachineService cellPlanClimbMachineService;

    public CellPlanClimbMachineController(CellPlanClimbMachineService cellPlanClimbMachineService) {
        this.cellPlanClimbMachineService = cellPlanClimbMachineService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "新基地机台切换爬坡表分页查询")
    public ResponseEntity<Results<Page<CellPlanClimbMachineDTO>>> page(@RequestBody CellPlanClimbMachineQuery query) {
        return Results.createSuccessRes(cellPlanClimbMachineService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "新基地机台切换爬坡表详情")
    public ResponseEntity<Results<CellPlanClimbMachineDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellPlanClimbMachineService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增新基地机台切换爬坡表")
    public ResponseEntity<Results<CellPlanClimbMachineDTO>> insert(@RequestBody CellPlanClimbMachineDTO cellPlanClimbMachineDTO) {
        validObject(cellPlanClimbMachineDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellPlanClimbMachineService.saveOrUpdate(cellPlanClimbMachineDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新新基地机台切换爬坡表")
    public ResponseEntity<Results<CellPlanClimbMachineDTO>> update(@RequestBody CellPlanClimbMachineDTO cellPlanClimbMachineDTO) {
        validObject(cellPlanClimbMachineDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellPlanClimbMachineService.saveOrUpdate(cellPlanClimbMachineDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除新基地机台切换爬坡表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellPlanClimbMachineService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出新基地机台切换爬坡表")
    @PostMapping("/export")
    public void export(@RequestBody CellPlanClimbMachineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellPlanClimbMachineService.export(query, response);
    }

    @ApiOperation(value = "导入新基地机台切换爬坡表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellPlanClimbMachineService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}