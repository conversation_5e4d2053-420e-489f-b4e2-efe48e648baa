package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbResultDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanClimbResultQuery;
import com.jinkosolar.scp.mps.domain.query.CellPlanClimbSectionResultQuery;
import com.jinkosolar.scp.mps.service.CellPlanClimbResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 爬坡产能需求结果表相关操作控制层
 * 
 * <AUTHOR> 2024-08-01 14:59:12
 */
@RequestMapping(value = "/cell-plan-climb-result")
@RestController
@Api(value = "cellPlanClimbResult", tags = "爬坡产能需求结果表相关操作控制层")
public class CellPlanClimbResultController extends BaseController {    
    private final CellPlanClimbResultService cellPlanClimbResultService;

    public CellPlanClimbResultController(CellPlanClimbResultService cellPlanClimbResultService) {
        this.cellPlanClimbResultService = cellPlanClimbResultService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "爬坡产能需求结果表分页查询")
    public ResponseEntity<Results<Page<CellPlanClimbResultDTO>>> page(@RequestBody CellPlanClimbResultQuery query) {
        return Results.createSuccessRes(cellPlanClimbResultService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "爬坡产能需求结果表详情")
    public ResponseEntity<Results<CellPlanClimbResultDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellPlanClimbResultService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增爬坡产能需求结果表")
    public ResponseEntity<Results<CellPlanClimbResultDTO>> insert(@RequestBody CellPlanClimbResultDTO cellPlanClimbResultDTO) {
        validObject(cellPlanClimbResultDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellPlanClimbResultService.saveOrUpdate(cellPlanClimbResultDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新爬坡产能需求结果表")
    public ResponseEntity<Results<CellPlanClimbResultDTO>> update(@RequestBody CellPlanClimbResultDTO cellPlanClimbResultDTO) {
        validObject(cellPlanClimbResultDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellPlanClimbResultService.saveOrUpdate(cellPlanClimbResultDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除爬坡产能需求结果表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellPlanClimbResultService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出爬坡产能需求结果表")
    @PostMapping("/export")
    public void export(@RequestBody CellPlanClimbResultQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellPlanClimbResultService.export(query, response);
    }

    @ApiOperation(value = "导入爬坡产能需求结果表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellPlanClimbResultService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/caculateResult")
    @ApiOperation(value = "生成爬坡产能需求结果表")
    public ResponseEntity<Results<Object>> caculateResult(@RequestBody CellPlanClimbSectionResultQuery query) {
        try{
            cellPlanClimbResultService.caculateResult(query.getModelType());
        }catch (Exception e){
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
        return Results.createAsprovaSuccessRes(true);
    }
}