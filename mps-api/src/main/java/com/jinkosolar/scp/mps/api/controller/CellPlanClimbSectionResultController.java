package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSectionResultDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanClimbSectionResultQuery;
import com.jinkosolar.scp.mps.service.CellPlanClimbResultService;
import com.jinkosolar.scp.mps.service.CellPlanClimbSectionResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 爬坡产能需求结果表相关操作控制层
 * 
 * <AUTHOR> 2024-08-20 19:37:41
 */
@RequestMapping(value = "/cell-plan-climb-section-result")
@RestController
@Api(value = "cellPlanClimbSectionResult", tags = "爬坡产能需求结果表相关操作控制层")
@RequiredArgsConstructor  
public class CellPlanClimbSectionResultController extends BaseController {    
    private final CellPlanClimbSectionResultService cellPlanClimbSectionResultService;
    @Autowired
    private CellPlanClimbResultService cellPlanClimbResultService;

    @PostMapping("/page")
    @ApiOperation(value = "爬坡产能需求结果表分页查询")
    public ResponseEntity<Results<Page<CellPlanClimbSectionResultDTO>>> page(@RequestBody CellPlanClimbSectionResultQuery query) {
        return Results.createSuccessRes(cellPlanClimbSectionResultService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "爬坡产能需求结果表详情")
    public ResponseEntity<Results<CellPlanClimbSectionResultDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellPlanClimbSectionResultService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增爬坡产能需求结果表")
    public ResponseEntity<Results<Void>> insert(@RequestBody CellPlanClimbSectionResultDTO cellPlanClimbSectionResultDTO) {
        validObject(cellPlanClimbSectionResultDTO, ValidGroups.Insert.class);
        cellPlanClimbSectionResultService.saveOrUpdate(cellPlanClimbSectionResultDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新爬坡产能需求结果表")
    public ResponseEntity<Results<Void>> update(@RequestBody CellPlanClimbSectionResultDTO cellPlanClimbSectionResultDTO) {
        validObject(cellPlanClimbSectionResultDTO, ValidGroups.Update.class);
        cellPlanClimbSectionResultService.saveOrUpdate(cellPlanClimbSectionResultDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除爬坡产能需求结果表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellPlanClimbSectionResultService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出爬坡产能需求结果表")
    @PostMapping("/export")
    public void export(@RequestBody CellPlanClimbSectionResultQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellPlanClimbSectionResultService.export(query, response);
    }

    @ApiOperation(value = "导入爬坡产能需求结果表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellPlanClimbSectionResultService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/result")
    @ApiOperation(value = "生成爬坡产能需求结果表")
    public ResponseEntity<Results<Object>> result(@RequestBody CellPlanClimbSectionResultQuery query) {
        try{
            cellPlanClimbSectionResultService.result(query.getModelType());
        }catch (Exception e){
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
        return Results.createAsprovaSuccessRes(true);
    }

    @PostMapping("/countResult")
    @ApiOperation(value = "生成爬坡产能需求结果表")
    public ResponseEntity<Results<Object>> countResult(@RequestBody CellPlanClimbSectionResultQuery query) {
        try{
            cellPlanClimbResultService.caculateResult(query.getModelType());
            cellPlanClimbSectionResultService.result(query.getModelType());
        }catch (Exception e){
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
        return Results.createAsprovaSuccessRes(true);
    }
}