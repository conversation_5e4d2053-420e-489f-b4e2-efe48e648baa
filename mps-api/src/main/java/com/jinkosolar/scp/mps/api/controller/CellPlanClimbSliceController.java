package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSliceDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanClimbSliceQuery;
import com.jinkosolar.scp.mps.service.CellPlanClimbSliceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品切片爬坡表相关操作控制层
 * 
 * <AUTHOR> 2024-07-23 15:02:25
 */
@RequestMapping(value = "/cell-plan-climb-slice")
@RestController
@Api(value = "cellPlanClimbSlice", tags = "产品切片爬坡表相关操作控制层")
public class CellPlanClimbSliceController extends BaseController {    
    private final CellPlanClimbSliceService cellPlanClimbSliceService;

    public CellPlanClimbSliceController(CellPlanClimbSliceService cellPlanClimbSliceService) {
        this.cellPlanClimbSliceService = cellPlanClimbSliceService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "产品切片爬坡表分页查询")
    public ResponseEntity<Results<Page<CellPlanClimbSliceDTO>>> page(@RequestBody CellPlanClimbSliceQuery query) {
        return Results.createSuccessRes(cellPlanClimbSliceService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品切片爬坡表详情")
    public ResponseEntity<Results<CellPlanClimbSliceDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellPlanClimbSliceService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品切片爬坡表")
    public ResponseEntity<Results<CellPlanClimbSliceDTO>> insert(@RequestBody CellPlanClimbSliceDTO cellPlanClimbSliceDTO) {
        validObject(cellPlanClimbSliceDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellPlanClimbSliceService.saveOrUpdate(cellPlanClimbSliceDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品切片爬坡表")
    public ResponseEntity<Results<CellPlanClimbSliceDTO>> update(@RequestBody CellPlanClimbSliceDTO cellPlanClimbSliceDTO) {
        validObject(cellPlanClimbSliceDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellPlanClimbSliceService.saveOrUpdate(cellPlanClimbSliceDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品切片爬坡表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellPlanClimbSliceService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品切片爬坡表")
    @PostMapping("/export")
    public void export(@RequestBody CellPlanClimbSliceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellPlanClimbSliceService.export(query, response);
    }

    @ApiOperation(value = "导入产品切片爬坡表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellPlanClimbSliceService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}