package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSwitchDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanClimbSwitchQuery;
import com.jinkosolar.scp.mps.service.CellPlanClimbSwitchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 新基地产品切换爬坡表相关操作控制层
 * 
 * <AUTHOR> 2024-07-23 15:02:25
 */
@RequestMapping(value = "/cell-plan-climb-switch")
@RestController
@Api(value = "cellPlanClimbSwitch", tags = "新基地产品切换爬坡表相关操作控制层")
public class CellPlanClimbSwitchController extends BaseController {    
    private final CellPlanClimbSwitchService cellPlanClimbSwitchService;

    public CellPlanClimbSwitchController(CellPlanClimbSwitchService cellPlanClimbSwitchService) {
        this.cellPlanClimbSwitchService = cellPlanClimbSwitchService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "新基地产品切换爬坡表分页查询")
    public ResponseEntity<Results<Page<CellPlanClimbSwitchDTO>>> page(@RequestBody CellPlanClimbSwitchQuery query) {
        return Results.createSuccessRes(cellPlanClimbSwitchService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "新基地产品切换爬坡表详情")
    public ResponseEntity<Results<CellPlanClimbSwitchDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellPlanClimbSwitchService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增新基地产品切换爬坡表")
    public ResponseEntity<Results<CellPlanClimbSwitchDTO>> insert(@RequestBody CellPlanClimbSwitchDTO cellPlanClimbSwitchDTO) {
        validObject(cellPlanClimbSwitchDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellPlanClimbSwitchService.saveOrUpdate(cellPlanClimbSwitchDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新新基地产品切换爬坡表")
    public ResponseEntity<Results<CellPlanClimbSwitchDTO>> update(@RequestBody CellPlanClimbSwitchDTO cellPlanClimbSwitchDTO) {
        validObject(cellPlanClimbSwitchDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellPlanClimbSwitchService.saveOrUpdate(cellPlanClimbSwitchDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除新基地产品切换爬坡表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellPlanClimbSwitchService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出新基地产品切换爬坡表")
    @PostMapping("/export")
    public void export(@RequestBody CellPlanClimbSwitchQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellPlanClimbSwitchService.export(query, response);
    }

    @ApiOperation(value = "导入新基地产品切换爬坡表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellPlanClimbSwitchService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}