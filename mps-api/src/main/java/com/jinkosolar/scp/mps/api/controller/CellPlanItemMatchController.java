package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.query.CellProductionPlanQuery;
import com.jinkosolar.scp.mps.domain.save.CellProductionPlanSaveDTO;
import com.jinkosolar.scp.mps.service.CellProductionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池片料号匹配 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-plan-item-match")
@RequiredArgsConstructor
@Api(value = "cell-plan-item-match", tags = "电池料号匹配")
public class CellPlanItemMatchController {
    private final CellProductionPlanService cellProductionPlanService;

    /**
     * 电池片料号匹配分页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池片料号匹配分页列表", notes = "电池片料号匹配分页列表")
    public ResponseEntity<Results<Page<CellProductionPlanDTO>>> queryItemMatchByPage(@RequestBody CellProductionPlanQuery query) {
        return Results.createSuccessRes(cellProductionPlanService.queryItemMatchByPage(query));
    }

    /**
     * 电池片料号匹配列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "电池片料号匹配列表", notes = "电池片料号匹配列表")
    public ResponseEntity<Results<List<CellProductionPlanDTO>>> queryItemMatchByList(@RequestBody CellProductionPlanQuery query) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        Page<CellProductionPlanDTO> page = cellProductionPlanService.queryItemMatchByPage(query);
        return Results.createSuccessRes(page.getContent());
    }

    /**
     * 电池片料号匹配后同步结果数据
     */
    @PostMapping("/changeDataByItemMatch")
    @ApiOperation(value = "电池片料号匹配后同步结果数据")
    public ResponseEntity<Results<Object>> changeDataByItemMatch(@RequestBody List<CellProductionPlanDTO> dtoList) {
        cellProductionPlanService.changeDataByItemMatch(dtoList);
        return Results.createSuccessRes();
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/matchItemSave")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellProductionPlanDTO>> matchItemSave(@Valid @RequestBody CellProductionPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(cellProductionPlanService.matchItemSave(saveDTO));
    }

    /**
     * 电池片料号匹配批量发布
     */
    @PostMapping("/barchMatchItemConfirm")
    @ApiOperation(value = "电池片料号匹配批量发布")
    public ResponseEntity<Results<Object>> barchMatchItemConfirm(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellProductionPlanService.barchMatchItemConfirm(ids);
        return Results.createSuccessRes();
    }

}
