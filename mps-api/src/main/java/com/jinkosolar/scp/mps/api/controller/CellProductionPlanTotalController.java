package com.jinkosolar.scp.mps.api.controller;

import com.jinkosolar.scp.mps.domain.convert.CellProductionPlanTotalDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanTotalDTO;
import com.jinkosolar.scp.mps.domain.query.CellProductionPlanTotalQuery;
import com.jinkosolar.scp.mps.domain.save.CellProductionPlanTotalSaveDTO;
import com.jinkosolar.scp.mps.service.CellProductionPlanTotalService;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 投产计划汇总表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/cell-production-plan-total")
@RequiredArgsConstructor
@Api(value = "cell-production-plan-total", tags = "投产计划汇总表操作")
public class CellProductionPlanTotalController {
    private final CellProductionPlanTotalService cellProductionPlanTotalService;
    private  final CellProductionPlanTotalDEConvert cellProductionPlanTotalDEConvert;

    /**
     * 查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "查询", notes = "查询")
    public ResponseEntity<Results<Page<CellProductionPlanTotalDTO>>> queryByPage(@RequestBody CellProductionPlanTotalQuery query) {
       query= cellProductionPlanTotalDEConvert.toCellProductionPlanTotalQueryByName(query);
        return Results.createSuccessRes(cellProductionPlanTotalService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
  //  @PostMapping("/detail")
  //  @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellProductionPlanTotalDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellProductionPlanTotalService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
   // @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellProductionPlanTotalDTO>> save(@Valid @RequestBody CellProductionPlanTotalSaveDTO saveDTO) {
        return Results.createSuccessRes(cellProductionPlanTotalService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
  //  @PostMapping("/delete")
  //  @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellProductionPlanTotalService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出",notes = "导出")
    public void export(@RequestBody CellProductionPlanTotalQuery query, HttpServletResponse response) {
        query= cellProductionPlanTotalDEConvert.toCellProductionPlanTotalQueryByName(query);
        query.setPageNumber(1);

        query.setPageSize(GlobalConstant.max_page_size);
            cellProductionPlanTotalService.export(query, response);
    }
    /**
     * 邮件发送
     * @param query
     * @return
     */
    @PostMapping("/email")
    @ApiOperation(value = "邮件发送",notes = "发送邮件")
    public  ResponseEntity<Results<Object>>  email(@RequestBody CellProductionPlanTotalQuery query) {
        query= cellProductionPlanTotalDEConvert.toCellProductionPlanTotalQueryByName(query);
        cellProductionPlanTotalService.email(query);
        return Results.createSuccessRes();
    }
}
