package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellTransferPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.CellTransferPlanDynamicDTO;
import com.jinkosolar.scp.mps.domain.query.CellTransferPlanQuery;
import com.jinkosolar.scp.mps.domain.save.CellTransferPlanSaveDTO;
import com.jinkosolar.scp.mps.service.CellTransferPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池转运计划 Controller
 *
 * @author: gencode 2024-12-17 10:00:00
 */
@Api(tags = "电池转运计划")
@RestController
@RequestMapping("/cellTransferPlan")
@RequiredArgsConstructor
public class CellTransferPlanController {

    private final CellTransferPlanService cellTransferPlanService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询电池转运计划")
    public ResponseEntity<Results<Page<CellTransferPlanDTO>>> page(@RequestBody CellTransferPlanQuery query) {
        return Results.createSuccessRes(cellTransferPlanService.page(query));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增电池转运计划")
    public ResponseEntity<Results<CellTransferPlanDTO>> save(@RequestBody CellTransferPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(cellTransferPlanService.save(saveDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池转运计划")
    public ResponseEntity<Results<CellTransferPlanDTO>> update(@RequestBody CellTransferPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(cellTransferPlanService.update(saveDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池转运计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellTransferPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @PostMapping("/detail")
    @ApiOperation(value = "获取电池转运计划详情")
    public ResponseEntity<Results<CellTransferPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellTransferPlanService.detail(Long.parseLong(idDTO.getId())));
    }

    @ApiOperation(value = "导入电池转运计划")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellTransferPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "动态列查询电池转运计划")
    @PostMapping("/dynamicQuery")
    public ResponseEntity<Results<List<CellTransferPlanDynamicDTO>>> dynamicQuery(@RequestBody CellTransferPlanQuery query) {
        List<CellTransferPlanDynamicDTO> result = cellTransferPlanService.dynamicQuery(query);
        return Results.createSuccessRes(result);
    }

    @ApiOperation(value = "导出电池转运计划")
    @PostMapping("/export")
    public void export(@RequestBody CellTransferPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellTransferPlanService.export(query, response);
    }
} 