package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CellTransformPlanDTO;
import com.jinkosolar.scp.mps.domain.query.CellTransformPlanQuery;
import com.jinkosolar.scp.mps.service.CellTransformPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网版切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-05-24 13:24:00
 */
@RequestMapping(value = "/cell-transform-plan")
@RestController
@Api(value = "cellTransformPlan", tags = "网版切换计划相关操作控制层")
public class CellTransformPlanController extends BaseController {
    @Autowired
    private CellTransformPlanService cellTransformPlanService;

    @PostMapping("/page")
    @ApiOperation(value = "网版切换计划分页查询")
    public ResponseEntity<Results<Page<CellTransformPlanDTO>>> page(@RequestBody CellTransformPlanQuery query) {
        return Results.createSuccessRes(cellTransformPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "网版切换计划详情")
    public ResponseEntity<Results<CellTransformPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(cellTransformPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增网版切换计划")
    public ResponseEntity<Results<CellTransformPlanDTO>> insert(@RequestBody CellTransformPlanDTO cellTransformPlanDTO) {
        validObject(cellTransformPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(cellTransformPlanService.saveOrUpdate(cellTransformPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新网版切换计划")
    public ResponseEntity<Results<CellTransformPlanDTO>> update(@RequestBody CellTransformPlanDTO cellTransformPlanDTO) {
        validObject(cellTransformPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(cellTransformPlanService.saveOrUpdate(cellTransformPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除网版切换计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        cellTransformPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出网版切换计划")
    @PostMapping("/export")
    public void export(@RequestBody CellTransformPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        cellTransformPlanService.export(query, response);
    }

    @ApiOperation(value = "导入网版切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = cellTransformPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}