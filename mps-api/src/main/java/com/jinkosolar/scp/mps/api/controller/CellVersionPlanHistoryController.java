package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellVersionPlanHistoryDTO;
import com.jinkosolar.scp.mps.domain.query.CellVersionPlanHistoryQuery;
import com.jinkosolar.scp.mps.domain.save.CellVersionPlanHistorySaveDTO;
import com.jinkosolar.scp.mps.service.CellVersionPlanHistoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 计划与上一版本计划对比 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@RestController
@RequestMapping("/cell-version-plan-history")
@RequiredArgsConstructor
@Api(value = "cell-version-plan-history", tags = "计划与上一版本计划对比操作")
public class CellVersionPlanHistoryController {
    private final CellVersionPlanHistoryService cellVersionPlanHistoryService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/query")
    @ApiOperation(value = "计划与上一版本计划对比分页列表", notes = "获得计划与上一版本计划对比分页列表")
    public ResponseEntity<Results<Map<String,Object>>> queryByPage(@RequestBody CellVersionPlanHistoryQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(cellVersionPlanHistoryService.makeReport(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
  //  @PostMapping("/detail")
   // @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellVersionPlanHistoryDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellVersionPlanHistoryService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
  //  @PostMapping("/save")
  //  @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellVersionPlanHistoryDTO>> save(@Valid @RequestBody CellVersionPlanHistorySaveDTO saveDTO) {
        return Results.createSuccessRes(cellVersionPlanHistoryService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
   // @PostMapping("/delete")
   // @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellVersionPlanHistoryService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellVersionPlanHistoryQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellVersionPlanHistoryService.export(query, response);
    }
}
