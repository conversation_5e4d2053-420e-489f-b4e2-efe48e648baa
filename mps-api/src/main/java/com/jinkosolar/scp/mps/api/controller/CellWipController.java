package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellWipDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanLineQuery;
import com.jinkosolar.scp.mps.domain.query.CellWipQuery;
import com.jinkosolar.scp.mps.domain.save.CellWipSaveDTO;
import com.jinkosolar.scp.mps.service.CellWipService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工单生成预览 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@RestController
@RequestMapping("/cell-wip")
@RequiredArgsConstructor
@Api(value = "cell-wip", tags = "工单生成预览操作")
public class CellWipController {
    private final CellWipService cellWipService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "工单生成预览分页列表", notes = "获得工单生成预览分页列表")
    public ResponseEntity<Results<Page<CellWipDTO>>> queryByPage(@RequestBody CellWipQuery query) {
        return Results.createSuccessRes(cellWipService.queryByPage(query));
    }
    /**
     * 工单预览投产明细
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/TicketPreviewDetail")
    @ApiOperation(value = "工单预览投产明细", notes = "工单预览投产明细")
    public ResponseEntity<Results<List<CellWipDTO>>> TicketPreview(@RequestBody CellPlanLineQuery query) {
        return Results.createSuccessRes(cellWipService.TicketPreview(query));
    }
    /**
     * 存在返工的料号信息
     * 工单下发erp生成工单记录 待确认
     */
    @PostMapping("/erpNo/save")
    @ApiOperation(value = "工单下发待确认", notes = "工单下发待确认")
    public ResponseEntity<Results<List<CellWipDTO>>> wipCreate(@RequestBody List<CellWipDTO> cellWipDTOs) {
        return Results.createSuccessRes(cellWipService.cellWipCreate(cellWipDTOs));
    }
    /**
     * 存在返工的料号信息
     * 工单下发erp生成工单记录 已确认
     * 继续下一步
     */
    @PostMapping("/erpNo/saveContinue")
    @ApiOperation(value = "工单下发已确认", notes = "工单下发已确认")
    public ResponseEntity<Results<List<CellWipDTO>>> saveContinueCreateWip(@RequestParam String userId, @RequestBody List<CellWipDTO> cellWipDTO) {
        cellWipDTO.stream().forEach(p->p.setUserId(userId));
        return Results.createSuccessRes(cellWipService.saveContinueCreateWip(cellWipDTO));
    }
    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellWipDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellWipService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellWipDTO>> save(@Valid @RequestBody CellWipSaveDTO saveDTO) {
        return Results.createSuccessRes(cellWipService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellWipService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellWipQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellWipService.export(query, response);
    }
}
