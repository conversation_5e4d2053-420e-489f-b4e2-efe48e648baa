package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellWipTotalDTO;
import com.jinkosolar.scp.mps.domain.query.CellWipTotalQuery;
import com.jinkosolar.scp.mps.domain.save.CellWipTotalSaveDTO;
import com.jinkosolar.scp.mps.service.CellWipTotalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 开立工单明细表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@RestController
@RequestMapping("/cell-wip-total")
@RequiredArgsConstructor
@Api(value = "cell-wip-total", tags = "开立工单明细表操作")
public class CellWipTotalController {
    private final CellWipTotalService cellWipTotalService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "开立工单明细表分页列表", notes = "获得开立工单明细表分页列表")
    public ResponseEntity<Results<Page<CellWipTotalDTO>>> queryByPage(@RequestBody CellWipTotalQuery query) {
        return Results.createSuccessRes(cellWipTotalService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellWipTotalDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellWipTotalService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellWipTotalDTO>> save(@Valid @RequestBody CellWipTotalSaveDTO saveDTO) {
        return Results.createSuccessRes(cellWipTotalService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellWipTotalService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellWipTotalQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            cellWipTotalService.export(query, response);
    }
}
