package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanCompDTO;
import com.jinkosolar.scp.mps.domain.query.ComponentAlignmentPlanCompQuery;
import com.jinkosolar.scp.mps.domain.save.ComponentAlignmentPlanCompGenerateSaveDTO;
import com.jinkosolar.scp.mps.service.ComponentAlignmentPlanCompService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 组件定线规划 版本对比表 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-28 11:29:31
 */
@RestController
@RequestMapping("/component-alignment-plan-comp")
@RequiredArgsConstructor
@Api(value = "component-alignment-plan-comp", tags = "组件定线规划 版本对比表操作")
public class ComponentAlignmentPlanCompController {
    private final ComponentAlignmentPlanCompService componentAlignmentPlanCompService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "组件定线规划 版本对比表分页列表", notes = "获得组件定线规划 版本对比表分页列表")
    public ResponseEntity<Results<Page<ComponentAlignmentPlanCompDTO>>> queryByPage(@RequestBody ComponentAlignmentPlanCompQuery query) {
        return Results.createSuccessRes(componentAlignmentPlanCompService.queryByPage(query));
    }

    /**
     * 页列表
     *
     * @param generateSaveDTO 筛选条件
     * @return 查询结果
     */
    @PostMapping("/generate")
    @ApiOperation(value = "组件定线规划 版本对比 生成", notes = "获得组件定线规划 组件定线规划 版本对比 生成")
    public ResponseEntity<Results<Object>> generate(@RequestBody @Valid ComponentAlignmentPlanCompGenerateSaveDTO generateSaveDTO) {
        componentAlignmentPlanCompService.generate(generateSaveDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ComponentAlignmentPlanCompQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        componentAlignmentPlanCompService.export(query, response);
    }
}
