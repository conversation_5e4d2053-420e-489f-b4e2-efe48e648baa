package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanChangeStaQuery;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanVersionDTO;
import com.jinkosolar.scp.mps.domain.query.ComponentAlignmentPlanQuery;
import com.jinkosolar.scp.mps.service.ComponentAlignmentPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组件定线规划表相关操作控制层
 *
 * <AUTHOR> 2024-04-19 10:12:03
 */
@RequestMapping(value = "/component-alignment-plan")
@RestController
@Api(value = "componentAlignmentPlan", tags = "组件定线规划表相关操作控制层")
public class ComponentAlignmentPlanController extends BaseController {
    @Autowired
    private ComponentAlignmentPlanService componentAlignmentPlanService;

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<ComponentAlignmentPlanVersionDTO>>> versions() {
        return Results.createSuccessRes(componentAlignmentPlanService.top10Versions());
    }

    @PostMapping("/queryLastVersionList")
    @ApiOperation(value = "查询最新版本号数据")
    public ResponseEntity<Results<List<ComponentAlignmentPlanDTO>>> queryLastVersionList(@RequestBody ComponentAlignmentPlanQuery query) {
        return Results.createSuccessRes(componentAlignmentPlanService.queryLastVersionList(query));
    }

    @PostMapping("/page")
    @ApiOperation(value = "组件定线规划表分页查询")
    public ResponseEntity<Results<Page<ComponentAlignmentPlanDTO>>> page(@RequestBody ComponentAlignmentPlanQuery query) {
        return Results.createSuccessRes(componentAlignmentPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件定线规划表详情")
    public ResponseEntity<Results<ComponentAlignmentPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(componentAlignmentPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件定线规划表")
    public ResponseEntity<Results<ComponentAlignmentPlanDTO>> insert(@RequestBody ComponentAlignmentPlanDTO componentAlignmentPlanDTO) {
        validObject(componentAlignmentPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(componentAlignmentPlanService.saveOrUpdate(componentAlignmentPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件定线规划表")
    public ResponseEntity<Results<ComponentAlignmentPlanDTO>> update(@RequestBody ComponentAlignmentPlanDTO componentAlignmentPlanDTO) {
        validObject(componentAlignmentPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(componentAlignmentPlanService.saveOrUpdate(componentAlignmentPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件定线规划表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        componentAlignmentPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件定线规划表")
    @PostMapping("/export")
    public void export(@RequestBody ComponentAlignmentPlanQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        componentAlignmentPlanService.export(query, response);
    }

    @ApiOperation(value = "导入组件定线规划表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara,@RequestParam(value = "versionRemark",required = false)String versionRemark) {
        if(versionRemark != null){
            Assert.isTrue(versionRemark.length()<=100,"版本备注长度需要在1-100之间");
        }

        ImportResultDTO importResultDTO = componentAlignmentPlanService.importData(multipartFile, excelPara,versionRemark);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/changeStatus")
    @ApiOperation(value = "更改状态 升版或确认时调用")
    public ResponseEntity<Results<Object>> changeStatus(@RequestBody ComponentAlignmentPlanChangeStaQuery query) {
//        Assert.notEmpty(query.getIdsDTO().getIds(), "传入的ids不能为空");
        componentAlignmentPlanService.changeStatus(query);
        return Results.createSuccessRes();
    }
}