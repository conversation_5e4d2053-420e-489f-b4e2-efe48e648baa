package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanRepDTO;
import com.jinkosolar.scp.mps.domain.query.ComponentAlignmentPlanQuery;
import com.jinkosolar.scp.mps.domain.query.ComponentAlignmentPlanRepQuery;
import com.jinkosolar.scp.mps.service.ComponentAlignmentPlanRepService;
import com.jinkosolar.scp.mps.service.ComponentAlignmentPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组件定线规划表相关操作控制层
 *
 * <AUTHOR> 2024-04-19 10:12:03
 */
@RequestMapping(value = "/component-alignment-plan-rep")
@RestController
@Api(value = "componentAlignmentPlanRep", tags = "组件定线规划对比报表相关操作控制层")
public class ComponentAlignmentPlanRepController extends BaseController {
    @Autowired
    private ComponentAlignmentPlanRepService componentAlignmentPlanRepService;

    @PostMapping("/get")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<ComponentAlignmentPlanRepDTO>>> get(@RequestBody ComponentAlignmentPlanRepQuery query) {
        return Results.createSuccessRes(componentAlignmentPlanRepService.get(query));
    }

    @PostMapping("/getExhibit")
    @ApiOperation(value = "查询最新版本号数据")
    public ResponseEntity<Results<Map<Integer, String>>> getExhibit() {
        return Results.createSuccessRes(componentAlignmentPlanRepService.getExhibit());
    }

    @PostMapping("/getChangeType")
    @ApiOperation(value = "组件定线规划表分页查询")
    public ResponseEntity<Results<Map<Integer, String>>> getChangeType() {
        return Results.createSuccessRes(componentAlignmentPlanRepService.getChangeType());
    }

//    导出
    @ApiOperation(value = "导出组件定线规划对比报表")
    @PostMapping("/export")
    public void export(@RequestBody ComponentAlignmentPlanRepQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        componentAlignmentPlanRepService.export(query, response);
    }
}