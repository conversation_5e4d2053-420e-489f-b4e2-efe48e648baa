package com.jinkosolar.scp.mps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ibm.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jinkosolar.scp.mps.domain.query.ComponentLinePlanDetailQuery;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.jinkosolar.scp.mps.domain.save.ComponentLinePlanDetailSaveDTO;
import com.jinkosolar.scp.mps.domain.entity.ComponentLinePlanDetail;
import com.jinkosolar.scp.mps.domain.dto.ComponentLinePlanDetailDTO;
import com.jinkosolar.scp.mps.service.ComponentLinePlanDetailService;
import com.ibm.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * [说明]定线规划明细表 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-11-13
 */
@RestController
@RequestMapping("/component-line-plan-detail")
@RequiredArgsConstructor
@Api(value = "component-line-plan-detail", tags = "定线规划明细表操作")
public class ComponentLinePlanDetailController {
    private final ComponentLinePlanDetailService componentLinePlanDetailService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "定线规划明细表分页列表", notes = "获得定线规划明细表分页列表")
    public ResponseEntity<Results<Page<ComponentLinePlanDetailDTO>>> queryByPage(@RequestBody ComponentLinePlanDetailQuery query) {
        return Results.createSuccessRes(componentLinePlanDetailService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ComponentLinePlanDetailDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(componentLinePlanDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ComponentLinePlanDetailDTO>> save(@Valid @RequestBody ComponentLinePlanDetailSaveDTO saveDTO) {
        return Results.createSuccessRes(componentLinePlanDetailService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        componentLinePlanDetailService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ComponentLinePlanDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            componentLinePlanDetailService.export(query, response);
    }
}
