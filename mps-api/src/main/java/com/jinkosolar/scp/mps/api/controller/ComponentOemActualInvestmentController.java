package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ComponentOemActualInvestmentDTO;
import com.jinkosolar.scp.mps.domain.query.ComponentOemActualInvestmentQuery;
import com.jinkosolar.scp.mps.service.ComponentOemActualInvestmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件OEM实投相关操作控制层
 * 
 * <AUTHOR> 2024-08-07 15:48:17
 */
@RequestMapping(value = "/component-oem-actual-investment")
@RestController
@Api(value = "componentOemActualInvestment", tags = "组件OEM实投相关操作控制层")
@RequiredArgsConstructor  
public class ComponentOemActualInvestmentController extends BaseController {    
    private final ComponentOemActualInvestmentService componentOemActualInvestmentService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件OEM实投分页查询")
    public ResponseEntity<Results<Page<ComponentOemActualInvestmentDTO>>> page(@RequestBody ComponentOemActualInvestmentQuery query) {
        return Results.createSuccessRes(componentOemActualInvestmentService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件OEM实投详情")
    public ResponseEntity<Results<ComponentOemActualInvestmentDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(componentOemActualInvestmentService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件OEM实投")
    public ResponseEntity<Results<ComponentOemActualInvestmentDTO>> insert(@RequestBody ComponentOemActualInvestmentDTO componentOemActualInvestmentDTO) {
        validObject(componentOemActualInvestmentDTO, ValidGroups.Insert.class);
        componentOemActualInvestmentDTO = componentOemActualInvestmentService.saveOrUpdate(componentOemActualInvestmentDTO);
        // 编辑成功以后传APS
        componentOemActualInvestmentService.syncComponentOemActualInvestmentAps();
        return Results.createSuccessRes(componentOemActualInvestmentDTO);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件OEM实投")
    public ResponseEntity<Results<ComponentOemActualInvestmentDTO>> update(@RequestBody ComponentOemActualInvestmentDTO componentOemActualInvestmentDTO) {
        validObject(componentOemActualInvestmentDTO, ValidGroups.Update.class);
        componentOemActualInvestmentDTO = componentOemActualInvestmentService.saveOrUpdate(componentOemActualInvestmentDTO);
        // 编辑成功以后传APS
        componentOemActualInvestmentService.syncComponentOemActualInvestmentAps();
        return Results.createSuccessRes(componentOemActualInvestmentDTO);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件OEM实投")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        componentOemActualInvestmentService.logicDeleteByIds(ids);
        // 删除成功以后传APS
        componentOemActualInvestmentService.syncComponentOemActualInvestmentAps();
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件OEM实投")
    @PostMapping("/export")
    public void export(@RequestBody ComponentOemActualInvestmentQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        componentOemActualInvestmentService.export(query, response);
    }

    @ApiOperation(value = "导入组件OEM实投")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = componentOemActualInvestmentService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            // 导入成功以后传APS
            componentOemActualInvestmentService.syncComponentOemActualInvestmentAps();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}