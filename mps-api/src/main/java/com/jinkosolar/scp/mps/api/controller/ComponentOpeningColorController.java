package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ComponentOpeningColorDTO;
import com.jinkosolar.scp.mps.domain.query.ComponentOpeningColorQuery;
import com.jinkosolar.scp.mps.service.ComponentOpeningColorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件客开调整颜色相关操作控制层
 * 
 * <AUTHOR> 2024-08-07 15:48:17
 */
@RequestMapping(value = "/component-opening-color")
@RestController
@Api(value = "componentOpeningColor", tags = "组件客开调整颜色相关操作控制层")
@RequiredArgsConstructor  
public class ComponentOpeningColorController extends BaseController {    
    private final ComponentOpeningColorService componentOpeningColorService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件客开调整颜色分页查询")
    public ResponseEntity<Results<Page<ComponentOpeningColorDTO>>> page(@RequestBody ComponentOpeningColorQuery query) {
        return Results.createSuccessRes(componentOpeningColorService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件客开调整颜色详情")
    public ResponseEntity<Results<ComponentOpeningColorDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(componentOpeningColorService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件客开调整颜色")
    public ResponseEntity<Results<ComponentOpeningColorDTO>> insert(@RequestBody ComponentOpeningColorDTO componentOpeningColorDTO) {
        validObject(componentOpeningColorDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(componentOpeningColorService.saveOrUpdate(componentOpeningColorDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件客开调整颜色")
    public ResponseEntity<Results<ComponentOpeningColorDTO>> update(@RequestBody ComponentOpeningColorDTO componentOpeningColorDTO) {
        validObject(componentOpeningColorDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(componentOpeningColorService.saveOrUpdate(componentOpeningColorDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件客开调整颜色")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        componentOpeningColorService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件客开调整颜色")
    @PostMapping("/export")
    public void export(@RequestBody ComponentOpeningColorQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        componentOpeningColorService.export(query, response);
    }

    @ApiOperation(value = "导入组件客开调整颜色")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = componentOpeningColorService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}