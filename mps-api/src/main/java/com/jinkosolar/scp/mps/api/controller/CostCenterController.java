package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CostCenterDTO;
import com.jinkosolar.scp.mps.domain.query.CostCenterQuery;
import com.jinkosolar.scp.mps.domain.save.CostCenterSaveDTO;
import com.jinkosolar.scp.mps.service.CostCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;


@Api(value = "CostCenter", tags = "管理后台 - 成本中心维护")
@RestController
@RequestMapping("/CostCenter")
@Validated
public class CostCenterController {

    @Resource
    private CostCenterService costCenterService;

//    @GetMapping("/code/list")
//    @ApiOperation(value =  "获得成本中心code列表")
//    public ResponseEntity<Results<List<CostCenterDTO>>> getCodeList() {
//        return Results.createSuccessRes(costCenterService.queryCostCenterCodeList());
//    }

    @PostMapping("/create")
    @ApiOperation(value =  "创建成本中心维护")
    public ResponseEntity<Results<CostCenterDTO>> createCostCenter(@Valid @RequestBody CostCenterSaveDTO createReqVO) {
    return Results.createSuccessRes(costCenterService.save(createReqVO));
    }

    @PutMapping("/update")
    @ApiOperation(value =  "更新成本中心维护")
    public ResponseEntity<Results<CostCenterDTO>> updateCostCenter(@Valid @RequestBody CostCenterSaveDTO updateReqVO) {
        return Results.createSuccessRes(costCenterService.save(updateReqVO));
    }

    @DeleteMapping("/delete")
    @ApiOperation(value =  "删除成本中心维护")
    @ApiParam(name = "id", value = "编号", required = true)
    public ResponseEntity<Results<Boolean>> deleteCostCenter(@RequestParam("ids") List<Long> ids) {
            costCenterService.logicDeleteByIds(ids);
        return Results.createSuccessRes(true);
    }

    @GetMapping("/get")
    @ApiOperation(value =  "获得成本中心维护")
    @ApiParam(name = "id", value = "编号", required = true)
    public ResponseEntity<Results<CostCenterDTO>> getCostCenter(@RequestParam("id") Long id) {
        CostCenterDTO CostCenter = costCenterService.queryById(id);
        return Results.createSuccessRes(CostCenter);
    }

    @GetMapping("/page")
    @ApiOperation(value =  "获得成本中心维护分页")
    public ResponseEntity<Results<Page<CostCenterDTO>>> getCostCenterPage(@Valid CostCenterQuery pageReqVO) {
    Page<CostCenterDTO> pageResult = costCenterService.queryByPage(pageReqVO);
        return Results.createSuccessRes(pageResult);
    }

    @GetMapping("/export-excel")
    @ApiOperation(value =  "导出成本中心维护 Excel")
    public void exportCostCenterExcel(@RequestBody @Valid CostCenterQuery pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(GlobalConstant.max_page_size);
        costCenterService.export(pageReqVO, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(costCenterService.importData(multipartFile, excelPara));
    }

}