package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CraftLimitDTO;
import com.jinkosolar.scp.mps.domain.query.CraftLimitQuery;
import com.jinkosolar.scp.mps.domain.save.CraftLimitSaveDTO;
import com.jinkosolar.scp.mps.service.CraftLimitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


@Api(value = "CraftLimit", tags = "管理后台 - 工艺限制")
@RestController
@RequestMapping("/craft-limit")
@Validated
public class CraftLimitController {

    @Resource
    private CraftLimitService craftLimitService;
    
    @PostMapping("/insert")
    @ApiOperation(value =  "创建工艺限制")
    public ResponseEntity<Results<CraftLimitDTO>> createCraftLimit(@Valid @RequestBody CraftLimitSaveDTO createReqVO) {
    return Results.createSuccessRes(craftLimitService.save(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value =  "更新工艺限制")
    public ResponseEntity<Results<CraftLimitDTO>> updateCraftLimit(@Valid @RequestBody CraftLimitSaveDTO updateReqVO) {
        return Results.createSuccessRes(craftLimitService.save(updateReqVO));
    }

    @PostMapping("/delete")
    @ApiOperation(value =  "删除工艺限制")
    @ApiParam(name = "id", value = "编号", required = true)
    public ResponseEntity<Results<Boolean>> deleteCraftLimit(@RequestBody IdsDTO idsDTO) {
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        craftLimitService.logicDeleteByIds(ids);
        return Results.createSuccessRes(true);
    }

    @PostMapping("/get")
    @ApiOperation(value =  "获得工艺限制")
    @ApiParam(name = "id", value = "编号", required = true)
    public ResponseEntity<Results<CraftLimitDTO>> getCraftLimit(@RequestBody IdDTO idDTO) {
        CraftLimitDTO CraftLimit = craftLimitService.queryById(Long.valueOf(idDTO.getId()));
        return Results.createSuccessRes(CraftLimit);
    }

    @PostMapping("/page")
    @ApiOperation(value =  "获得工艺限制分页")
    public ResponseEntity<Results<Page<CraftLimitDTO>>> getCraftLimitPage(@RequestBody CraftLimitQuery pageReqVO) {
    Page<CraftLimitDTO> pageResult = craftLimitService.queryByPage(pageReqVO);
        return Results.createSuccessRes(pageResult);
    }

    @PostMapping("/export-excel")
    @ApiOperation(value =  "导出工艺限制 Excel")
    public void exportCraftLimitExcel(@RequestBody @Valid CraftLimitQuery pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(GlobalConstant.max_page_size);
        craftLimitService.export(pageReqVO, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(craftLimitService.importData(multipartFile, excelPara));
    }

}