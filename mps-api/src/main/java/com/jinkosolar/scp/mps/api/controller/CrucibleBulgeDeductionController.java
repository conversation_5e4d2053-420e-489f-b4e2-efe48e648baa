package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrucibleBulgeDeductionDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrucibleBulgeDeductionQuery;
import com.jinkosolar.scp.mps.service.CrucibleBulgeDeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 坩埚鼓包扣减相关操作控制层
 * 
 * <AUTHOR> 2024-07-26 15:16:33
 */
@RequestMapping(value = "/crucible-bulge-deduction")
@RestController
@Api(value = "crucibleBulgeDeduction", tags = "坩埚鼓包扣减相关操作控制层")
public class CrucibleBulgeDeductionController extends BaseController {    
    private final CrucibleBulgeDeductionService crucibleBulgeDeductionService;

    public CrucibleBulgeDeductionController(CrucibleBulgeDeductionService crucibleBulgeDeductionService) {
        this.crucibleBulgeDeductionService = crucibleBulgeDeductionService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "坩埚鼓包扣减分页查询")
    public ResponseEntity<Results<Page<CrucibleBulgeDeductionDTO>>> page(@RequestBody CrucibleBulgeDeductionQuery query) {
        return Results.createSuccessRes(crucibleBulgeDeductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "坩埚鼓包扣减详情")
    public ResponseEntity<Results<CrucibleBulgeDeductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crucibleBulgeDeductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增坩埚鼓包扣减")
    public ResponseEntity<Results<CrucibleBulgeDeductionDTO>> insert(@RequestBody CrucibleBulgeDeductionDTO crucibleBulgeDeductionDTO) {
        validObject(crucibleBulgeDeductionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crucibleBulgeDeductionService.saveOrUpdate(crucibleBulgeDeductionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新坩埚鼓包扣减")
    public ResponseEntity<Results<CrucibleBulgeDeductionDTO>> update(@RequestBody CrucibleBulgeDeductionDTO crucibleBulgeDeductionDTO) {
        validObject(crucibleBulgeDeductionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crucibleBulgeDeductionService.saveOrUpdate(crucibleBulgeDeductionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除坩埚鼓包扣减")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crucibleBulgeDeductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出坩埚鼓包扣减")
    @PostMapping("/export")
    public void export(@RequestBody CrucibleBulgeDeductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crucibleBulgeDeductionService.export(query, response);
    }

    @ApiOperation(value = "导入坩埚鼓包扣减")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                     @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crucibleBulgeDeductionService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crucibleBulgeDeductionService.queryVersions(dataType.getId()));
    }
}