package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrucibleHeightDeductionDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrucibleHeightDeductionQuery;
import com.jinkosolar.scp.mps.service.CrucibleHeightDeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 坩埚高度扣减相关操作控制层
 * 
 * <AUTHOR> 2024-08-06 09:35:05
 */
@RequestMapping(value = "/crucible-height-deduction")
@RestController
@Api(value = "crucibleHeightDeduction", tags = "坩埚高度扣减相关操作控制层")
public class CrucibleHeightDeductionController extends BaseController {    
    private final CrucibleHeightDeductionService crucibleHeightDeductionService;

    public CrucibleHeightDeductionController(CrucibleHeightDeductionService crucibleHeightDeductionService) {
        this.crucibleHeightDeductionService = crucibleHeightDeductionService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "坩埚高度扣减分页查询")
    public ResponseEntity<Results<Page<CrucibleHeightDeductionDTO>>> page(@RequestBody CrucibleHeightDeductionQuery query) {
        return Results.createSuccessRes(crucibleHeightDeductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "坩埚高度扣减详情")
    public ResponseEntity<Results<CrucibleHeightDeductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crucibleHeightDeductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增坩埚高度扣减")
    public ResponseEntity<Results<CrucibleHeightDeductionDTO>> insert(@RequestBody CrucibleHeightDeductionDTO crucibleHeightDeductionDTO) {
        validObject(crucibleHeightDeductionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crucibleHeightDeductionService.saveOrUpdate(crucibleHeightDeductionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新坩埚高度扣减")
    public ResponseEntity<Results<CrucibleHeightDeductionDTO>> update(@RequestBody CrucibleHeightDeductionDTO crucibleHeightDeductionDTO) {
        validObject(crucibleHeightDeductionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crucibleHeightDeductionService.saveOrUpdate(crucibleHeightDeductionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除坩埚高度扣减")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crucibleHeightDeductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出坩埚高度扣减")
    @PostMapping("/export")
    public void export(@RequestBody CrucibleHeightDeductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crucibleHeightDeductionService.export(query, response);
    }

    @ApiOperation(value = "导入坩埚高度扣减")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crucibleHeightDeductionService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crucibleHeightDeductionService.queryVersions(dataType.getId()));
    }
}