package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.SyncTableDTO;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.CrystalActualYieldDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalActualYieldQuery;
import com.jinkosolar.scp.mps.service.CrystalActualYieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶实际良率表相关操作控制层
 * 
 * <AUTHOR> 2024-08-07 15:17:05
 */
@RequestMapping(value = "/crystal-actual-yield")
@RestController
@Api(value = "crystalActualYield", tags = "拉晶实际良率表相关操作控制层")
@RequiredArgsConstructor  
public class CrystalActualYieldController extends BaseController {    
    private final CrystalActualYieldService crystalActualYieldService;

    @Autowired
    private SyncTableUtils syncTableUtils;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶实际良率表分页查询")
    public ResponseEntity<Results<Page<CrystalActualYieldDTO>>> page(@RequestBody CrystalActualYieldQuery query) {
        return Results.createSuccessRes(crystalActualYieldService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶实际良率表详情")
    public ResponseEntity<Results<CrystalActualYieldDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalActualYieldService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶实际良率表")
    public ResponseEntity<Results<CrystalActualYieldDTO>> insert(@RequestBody CrystalActualYieldDTO crystalActualYieldDTO) {
        validObject(crystalActualYieldDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalActualYieldService.saveOrUpdate(crystalActualYieldDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶实际良率表")
    public ResponseEntity<Results<CrystalActualYieldDTO>> update(@RequestBody CrystalActualYieldDTO crystalActualYieldDTO) {
        validObject(crystalActualYieldDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalActualYieldService.saveOrUpdate(crystalActualYieldDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶实际良率表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalActualYieldService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶实际良率表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalActualYieldQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalActualYieldService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶实际良率表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalActualYieldService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncCrmStockToAPS();
            return Results.createSuccessRes(importResultDTO);

        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versionNumber")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalActualYieldService.queryVersions(dataType.getId()));
    }

    public void syncCrmStockToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> tables = new ArrayList<>();
        tables.add("mps_crystal_actual_yield");
        syncTableDTO.setLovCodes(tables);
        syncTableUtils.syncTables(syncTableDTO);
    }
}