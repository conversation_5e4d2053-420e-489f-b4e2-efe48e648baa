package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalActualYieldMesInfoDTO;
import com.jinkosolar.scp.mps.domain.dto.CrystalActualYieldMesInfoReportDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalActualYieldMesInfoQuery;
import com.jinkosolar.scp.mps.service.CrystalActualYieldMesInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶实际良率数据（mes接口返回）相关操作控制层
 * 
 * <AUTHOR> 2024-10-16 18:33:16
 */
@RequestMapping(value = "/crystal-actual-yield-mes")
@RestController
@Api(value = "crystalActualYieldMesInfo", tags = "拉晶实际良率数据（mes接口返回）相关操作控制层")
@RequiredArgsConstructor  
public class CrystalActualYieldMesInfoController extends BaseController {    
    private final CrystalActualYieldMesInfoService crystalActualYieldMesInfoService; 

    @PostMapping("/page")
    @ApiOperation(value = "拉晶实际良率数据（mes接口返回）分页查询")
    public ResponseEntity<Results<Page<CrystalActualYieldMesInfoDTO>>> page(@RequestBody CrystalActualYieldMesInfoQuery query) {
        return Results.createSuccessRes(crystalActualYieldMesInfoService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶实际良率数据（mes接口返回）详情")
    public ResponseEntity<Results<CrystalActualYieldMesInfoDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalActualYieldMesInfoService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶实际良率数据（mes接口返回）")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalActualYieldMesInfoDTO crystalActualYieldMesInfoDTO) {
        validObject(crystalActualYieldMesInfoDTO, ValidGroups.Insert.class);
        crystalActualYieldMesInfoService.saveOrUpdate(crystalActualYieldMesInfoDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶实际良率数据（mes接口返回）")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalActualYieldMesInfoDTO crystalActualYieldMesInfoDTO) {
        validObject(crystalActualYieldMesInfoDTO, ValidGroups.Update.class);
        crystalActualYieldMesInfoService.saveOrUpdate(crystalActualYieldMesInfoDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶实际良率数据（mes接口返回）")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalActualYieldMesInfoService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导入拉晶实际良率数据（mes接口返回）")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalActualYieldMesInfoService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/list")
    @ApiOperation(value = "MES实际良率报表")
    public ResponseEntity<Results<List<CrystalActualYieldMesInfoReportDTO>>> getList(@RequestBody CrystalActualYieldMesInfoQuery query) {
        return Results.createSuccessRes(crystalActualYieldMesInfoService.getList(query));
    }

    @ApiOperation(value = "导出拉晶实际良率数据（mes接口返回）")
    @PostMapping("/export")
    public void export(@RequestBody CrystalActualYieldMesInfoQuery query, HttpServletResponse response) {
        crystalActualYieldMesInfoService.export(query, response);
    }
}