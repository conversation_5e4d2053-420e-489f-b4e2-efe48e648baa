package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalCaculateMachineDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalCaculateMachineQuery;
import com.jinkosolar.scp.mps.service.CrystalCaculateMachineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶需求拆分机台数计算表相关操作控制层
 * 
 * <AUTHOR> 2024-10-10 10:28:14
 */
@RequestMapping(value = "/crystal-caculate-machine")
@RestController
@Api(value = "crystalCaculateMachine", tags = "拉晶需求拆分机台数计算表相关操作控制层")
@RequiredArgsConstructor  
public class CrystalCaculateMachineController extends BaseController {    
    private final CrystalCaculateMachineService crystalCaculateMachineService; 

    @PostMapping("/page")
    @ApiOperation(value = "拉晶需求拆分机台数计算表分页查询")
    public ResponseEntity<Results<Page<CrystalCaculateMachineDTO>>> page(@RequestBody CrystalCaculateMachineQuery query) {
        return Results.createSuccessRes(crystalCaculateMachineService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶需求拆分机台数计算表详情")
    public ResponseEntity<Results<CrystalCaculateMachineDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalCaculateMachineService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶需求拆分机台数计算表")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalCaculateMachineDTO crystalCaculateMachineDTO) {
        validObject(crystalCaculateMachineDTO, ValidGroups.Insert.class);
        crystalCaculateMachineService.saveOrUpdate(crystalCaculateMachineDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶需求拆分机台数计算表")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalCaculateMachineDTO crystalCaculateMachineDTO) {
        validObject(crystalCaculateMachineDTO, ValidGroups.Update.class);
        crystalCaculateMachineService.saveOrUpdate(crystalCaculateMachineDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶需求拆分机台数计算表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalCaculateMachineService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶需求拆分机台数计算表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalCaculateMachineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalCaculateMachineService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶需求拆分机台数计算表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalCaculateMachineService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "轮次拆分")
    @PostMapping("/caculateMachine")
    public ResponseEntity<Results<Void>> caculateMachine(@RequestBody CrystalCaculateMachineQuery query) {
        crystalCaculateMachineService.caculateMachine(query.getModelType());
        return Results.createSuccessRes();
    }
}