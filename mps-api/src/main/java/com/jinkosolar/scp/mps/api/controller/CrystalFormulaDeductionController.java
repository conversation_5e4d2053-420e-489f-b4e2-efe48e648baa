package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalFormulaDeductionDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalFormulaDeductionQuery;
import com.jinkosolar.scp.mps.service.CrystalFormulaDeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶配方扣减表相关操作控制层
 * 
 * <AUTHOR> 2024-07-31 19:03:11
 */
@RequestMapping(value = "/crystal-formula-deduction")
@RestController
@Api(value = "crystalFormulaDeduction", tags = "拉晶配方扣减表相关操作控制层")
public class CrystalFormulaDeductionController extends BaseController {    
    private final CrystalFormulaDeductionService crystalFormulaDeductionService;

    public CrystalFormulaDeductionController(CrystalFormulaDeductionService crystalFormulaDeductionService) {
        this.crystalFormulaDeductionService = crystalFormulaDeductionService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶配方扣减表分页查询")
    public ResponseEntity<Results<Page<CrystalFormulaDeductionDTO>>> page(@RequestBody CrystalFormulaDeductionQuery query) {
        return Results.createSuccessRes(crystalFormulaDeductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶配方扣减表详情")
    public ResponseEntity<Results<CrystalFormulaDeductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalFormulaDeductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶配方扣减表")
    public ResponseEntity<Results<CrystalFormulaDeductionDTO>> insert(@RequestBody CrystalFormulaDeductionDTO crystalFormulaDeductionDTO) {
        validObject(crystalFormulaDeductionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalFormulaDeductionService.saveOrUpdate(crystalFormulaDeductionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶配方扣减表")
    public ResponseEntity<Results<CrystalFormulaDeductionDTO>> update(@RequestBody CrystalFormulaDeductionDTO crystalFormulaDeductionDTO) {
        validObject(crystalFormulaDeductionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalFormulaDeductionService.saveOrUpdate(crystalFormulaDeductionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶配方扣减表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalFormulaDeductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶配方扣减表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalFormulaDeductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalFormulaDeductionService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶配方扣减表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                     @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalFormulaDeductionService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalFormulaDeductionService.queryVersions(dataType.getId()));
    }


}