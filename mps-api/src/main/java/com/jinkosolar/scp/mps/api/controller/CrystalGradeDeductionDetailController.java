package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeDeductionDetailDTO;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeDeductionMainDTO;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalGradeDeductionDetailQuery;
import com.jinkosolar.scp.mps.domain.query.CrystalSpecialDeductionDetailQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.CrystalGradeDeductionDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 拉晶坩埚等级扣减明细表相关操作控制层
 * 
 * <AUTHOR> 2024-07-24 17:04:51
 */
@RequestMapping(value = "/crystal-grade-deduction-detail")
@RestController
@Api(value = "crystalGradeDeductionDetail", tags = "拉晶坩埚等级扣减明细表相关操作控制层")
public class CrystalGradeDeductionDetailController extends BaseController {    
    private final CrystalGradeDeductionDetailService crystalGradeDeductionDetailService;

    public CrystalGradeDeductionDetailController(CrystalGradeDeductionDetailService crystalGradeDeductionDetailService) {
        this.crystalGradeDeductionDetailService = crystalGradeDeductionDetailService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶坩埚等级扣减明细表分页查询")
    public ResponseEntity<Results<Page<CrystalGradeDeductionDetailDTO>>> page(@RequestBody CrystalGradeDeductionDetailQuery query) {
        return Results.createSuccessRes(crystalGradeDeductionDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶坩埚等级扣减明细表详情")
    public ResponseEntity<Results<CrystalGradeDeductionDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalGradeDeductionDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶坩埚等级扣减明细表")
    public ResponseEntity<Results<CrystalGradeDeductionDetailDTO>> insert(@RequestBody CrystalGradeDeductionDetailDTO crystalGradeDeductionDetailDTO) {
        validObject(crystalGradeDeductionDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalGradeDeductionDetailService.saveOrUpdate(crystalGradeDeductionDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶坩埚等级扣减明细表")
    public ResponseEntity<Results<CrystalGradeDeductionDetailDTO>> update(@RequestBody CrystalGradeDeductionDetailDTO crystalGradeDeductionDetailDTO) {
        validObject(crystalGradeDeductionDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalGradeDeductionDetailService.saveOrUpdate(crystalGradeDeductionDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶坩埚等级扣减明细表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalGradeDeductionDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶坩埚等级扣减明细表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalGradeDeductionDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalGradeDeductionDetailService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶坩埚等级扣减明细表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                     @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalGradeDeductionDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "查询拉晶坩埚等级扣减版本集合")
    @PostMapping("/queryMainVersionList")
    public ResponseEntity<Results<List<String>>> queryMainVersionList(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(crystalGradeDeductionDetailService.queryMainVersionList(versionQuery));
    }



    @ApiOperation(value = "查询拉晶特扣减主列表分页")
    @PostMapping("/pageMainList")
    public ResponseEntity<Results<PageColumnDto>> pageMainList(@RequestBody CrystalGradeDeductionDetailQuery query) {
        return Results.createSuccessRes(crystalGradeDeductionDetailService.pageMainList(query));
    }
}