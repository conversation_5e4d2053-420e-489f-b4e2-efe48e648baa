package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeDeductionMainDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalGradeDeductionMainQuery;
import com.jinkosolar.scp.mps.service.CrystalGradeDeductionMainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶坩埚等级扣减主表相关操作控制层
 * 
 * <AUTHOR> 2024-07-24 17:04:51
 */
@RequestMapping(value = "/crystal-grade-deduction-main")
@RestController
@Api(value = "crystalGradeDeductionMain", tags = "拉晶坩埚等级扣减主表相关操作控制层")
public class CrystalGradeDeductionMainController extends BaseController {    
    private final CrystalGradeDeductionMainService crystalGradeDeductionMainService;

    public CrystalGradeDeductionMainController(CrystalGradeDeductionMainService crystalGradeDeductionMainService) {
        this.crystalGradeDeductionMainService = crystalGradeDeductionMainService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶坩埚等级扣减主表分页查询")
    public ResponseEntity<Results<Page<CrystalGradeDeductionMainDTO>>> page(@RequestBody CrystalGradeDeductionMainQuery query) {
        return Results.createSuccessRes(crystalGradeDeductionMainService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶坩埚等级扣减主表详情")
    public ResponseEntity<Results<CrystalGradeDeductionMainDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalGradeDeductionMainService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶坩埚等级扣减主表")
    public ResponseEntity<Results<CrystalGradeDeductionMainDTO>> insert(@RequestBody CrystalGradeDeductionMainDTO crystalGradeDeductionMainDTO) {
        validObject(crystalGradeDeductionMainDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalGradeDeductionMainService.saveOrUpdate(crystalGradeDeductionMainDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶坩埚等级扣减主表")
    public ResponseEntity<Results<CrystalGradeDeductionMainDTO>> update(@RequestBody CrystalGradeDeductionMainDTO crystalGradeDeductionMainDTO) {
        validObject(crystalGradeDeductionMainDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalGradeDeductionMainService.saveOrUpdate(crystalGradeDeductionMainDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶坩埚等级扣减主表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalGradeDeductionMainService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶坩埚等级扣减主表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalGradeDeductionMainQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalGradeDeductionMainService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶坩埚等级扣减主表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalGradeDeductionMainService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}