package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeSwitchDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalGradeSwitchQuery;
import com.jinkosolar.scp.mps.service.CrystalGradeSwitchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 坩埚等级切换计划表相关操作控制层
 * 
 * <AUTHOR> 2024-08-05 13:10:33
 */
@RequestMapping(value = "/crystal-grade-switch")
@RestController
@Api(value = "crystalGradeSwitch", tags = "坩埚等级切换计划表相关操作控制层")
public class CrystalGradeSwitchController extends BaseController {    
    private final CrystalGradeSwitchService crystalGradeSwitchService;

    public CrystalGradeSwitchController(CrystalGradeSwitchService crystalGradeSwitchService) {
        this.crystalGradeSwitchService = crystalGradeSwitchService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "坩埚等级切换计划表分页查询")
    public ResponseEntity<Results<Page<CrystalGradeSwitchDTO>>> page(@RequestBody CrystalGradeSwitchQuery query) {
        return Results.createSuccessRes(crystalGradeSwitchService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "坩埚等级切换计划表详情")
    public ResponseEntity<Results<CrystalGradeSwitchDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalGradeSwitchService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增坩埚等级切换计划表")
    public ResponseEntity<Results<CrystalGradeSwitchDTO>> insert(@RequestBody CrystalGradeSwitchDTO crystalGradeSwitchDTO) {
        validObject(crystalGradeSwitchDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalGradeSwitchService.saveOrUpdate(crystalGradeSwitchDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新坩埚等级切换计划表")
    public ResponseEntity<Results<CrystalGradeSwitchDTO>> update(@RequestBody CrystalGradeSwitchDTO crystalGradeSwitchDTO) {
        validObject(crystalGradeSwitchDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalGradeSwitchService.saveOrUpdate(crystalGradeSwitchDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除坩埚等级切换计划表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalGradeSwitchService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出坩埚等级切换计划表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalGradeSwitchQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalGradeSwitchService.export(query, response);
    }

    @ApiOperation(value = "导入坩埚等级切换计划表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalGradeSwitchService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalGradeSwitchService.queryVersions(dataType.getId()));
    }
}