package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.SyncTableDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.SyncTableUtils;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalHeightSwitchDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalHeightSwitchQuery;
import com.jinkosolar.scp.mps.service.CrystalHeightSwitchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 坩埚高度切换计划表相关操作控制层
 *
 * <AUTHOR> 2024-08-05 16:56:36
 */
@RequestMapping(value = "/crystal-height-switch")
@RestController
@Api(value = "crystalHeightSwitch", tags = "坩埚高度切换计划表相关操作控制层")
public class CrystalHeightSwitchController extends BaseController {
    private final CrystalHeightSwitchService crystalHeightSwitchService;

    @Autowired
    private SyncTableUtils syncTableUtils;

    public CrystalHeightSwitchController(CrystalHeightSwitchService crystalHeightSwitchService) {
        this.crystalHeightSwitchService = crystalHeightSwitchService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "坩埚高度切换计划表分页查询")
    public ResponseEntity<Results<Page<CrystalHeightSwitchDTO>>> page(@RequestBody CrystalHeightSwitchQuery query) {
        return Results.createSuccessRes(crystalHeightSwitchService.page(query));
    }

    @PostMapping("/list")
    @ApiOperation(value = "坩埚高度切换计划表查询")
    public ResponseEntity<Results<List<CrystalHeightSwitchDTO>>> list(@RequestBody CrystalHeightSwitchQuery query) {
        return Results.createSuccessRes(crystalHeightSwitchService.list(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "坩埚高度切换计划表详情")
    public ResponseEntity<Results<CrystalHeightSwitchDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalHeightSwitchService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增坩埚高度切换计划表")
    public ResponseEntity<Results<CrystalHeightSwitchDTO>> insert(@RequestBody CrystalHeightSwitchDTO crystalHeightSwitchDTO) {
        validObject(crystalHeightSwitchDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalHeightSwitchService.saveOrUpdate(crystalHeightSwitchDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新坩埚高度切换计划表")
    public ResponseEntity<Results<CrystalHeightSwitchDTO>> update(@RequestBody CrystalHeightSwitchDTO crystalHeightSwitchDTO) {
        validObject(crystalHeightSwitchDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalHeightSwitchService.saveOrUpdate(crystalHeightSwitchDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除坩埚高度切换计划表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalHeightSwitchService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出坩埚高度切换计划表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalHeightSwitchQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalHeightSwitchService.export(query, response);
    }

    @ApiOperation(value = "导入坩埚高度切换计划表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalHeightSwitchService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncCrmStockToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalHeightSwitchService.queryVersions(dataType.getId()));
    }

    public void syncCrmStockToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> tables = new ArrayList<>();
        tables.add("mps_crystal_height_switch");
        syncTableDTO.setLovCodes(tables);
        syncTableUtils.syncTables(syncTableDTO);
    }
}