package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalJkeDeductionDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalJkeDeductionQuery;
import com.jinkosolar.scp.mps.service.CrystalJkeDeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶JKE扣减表相关操作控制层
 * 
 * <AUTHOR> 2024-08-01 08:28:24
 */
@RequestMapping(value = "/crystal-jke-deduction")
@RestController
@Api(value = "crystalJkeDeduction", tags = "拉晶JKE扣减表相关操作控制层")
public class CrystalJkeDeductionController extends BaseController {    
    private final CrystalJkeDeductionService crystalJkeDeductionService;

    public CrystalJkeDeductionController(CrystalJkeDeductionService crystalJkeDeductionService) {
        this.crystalJkeDeductionService = crystalJkeDeductionService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶JKE扣减表分页查询")
    public ResponseEntity<Results<Page<CrystalJkeDeductionDTO>>> page(@RequestBody CrystalJkeDeductionQuery query) {
        return Results.createSuccessRes(crystalJkeDeductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶JKE扣减表详情")
    public ResponseEntity<Results<CrystalJkeDeductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalJkeDeductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶JKE扣减表")
    public ResponseEntity<Results<CrystalJkeDeductionDTO>> insert(@RequestBody CrystalJkeDeductionDTO crystalJkeDeductionDTO) {
        validObject(crystalJkeDeductionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalJkeDeductionService.saveOrUpdate(crystalJkeDeductionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶JKE扣减表")
    public ResponseEntity<Results<CrystalJkeDeductionDTO>> update(@RequestBody CrystalJkeDeductionDTO crystalJkeDeductionDTO) {
        validObject(crystalJkeDeductionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalJkeDeductionService.saveOrUpdate(crystalJkeDeductionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶JKE扣减表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalJkeDeductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶JKE扣减表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalJkeDeductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalJkeDeductionService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶JKE扣减表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalJkeDeductionService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalJkeDeductionService.queryVersions(dataType.getId()));
    }
}