package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalPlanClimbSectionResultDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalPlanClimbSectionResultQuery;
import com.jinkosolar.scp.mps.service.CrystalCaculateMachineService;
import com.jinkosolar.scp.mps.service.CrystalPlanClimbSectionResultService;
import com.jinkosolar.scp.mps.service.HearthClimbRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶产能需求结果表相关操作控制层
 * 
 * <AUTHOR> 2024-08-23 09:15:57
 */
@Slf4j
@RequestMapping(value = "/crystal-plan-climb-section-result")
@RestController
@Api(value = "crystalPlanClimbSectionResult", tags = "拉晶产能需求结果表相关操作控制层")
@RequiredArgsConstructor  
public class CrystalPlanClimbSectionResultController extends BaseController {    
    private final CrystalPlanClimbSectionResultService crystalPlanClimbSectionResultService;
    private final CrystalCaculateMachineService crystalCaculateMachineService;
    private final HearthClimbRuleService hearthClimbRuleService;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶产能需求结果表分页查询")
    public ResponseEntity<Results<Page<CrystalPlanClimbSectionResultDTO>>> page(@RequestBody CrystalPlanClimbSectionResultQuery query) {
        return Results.createSuccessRes(crystalPlanClimbSectionResultService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶产能需求结果表详情")
    public ResponseEntity<Results<CrystalPlanClimbSectionResultDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalPlanClimbSectionResultService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶产能需求结果表")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalPlanClimbSectionResultDTO crystalPlanClimbSectionResultDTO) {
        validObject(crystalPlanClimbSectionResultDTO, ValidGroups.Insert.class);
        crystalPlanClimbSectionResultService.saveOrUpdate(crystalPlanClimbSectionResultDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶产能需求结果表")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalPlanClimbSectionResultDTO crystalPlanClimbSectionResultDTO) {
        validObject(crystalPlanClimbSectionResultDTO, ValidGroups.Update.class);
        crystalPlanClimbSectionResultService.saveOrUpdate(crystalPlanClimbSectionResultDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶产能需求结果表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalPlanClimbSectionResultService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶产能需求结果表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalPlanClimbSectionResultQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalPlanClimbSectionResultService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶产能需求结果表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalPlanClimbSectionResultService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "爬坡规则拆分时间")
    @PostMapping("/splitDate")
    public ResponseEntity<Results<Object>> splitDate(@RequestBody CrystalPlanClimbSectionResultQuery query) {
        try {
            hearthClimbRuleService.splitDate(query.getModelType());
        }catch (Exception e){
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
        return Results.createAsprovaSuccessRes(true);
    }

    @PostMapping("/count")
    @ApiOperation(value = "生成爬坡产能需求结果表")
    public ResponseEntity<Results<Object>> count(@RequestBody CrystalPlanClimbSectionResultQuery query) {
        try{
            crystalPlanClimbSectionResultService.caculateResult(query.getModelType());
        }catch (Exception e){
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
        return Results.createAsprovaSuccessRes(true);
    }

    @PostMapping("/countResult")
    @ApiOperation(value = "生成爬坡产能需求结果表")
    public ResponseEntity<Results<Object>> countResult(@RequestBody CrystalPlanClimbSectionResultQuery query) {
        try{
            hearthClimbRuleService.splitDate(query.getModelType());
            crystalCaculateMachineService.caculateMachine(query.getModelType());
            String msg = crystalPlanClimbSectionResultService.caculateResult(query.getModelType());
            return Results.createAsprovaSuccessRes(msg);
        }catch (Exception e){
            log.info("countResult.error:{}", e.getMessage(), e);
            String msg = e.getMessage();
            if (StringUtils.isBlank(msg)) {
                StringBuilder builder = new StringBuilder();
                builder.append(e.getClass().getName()).append(": ").append(e.getMessage());
                for (StackTraceElement ele : e.getStackTrace()) {
                    builder.append(String.format("  %s.%s(%s:%s) ", ele.getClassName(), ele.getMethodName(), ele.getFileName(), ele.getLineNumber()));
                }
                msg = builder.toString();
            }
            return Results.createAsprovaFailRes(msg, "A0001");
        }
    }
}