package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalPlanYearCrucibleRateDTO;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.query.CrystalPlanYearCrucibleRateQuery;
import com.jinkosolar.scp.mps.service.CrystalPlanYearCrucibleRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶年度预算坩埚比例相关操作控制层
 * 
 * <AUTHOR> 2024-10-28 10:14:23
 */
@RequestMapping(value = "/crystal-plan-year-crucible-rate")
@RestController
@Api(value = "crystalPalnYearCrucibleRate", tags = "拉晶年度预算坩埚比例相关操作控制层")
@RequiredArgsConstructor  
public class CrystalPlanYearCrucibleRateController extends BaseController {
    private final CrystalPlanYearCrucibleRateService crystalPlanYearCrucibleRateService;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶年度预算坩埚比例分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody CrystalPlanYearCrucibleRateQuery query) {
        return Results.createSuccessRes(crystalPlanYearCrucibleRateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶年度预算坩埚比例详情")
    public ResponseEntity<Results<CrystalPlanYearCrucibleRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalPlanYearCrucibleRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增拉晶年度预算坩埚比例")
    public ResponseEntity<Results<Void>> save(@RequestBody CrystalPlanYearCrucibleRateDTO crystalPlanYearCrucibleRateDTO) {
        validObject(crystalPlanYearCrucibleRateDTO, ValidGroups.Insert.class);
        crystalPlanYearCrucibleRateService.saveOrUpdate(crystalPlanYearCrucibleRateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶年度预算坩埚比例")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalPlanYearCrucibleRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶年度预算坩埚比例")
    @PostMapping("/export")
    public void export(@RequestBody CrystalPlanYearCrucibleRateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalPlanYearCrucibleRateService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶年度预算坩埚比例")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalPlanYearCrucibleRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "版本号下拉列表")
    public ResponseEntity<Results<List<String>>> versionList(@RequestBody CrystalPlanYearCrucibleRateQuery query) {
        return Results.createSuccessRes(crystalPlanYearCrucibleRateService.versionList(query));
    }
}