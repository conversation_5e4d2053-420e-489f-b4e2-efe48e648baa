package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalProductFineTuningDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalProductFineTuningQuery;
import com.jinkosolar.scp.mps.service.CrystalProductFineTuningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶单产微调相关操作控制层
 * 
 * <AUTHOR> 2024-09-11 19:28:28
 */
@RequestMapping(value = "/crystal-product-fine-tuning")
@RestController
@Api(value = "crystalProductFineTuning", tags = "拉晶单产微调相关操作控制层")
@RequiredArgsConstructor  
public class CrystalProductFineTuningController extends BaseController {    
    private final CrystalProductFineTuningService crystalProductFineTuningService; 

    @PostMapping("/page")
    @ApiOperation(value = "拉晶单产微调分页查询")
    public ResponseEntity<Results<Page<CrystalProductFineTuningDTO>>> page(@RequestBody CrystalProductFineTuningQuery query) {
        return Results.createSuccessRes(crystalProductFineTuningService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶单产微调详情")
    public ResponseEntity<Results<CrystalProductFineTuningDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalProductFineTuningService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶单产微调")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalProductFineTuningDTO crystalProductFineTuningDTO) {
        validObject(crystalProductFineTuningDTO, ValidGroups.Insert.class);
        crystalProductFineTuningService.saveOrUpdate(crystalProductFineTuningDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶单产微调")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalProductFineTuningDTO crystalProductFineTuningDTO) {
        validObject(crystalProductFineTuningDTO, ValidGroups.Update.class);
        crystalProductFineTuningService.saveOrUpdate(crystalProductFineTuningDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶单产微调")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalProductFineTuningService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶单产微调")
    @PostMapping("/export")
    public void export(@RequestBody CrystalProductFineTuningQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalProductFineTuningService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶单产微调")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalProductFineTuningService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versionNumber")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalProductFineTuningService.queryVersions(dataType.getId()));
    }
}