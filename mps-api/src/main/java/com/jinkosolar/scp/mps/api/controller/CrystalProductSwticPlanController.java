package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalProductSwticPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalProductSwticPlanQuery;
import com.jinkosolar.scp.mps.service.CrystalProductSwticPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-09-09 16:12:38
 */
@RequestMapping(value = "/crystal-product-swtic-plan")
@RestController
@Api(value = "crystalProductSwticPlan", tags = "产品切换计划相关操作控制层")
@RequiredArgsConstructor  
public class CrystalProductSwticPlanController extends BaseController {    
    private final CrystalProductSwticPlanService crystalProductSwticPlanService; 

    @PostMapping("/page")
    @ApiOperation(value = "产品切换计划分页查询")
    public ResponseEntity<Results<Page<CrystalProductSwticPlanDTO>>> page(@RequestBody CrystalProductSwticPlanQuery query) {
        return Results.createSuccessRes(crystalProductSwticPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品切换计划详情")
    public ResponseEntity<Results<CrystalProductSwticPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalProductSwticPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品切换计划")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalProductSwticPlanDTO crystalProductSwticPlanDTO) {
        validObject(crystalProductSwticPlanDTO, ValidGroups.Insert.class);
        crystalProductSwticPlanService.saveOrUpdate(crystalProductSwticPlanDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品切换计划")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalProductSwticPlanDTO crystalProductSwticPlanDTO) {
        validObject(crystalProductSwticPlanDTO, ValidGroups.Update.class);
        crystalProductSwticPlanService.saveOrUpdate(crystalProductSwticPlanDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品切换计划")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalProductSwticPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品切换计划")
    @PostMapping("/export")
    public void export(@RequestBody CrystalProductSwticPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalProductSwticPlanService.export(query, response);
    }

    @ApiOperation(value = "导入产品切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = crystalProductSwticPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versionNumber")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(crystalProductSwticPlanService.queryVersions(dataType.getId()));
    }
}