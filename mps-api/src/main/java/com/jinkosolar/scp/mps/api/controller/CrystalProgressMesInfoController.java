package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CrystalProgressMesInfoDTO;
import com.jinkosolar.scp.mps.domain.dto.mes.CrystalProgressMesInfoRequestDTO;
import com.jinkosolar.scp.mps.domain.dto.mes.CrystalProgressMesInfoResponseDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalProgressMesInfoQuery;
import com.jinkosolar.scp.mps.domain.save.CrystalProgressMesInfoSaveDTO;
import com.jinkosolar.scp.mps.service.CrystalProgressMesInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * [说明]籽晶酸洗切方进度（mes接口返回） 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-11-11
 */
@RestController
// url不能加info, 会403
@RequestMapping(value ="/crystal-progress-mes")
@Api(value = "crystal-progress-mes-info", tags = "籽晶酸洗切方进度（mes接口返回）操作")
public class CrystalProgressMesInfoController extends BaseController {
    @Autowired
    private CrystalProgressMesInfoService crystalProgressMesInfoService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "籽晶酸洗切方进度（mes接口返回）分页列表", notes = "获得籽晶酸洗切方进度（mes接口返回）分页列表")
    public ResponseEntity<Results<Page<CrystalProgressMesInfoDTO>>> queryByPage(@RequestBody CrystalProgressMesInfoQuery query) {
        return Results.createSuccessRes(crystalProgressMesInfoService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CrystalProgressMesInfoDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(crystalProgressMesInfoService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CrystalProgressMesInfoDTO>> save(@Valid @RequestBody CrystalProgressMesInfoSaveDTO saveDTO) {
        return Results.createSuccessRes(crystalProgressMesInfoService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        crystalProgressMesInfoService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CrystalProgressMesInfoQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            crystalProgressMesInfoService.export(query, response);
    }

    @PostMapping("/mes/sync")
    @ApiOperation(value = "mes同步")
    @com.ibm.scp.common.api.annotation.NoAuthorization
    @com.ibm.dpf.gateway.annotation.NoAuthorization
    public CrystalProgressMesInfoResponseDTO mesSync(@RequestBody CrystalProgressMesInfoRequestDTO crystalProgressMesInfoRequestDTO) {
        CrystalProgressMesInfoResponseDTO responseDTO = crystalProgressMesInfoService.mesSync(crystalProgressMesInfoRequestDTO);
        responseDTO.getEtReturn().setStatus("S");
        responseDTO.getEtReturn().setMsg("success");
        return responseDTO;
    }
}
