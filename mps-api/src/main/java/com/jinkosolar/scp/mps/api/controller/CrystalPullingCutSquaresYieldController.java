package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.SyncTableUtils;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.CrystalActualYieldMesInfoReportDTO;
import com.jinkosolar.scp.mps.domain.dto.CrystalPullingCutSquaresYieldDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalActualYieldMesInfoQuery;
import com.jinkosolar.scp.mps.domain.query.CrystalPullingCutSquaresYieldQuery;
import com.jinkosolar.scp.mps.service.CrystalPullingCutSquaresYieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶切方良率表（数据来源于mes）相关操作控制层
 *
 * <AUTHOR> 2024-11-11 14:28:20
 */
@RequestMapping(value = "/crystal-pulling-cut-squares-yield")
@RestController
@Api(value = "crystalPullingCutSquaresYield", tags = "拉晶切方良率表（数据来源于mes）相关操作控制层")
@RequiredArgsConstructor
public class CrystalPullingCutSquaresYieldController extends BaseController {
    private final CrystalPullingCutSquaresYieldService crystalPullingCutSquaresYieldService;

    @Autowired
    private SyncTableUtils syncTableUtils;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶切方良率表（数据来源于mes）分页查询")
    public ResponseEntity<Results<Page<CrystalPullingCutSquaresYieldDTO>>> page(@RequestBody CrystalPullingCutSquaresYieldQuery query) {
        return Results.createSuccessRes(crystalPullingCutSquaresYieldService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶切方良率表（数据来源于mes）详情")
    public ResponseEntity<Results<CrystalPullingCutSquaresYieldDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalPullingCutSquaresYieldService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶切方良率表（数据来源于mes）")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalPullingCutSquaresYieldDTO crystalPullingCutSquaresYieldDTO) {
        validObject(crystalPullingCutSquaresYieldDTO, ValidGroups.Insert.class);
        crystalPullingCutSquaresYieldService.saveOrUpdate(crystalPullingCutSquaresYieldDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶切方良率表（数据来源于mes）")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalPullingCutSquaresYieldDTO crystalPullingCutSquaresYieldDTO) {
        validObject(crystalPullingCutSquaresYieldDTO, ValidGroups.Update.class);
        crystalPullingCutSquaresYieldService.saveOrUpdate(crystalPullingCutSquaresYieldDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶切方良率表（数据来源于mes）")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalPullingCutSquaresYieldService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }



    @ApiOperation(value = "导入拉晶切方良率表（数据来源于mes）")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalPullingCutSquaresYieldService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }



    @PostMapping("/syncCrystalPullingCutSquaresYield")
    @ApiOperation(value = "国内MES接口同步切方良率接口")
    public JipResponseData syncCrystalPullingCutSquaresYield() {
        crystalPullingCutSquaresYieldService.syncCrystalPullingCutSquaresYield();
        //切方良率同步到APS
        syncCrystalPullingCutSquaresYieldToAPS();
        return JipResponseData.success();
    }

    @PostMapping("/list")
    @ApiOperation(value = "MES实际良率报表")
    public ResponseEntity<Results<List<CrystalActualYieldMesInfoReportDTO>>> getList(@RequestBody CrystalPullingCutSquaresYieldQuery query) {
        return Results.createSuccessRes(crystalPullingCutSquaresYieldService.getList(query));
    }

    @ApiOperation(value = "导出拉晶切方良率表（数据来源于mes）")
    @PostMapping("/export")
    public void export(@RequestBody CrystalPullingCutSquaresYieldQuery query, HttpServletResponse response) {
        crystalPullingCutSquaresYieldService.export(query, response);
    }

    public void syncCrystalPullingCutSquaresYieldToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> tables = new ArrayList<>();
        tables.add("mps_crystal_pulling_cut_squares_yield");
        syncTableDTO.setLovCodes(tables);
        syncTableUtils.syncTables(syncTableDTO);
    }



}