package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalSectionOrderDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalSectionOrderQuery;
import com.jinkosolar.scp.mps.service.CrystalSectionOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶需求表相关操作控制层
 * 
 * <AUTHOR> 2024-08-23 09:15:04
 */
@RequestMapping(value = "/crystal-section-order")
@RestController
@Api(value = "crystalSectionOrder", tags = "拉晶需求表相关操作控制层")
@RequiredArgsConstructor  
public class CrystalSectionOrderController extends BaseController {    
    private final CrystalSectionOrderService crystalSectionOrderService; 

    @PostMapping("/page")
    @ApiOperation(value = "拉晶需求表分页查询")
    public ResponseEntity<Results<Page<CrystalSectionOrderDTO>>> page(@RequestBody CrystalSectionOrderQuery query) {
        return Results.createSuccessRes(crystalSectionOrderService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶需求表详情")
    public ResponseEntity<Results<CrystalSectionOrderDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalSectionOrderService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶需求表")
    public ResponseEntity<Results<Void>> insert(@RequestBody CrystalSectionOrderDTO crystalSectionOrderDTO) {
        validObject(crystalSectionOrderDTO, ValidGroups.Insert.class);
        crystalSectionOrderService.saveOrUpdate(crystalSectionOrderDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶需求表")
    public ResponseEntity<Results<Void>> update(@RequestBody CrystalSectionOrderDTO crystalSectionOrderDTO) {
        validObject(crystalSectionOrderDTO, ValidGroups.Update.class);
        crystalSectionOrderService.saveOrUpdate(crystalSectionOrderDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶需求表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalSectionOrderService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶需求表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalSectionOrderQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalSectionOrderService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶需求表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalSectionOrderService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}