package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelUtils;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalSiliconSupplyDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalSiliconSupplyQuery;
import com.jinkosolar.scp.mps.service.CrystalSiliconSupplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶硅料供应相关操作控制层
 *
 * <AUTHOR> 2024-04-19 10:39:46
 */
@RequestMapping(value = "/crystal-silicon-supply")
@RestController
@Api(value = "crystalSiliconSupply", tags = "拉晶硅料供应相关操作控制层")
public class CrystalSiliconSupplyController extends BaseController {

    @Autowired
    private CrystalSiliconSupplyService crystalSiliconSupplyService;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶硅料供应分页查询")
    public ResponseEntity<Results<Page<CrystalSiliconSupplyDTO>>> page(@RequestBody CrystalSiliconSupplyQuery query) {
        return Results.createSuccessRes(crystalSiliconSupplyService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶硅料供应详情")
    public ResponseEntity<Results<CrystalSiliconSupplyDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalSiliconSupplyService.getById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶硅料供应")
    public ResponseEntity<Results<CrystalSiliconSupplyDTO>> insert(@RequestBody CrystalSiliconSupplyDTO crystalSiliconSupplyDTO) {
        validObject(crystalSiliconSupplyDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalSiliconSupplyService.saveOrUpdate(crystalSiliconSupplyDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶硅料供应")
    public ResponseEntity<Results<CrystalSiliconSupplyDTO>> update(@RequestBody CrystalSiliconSupplyDTO crystalSiliconSupplyDTO) {
        validObject(crystalSiliconSupplyDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalSiliconSupplyService.saveOrUpdate(crystalSiliconSupplyDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶硅料供应")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalSiliconSupplyService.deleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶硅料供应")
    @PostMapping("/export")
    public void export(@RequestBody CrystalSiliconSupplyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<CrystalSiliconSupplyDTO> list = crystalSiliconSupplyService.page(query).getContent();
        List<List<Object>> excelData = ExcelUtils.getList(list, query.getExcelPara(), CrystalSiliconSupplyDTO::getDynamicColumnMap);
        String fileName = getMessage("mps.mps-crystal-silicon-supply.export-name");
        ExcelUtils.exportEx(response, fileName, fileName, query.getExcelPara().getSimpleHeader(), excelData);
    }

    @ApiOperation(value = "导入拉晶硅料供应")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultExDTO>> importData(@RequestParam("file") MultipartFile multipartFile) {
        ImportResultExDTO importResultDTO = crystalSiliconSupplyService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "导入拉晶硅料供应")
    @PostMapping("/getVersionNumber")
    public ResponseEntity<Results<List<String>>> getVersionNumber(@RequestBody CrystalSiliconSupplyQuery query) {
        return Results.createSuccessRes(crystalSiliconSupplyService.getVersionNumber(query));
    }

}