package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalSpecialDeductionDetailDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalSpecialDeductionDetailQuery;
import com.jinkosolar.scp.mps.service.CrystalSpecialDeductionDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶特殊扣减明细表相关操作控制层
 * 
 * <AUTHOR> 2024-07-24 10:46:08
 */
@RequestMapping(value = "/crystal-special-deduction-detail")
@RestController
@Api(value = "crystalSpecialDeductionDetail", tags = "拉晶特殊扣减明细表相关操作控制层")
public class CrystalSpecialDeductionDetailController extends BaseController {    
    private final CrystalSpecialDeductionDetailService crystalSpecialDeductionDetailService;

    public CrystalSpecialDeductionDetailController(CrystalSpecialDeductionDetailService crystalSpecialDeductionDetailService) {
        this.crystalSpecialDeductionDetailService = crystalSpecialDeductionDetailService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶特殊扣减明细表分页查询")
    public ResponseEntity<Results<Page<CrystalSpecialDeductionDetailDTO>>> page(@RequestBody CrystalSpecialDeductionDetailQuery query) {
        return Results.createSuccessRes(crystalSpecialDeductionDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶特殊扣减明细表详情")
    public ResponseEntity<Results<CrystalSpecialDeductionDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalSpecialDeductionDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶特殊扣减明细表")
    public ResponseEntity<Results<CrystalSpecialDeductionDetailDTO>> insert(@RequestBody CrystalSpecialDeductionDetailDTO crystalSpecialDeductionDetailDTO) {
        validObject(crystalSpecialDeductionDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalSpecialDeductionDetailService.saveOrUpdate(crystalSpecialDeductionDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶特殊扣减明细表")
    public ResponseEntity<Results<CrystalSpecialDeductionDetailDTO>> update(@RequestBody CrystalSpecialDeductionDetailDTO crystalSpecialDeductionDetailDTO) {
        validObject(crystalSpecialDeductionDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalSpecialDeductionDetailService.saveOrUpdate(crystalSpecialDeductionDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶特殊扣减明细表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalSpecialDeductionDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶特殊扣减明细表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalSpecialDeductionDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalSpecialDeductionDetailService.export(query, response);
    }

    @ApiOperation(value = "查询拉晶特殊扣减版本集合")
    @PostMapping("/queryMainVersionList")
    public ResponseEntity<Results<List<String>>> queryMainVersionList(@RequestBody CrystalSpecialDeductionDetailQuery query) {
        return Results.createSuccessRes(crystalSpecialDeductionDetailService.queryMainVersionList(query));
    }


    @ApiOperation(value = "导入拉晶特殊扣减明细表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara, @RequestPart("deductionType") String deductionType) {
        ImportResultExDTO importResultDTO = crystalSpecialDeductionDetailService.importData(multipartFile, excelPara, deductionType);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}