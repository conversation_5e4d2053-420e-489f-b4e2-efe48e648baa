package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalSpecialDeductionMainDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalSpecialDeductionMainQuery;
import com.jinkosolar.scp.mps.service.CrystalSpecialDeductionMainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶特殊扣减主表相关操作控制层
 * 
 * <AUTHOR> 2024-07-24 10:46:08
 */
@RequestMapping(value = "/crystal-special-deduction-main")
@RestController
@Api(value = "crystalSpecialDeductionMain", tags = "拉晶特殊扣减主表相关操作控制层")
public class CrystalSpecialDeductionMainController extends BaseController {    
    private final CrystalSpecialDeductionMainService crystalSpecialDeductionMainService;

    public CrystalSpecialDeductionMainController(CrystalSpecialDeductionMainService crystalSpecialDeductionMainService) {
        this.crystalSpecialDeductionMainService = crystalSpecialDeductionMainService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶特殊扣减主表分页查询")
    public ResponseEntity<Results<Page<CrystalSpecialDeductionMainDTO>>> page(@RequestBody CrystalSpecialDeductionMainQuery query) {
        return Results.createSuccessRes(crystalSpecialDeductionMainService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶特殊扣减主表详情")
    public ResponseEntity<Results<CrystalSpecialDeductionMainDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalSpecialDeductionMainService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶特殊扣减主表")
    public ResponseEntity<Results<CrystalSpecialDeductionMainDTO>> insert(@RequestBody CrystalSpecialDeductionMainDTO crystalSpecialDeductionMainDTO) {
        validObject(crystalSpecialDeductionMainDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalSpecialDeductionMainService.saveOrUpdate(crystalSpecialDeductionMainDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶特殊扣减主表")
    public ResponseEntity<Results<CrystalSpecialDeductionMainDTO>> update(@RequestBody CrystalSpecialDeductionMainDTO crystalSpecialDeductionMainDTO) {
        validObject(crystalSpecialDeductionMainDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalSpecialDeductionMainService.saveOrUpdate(crystalSpecialDeductionMainDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶特殊扣减主表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalSpecialDeductionMainService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶特殊扣减主表")
    @PostMapping("/export")
    public void export(@RequestBody CrystalSpecialDeductionMainQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalSpecialDeductionMainService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶特殊扣减主表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalSpecialDeductionMainService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}