package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalStandardDosageDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalStandardDosageQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.CrystalStandardDosageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 籽晶标准用量相关操作控制层
 * 
 * <AUTHOR> 2024-07-03 16:06:41
 */
@RequestMapping(value = "/crystal-standard-dosage")
@RestController
@Api(value = "crystalStandardDosage", tags = "籽晶标准用量相关操作控制层")
public class CrystalStandardDosageController extends BaseController {
    @Autowired
    private CrystalStandardDosageService crystalStandardDosageService;

    @PostMapping("/page")
    @ApiOperation(value = "籽晶标准用量分页查询")
    public ResponseEntity<Results<Page<CrystalStandardDosageDTO>>> page(@RequestBody CrystalStandardDosageQuery query) {
        return Results.createSuccessRes(crystalStandardDosageService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "籽晶标准用量详情")
    public ResponseEntity<Results<CrystalStandardDosageDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalStandardDosageService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增籽晶标准用量")
    public ResponseEntity<Results<CrystalStandardDosageDTO>> insert(@RequestBody CrystalStandardDosageDTO crystalStandardDosageDTO) {
        validObject(crystalStandardDosageDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalStandardDosageService.saveOrUpdate(crystalStandardDosageDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新籽晶标准用量")
    public ResponseEntity<Results<CrystalStandardDosageDTO>> update(@RequestBody CrystalStandardDosageDTO crystalStandardDosageDTO) {
        validObject(crystalStandardDosageDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalStandardDosageService.saveOrUpdate(crystalStandardDosageDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除籽晶标准用量")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalStandardDosageService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出籽晶标准用量")
    @PostMapping("/export")
    public void export(@RequestBody CrystalStandardDosageQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalStandardDosageService.export(query, response);
    }

    @ApiOperation(value = "导入籽晶标准用量")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = crystalStandardDosageService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(crystalStandardDosageService.versions(versionQuery));
    }
}