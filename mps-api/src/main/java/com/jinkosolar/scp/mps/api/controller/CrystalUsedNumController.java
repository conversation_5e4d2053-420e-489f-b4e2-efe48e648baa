package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CrystalUsedNumDTO;
import com.jinkosolar.scp.mps.domain.query.CrystalUsedNumQuery;
import com.jinkosolar.scp.mps.service.CrystalUsedNumService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 籽晶标准用量相关操作控制层
 *
 * <AUTHOR> 2024-07-18 18:29:05
 */
@RequestMapping(value = "/crystal-used-num")
@RestController
@Api(value = "crystalUsedNum", tags = "籽晶标准用量相关操作控制层")
public class CrystalUsedNumController extends BaseController {
    @Autowired
    private CrystalUsedNumService crystalUsedNumService;

    @PostMapping("/page")
    @ApiOperation(value = "籽晶标准用量分页查询")
    public ResponseEntity<Results<Page<CrystalUsedNumDTO>>> page(@RequestBody CrystalUsedNumQuery query) {
        return Results.createSuccessRes(crystalUsedNumService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "籽晶标准用量详情")
    public ResponseEntity<Results<CrystalUsedNumDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(crystalUsedNumService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增籽晶标准用量")
    public ResponseEntity<Results<CrystalUsedNumDTO>> insert(@RequestBody CrystalUsedNumDTO crystalUsedNumDTO) {
        validObject(crystalUsedNumDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(crystalUsedNumService.saveOrUpdate(crystalUsedNumDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新籽晶标准用量")
    public ResponseEntity<Results<CrystalUsedNumDTO>> update(@RequestBody CrystalUsedNumDTO crystalUsedNumDTO) {
        validObject(crystalUsedNumDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(crystalUsedNumService.saveOrUpdate(crystalUsedNumDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除籽晶标准用量")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        crystalUsedNumService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出籽晶标准用量")
    @PostMapping("/export")
    public void export(@RequestBody CrystalUsedNumQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        crystalUsedNumService.export(query, response);
    }

    @ApiOperation(value = "导入籽晶标准用量")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultDTO importResultDTO = crystalUsedNumService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号-三个数据分类汇总")
    public ResponseEntity<Results<List<String>>> queryVersions() {
        return Results.createSuccessRes(crystalUsedNumService.queryVersions());
    }

    @PostMapping("/type-versions")
    @ApiOperation(value = "查询版本号-三个数据分类对象")
    public ResponseEntity<Results<Map<Long,List<String>>>> queryDateTypeVersions() {
        return Results.createSuccessRes(crystalUsedNumService.queryDateTypeVersions());
    }
}
