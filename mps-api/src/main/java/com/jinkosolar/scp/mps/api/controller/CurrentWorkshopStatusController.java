package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncCurrentWorkshopStatusRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncCurrentWorkshopStatusResponse;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusResponse;
import com.jinkosolar.scp.jip.api.service.SyncCurrentWorkshopStatusService;
import com.jinkosolar.scp.mps.domain.dto.CurrentWorkshopStatusDTO;
import com.jinkosolar.scp.mps.domain.query.CurrentWorkshopStatusQuery;
import com.jinkosolar.scp.mps.service.CurrentWorkshopStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 切片车间当前状态相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 15:26:38
 */
@RequestMapping(value = "/current-workshop-status")
@RestController
@Api(value = "currentWorkshopStatus", tags = "切片车间当前状态相关操作控制层")
public class CurrentWorkshopStatusController extends BaseController {
    @Autowired
    private CurrentWorkshopStatusService currentWorkshopStatusService;
    @Autowired
    private SyncCurrentWorkshopStatusService syncCurrentWorkshopStatusService;

    @PostMapping("/page")
    @ApiOperation(value = "切片车间当前状态分页查询")
    public ResponseEntity<Results<Page<CurrentWorkshopStatusDTO>>> page(@RequestBody CurrentWorkshopStatusQuery query) {
        return Results.createSuccessRes(currentWorkshopStatusService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "切片车间当前状态详情")
    public ResponseEntity<Results<CurrentWorkshopStatusDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(currentWorkshopStatusService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增切片车间当前状态")
    public ResponseEntity<Results<CurrentWorkshopStatusDTO>> insert(@RequestBody CurrentWorkshopStatusDTO currentWorkshopStatusDTO) {
        validObject(currentWorkshopStatusDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(currentWorkshopStatusService.saveOrUpdate(currentWorkshopStatusDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新切片车间当前状态")
    public ResponseEntity<Results<CurrentWorkshopStatusDTO>> update(@RequestBody CurrentWorkshopStatusDTO currentWorkshopStatusDTO) {
        validObject(currentWorkshopStatusDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(currentWorkshopStatusService.saveOrUpdate(currentWorkshopStatusDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除切片车间当前状态")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        currentWorkshopStatusService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出切片车间当前状态")
    @PostMapping("/export")
    public void export(@RequestBody CurrentWorkshopStatusQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        currentWorkshopStatusService.export(query, response);
    }

    @ApiOperation(value = "导入切片车间当前状态")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = currentWorkshopStatusService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncCurrentWorkshopStatus")
    @ApiOperation(value = "切片车间当前状态同步")
    public JipResponseData syncCurrentWorkshopStatus() {
        currentWorkshopStatusService.sync();
        return JipResponseData.success();
    }
}