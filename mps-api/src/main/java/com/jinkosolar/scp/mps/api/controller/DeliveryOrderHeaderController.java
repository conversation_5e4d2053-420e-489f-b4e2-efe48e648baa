package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DeliveryOrderHeaderDTO;
import com.jinkosolar.scp.mps.domain.query.DeliveryOrderHeaderQuery;
import com.jinkosolar.scp.mps.service.DeliveryOrderHeaderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ERP交货单数据头表相关操作控制层
 * 
 * <AUTHOR> 2024-11-01 10:32:40
 */
@RequestMapping(value = "/delivery-order-header")
@RestController
@Api(value = "deliveryOrderHeader", tags = "ERP交货单数据头表相关操作控制层")
@RequiredArgsConstructor  
public class DeliveryOrderHeaderController extends BaseController {    
    private final DeliveryOrderHeaderService deliveryOrderHeaderService; 

    @PostMapping("/page")
    @ApiOperation(value = "ERP交货单数据头表分页查询")
    public ResponseEntity<Results<Page<DeliveryOrderHeaderDTO>>> page(@RequestBody DeliveryOrderHeaderQuery query) {
        return Results.createSuccessRes(deliveryOrderHeaderService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "ERP交货单数据头表详情")
    public ResponseEntity<Results<DeliveryOrderHeaderDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(deliveryOrderHeaderService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增ERP交货单数据头表")
    public ResponseEntity<Results<Void>> insert(@RequestBody DeliveryOrderHeaderDTO deliveryOrderHeaderDTO) {
        validObject(deliveryOrderHeaderDTO, ValidGroups.Insert.class);
        deliveryOrderHeaderService.saveOrUpdate(deliveryOrderHeaderDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ERP交货单数据头表")
    public ResponseEntity<Results<Void>> update(@RequestBody DeliveryOrderHeaderDTO deliveryOrderHeaderDTO) {
        validObject(deliveryOrderHeaderDTO, ValidGroups.Update.class);
        deliveryOrderHeaderService.saveOrUpdate(deliveryOrderHeaderDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除ERP交货单数据头表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        deliveryOrderHeaderService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出ERP交货单数据头表")
    @PostMapping("/export")
    public void export(@RequestBody DeliveryOrderHeaderQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        deliveryOrderHeaderService.export(query, response);
    }

    @ApiOperation(value = "导入ERP交货单数据头表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = deliveryOrderHeaderService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "erp交货单数据查询")
    @PostMapping("/syncDeliveryOrder")
    public void syncDeliveryOrder() {
        deliveryOrderHeaderService.syncDeliveryOrder();
    }
}