package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DeliveryOrderLineDTO;
import com.jinkosolar.scp.mps.domain.query.DeliveryOrderLineQuery;
import com.jinkosolar.scp.mps.service.DeliveryOrderLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ERP交货单数据行表相关操作控制层
 * 
 * <AUTHOR> 2024-11-01 10:32:58
 */
@RequestMapping(value = "/delivery-order-line")
@RestController
@Api(value = "deliveryOrderLine", tags = "ERP交货单数据行表相关操作控制层")
@RequiredArgsConstructor  
public class DeliveryOrderLineController extends BaseController {    
    private final DeliveryOrderLineService deliveryOrderLineService; 

    @PostMapping("/page")
    @ApiOperation(value = "ERP交货单数据行表分页查询")
    public ResponseEntity<Results<Page<DeliveryOrderLineDTO>>> page(@RequestBody DeliveryOrderLineQuery query) {
        return Results.createSuccessRes(deliveryOrderLineService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "ERP交货单数据行表详情")
    public ResponseEntity<Results<DeliveryOrderLineDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(deliveryOrderLineService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增ERP交货单数据行表")
    public ResponseEntity<Results<Void>> insert(@RequestBody DeliveryOrderLineDTO deliveryOrderLineDTO) {
        validObject(deliveryOrderLineDTO, ValidGroups.Insert.class);
        deliveryOrderLineService.saveOrUpdate(deliveryOrderLineDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ERP交货单数据行表")
    public ResponseEntity<Results<Void>> update(@RequestBody DeliveryOrderLineDTO deliveryOrderLineDTO) {
        validObject(deliveryOrderLineDTO, ValidGroups.Update.class);
        deliveryOrderLineService.saveOrUpdate(deliveryOrderLineDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除ERP交货单数据行表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        deliveryOrderLineService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出ERP交货单数据行表")
    @PostMapping("/export")
    public void export(@RequestBody DeliveryOrderLineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        deliveryOrderLineService.export(query, response);
    }

    @ApiOperation(value = "导入ERP交货单数据行表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = deliveryOrderLineService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}