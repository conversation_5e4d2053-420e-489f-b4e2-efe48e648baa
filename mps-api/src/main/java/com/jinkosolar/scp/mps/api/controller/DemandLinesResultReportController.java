package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DemandLinesResultReportDTO;
import com.jinkosolar.scp.mps.domain.query.DemandLinesResultReportQuery;
import com.jinkosolar.scp.mps.service.DemandLinesResultReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件生产计划兜底结果表
相关操作控制层
 * 
 * <AUTHOR> 2024-08-29 20:47:44
 */
@RequestMapping(value = "/demand-lines-result-report")
@RestController
@Api(value = "demandLinesResultReport", tags = "组件生产计划兜底结果表相关操作控制层")
@RequiredArgsConstructor  
public class DemandLinesResultReportController extends BaseController {    
    private final DemandLinesResultReportService demandLinesResultReportService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件生产计划兜底结果表分页查询")
    public ResponseEntity<Results<Page<DemandLinesResultReportDTO>>> page(@RequestBody DemandLinesResultReportQuery query) {
        return Results.createSuccessRes(demandLinesResultReportService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件生产计划兜底结果表 详情")
    public ResponseEntity<Results<DemandLinesResultReportDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(demandLinesResultReportService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件生产计划兜底结果表")
    public ResponseEntity<Results<Void>> insert(@RequestBody DemandLinesResultReportDTO demandLinesResultReportDTO) {
        validObject(demandLinesResultReportDTO, ValidGroups.Insert.class);
        demandLinesResultReportService.saveOrUpdate(demandLinesResultReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件生产计划兜底结果表")
    public ResponseEntity<Results<Void>> update(@RequestBody DemandLinesResultReportDTO demandLinesResultReportDTO) {
        validObject(demandLinesResultReportDTO, ValidGroups.Update.class);
        demandLinesResultReportService.saveOrUpdate(demandLinesResultReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件生产计划兜底结果表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        demandLinesResultReportService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件生产计划兜底结果表")
    @PostMapping("/export")
    public void export(@RequestBody DemandLinesResultReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        demandLinesResultReportService.export(query, response);
    }

    @ApiOperation(value = "导入组件生产计划兜底结果表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = demandLinesResultReportService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}