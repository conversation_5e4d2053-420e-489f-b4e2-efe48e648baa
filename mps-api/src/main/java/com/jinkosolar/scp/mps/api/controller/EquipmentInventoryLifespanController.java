package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.sap.ProductionSuggestionRequest;
import com.jinkosolar.scp.mps.domain.dto.EquipmentInventoryLifespanDTO;
import com.jinkosolar.scp.mps.domain.dto.SyncEquipmentInventoryLifespanDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalProductSwticPlan;
import com.jinkosolar.scp.mps.domain.query.EquipmentInventoryLifespanQuery;
import com.jinkosolar.scp.mps.service.EquipmentInventoryLifespanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.prefs.AbstractPreferences;
import java.util.stream.Collectors;

/**
 * 炉台坩埚寿命表相关操作控制层
 * 
 * <AUTHOR> 2024-07-03 13:47:45
 */
@RequestMapping(value = "/equipment-inventory-lifespan")
@RestController
@Api(value = "equipmentInventoryLifespan", tags = "炉台坩埚寿命表相关操作控制层")
public class EquipmentInventoryLifespanController extends BaseController {
    @Autowired
    private EquipmentInventoryLifespanService equipmentInventoryLifespanService;

    @PostMapping("/page")
    @ApiOperation(value = "炉台坩埚寿命表分页查询")
    public ResponseEntity<Results<Page<EquipmentInventoryLifespanDTO>>> page(@RequestBody EquipmentInventoryLifespanQuery query) {
        return Results.createSuccessRes(equipmentInventoryLifespanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "炉台坩埚寿命表详情")
    public ResponseEntity<Results<EquipmentInventoryLifespanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(equipmentInventoryLifespanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增炉台坩埚寿命表")
    public ResponseEntity<Results<EquipmentInventoryLifespanDTO>> insert(@RequestBody EquipmentInventoryLifespanDTO equipmentInventoryLifespanDTO) {
        validObject(equipmentInventoryLifespanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(equipmentInventoryLifespanService.saveOrUpdate(equipmentInventoryLifespanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新炉台坩埚寿命表")
    public ResponseEntity<Results<EquipmentInventoryLifespanDTO>> update(@RequestBody EquipmentInventoryLifespanDTO equipmentInventoryLifespanDTO) {
        validObject(equipmentInventoryLifespanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(equipmentInventoryLifespanService.saveOrUpdate(equipmentInventoryLifespanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除炉台坩埚寿命表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        equipmentInventoryLifespanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出炉台坩埚寿命表")
    @PostMapping("/export")
    public void export(@RequestBody EquipmentInventoryLifespanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        equipmentInventoryLifespanService.export(query, response);
    }

    @ApiOperation(value = "导入炉台坩埚寿命表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = equipmentInventoryLifespanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @JipFeignLog
    @PostMapping("/syncEquipmentInventoryLifespan")
    @ApiOperation(value = "拉晶坩埚寿命接口")
    public JipResponseData syncEquipmentInventoryLifespan(@RequestBody SyncEquipmentInventoryLifespanDTO dto) {
        SyncEquipmentInventoryLifespanDTO.EquipmentInventoryLifespan_IT_DATA saveDTO = dto.getEquipmentInventoryLifespan_IT_DATA();
        equipmentInventoryLifespanService.sync(saveDTO);
        return JipResponseData.success();
    }

    @PostMapping("/getSendMesData")
    @ApiOperation(value = "拉晶坩埚寿命接口")
    public JipResponseData getSendMesData(@RequestBody EquipmentInventoryLifespanDTO dto) {
        equipmentInventoryLifespanService.findCrystalProductSwticPlan(dto.getId(),dto.getWorkCenterId(),dto.getProcessId(), LocalDate.now());
        return JipResponseData.success();
    }

    @PostMapping("/getSendMesData1")
    @ApiOperation(value = "拉晶坩埚寿命接口")
    public ResponseEntity<Results<Object>> getSendMesData1(@RequestBody EquipmentInventoryLifespanDTO dto) {
        List<CrystalProductSwticPlan> list = equipmentInventoryLifespanService.findCrystalProductSwticPlan(dto.getId(), dto.getWorkCenterId(), dto.getProcessId(), dto.getMesDate());
        return Results.createSuccessRes(list);
    }

    @PostMapping("/getSyncData")
    @ApiOperation(value = "拉晶坩埚寿命接口")
    public ResponseEntity<Results<Object>> getSyncData(@RequestBody SyncEquipmentInventoryLifespanDTO dto) {
        ProductionSuggestionRequest syncData = equipmentInventoryLifespanService.getSyncData(dto.getEquipmentInventoryLifespan_IT_DATA());
        return Results.createSuccessRes(syncData);
    }
}