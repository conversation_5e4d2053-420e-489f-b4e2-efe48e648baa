package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusResponse;
import com.jinkosolar.scp.jip.api.dto.mps.SyncSWTwoHourOutPutRecordRequest;
import com.jinkosolar.scp.jip.api.dto.mps.SyncSWTwoHourOutPutRecordResponse;
import com.jinkosolar.scp.jip.api.service.SyncEquipmentInventoryStatusService;
import com.jinkosolar.scp.mps.domain.dto.EquipmentInventoryStatusDTO;
import com.jinkosolar.scp.mps.domain.query.EquipmentInventoryStatusQuery;
import com.jinkosolar.scp.mps.service.EquipmentInventoryStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 炉台信息表相关操作控制层
 * 
 * <AUTHOR> 2024-07-02 16:21:08
 */
@RequestMapping(value = "/equipment-inventory-status")
@RestController
@Api(value = "equipmentInventoryStatus", tags = "炉台信息表相关操作控制层")
public class EquipmentInventoryStatusController extends BaseController {

    @Autowired
    private EquipmentInventoryStatusService equipmentInventoryStatusService;
    @Autowired
    private SyncEquipmentInventoryStatusService syncEquipmentInventoryStatusService;


    @PostMapping("/page")
    @ApiOperation(value = "炉台信息表分页查询")
    public ResponseEntity<Results<Page<EquipmentInventoryStatusDTO>>> page(@RequestBody EquipmentInventoryStatusQuery query) {
        return Results.createSuccessRes(equipmentInventoryStatusService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "炉台信息表详情")
    public ResponseEntity<Results<EquipmentInventoryStatusDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(equipmentInventoryStatusService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增炉台信息表")
    public ResponseEntity<Results<EquipmentInventoryStatusDTO>> insert(@RequestBody EquipmentInventoryStatusDTO equipmentInventoryStatusDTO) {
        validObject(equipmentInventoryStatusDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(equipmentInventoryStatusService.saveOrUpdate(equipmentInventoryStatusDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新炉台信息表")
    public ResponseEntity<Results<EquipmentInventoryStatusDTO>> update(@RequestBody EquipmentInventoryStatusDTO equipmentInventoryStatusDTO) {
        validObject(equipmentInventoryStatusDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(equipmentInventoryStatusService.saveOrUpdate(equipmentInventoryStatusDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除炉台信息表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        equipmentInventoryStatusService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出炉台信息表")
    @PostMapping("/export")
    public void export(@RequestBody EquipmentInventoryStatusQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        equipmentInventoryStatusService.export(query, response);
    }

    @ApiOperation(value = "导入炉台信息表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = equipmentInventoryStatusService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncEquipmentInventoryStatus")
    @ApiOperation(value = "拉晶炉台当前状态同步")
    public JipResponseData syncEquipmentInventoryStatus() {
        equipmentInventoryStatusService.sync();
        return JipResponseData.success();
    }
}