package com.jinkosolar.scp.mps.api.controller;

import com.ibm.dpf.gateway.annotation.Authorization;
import com.jinkosolar.scp.mps.domain.dto.ErpApprovalSupplierDTO;
import com.jinkosolar.scp.mps.domain.query.ErpApprovalSupplierQuery;
import com.jinkosolar.scp.mps.domain.save.ErpApprovalSupplierSaveDTO;
import com.jinkosolar.scp.mps.domain.save.ErpApprovalSupplierSavesDTO;
import com.jinkosolar.scp.mps.service.ErpApprovalSupplierService;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


/**
 * 批准供应商 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@RestController
@RequestMapping("/erp-approval-supplier")
@Api(value = "erp-approval-supplier", tags = "Erp批准供应商操作")
public class ErpApprovalSupplierController extends BaseController {
    @Autowired
    ErpApprovalSupplierService erpApprovalSupplierService;

    @Authorization
    @PostMapping("/page")
    @ApiOperation(value = "批准供应商分页列表", notes = "获得批准供应商分页列表")
    public ResponseEntity<Results<Page<ErpApprovalSupplierDTO>>> queryByPage(@RequestBody ErpApprovalSupplierQuery query) {
        return Results.createSuccessRes(erpApprovalSupplierService.queryByPage(query));
    }

    @Authorization
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ErpApprovalSupplierDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(erpApprovalSupplierService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Authorization
    @PostMapping("/save")
    @ApiOperation(value = "更新数据")
    public ResponseEntity<Results<ErpApprovalSupplierDTO>> save(@RequestBody ErpApprovalSupplierSaveDTO saveDTO) {
        return Results.createSuccessRes(erpApprovalSupplierService.save(saveDTO));
    }

    @Authorization
    @PostMapping("/saves")
    @ApiOperation(value = "批量更新数据")
    public ResponseEntity<Results<Object>> save(@RequestBody ErpApprovalSupplierSavesDTO saveDTO) {
        for (ErpApprovalSupplierSaveDTO data : saveDTO.getDatas()) {
            erpApprovalSupplierService.save(data);
        }
        return Results.createSuccessRes();
    }


    /**
     * 导出数据
     *
     * @param response query 条件查询
     */
    @Authorization
    @PostMapping("/export")
    @ApiOperation(value = "导出收集数据")
    public void export(HttpServletResponse response, @RequestBody ErpApprovalSupplierQuery query) throws IOException {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        erpApprovalSupplierService.export(query, response);

    }
}
