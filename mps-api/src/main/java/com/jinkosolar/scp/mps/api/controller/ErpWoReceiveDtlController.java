package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.ErpWoReceiveDtlDTO;
import com.jinkosolar.scp.mps.domain.query.ErpWoReceiveDtlQuery;
import com.jinkosolar.scp.mps.service.ErpWoReceiveDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 相关操作控制层
 * 
 * <AUTHOR> 2024-07-10 16:38:28
 */
@RequestMapping(value = "/erp-wo-receive-dtl")
@RestController
@Api(value = "erpWoReceiveDtl", tags = "相关操作控制层")
public class ErpWoReceiveDtlController extends BaseController {
    @Autowired
    private ErpWoReceiveDtlService erpWoReceiveDtlService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询")
    public ResponseEntity<Results<Page<ErpWoReceiveDtlDTO>>> page(@RequestBody ErpWoReceiveDtlQuery query) {
        return Results.createSuccessRes(erpWoReceiveDtlService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "详情")
    public ResponseEntity<Results<ErpWoReceiveDtlDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(erpWoReceiveDtlService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增")
    public ResponseEntity<Results<ErpWoReceiveDtlDTO>> insert(@RequestBody ErpWoReceiveDtlDTO erpWoReceiveDtlDTO) {
        validObject(erpWoReceiveDtlDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(erpWoReceiveDtlService.saveOrUpdate(erpWoReceiveDtlDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    public ResponseEntity<Results<ErpWoReceiveDtlDTO>> update(@RequestBody ErpWoReceiveDtlDTO erpWoReceiveDtlDTO) {
        validObject(erpWoReceiveDtlDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(erpWoReceiveDtlService.saveOrUpdate(erpWoReceiveDtlDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        erpWoReceiveDtlService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出")
    @PostMapping("/export")
    public void export(@RequestBody ErpWoReceiveDtlQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        erpWoReceiveDtlService.export(query, response);
    }

    @ApiOperation(value = "导入")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = erpWoReceiveDtlService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @PostMapping("/syncErpWoReceiverDtlController")
    @ApiOperation(value = "工厂采购入库记录表分页查询")
    public JipResponseData syncErpWoReceiverDtlController(@RequestParam(required = false,value = "date") String date) {
        erpWoReceiveDtlService.sync(date);
        return JipResponseData.success();
    }

}