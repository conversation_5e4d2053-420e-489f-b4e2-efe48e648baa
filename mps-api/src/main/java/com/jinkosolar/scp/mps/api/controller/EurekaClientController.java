package com.jinkosolar.scp.mps.api.controller;

import com.ibm.dpf.common.config.openapi.OpenApi;
import com.ibm.dpf.common.response.RestResponse;
import com.netflix.discovery.DiscoveryManager;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.Timer;
import java.util.TimerTask;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Api(tags = { "Eurekae服务注册" })
@RequestMapping(value = "/eureka")
@RestController
@Slf4j
public class EurekaClientController {

	@ApiOperation(value = "停止Eurekae服务注册")
	@OpenApi
	@RequestMapping(value = { "/stop" }, method = RequestMethod.GET)
	// @Authorization("msg:batch:query")
	public ResponseEntity<RestResponse> list() {
		try {
			log.info("停止Eurekae服务注册开始");
			DiscoveryManager.getInstance().shutdownComponent();
			
			Timer  timer = new Timer();
	        timer.schedule(new RemindTask(), 120 * 1000);
	        
			log.info("停止Eurekae服务注册结束");

			return RestResponse.createSuccessRes("ok");
		} catch (Exception e) {
			log.error("停止Eurekae服务注册", e.getMessage());
			return RestResponse.createFailRes("失败:" + e.getMessage());
		}
	}

	 class RemindTask extends TimerTask {
	        public void run() {
				Runtime.getRuntime().exit(0);
	        }
	    }
}
