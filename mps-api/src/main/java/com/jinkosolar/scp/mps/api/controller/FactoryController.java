package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.FactoryDTO;
import com.jinkosolar.scp.mps.domain.query.FactoryQuery;
import com.jinkosolar.scp.mps.service.FactoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作中心主表相关操作控制层
 *
 * <AUTHOR> 2024-05-20 09:14:44
 */
@RequestMapping(value = "/factory")
@RestController
@Api(value = "factory", tags = "工作中心主表相关操作控制层")
public class FactoryController extends BaseController {
    @Autowired
    private FactoryService factoryService;

    @PostMapping("/page")
    @ApiOperation(value = "工作中心主表分页查询")
    public ResponseEntity<Results<Page<FactoryDTO>>> page(@RequestBody FactoryQuery query) {
        return Results.createSuccessRes(factoryService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "工作中心主表详情")
    public ResponseEntity<Results<FactoryDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(factoryService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增工作中心主表")
    public ResponseEntity<Results<FactoryDTO>> insert(@RequestBody FactoryDTO factoryDTO) {
        validObject(factoryDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(factoryService.saveOrUpdate(factoryDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新工作中心主表")
    public ResponseEntity<Results<FactoryDTO>> update(@RequestBody FactoryDTO factoryDTO) {
        validObject(factoryDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(factoryService.saveOrUpdate(factoryDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除工作中心主表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        factoryService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出工作中心主表")
    @PostMapping("/export")
    public void export(@RequestBody FactoryQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        factoryService.export(query, response);
    }

    @ApiOperation(value = "导入工作中心主表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = factoryService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/sync")
    @ApiOperation(value = "同步接口外部调用")
    public ResponseEntity<Results<Object>> realtimeSync() {
        factoryService.sync();
        return Results.createSuccessRes();
    }
}
