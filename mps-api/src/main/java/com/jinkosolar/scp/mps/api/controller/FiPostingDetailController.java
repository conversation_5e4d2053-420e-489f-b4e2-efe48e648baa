package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.FiPostingDetailDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.FiPostingDetailSyncDTO;
import com.jinkosolar.scp.mps.domain.query.FiPostingDetailQuery;
import com.jinkosolar.scp.mps.service.FiPostingDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MES FI过账明细相关操作控制层
 * 
 * <AUTHOR> 2024-05-06 20:09:02
 */
@Slf4j
@RequestMapping(value = "/fi-posting-detail")
@RestController
@Api(value = "fiPostingDetail", tags = "MES FI过账明细相关操作控制层")
public class FiPostingDetailController extends BaseController {
    @Autowired
    private FiPostingDetailService fiPostingDetailService;

    /**
     * 接收List<xxx> 参数，须在请求头中添加 accesstoken ，详见{@link com.ibm.scp.common.api.interceptor.SessionInterceptor}
     *
     * @param dataList 数据列表
     * @return 同步结果
     */
    @JipFeignLog
    @PostMapping("/sync")
    @ApiOperation(value = "同步MES FI过账明细")
    public JipResponseData sync(@RequestBody List<FiPostingDetailSyncDTO> dataList) {
        try {
            dataList.forEach(item -> validObject(item, ValidGroups.Insert.class));
            fiPostingDetailService.sync(dataList);
        } catch (BizException e) {
            log.error("业务异常：FI过账明细数据同步失败", e);
            return JipResponseData.error(MessageHelper.getMessage(e.getI18nCode(), e.getParameters()).getDesc());
        } catch (Exception e) {
            log.error("FI过账明细数据同步失败", e);
            return JipResponseData.error(MessageHelper.getMessage("error.system").getDesc());
        }
        return JipResponseData.success();
    }

    @PostMapping("/page")
    @ApiOperation(value = "MES FI过账明细分页查询")
    public ResponseEntity<Results<Page<FiPostingDetailDTO>>> page(@RequestBody FiPostingDetailQuery query) {
        return Results.createSuccessRes(fiPostingDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "MES FI过账明细详情")
    public ResponseEntity<Results<FiPostingDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(fiPostingDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增MES FI过账明细")
    public ResponseEntity<Results<FiPostingDetailDTO>> insert(@RequestBody FiPostingDetailDTO fiPostingDetailDTO) {
        validObject(fiPostingDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(fiPostingDetailService.saveOrUpdate(fiPostingDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新MES FI过账明细")
    public ResponseEntity<Results<FiPostingDetailDTO>> update(@RequestBody FiPostingDetailDTO fiPostingDetailDTO) {
        validObject(fiPostingDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(fiPostingDetailService.saveOrUpdate(fiPostingDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除MES FI过账明细")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        fiPostingDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出MES FI过账明细")
    @PostMapping("/export")
    public void export(@RequestBody FiPostingDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        fiPostingDetailService.export(query, response);
    }

    @ApiOperation(value = "导入MES FI过账明细")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = fiPostingDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}