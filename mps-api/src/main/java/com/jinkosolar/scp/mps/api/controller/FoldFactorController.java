package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.FoldFactorDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.FoldFactorQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.FoldFactorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 折重系数相关操作控制层
 *
 * <AUTHOR> 2024-07-16 18:35:16
 */
@RequestMapping(value = "/fold-factor")
@RestController
@Api(value = "foldFactor", tags = "折重系数相关操作控制层")
public class FoldFactorController extends BaseController {
    @Autowired
    private FoldFactorService foldFactorService;

    @PostMapping("/page")
    @ApiOperation(value = "折重系数分页查询")
    public ResponseEntity<Results<Page<FoldFactorDTO>>> page(@RequestBody FoldFactorQuery query) {
        return Results.createSuccessRes(foldFactorService.page(query));
    }
    @PostMapping("/query-data-for-dp")
    @ApiOperation(value = "折重系数给DP使用")
    public ResponseEntity<Results<List<FoldFactorDTO>>> getByDataTypeAndMaxVersion(@RequestBody FoldFactorQuery query) {
        return Results.createSuccessRes(foldFactorService.getByDataTypeAndMaxVersion(query));
    }
    @PostMapping("/detail")
    @ApiOperation(value = "折重系数详情")
    public ResponseEntity<Results<FoldFactorDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(foldFactorService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增折重系数")
    public ResponseEntity<Results<FoldFactorDTO>> insert(@RequestBody FoldFactorDTO foldFactorDTO) {
        validObject(foldFactorDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(foldFactorService.saveOrUpdate(foldFactorDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新折重系数")
    public ResponseEntity<Results<FoldFactorDTO>> update(@RequestBody FoldFactorDTO foldFactorDTO) {
        validObject(foldFactorDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(foldFactorService.saveOrUpdate(foldFactorDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除折重系数")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        foldFactorService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出折重系数")
    @PostMapping("/export")
    public void export(@RequestBody FoldFactorQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        foldFactorService.export(query, response);
    }

    @ApiOperation(value = "导入折重系数")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultExDTO importResultDTO = foldFactorService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            foldFactorService.syncApsTable();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncApsTable")
    @ApiOperation(value = "syncApsTable")
    public ResponseEntity<Results<Object>> syncApsTable(@RequestBody FoldFactorQuery query) {
        foldFactorService.syncApsTable();
        return Results.createSuccessRes(Results.SUCCESS_CODE);
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号-三个数据分类汇总")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(foldFactorService.queryVersions(versionQuery));
    }

    @PostMapping("/type-versions")
    @ApiOperation(value = "查询版本号-三个数据分类对象")
    public ResponseEntity<Results<Map<Long,List<String>>>> queryDateTypeVersions() {
        return Results.createSuccessRes(foldFactorService.queryDateTypeVersions());
    }
}
