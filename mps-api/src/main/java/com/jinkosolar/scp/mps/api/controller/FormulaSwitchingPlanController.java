package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.SyncTableDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.SyncTableUtils;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.FormulaSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.query.FormulaSwitchingPlanQuery;
import com.jinkosolar.scp.mps.service.FormulaSwitchingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配方切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-08-05 11:20:10
 */
@RequestMapping(value = "/formula-switching-plan")
@RestController
@Api(value = "formulaSwitchingPlan", tags = "配方切换计划相关操作控制层")
public class FormulaSwitchingPlanController extends BaseController {    
    private final FormulaSwitchingPlanService formulaSwitchingPlanService;

    @Autowired
    private SyncTableUtils syncTableUtils;

    public FormulaSwitchingPlanController(FormulaSwitchingPlanService formulaSwitchingPlanService) {
        this.formulaSwitchingPlanService = formulaSwitchingPlanService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "配方切换计划分页查询")
    public ResponseEntity<Results<Page<FormulaSwitchingPlanDTO>>> page(@RequestBody FormulaSwitchingPlanQuery query) {
        return Results.createSuccessRes(formulaSwitchingPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "配方切换计划详情")
    public ResponseEntity<Results<FormulaSwitchingPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(formulaSwitchingPlanService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody FormulaSwitchingPlanDTO formulaSwitchingPlanDTO) {
        return Results.createSuccessRes(formulaSwitchingPlanService.getVersionNumberByDataType(formulaSwitchingPlanDTO));
    }
    @PostMapping("/insert")
    @ApiOperation(value = "新增配方切换计划")
    public ResponseEntity<Results<FormulaSwitchingPlanDTO>> insert(@RequestBody FormulaSwitchingPlanDTO formulaSwitchingPlanDTO) {
        validObject(formulaSwitchingPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(formulaSwitchingPlanService.saveOrUpdate(formulaSwitchingPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新配方切换计划")
    public ResponseEntity<Results<FormulaSwitchingPlanDTO>> update(@RequestBody FormulaSwitchingPlanDTO formulaSwitchingPlanDTO) {
        validObject(formulaSwitchingPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(formulaSwitchingPlanService.saveOrUpdate(formulaSwitchingPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除配方切换计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        formulaSwitchingPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出配方切换计划")
    @PostMapping("/export")
    public void export(@RequestBody FormulaSwitchingPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        formulaSwitchingPlanService.export(query, response);
    }

    @ApiOperation(value = "导入配方切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = formulaSwitchingPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncCrmStockToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    public void syncCrmStockToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> tables = new ArrayList<>();
        tables.add("mps_formula_switching_plan");
        syncTableDTO.setLovCodes(tables);
        syncTableUtils.syncTables(syncTableDTO);
    }
}