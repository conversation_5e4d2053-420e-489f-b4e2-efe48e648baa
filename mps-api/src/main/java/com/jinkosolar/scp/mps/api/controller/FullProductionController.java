
package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.FullProductionDTO;
import com.jinkosolar.scp.mps.domain.query.FullProductionQuery;
import com.jinkosolar.scp.mps.domain.save.ApprovalDTO;
import com.jinkosolar.scp.mps.service.FullProductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 满产产能相关操作控制层
 *
 * <AUTHOR> 2024年4月16日16:14:04
 */
@RequestMapping(value = "/full-production")
@RestController
@Api(value = "Full Production", tags = "满产产能相关操作控制层")
public class FullProductionController extends BaseController {
    @Autowired
    private FullProductionService fullProductionService;


    @PostMapping("/versions/{category}")
    @ApiOperation(value = "查询版本号")
    @ApiParam(name = "category", value = "版型类型,CG：常规，TS：特殊", required = true)
    public ResponseEntity<Results<List<String>>> versions(@PathVariable("category") String category) {
        return Results.createSuccessRes(fullProductionService.versions(category));
    }

    @PostMapping("/page")
    @ApiOperation(value = "满产产能分页查询")
    public ResponseEntity<Results<Page<FullProductionDTO>>> page(@RequestBody FullProductionQuery query) {
        return Results.createSuccessRes(fullProductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "满产产能详情")
    public ResponseEntity<Results<FullProductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(fullProductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增满产产能")
    public ResponseEntity<Results<FullProductionDTO>> insert(@RequestBody FullProductionDTO fullProductionDTO) {
        validObject(fullProductionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(fullProductionService.saveOrUpdate(fullProductionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新满产产能")
    public ResponseEntity<Results<FullProductionDTO>> update(@RequestBody FullProductionDTO fullProductionDTO) {
        validObject(fullProductionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(fullProductionService.saveOrUpdate(fullProductionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除满产产能")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        fullProductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出满产产能")
    @PostMapping("/export")
    @ApiParam(name = "category", value = "版型类型,CG：常规，TS：特殊", required = true)
    public void export(@RequestBody FullProductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        fullProductionService.export(query, response);
    }

    @ApiOperation(value = "导入满产产能")
    @PostMapping("/import/{category}")
    @ApiParam(name = "category", value = "版型类型,CG：常规，TS：特殊", required = true)
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @PathVariable("category") String category,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = fullProductionService.importData(multipartFile, excelPara, category);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @PostMapping("/doApproval")
    @ApiOperation(value = "审批操作")
    public ResponseEntity<Results<Object>> doApproval(@RequestBody ApprovalDTO query) {
        fullProductionService.doApproval(query.getIds(),query.getApprovalType());
        return Results.createSuccessRes();
    }

}
