package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.SyncTableDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.SyncTableUtils;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.FurnaceSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.query.FurnaceSwitchingPlanQuery;
import com.jinkosolar.scp.mps.service.FurnaceSwitchingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 炉型切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-08-05 11:20:10
 */
@RequestMapping(value = "/furnace-switching-plan")
@RestController
@Api(value = "furnaceSwitchingPlan", tags = "炉型切换计划相关操作控制层")
public class FurnaceSwitchingPlanController extends BaseController {    
    private final FurnaceSwitchingPlanService furnaceSwitchingPlanService;

    @Autowired
    private SyncTableUtils syncTableUtils;

    public FurnaceSwitchingPlanController(FurnaceSwitchingPlanService furnaceSwitchingPlanService) {
        this.furnaceSwitchingPlanService = furnaceSwitchingPlanService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "炉型切换计划分页查询")
    public ResponseEntity<Results<Page<FurnaceSwitchingPlanDTO>>> page(@RequestBody FurnaceSwitchingPlanQuery query) {
        return Results.createSuccessRes(furnaceSwitchingPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "炉型切换计划详情")
    public ResponseEntity<Results<FurnaceSwitchingPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(furnaceSwitchingPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody FurnaceSwitchingPlanDTO furnaceSwitchingPlanDTO) {
        return Results.createSuccessRes(furnaceSwitchingPlanService.getVersionNumberByDataType(furnaceSwitchingPlanDTO));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增炉型切换计划")
    public ResponseEntity<Results<FurnaceSwitchingPlanDTO>> insert(@RequestBody FurnaceSwitchingPlanDTO furnaceSwitchingPlanDTO) {
        validObject(furnaceSwitchingPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(furnaceSwitchingPlanService.saveOrUpdate(furnaceSwitchingPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新炉型切换计划")
    public ResponseEntity<Results<FurnaceSwitchingPlanDTO>> update(@RequestBody FurnaceSwitchingPlanDTO furnaceSwitchingPlanDTO) {
        validObject(furnaceSwitchingPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(furnaceSwitchingPlanService.saveOrUpdate(furnaceSwitchingPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除炉型切换计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        furnaceSwitchingPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出炉型切换计划")
    @PostMapping("/export")
    public void export(@RequestBody FurnaceSwitchingPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        furnaceSwitchingPlanService.export(query, response);
    }

    @ApiOperation(value = "导入炉型切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = furnaceSwitchingPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncCrmStockToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    public void syncCrmStockToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> tables = new ArrayList<>();
        tables.add("mps_furnace_switching_plan");
        syncTableDTO.setLovCodes(tables);
        syncTableUtils.syncTables(syncTableDTO);
    }
}