package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.GearCertificationDTO;
import com.jinkosolar.scp.mps.domain.query.GearCertificationQuery;
import com.jinkosolar.scp.mps.service.GearCertificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;


/**
 * 档位认证
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gear-certification")
@Api(value = "gear-certification", tags = "档位认证")
public class GearCertificationController {

    @Autowired
    GearCertificationService gearCertificationService;

    @PostMapping("/page")
    @ApiOperation(value = "列表")
    public ResponseEntity<Results<Page<GearCertificationDTO>>> listGearCertificationQuery(@RequestBody GearCertificationQuery gearCertificationQuery) {
        Page<GearCertificationDTO> page = gearCertificationService.listGearCertificationQuery(gearCertificationQuery);
        return Results.createSuccessRes(page);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Boolean>> batchDelete(@RequestBody IdsDTO idsDTO) {
        gearCertificationService.deleteGearCertification(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes(Boolean.TRUE);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody GearCertificationQuery gearCertificationQuery, HttpServletResponse response) {
        gearCertificationQuery.setPageNumber(1);
        gearCertificationQuery.setPageSize(GlobalConstant.max_page_size);
        gearCertificationService.exportGearCertificationData(gearCertificationQuery, response);
    }

    /**
     * 导入数据
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入数据")
    public ResponseEntity<Results<Boolean>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws Exception {
        gearCertificationService.importData(file, excelPara);
        return Results.createSuccessRes(Boolean.TRUE);
    }

    /**
     * 全量处理生成档位
     */
    @PostMapping("/createGearCertificationData")
    @ApiOperation(value = "生成数据")
    public ResponseEntity<Results<Boolean>> createGearCertificationData() {
        gearCertificationService.createGearCertificationData();
        return Results.createSuccessRes(Boolean.TRUE);
    }

}
