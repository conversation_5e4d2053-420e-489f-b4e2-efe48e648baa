package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.HearthClimbRuleDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.HearthClimbRuleQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.HearthClimbRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 炉台爬坡规则相关操作控制层
 *
 * <AUTHOR> 2024-07-18 15:43:06
 */
@RequestMapping(value = "/hearth-climb-rule")
@RestController
@Api(value = "hearthClimbRule", tags = "炉台爬坡规则相关操作控制层")
public class HearthClimbRuleController extends BaseController {
    @Autowired
    private HearthClimbRuleService hearthClimbRuleService;

    @PostMapping("/page")
    @ApiOperation(value = "炉台爬坡规则分页查询")
    public ResponseEntity<Results<Page<HearthClimbRuleDTO>>> page(@RequestBody HearthClimbRuleQuery query) {
        return Results.createSuccessRes(hearthClimbRuleService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "炉台爬坡规则详情")
    public ResponseEntity<Results<HearthClimbRuleDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(hearthClimbRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增炉台爬坡规则")
    public ResponseEntity<Results<HearthClimbRuleDTO>> insert(@RequestBody HearthClimbRuleDTO hearthClimbRuleDTO) {
        validObject(hearthClimbRuleDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(hearthClimbRuleService.saveOrUpdate(hearthClimbRuleDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新炉台爬坡规则")
    public ResponseEntity<Results<HearthClimbRuleDTO>> update(@RequestBody HearthClimbRuleDTO hearthClimbRuleDTO) {
        validObject(hearthClimbRuleDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(hearthClimbRuleService.saveOrUpdate(hearthClimbRuleDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除炉台爬坡规则")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        hearthClimbRuleService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出炉台爬坡规则")
    @PostMapping("/export")
    public void export(@RequestBody HearthClimbRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        hearthClimbRuleService.export(query, response);
    }

    @ApiOperation(value = "导入炉台爬坡规则")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultExDTO importResultDTO = hearthClimbRuleService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号-三个数据分类汇总")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(hearthClimbRuleService.queryVersions(versionQuery));
    }

    @PostMapping("/type-versions")
    @ApiOperation(value = "查询版本号-三个数据分类对象")
    public ResponseEntity<Results<Map<Long,List<String>>>> queryDateTypeVersions() {
        return Results.createSuccessRes(hearthClimbRuleService.queryDateTypeVersions());
    }
}
