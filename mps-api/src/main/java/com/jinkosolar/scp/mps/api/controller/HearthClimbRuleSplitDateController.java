package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.HearthClimbRuleSplitDateDTO;
import com.jinkosolar.scp.mps.domain.query.HearthClimbRuleSplitDateQuery;
import com.jinkosolar.scp.mps.service.HearthClimbRuleSplitDateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 炉台爬坡规则相关操作控制层
 * 
 * <AUTHOR> 2024-11-26 15:58:48
 */
@RequestMapping(value = "/hearth-climb-rule-split-date")
@RestController
@Api(value = "hearthClimbRuleSplitDate", tags = "炉台爬坡规则相关操作控制层")
@RequiredArgsConstructor  
public class HearthClimbRuleSplitDateController extends BaseController {    
    private final HearthClimbRuleSplitDateService hearthClimbRuleSplitDateService; 

    @PostMapping("/page")
    @ApiOperation(value = "炉台爬坡规则分页查询")
    public ResponseEntity<Results<Page<HearthClimbRuleSplitDateDTO>>> page(@RequestBody HearthClimbRuleSplitDateQuery query) {
        return Results.createSuccessRes(hearthClimbRuleSplitDateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "炉台爬坡规则详情")
    public ResponseEntity<Results<HearthClimbRuleSplitDateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(hearthClimbRuleSplitDateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增炉台爬坡规则")
    public ResponseEntity<Results<Void>> insert(@RequestBody HearthClimbRuleSplitDateDTO hearthClimbRuleSplitDateDTO) {
        validObject(hearthClimbRuleSplitDateDTO, ValidGroups.Insert.class);
        hearthClimbRuleSplitDateService.saveOrUpdate(hearthClimbRuleSplitDateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新炉台爬坡规则")
    public ResponseEntity<Results<Void>> update(@RequestBody HearthClimbRuleSplitDateDTO hearthClimbRuleSplitDateDTO) {
        validObject(hearthClimbRuleSplitDateDTO, ValidGroups.Update.class);
        hearthClimbRuleSplitDateService.saveOrUpdate(hearthClimbRuleSplitDateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除炉台爬坡规则")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        hearthClimbRuleSplitDateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出炉台爬坡规则")
    @PostMapping("/export")
    public void export(@RequestBody HearthClimbRuleSplitDateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        hearthClimbRuleSplitDateService.export(query, response);
    }

    @ApiOperation(value = "导入炉台爬坡规则")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = hearthClimbRuleSplitDateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}