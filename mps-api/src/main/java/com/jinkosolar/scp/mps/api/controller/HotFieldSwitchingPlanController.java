package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.HotFieldSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.query.HotFieldSwitchingPlanQuery;
import com.jinkosolar.scp.mps.service.HotFieldSwitchingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 热场切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-08-02 13:14:01
 */
@RequestMapping(value = "/hot-field-switching-plan")
@RestController
@Api(value = "hotFieldSwitchingPlan", tags = "热场切换计划相关操作控制层")
public class HotFieldSwitchingPlanController extends BaseController {    
    private final HotFieldSwitchingPlanService hotFieldSwitchingPlanService;

    public HotFieldSwitchingPlanController(HotFieldSwitchingPlanService hotFieldSwitchingPlanService) {
        this.hotFieldSwitchingPlanService = hotFieldSwitchingPlanService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "热场切换计划分页查询")
    public ResponseEntity<Results<Page<HotFieldSwitchingPlanDTO>>> page(@RequestBody HotFieldSwitchingPlanQuery query) {
        return Results.createSuccessRes(hotFieldSwitchingPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "热场切换计划详情")
    public ResponseEntity<Results<HotFieldSwitchingPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(hotFieldSwitchingPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody HotFieldSwitchingPlanDTO hotFieldSwitchingPlanDTO) {
        return Results.createSuccessRes(hotFieldSwitchingPlanService.getVersionNumberByDataType(hotFieldSwitchingPlanDTO));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增热场切换计划")
    public ResponseEntity<Results<HotFieldSwitchingPlanDTO>> insert(@RequestBody HotFieldSwitchingPlanDTO hotFieldSwitchingPlanDTO) {
        validObject(hotFieldSwitchingPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(hotFieldSwitchingPlanService.saveOrUpdate(hotFieldSwitchingPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新热场切换计划")
    public ResponseEntity<Results<HotFieldSwitchingPlanDTO>> update(@RequestBody HotFieldSwitchingPlanDTO hotFieldSwitchingPlanDTO) {
        validObject(hotFieldSwitchingPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(hotFieldSwitchingPlanService.saveOrUpdate(hotFieldSwitchingPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除热场切换计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        hotFieldSwitchingPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出热场切换计划")
    @PostMapping("/export")
    public void export(@RequestBody HotFieldSwitchingPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        hotFieldSwitchingPlanService.export(query, response);
    }

    @ApiOperation(value = "导入热场切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = hotFieldSwitchingPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            hotFieldSwitchingPlanService.syncHotFieldToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}