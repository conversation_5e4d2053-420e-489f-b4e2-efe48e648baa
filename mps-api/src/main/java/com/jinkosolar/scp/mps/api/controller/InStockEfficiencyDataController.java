package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.InStockEfficiencyDataDTO;
import com.jinkosolar.scp.mps.domain.query.InStockEfficiencyDataQuery;
import com.jinkosolar.scp.mps.domain.save.InStockEfficiencyDataSaveDTO;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import com.jinkosolar.scp.mps.service.InStockEfficiencyDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 入库效率数据 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-16 11:09:43
 */
@RestController
@RequestMapping("/in-stock-efficiency-data")
@RequiredArgsConstructor
@Api(value = "in-stock-efficiency-data", tags = "入库效率数据操作")
public class InStockEfficiencyDataController {
    private final InStockEfficiencyDataService inStockEfficiencyDataService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "入库效率数据分页列表", notes = "获得入库效率数据分页列表")
    public ResponseEntity<Results<Page<InStockEfficiencyDataDTO>>> queryByPage(@RequestBody InStockEfficiencyDataQuery query) {
        return Results.createSuccessRes(inStockEfficiencyDataService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<InStockEfficiencyDataDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(inStockEfficiencyDataService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @PostMapping("/refreshMonthData")
    @ApiOperation(value = "按月份刷新数据: 202501")
    public ResponseEntity<Results<Object>> refreshMonthData(@RequestBody IdDTO idDTO) {
        LocalDate localDate = DateUtil.monthLocalDate(idDTO.getId());
        List<LocalDate> allDatesOfMonth = DateUtil.getAllDatesOfMonth(localDate);
        for (LocalDate date : allDatesOfMonth) {
            inStockEfficiencyDataService.refreshByDate(date);
        }
        return Results.createSuccessRes();
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<InStockEfficiencyDataDTO>> save(@Valid @RequestBody InStockEfficiencyDataSaveDTO saveDTO) {
        return Results.createSuccessRes(inStockEfficiencyDataService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        inStockEfficiencyDataService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody InStockEfficiencyDataQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        inStockEfficiencyDataService.export(query, response);
    }
}
