package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.query.InStockEfficiencyDataQuery;
import com.jinkosolar.scp.mps.service.InStockEfficiencyDataService;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 入库效率监控控制层
 *
 * <AUTHOR> 2024-07-10
 */
@RequestMapping(value = "/in-stock-efficiency")
@RestController
@Api(value = "inStockEfficiency", tags = "入库效率监控控制层")
public class InStockEfficiencyReportController {

    @Autowired
    private NonModuleProductionPlanService nonModuleProductionPlanService;

    @Autowired
    private InStockEfficiencyDataService inStockEfficiencyDataService;


    @ApiOperation(value = "查询入库效率监控报告")
    @PostMapping("/list")
    public ResponseEntity<Results<List<Map<String, Object>>>> list(@RequestBody InStockEfficiencyDataQuery query) {
        return Results.createSuccessRes(inStockEfficiencyDataService.listEfficiency(query));
    }

    @ApiOperation(value = "导出入库效率监控报告")
    @PostMapping("/export")
    public void export(@RequestBody InStockEfficiencyDataQuery query, HttpServletResponse response) {
        inStockEfficiencyDataService.exportEfficiency(query, response);
    }
}
