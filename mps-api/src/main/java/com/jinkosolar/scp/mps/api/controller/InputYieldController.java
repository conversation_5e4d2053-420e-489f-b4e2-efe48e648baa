package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncInputYieldRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncInputYieldResponse;
import com.jinkosolar.scp.jip.api.dto.mes.SyncWorkInProgressRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncWorkInProgressResponse;
import com.jinkosolar.scp.jip.api.service.SyncInputYieldService;
import com.jinkosolar.scp.mps.domain.dto.InputYieldDTO;
import com.jinkosolar.scp.mps.domain.query.InputYieldQuery;
import com.jinkosolar.scp.mps.service.InputYieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶实际单产表相关操作控制层
 * 
 * <AUTHOR> 2024-07-11 14:00:04
 */
@RequestMapping(value = "/input-yield")
@RestController
@Api(value = "inputYield", tags = "拉晶实际单产表相关操作控制层")
public class InputYieldController extends BaseController {
    @Autowired
    private InputYieldService inputYieldService;

    @Autowired
    private SyncInputYieldService syncInputYieldService;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶实际单产表分页查询")
    public ResponseEntity<Results<Page<InputYieldDTO>>> page(@RequestBody InputYieldQuery query) {
        return Results.createSuccessRes(inputYieldService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶实际单产表详情")
    public ResponseEntity<Results<InputYieldDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(inputYieldService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶实际单产表")
    public ResponseEntity<Results<InputYieldDTO>> insert(@RequestBody InputYieldDTO inputYieldDTO) {
        validObject(inputYieldDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(inputYieldService.saveOrUpdate(inputYieldDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶实际单产表")
    public ResponseEntity<Results<InputYieldDTO>> update(@RequestBody InputYieldDTO inputYieldDTO) {
        validObject(inputYieldDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(inputYieldService.saveOrUpdate(inputYieldDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶实际单产表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        inputYieldService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶实际单产表")
    @PostMapping("/export")
    public void export(@RequestBody InputYieldQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        inputYieldService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶实际单产表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = inputYieldService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @JipFeignLog
    @PostMapping("/syncInputYield")
    @ApiOperation(value = "同步拉晶实际单产")
    public JipResponseData syncInputYield() {

        inputYieldService.sync();
        return JipResponseData.success();
    }
}