package com.jinkosolar.scp.mps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ibm.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jinkosolar.scp.mps.domain.query.IntegratedOperatingRateReportQuery;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.jinkosolar.scp.mps.domain.save.IntegratedOperatingRateReportSaveDTO;
import com.jinkosolar.scp.mps.domain.entity.IntegratedOperatingRateReport;
import com.jinkosolar.scp.mps.domain.dto.IntegratedOperatingRateReportDTO;
import com.jinkosolar.scp.mps.service.IntegratedOperatingRateReportService;
import com.ibm.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * [说明]一体化开工率报表 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@RestController
@RequestMapping("/integrated-operating-rate-report")
@RequiredArgsConstructor
@Api(value = "integrated-operating-rate-report", tags = "一体化开工率报表操作")
public class IntegratedOperatingRateReportController {
    private final IntegratedOperatingRateReportService integratedOperatingRateReportService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "一体化开工率报表分页列表", notes = "获得一体化开工率报表分页列表")
    public ResponseEntity<Results<Page<IntegratedOperatingRateReportDTO>>> queryByPage(@RequestBody IntegratedOperatingRateReportQuery query) {
        return Results.createSuccessRes(integratedOperatingRateReportService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<IntegratedOperatingRateReportDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(integratedOperatingRateReportService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<IntegratedOperatingRateReportDTO>> save(@Valid @RequestBody IntegratedOperatingRateReportSaveDTO saveDTO) {
        return Results.createSuccessRes(integratedOperatingRateReportService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        integratedOperatingRateReportService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody IntegratedOperatingRateReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            integratedOperatingRateReportService.export(query, response);
    }

    @PostMapping("/calculation")
    @ApiOperation(value = "一体化开工率计算")
    public ResponseEntity<Results<Object>> calculation(@RequestBody IntegratedOperatingRateReportQuery query) {
        integratedOperatingRateReportService.calculation();
        return Results.createSuccessRes();
    }

    @PostMapping("/queryReport")
    @ApiOperation(value = "一体化开工率计算")
    public ResponseEntity<Results<Object>> queryReport(@RequestBody IntegratedOperatingRateReportQuery query) {
        List<IntegratedOperatingRateReportDTO.Result> resultList = integratedOperatingRateReportService.queryReport(query);
        return Results.createSuccessRes(resultList);
    }
}
