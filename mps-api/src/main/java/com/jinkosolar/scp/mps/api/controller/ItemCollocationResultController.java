package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ItemCollocationResultDTO;
import com.jinkosolar.scp.mps.domain.query.ItemCollocationResultQuery;
import com.jinkosolar.scp.mps.service.ItemCollocationResultService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MRP物料搭配结果表相关操作控制层
 * 
 * <AUTHOR> 2024-09-25 23:21:18
 */
@RequestMapping(value = "/item-collocation-result")
@RestController
@Api(value = "itemCollocationResult", tags = "MRP物料搭配结果表相关操作控制层")
@RequiredArgsConstructor  
public class ItemCollocationResultController extends BaseController {    
    private final ItemCollocationResultService itemCollocationResultService; 

    @PostMapping("/page")
    @ApiOperation(value = "MRP物料搭配结果表分页查询")
    public ResponseEntity<Results<Page<ItemCollocationResultDTO>>> page(@RequestBody ItemCollocationResultQuery query) {
        return Results.createSuccessRes(itemCollocationResultService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "MRP物料搭配结果表详情")
    public ResponseEntity<Results<ItemCollocationResultDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(itemCollocationResultService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增MRP物料搭配结果表")
    public ResponseEntity<Results<Void>> insert(@RequestBody ItemCollocationResultDTO itemCollocationResultDTO) {
        validObject(itemCollocationResultDTO, ValidGroups.Insert.class);
        itemCollocationResultService.saveOrUpdate(itemCollocationResultDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新MRP物料搭配结果表")
    public ResponseEntity<Results<Void>> update(@RequestBody ItemCollocationResultDTO itemCollocationResultDTO) {
        validObject(itemCollocationResultDTO, ValidGroups.Update.class);
        itemCollocationResultService.saveOrUpdate(itemCollocationResultDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除MRP物料搭配结果表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        itemCollocationResultService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出MRP物料搭配结果表")
    @PostMapping("/export")
    public void export(@RequestBody ItemCollocationResultQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        itemCollocationResultService.export(query, response);
    }

    @ApiOperation(value = "导入MRP物料搭配结果表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = itemCollocationResultService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}