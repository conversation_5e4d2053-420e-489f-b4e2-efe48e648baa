package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.JkeSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.query.JkeSwitchingPlanQuery;
import com.jinkosolar.scp.mps.service.JkeSwitchingPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * jke切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-08-05 19:23:43
 */
@RequestMapping(value = "/jke-switching-plan")
@RestController
@Api(value = "jkeSwitchingPlan", tags = "jke切换计划相关操作控制层")
public class JkeSwitchingPlanController extends BaseController {    
    private final JkeSwitchingPlanService jkeSwitchingPlanService;

    public JkeSwitchingPlanController(JkeSwitchingPlanService jkeSwitchingPlanService) {
        this.jkeSwitchingPlanService = jkeSwitchingPlanService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "jke切换计划分页查询")
    public ResponseEntity<Results<Page<JkeSwitchingPlanDTO>>> page(@RequestBody JkeSwitchingPlanQuery query) {
        return Results.createSuccessRes(jkeSwitchingPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "jke切换计划详情")
    public ResponseEntity<Results<JkeSwitchingPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(jkeSwitchingPlanService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody JkeSwitchingPlanDTO jkeSwitchingPlanDTO) {
        return Results.createSuccessRes(jkeSwitchingPlanService.getVersionNumberByDataType(jkeSwitchingPlanDTO));
    }
    @PostMapping("/insert")
    @ApiOperation(value = "新增jke切换计划")
    public ResponseEntity<Results<JkeSwitchingPlanDTO>> insert(@RequestBody JkeSwitchingPlanDTO jkeSwitchingPlanDTO) {
        validObject(jkeSwitchingPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(jkeSwitchingPlanService.saveOrUpdate(jkeSwitchingPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新jke切换计划")
    public ResponseEntity<Results<JkeSwitchingPlanDTO>> update(@RequestBody JkeSwitchingPlanDTO jkeSwitchingPlanDTO) {
        validObject(jkeSwitchingPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(jkeSwitchingPlanService.saveOrUpdate(jkeSwitchingPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除jke切换计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        jkeSwitchingPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出jke切换计划")
    @PostMapping("/export")
    public void export(@RequestBody JkeSwitchingPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        jkeSwitchingPlanService.export(query, response);
    }

    @ApiOperation(value = "导入jke切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = jkeSwitchingPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}