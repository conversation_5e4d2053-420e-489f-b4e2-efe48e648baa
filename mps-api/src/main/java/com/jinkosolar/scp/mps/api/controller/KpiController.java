package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.KpiDTO;
import com.jinkosolar.scp.mps.domain.dto.KpiDetailDTO;
import com.jinkosolar.scp.mps.domain.query.KpiQuery;
import com.jinkosolar.scp.mps.domain.query.PowerEfficiencyQuery;
import com.jinkosolar.scp.mps.service.KpiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * kpi管理相关操作控制层
 * 
 * <AUTHOR> 2024-05-13 13:56:39
 */
@RequestMapping(value = "/kpi")
@RestController
@Api(value = "kpi", tags = "kpi管理相关操作控制层")
public class KpiController extends BaseController {
    @Autowired
    private KpiService kpiService;

    @PostMapping("/page")
    @ApiOperation(value = "kpi管理分页查询")
    public ResponseEntity<Results<Page<KpiDTO>>> page(@RequestBody KpiQuery query) {
        return Results.createSuccessRes(kpiService.page(query));
    }
    @PostMapping("/queryPage")
    @ApiOperation(value = "kpi管理查询")
    public ResponseEntity<Results<List<KpiDTO>>> queryPage(@RequestBody Set<String> stringSet) {
        return Results.createSuccessRes(kpiService.queryPage(stringSet));
    }
    @PostMapping("/query-for-dp")
    @ApiOperation(value = "kpi管理查询给dp电池计划计算使用")
    public ResponseEntity<Results<List<KpiDTO>>> queryForDp( ) {
        return Results.createSuccessRes(kpiService.queryForDp());
    }
    @PostMapping("/queryKpiDetail")
    @ApiOperation(value = "kpi管理查询")
    public ResponseEntity<Results<List<KpiDetailDTO>>> queryKpiDetail(@RequestBody Set<Long> kpiIds) {
        return Results.createSuccessRes(kpiService.queryKpiDetail(kpiIds));
    }
    @PostMapping("/detail")
    @ApiOperation(value = "kpi管理详情")
    public ResponseEntity<Results<KpiDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(kpiService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增kpi管理")
    public ResponseEntity<Results<KpiDTO>> insert(@RequestBody KpiDTO kpiDTO) {
        validObject(kpiDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(kpiService.saveOrUpdate(kpiDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新kpi管理")
    public ResponseEntity<Results<KpiDTO>> update(@RequestBody KpiDTO kpiDTO) {
        validObject(kpiDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(kpiService.saveOrUpdate(kpiDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除kpi管理")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        kpiService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出kpi管理")
    @PostMapping("/export")
    public void export(@RequestBody KpiQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        kpiService.export(query, response);
    }

    @ApiOperation(value = "导入kpi管理")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultDTO importResultDTO = kpiService.importData(multipartFile);
        kpiService.syncApsTable();
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
    /**
     * dp需求调用接口
     * 根据工作中心编码id+产品id查询mps_kpi
     * 查到后再根据id作为mps_kpi_detail的外键查询年月column_name=xxx{例如2024/9}的数值
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryKpiListByDp")
    @ApiOperation(value = "基础KPI调用", notes = "基础KPI调用")
    public ResponseEntity<Results<BigDecimal>> queryKpiListByDp(@RequestBody KpiQuery query) {
        return Results.createSuccessRes(kpiService.queryKpiListByDp(query));
    }
}