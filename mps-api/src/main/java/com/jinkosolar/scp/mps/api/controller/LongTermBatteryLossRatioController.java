package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.LongTermBatteryLossRatioDTO;
import com.jinkosolar.scp.mps.domain.query.LongTermBatteryLossRatioQuery;
import com.jinkosolar.scp.mps.service.LongTermBatteryLossRatioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中长期电池损耗比例相关操作控制层
 *
 * <AUTHOR> 2024-11-19 13:31:05
 */
@RequestMapping(value = "/long-term-battery-loss-ratio")
@RestController
@Api(value = "longTermBatteryLossRatio", tags = "中长期电池损耗比例相关操作控制层")
@RequiredArgsConstructor
public class LongTermBatteryLossRatioController extends BaseController {
    private final LongTermBatteryLossRatioService longTermBatteryLossRatioService;

    @PostMapping("/page")
    @ApiOperation(value = "中长期电池损耗比例分页查询")
    public ResponseEntity<Results<Page<LongTermBatteryLossRatioDTO>>> page(@RequestBody LongTermBatteryLossRatioQuery query) {
        return Results.createSuccessRes(longTermBatteryLossRatioService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "中长期电池损耗比例详情")
    public ResponseEntity<Results<LongTermBatteryLossRatioDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(longTermBatteryLossRatioService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增中长期电池损耗比例")
    public ResponseEntity<Results<Void>> insert(@RequestBody LongTermBatteryLossRatioDTO longTermBatteryLossRatioDTO) {
        validObject(longTermBatteryLossRatioDTO, ValidGroups.Insert.class);
        longTermBatteryLossRatioService.saveOrUpdate(longTermBatteryLossRatioDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新中长期电池损耗比例")
    public ResponseEntity<Results<Void>> update(@RequestBody LongTermBatteryLossRatioDTO longTermBatteryLossRatioDTO) {
        validObject(longTermBatteryLossRatioDTO, ValidGroups.Update.class);
        longTermBatteryLossRatioService.saveOrUpdate(longTermBatteryLossRatioDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除中长期电池损耗比例")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        longTermBatteryLossRatioService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出中长期电池损耗比例")
    @PostMapping("/export")
    public void export(@RequestBody LongTermBatteryLossRatioQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        longTermBatteryLossRatioService.export(query, response);
    }

    @ApiOperation(value = "导入中长期电池损耗比例")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = longTermBatteryLossRatioService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}