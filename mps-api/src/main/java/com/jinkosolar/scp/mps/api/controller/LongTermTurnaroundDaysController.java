package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.LongTermTurnaroundDaysDTO;
import com.jinkosolar.scp.mps.domain.query.LongTermTurnaroundDaysQuery;
import com.jinkosolar.scp.mps.service.LongTermTurnaroundDaysService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中长期安全周转天数相关操作控制层
 *
 * <AUTHOR> 2024-11-19 13:30:25
 */
@RequestMapping(value = "/long-term-turnaround-days")
@RestController
@Api(value = "longTermTurnaroundDays", tags = "中长期安全周转天数相关操作控制层")
@RequiredArgsConstructor
public class LongTermTurnaroundDaysController extends BaseController {
    private final LongTermTurnaroundDaysService longTermTurnaroundDaysService;

    @PostMapping("/page")
    @ApiOperation(value = "中长期安全周转天数分页查询")
    public ResponseEntity<Results<Page<LongTermTurnaroundDaysDTO>>> page(@RequestBody LongTermTurnaroundDaysQuery query) {
        return Results.createSuccessRes(longTermTurnaroundDaysService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "中长期安全周转天数详情")
    public ResponseEntity<Results<LongTermTurnaroundDaysDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(longTermTurnaroundDaysService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增中长期安全周转天数")
    public ResponseEntity<Results<Void>> insert(@RequestBody LongTermTurnaroundDaysDTO longTermTurnaroundDaysDTO) {
        validObject(longTermTurnaroundDaysDTO, ValidGroups.Insert.class);
        longTermTurnaroundDaysService.saveOrUpdate(longTermTurnaroundDaysDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新中长期安全周转天数")
    public ResponseEntity<Results<Void>> update(@RequestBody LongTermTurnaroundDaysDTO longTermTurnaroundDaysDTO) {
        validObject(longTermTurnaroundDaysDTO, ValidGroups.Update.class);
        longTermTurnaroundDaysService.saveOrUpdate(longTermTurnaroundDaysDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除中长期安全周转天数")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        longTermTurnaroundDaysService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出中长期安全周转天数")
    @PostMapping("/export")
    public void export(@RequestBody LongTermTurnaroundDaysQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        longTermTurnaroundDaysService.export(query, response);
    }

    @ApiOperation(value = "导入中长期安全周转天数")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = longTermTurnaroundDaysService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}