package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ManufactureBomDTO;
import com.jinkosolar.scp.mps.domain.query.ManufactureBomQuery;
import com.jinkosolar.scp.mps.service.ManufactureBomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 制造BOM相关操作控制层
 * 
 * <AUTHOR> 2024-08-08 09:34:47
 */
@RequestMapping(value = "/manufacture-bom")
@RestController
@Api(value = "manufactureBom", tags = "制造BOM相关操作控制层")
@RequiredArgsConstructor
@Slf4j
public class ManufactureBomController extends BaseController {    
    private final ManufactureBomService manufactureBomService; 

    @PostMapping("/page")
    @ApiOperation(value = "制造BOM分页查询")
    public ResponseEntity<Results<Page<ManufactureBomDTO>>> page(@RequestBody ManufactureBomQuery query) {
        return Results.createSuccessRes(manufactureBomService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "制造BOM详情")
    public ResponseEntity<Results<ManufactureBomDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(manufactureBomService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增制造BOM")
    public ResponseEntity<Results<ManufactureBomDTO>> insert(@RequestBody ManufactureBomDTO manufactureBomDTO) {
        validObject(manufactureBomDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(manufactureBomService.saveOrUpdate(manufactureBomDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新制造BOM")
    public ResponseEntity<Results<ManufactureBomDTO>> update(@RequestBody ManufactureBomDTO manufactureBomDTO) {
        validObject(manufactureBomDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(manufactureBomService.saveOrUpdate(manufactureBomDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除制造BOM")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        manufactureBomService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出制造BOM")
    @PostMapping("/export")
    public void export(@RequestBody ManufactureBomQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        manufactureBomService.export(query, response);
    }

    @ApiOperation(value = "导入制造BOM")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = manufactureBomService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "制造bom生成")
    @PostMapping("/result")
    public ResponseEntity<Results<Object>> result(@RequestBody ManufactureBomQuery query, HttpServletResponse response) {
        try{
            manufactureBomService.result(query.getModelType());
        }catch (Exception e){
            log.info("ManufactureBomController.result error:{}", e.getMessage(), e);
            String msg = e.getMessage();
            if (StringUtils.isBlank(msg)) {
                StringBuilder builder = new StringBuilder();
                builder.append(e.getClass().getName()).append(": ").append(e.getMessage());
                for (StackTraceElement ele : e.getStackTrace()) {
                    builder.append(String.format("  %s.%s(%s:%s) ", ele.getClassName(), ele.getMethodName(), ele.getFileName(), ele.getLineNumber()));
                }
                msg = builder.toString();
            }
            return Results.createAsprovaFailRes(msg, "A0001");
        }
        return Results.createAsprovaSuccessRes(true);
    }

    @ApiOperation(value = "年度计划制造bom生成")
    @PostMapping("/yearResult")
    public ResponseEntity<Results<Object>> yearResult(@RequestBody ManufactureBomQuery query, HttpServletResponse response) {
        manufactureBomService.yearResult(query.getModelType());
        return Results.createAsprovaSuccessRes(true);
    }
}