package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ManufactureBomYearDTO;
import com.jinkosolar.scp.mps.domain.query.ManufactureBomYearQuery;
import com.jinkosolar.scp.mps.service.ManufactureBomYearService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 年度计划制造BOM相关操作控制层
 * 
 * <AUTHOR> 2024-10-30 09:02:45
 */
@RequestMapping(value = "/manufacture-bom-year")
@RestController
@Api(value = "manufactureBomYear", tags = "年度计划制造BOM相关操作控制层")
@RequiredArgsConstructor  
public class ManufactureBomYearController extends BaseController {    
    private final ManufactureBomYearService manufactureBomYearService; 

    @PostMapping("/page")
    @ApiOperation(value = "年度计划制造BOM分页查询")
    public ResponseEntity<Results<Page<ManufactureBomYearDTO>>> page(@RequestBody ManufactureBomYearQuery query) {
        return Results.createSuccessRes(manufactureBomYearService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "年度计划制造BOM详情")
    public ResponseEntity<Results<ManufactureBomYearDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(manufactureBomYearService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增年度计划制造BOM")
    public ResponseEntity<Results<Void>> insert(@RequestBody ManufactureBomYearDTO manufactureBomYearDTO) {
        validObject(manufactureBomYearDTO, ValidGroups.Insert.class);
        manufactureBomYearService.saveOrUpdate(manufactureBomYearDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新年度计划制造BOM")
    public ResponseEntity<Results<Void>> update(@RequestBody ManufactureBomYearDTO manufactureBomYearDTO) {
        validObject(manufactureBomYearDTO, ValidGroups.Update.class);
        manufactureBomYearService.saveOrUpdate(manufactureBomYearDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除年度计划制造BOM")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        manufactureBomYearService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出年度计划制造BOM")
    @PostMapping("/export")
    public void export(@RequestBody ManufactureBomYearQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        manufactureBomYearService.export(query, response);
    }

    @ApiOperation(value = "导入年度计划制造BOM")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = manufactureBomYearService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}