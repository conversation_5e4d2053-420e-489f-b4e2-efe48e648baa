package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.MaterialCertificationDTO;
import com.jinkosolar.scp.mps.domain.query.MaterialCertificationQuery;
import com.jinkosolar.scp.mps.service.MaterialCertificationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/material-certification")
@Api(value = "material-certification", tags = "材料认证")
public class MaterialCertificationController {

    @Autowired
    MaterialCertificationService materialCertificationService;

    @PostMapping("/page")
    @ApiOperation(value = "列表")
    public ResponseEntity<Results<Page<MaterialCertificationDTO>>> listMaterialCertification(@RequestBody MaterialCertificationQuery materialCertificationQuery) {
        Page<MaterialCertificationDTO> page = materialCertificationService.listMaterialCertification(materialCertificationQuery);
        return Results.createSuccessRes(page);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Boolean>> batchDelete(@RequestBody IdsDTO idsDTO) {
        materialCertificationService.deleteMaterialCertification(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes(Boolean.TRUE);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MaterialCertificationQuery materialCertificationQuery, HttpServletResponse response) {
        materialCertificationQuery.setPageNumber(1);
        materialCertificationQuery.setPageSize(GlobalConstant.max_page_size);
        materialCertificationService.exportMaterialCertificationData(materialCertificationQuery, response);
    }

    /**
     * 导入数据
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入数据")
    public ResponseEntity<Results<Boolean>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws Exception {
        materialCertificationService.importData(file, excelPara);
        return Results.createSuccessRes(Boolean.TRUE);
    }


    /**
     * 全量处理生成材料
     */
    @PostMapping("/createMaterialCertificationData")
    @ApiOperation(value = "生成数据")
    public ResponseEntity<Results<Boolean>> createMaterialCertificationData() {
        materialCertificationService.createMaterialCertificationData();
        return Results.createSuccessRes(Boolean.TRUE);
    }

}
