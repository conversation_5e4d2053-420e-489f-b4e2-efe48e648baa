package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.MaterialDpDemandLinesAttributeDTO;
import com.jinkosolar.scp.mps.domain.query.MaterialDpDemandLinesAttributeQuery;
import com.jinkosolar.scp.mps.service.MaterialDpDemandLinesAttributeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物控指定订单使用物料属性值相关操作控制层
 * 
 * <AUTHOR> 2024-05-29 10:35:32
 */
@RequestMapping(value = "/material-dp-demand-lines-attribute")
@RestController
@Api(value = "materialDpDemandLinesAttribute", tags = "物控指定订单使用物料属性值相关操作控制层")
public class MaterialDpDemandLinesAttributeController extends BaseController {
    @Autowired
    private MaterialDpDemandLinesAttributeService materialDpDemandLinesAttributeService;

    @PostMapping("/page")
    @ApiOperation(value = "物控指定订单使用物料属性值分页查询")
    public ResponseEntity<Results<Page<MaterialDpDemandLinesAttributeDTO>>> page(@RequestBody MaterialDpDemandLinesAttributeQuery query) {
        return Results.createSuccessRes(materialDpDemandLinesAttributeService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "物控指定订单使用物料属性值详情")
    public ResponseEntity<Results<MaterialDpDemandLinesAttributeDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(materialDpDemandLinesAttributeService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增物控指定订单使用物料属性值")
    public ResponseEntity<Results<MaterialDpDemandLinesAttributeDTO>> insert(@RequestBody MaterialDpDemandLinesAttributeDTO materialDpDemandLinesAttributeDTO) {
        validObject(materialDpDemandLinesAttributeDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(materialDpDemandLinesAttributeService.saveOrUpdate(materialDpDemandLinesAttributeDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新物控指定订单使用物料属性值")
    public ResponseEntity<Results<MaterialDpDemandLinesAttributeDTO>> update(@RequestBody MaterialDpDemandLinesAttributeDTO materialDpDemandLinesAttributeDTO) {
        validObject(materialDpDemandLinesAttributeDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(materialDpDemandLinesAttributeService.saveOrUpdate(materialDpDemandLinesAttributeDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除物控指定订单使用物料属性值")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        materialDpDemandLinesAttributeService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出物控指定订单使用物料属性值")
    @PostMapping("/export")
    public void export(@RequestBody MaterialDpDemandLinesAttributeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        materialDpDemandLinesAttributeService.export(query, response);
    }

    @ApiOperation(value = "导入物控指定订单使用物料属性值")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = materialDpDemandLinesAttributeService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}