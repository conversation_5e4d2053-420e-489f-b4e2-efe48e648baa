package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchDeliveryPlanDTO;
import com.jinkosolar.scp.mps.domain.query.MltBatteryMatchDeliveryPlanQuery;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchDeliveryPlanSaveDTO;
import com.jinkosolar.scp.mps.service.MltBatteryMatchDeliveryPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 中长期电池匹配-月到货计划 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@RestController
@RequestMapping("/mlt-battery-match-delivery-plan")
@RequiredArgsConstructor
@Api(value = "mlt-battery-match-delivery-plan", tags = "中长期电池匹配-月到货计划操作")
public class MltBatteryMatchDeliveryPlanController {
    private final MltBatteryMatchDeliveryPlanService mltBatteryMatchDeliveryPlanService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "中长期电池匹配-月到货计划分页列表", notes = "获得中长期电池匹配-月到货计划分页列表")
    public ResponseEntity<Results<Page<MltBatteryMatchDeliveryPlanDTO>>> queryByPage(@RequestBody MltBatteryMatchDeliveryPlanQuery query) {
        return Results.createSuccessRes(mltBatteryMatchDeliveryPlanService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MltBatteryMatchDeliveryPlanDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(mltBatteryMatchDeliveryPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<MltBatteryMatchDeliveryPlanDTO>> save(@Valid @RequestBody MltBatteryMatchDeliveryPlanSaveDTO saveDTO) {
        return Results.createSuccessRes(mltBatteryMatchDeliveryPlanService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        mltBatteryMatchDeliveryPlanService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MltBatteryMatchDeliveryPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mltBatteryMatchDeliveryPlanService.export(query, response);
    }

    @PostMapping("/generate")
    @ApiOperation(value = "生成数据")
    public ResponseEntity<Results<Object>> generate() {
        mltBatteryMatchDeliveryPlanService.generate(null);
        return Results.createSuccessRes();
    }
}
