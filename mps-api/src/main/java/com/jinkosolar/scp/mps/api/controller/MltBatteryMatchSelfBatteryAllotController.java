package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchSelfBatteryAllotDTO;
import com.jinkosolar.scp.mps.domain.query.MltBatteryMatchSelfBatteryAllotQuery;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchSelfBatteryAllotSaveDTO;
import com.jinkosolar.scp.mps.service.MltBatteryMatchSelfBatteryAllotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 中长期电池匹配-自产电池调拨 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:45
 */
@RestController
@RequestMapping("/mlt-battery-match-self-battery-allot")
@RequiredArgsConstructor
@Api(value = "mlt-battery-match-self-battery-allot", tags = "中长期电池匹配-自产电池调拨操作")
public class MltBatteryMatchSelfBatteryAllotController {
    private final MltBatteryMatchSelfBatteryAllotService mltBatteryMatchSelfBatteryAllotService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "中长期电池匹配-自产电池调拨分页列表", notes = "获得中长期电池匹配-自产电池调拨分页列表")
    public ResponseEntity<Results<Page<MltBatteryMatchSelfBatteryAllotDTO>>> queryByPage(@RequestBody MltBatteryMatchSelfBatteryAllotQuery query) {
        return Results.createSuccessRes(mltBatteryMatchSelfBatteryAllotService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<MltBatteryMatchSelfBatteryAllotDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(mltBatteryMatchSelfBatteryAllotService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<MltBatteryMatchSelfBatteryAllotDTO>> save(@Valid @RequestBody MltBatteryMatchSelfBatteryAllotSaveDTO saveDTO) {
        return Results.createSuccessRes(mltBatteryMatchSelfBatteryAllotService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        mltBatteryMatchSelfBatteryAllotService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody MltBatteryMatchSelfBatteryAllotQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        mltBatteryMatchSelfBatteryAllotService.export(query, response);
    }

    @PostMapping("/generate")
    @ApiOperation(value = "生成数据")
    public ResponseEntity<Results<Object>> generate() {
        mltBatteryMatchSelfBatteryAllotService.generate(null);
        return Results.createSuccessRes();
    }
}
