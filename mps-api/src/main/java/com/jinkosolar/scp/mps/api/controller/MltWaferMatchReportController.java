package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.MltWaferMatchReportDTO;
import com.jinkosolar.scp.mps.domain.dto.MltWaferMatchSummaryReportDTO;
import com.jinkosolar.scp.mps.domain.query.MltBatteryMatchReportQuery;
import com.jinkosolar.scp.mps.service.MltWaferMatchReportService;
import com.jinkosolar.scp.mps.service.MltWaferMatchSummaryReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 中长期电池匹配-历史实投 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@RestController
@RequestMapping("/mlt-wafer-match-report")
@RequiredArgsConstructor
@Api(value = "mlt-battery-match-report", tags = "中长期硅片匹配-报表")
public class MltWaferMatchReportController {
    private final MltWaferMatchReportService mltWaferMatchReportService;

    private final MltWaferMatchSummaryReportService mltWaferMatchSummaryReportService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/report")
    @ApiOperation(value = "中长期电池匹配-历史实投分页列表", notes = "获得中长期电池匹配-历史实投分页列表")
    public ResponseEntity<Results<List<MltWaferMatchReportDTO>>> report(@RequestBody MltBatteryMatchReportQuery query) {
        return Results.createSuccessRes(mltWaferMatchReportService.report(query));
    }

    @PostMapping("/generate")
    @ApiOperation(value = "生成数据")
    public ResponseEntity<Results<Object>> generate() {
        mltWaferMatchReportService.generate();
        return Results.createSuccessRes();
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/summary/report")
    @ApiOperation(value = "硅片匹配汇总报表", notes = "")
    public ResponseEntity<Results<List<MltWaferMatchSummaryReportDTO>>> summaryReport(@RequestBody MltBatteryMatchReportQuery query) {
        return Results.createSuccessRes(mltWaferMatchSummaryReportService.report(query));
    }
}
