package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.ModuleActualProductionQuantityDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.ActualProductionQuantitySyncMESDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.ModuleActualProductionQuantitySyncDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleActualProductionQuantityQuery;
import com.jinkosolar.scp.mps.service.ModuleActualProductionQuantityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * MES组件实投数量相关操作控制层
 *
 * <AUTHOR> 2024-05-06 20:10:00
 */
@Slf4j
@RequestMapping(value = "/module-actual-production-quantity")
@RestController
@Api(value = "moduleActualProductionQuantity", tags = "MES组件实投数量相关操作控制层")
public class ModuleActualProductionQuantityController extends BaseController {
    @Autowired
    private ModuleActualProductionQuantityService moduleActualProductionQuantityService;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 接收List<xxx> 参数，须在请求头中添加 accesstoken ，详见{@link com.ibm.scp.common.api.interceptor.SessionInterceptor}
     *
     * @param actualProductionDTO MES同步数据JIP入参
     * @return 同步结果
     */
    @JipFeignLog
    @PostMapping("/sync")
    @ApiOperation(value = "同步MES组件实投数量")
    public JipResponseData sync(@RequestBody ActualProductionQuantitySyncMESDTO actualProductionDTO) {
        if (actualProductionDTO.getItemDataList() == null || actualProductionDTO.getItemDataList().isEmpty()) {
            return JipResponseData.error("接口入参错误，IT_DATA 值为空");
        }
        if (actualProductionDTO.getInfo() == null) {
            return JipResponseData.error("接口入参错误，IS_BC_INFO 值为空");
        }
        if (StringUtils.isEmpty(actualProductionDTO.getInfo().getMsgId())) {
            return JipResponseData.error("接口入参错误，IS_BC_INFO 对象属性[MSGID] 值为空");
        }
        if (StringUtils.isEmpty(actualProductionDTO.getInfo().getUuid())) {
            return JipResponseData.error("接口入参错误，IS_BC_INFO 对象属性[UUID] 值为空");
        }
        // 使用msgId增加redis锁
        String lockKey = actualProductionDTO.getInfo().getMsgId();
        RLock lock = redissonClient.getLock(lockKey);
        /*boolean locked1 = lock.isLocked();
        if (locked1) {
            return JipResponseData.error(MessageHelper.getMessage("mps.error.running.task").getDesc());
        }*/
        try {
            //加分布式锁
            boolean locked = lock.tryLock(5, TimeUnit.MINUTES);
            if (!locked) {
                return JipResponseData.error(MessageHelper.getMessage("mps.error.running.task").getDesc());
            }
            List<ModuleActualProductionQuantitySyncDTO> dataList = actualProductionDTO.getItemDataList();
            dataList.forEach(item -> validObject(item, ValidGroups.Insert.class));
            moduleActualProductionQuantityService.sync(dataList);
        } catch (BizException e) {
            log.error("业务异常：MES组件实投数量数据同步失败", e);
            return JipResponseData.error(MessageHelper.getMessage(e.getI18nCode(), e.getParameters()).getDesc());
        } catch (Exception e) {
            log.error("MES组件实投数量数据同步失败", e);
            return JipResponseData.error(MessageHelper.getMessage("error.system").getDesc());
        } finally {
            lock.unlock();
        }
        return JipResponseData.success();
    }

    @PostMapping("/page")
    @ApiOperation(value = "MES组件实投数量分页查询")
    public ResponseEntity<Results<Page<ModuleActualProductionQuantityDTO>>> page(@RequestBody ModuleActualProductionQuantityQuery query) {
        return Results.createSuccessRes(moduleActualProductionQuantityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "MES组件实投数量详情")
    public ResponseEntity<Results<ModuleActualProductionQuantityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleActualProductionQuantityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增MES组件实投数量")
    public ResponseEntity<Results<ModuleActualProductionQuantityDTO>> insert(@RequestBody ModuleActualProductionQuantityDTO moduleActualProductionQuantityDTO) {
        validObject(moduleActualProductionQuantityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleActualProductionQuantityService.saveOrUpdate(moduleActualProductionQuantityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新MES组件实投数量")
    public ResponseEntity<Results<ModuleActualProductionQuantityDTO>> update(@RequestBody ModuleActualProductionQuantityDTO moduleActualProductionQuantityDTO) {
        validObject(moduleActualProductionQuantityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleActualProductionQuantityService.saveOrUpdate(moduleActualProductionQuantityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除MES组件实投数量")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleActualProductionQuantityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出MES组件实投数量")
    @PostMapping("/export")
    public void export(@RequestBody ModuleActualProductionQuantityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleActualProductionQuantityService.export(query, response);
    }

    @ApiOperation(value = "导入MES组件实投数量")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleActualProductionQuantityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/findBySalesOrderNo")
    @ApiOperation(value = "根据销售单号批量查询排产数量")
    public ResponseEntity<Results<List<ModuleActualProductionQuantityDTO>>> findBySalesOrderNo(@RequestBody List<String> salesOrderNo) {
        return Results.createSuccessRes(moduleActualProductionQuantityService.findBySalesOrderNo(salesOrderNo));
    }

}