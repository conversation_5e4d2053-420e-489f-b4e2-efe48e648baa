package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleActualProductionQuantitySnapshotDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleActualProductionQuantitySnapshotQuery;
import com.jinkosolar.scp.mps.service.ModuleActualProductionQuantitySnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MES组件实投数量快照表相关操作控制层
 * 
 * <AUTHOR> 2024-06-25 16:44:27
 */
@RequestMapping(value = "/module-actual-production-quantity-snapshot")
@RestController
@Api(value = "moduleActualProductionQuantitySnapshot", tags = "MES组件实投数量快照表相关操作控制层")
public class ModuleActualProductionQuantitySnapshotController extends BaseController {
    @Autowired
    private ModuleActualProductionQuantitySnapshotService moduleActualProductionQuantitySnapshotService;

    @PostMapping("/page")
    @ApiOperation(value = "MES组件实投数量快照表分页查询")
    public ResponseEntity<Results<Page<ModuleActualProductionQuantitySnapshotDTO>>> page(@RequestBody ModuleActualProductionQuantitySnapshotQuery query) {
        return Results.createSuccessRes(moduleActualProductionQuantitySnapshotService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "MES组件实投数量快照表详情")
    public ResponseEntity<Results<ModuleActualProductionQuantitySnapshotDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleActualProductionQuantitySnapshotService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增MES组件实投数量快照表")
    public ResponseEntity<Results<ModuleActualProductionQuantitySnapshotDTO>> insert(@RequestBody ModuleActualProductionQuantitySnapshotDTO moduleActualProductionQuantitySnapshotDTO) {
        validObject(moduleActualProductionQuantitySnapshotDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleActualProductionQuantitySnapshotService.saveOrUpdate(moduleActualProductionQuantitySnapshotDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新MES组件实投数量快照表")
    public ResponseEntity<Results<ModuleActualProductionQuantitySnapshotDTO>> update(@RequestBody ModuleActualProductionQuantitySnapshotDTO moduleActualProductionQuantitySnapshotDTO) {
        validObject(moduleActualProductionQuantitySnapshotDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleActualProductionQuantitySnapshotService.saveOrUpdate(moduleActualProductionQuantitySnapshotDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除MES组件实投数量快照表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleActualProductionQuantitySnapshotService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出MES组件实投数量快照表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleActualProductionQuantitySnapshotQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleActualProductionQuantitySnapshotService.export(query, response);
    }

    @ApiOperation(value = "导入MES组件实投数量快照表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleActualProductionQuantitySnapshotService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}