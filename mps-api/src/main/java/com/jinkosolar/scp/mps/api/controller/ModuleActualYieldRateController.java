package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.bi.PostActualyieldRateRequest;
import com.jinkosolar.scp.jip.api.dto.bi.PostActualyieldRateResponse;
import com.jinkosolar.scp.jip.api.dto.mps.SyncSWTwoHourOutPutRecordResponse;
import com.jinkosolar.scp.jip.api.service.SyncpostActuallyieldRateService;
import com.jinkosolar.scp.mps.domain.dto.ModuleActualYieldRateDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.ModuleActualYieldRateSyncDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleActualYieldRateQuery;
import com.jinkosolar.scp.mps.service.ModuleActualYieldRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BI组件实际良率相关操作控制层
 *
 * <AUTHOR> 2024-05-07 10:46:33
 */
@RequestMapping(value = "/module-actual-yield-rate")
@RestController
@Api(value = "moduleActualYieldRate", tags = "BI组件实际良率相关操作控制层")
public class ModuleActualYieldRateController extends BaseController {
    @Autowired
    private ModuleActualYieldRateService moduleActualYieldRateService;

    @JipFeignLog
    @PostMapping("/sync")
    @ApiOperation(value = "同步BI组件实际良率")
    public JipResponseData sync(@RequestBody ModuleActualYieldRateSyncDTO moduleActualYieldRateSyncDTO) {
        validObject(moduleActualYieldRateSyncDTO, ValidGroups.Insert.class);
        moduleActualYieldRateService.sync(moduleActualYieldRateSyncDTO);
        return JipResponseData.success();
    }


    @JipFeignLog
    @PostMapping("/syncModuleActualYieldRate")
    @ApiOperation(value = "定时同步BI组件实际良率")
    public JipResponseData syncsyncModuleActualYieldRate(@RequestBody PostActualyieldRateRequest query) {
        //TODO 这里要处理数据的存储
        moduleActualYieldRateService.save(query);
        return JipResponseData.success();
    }

    @PostMapping("/page")
    @ApiOperation(value = "BI组件实际良率分页查询")
    public ResponseEntity<Results<Page<ModuleActualYieldRateDTO>>> page(@RequestBody ModuleActualYieldRateQuery query) {
        return Results.createSuccessRes(moduleActualYieldRateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "BI组件实际良率详情")
    public ResponseEntity<Results<ModuleActualYieldRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleActualYieldRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增BI组件实际良率")
    public ResponseEntity<Results<ModuleActualYieldRateDTO>> insert(@RequestBody ModuleActualYieldRateDTO moduleActualYieldRateDTO) {
        validObject(moduleActualYieldRateDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleActualYieldRateService.saveOrUpdate(moduleActualYieldRateDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新BI组件实际良率")
    public ResponseEntity<Results<ModuleActualYieldRateDTO>> update(@RequestBody ModuleActualYieldRateDTO moduleActualYieldRateDTO) {
        validObject(moduleActualYieldRateDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleActualYieldRateService.saveOrUpdate(moduleActualYieldRateDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除BI组件实际良率")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleActualYieldRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出BI组件实际良率")
    @PostMapping("/export")
    public void export(@RequestBody ModuleActualYieldRateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleActualYieldRateService.export(query, response);
    }

    @ApiOperation(value = "导入BI组件实际良率")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleActualYieldRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}