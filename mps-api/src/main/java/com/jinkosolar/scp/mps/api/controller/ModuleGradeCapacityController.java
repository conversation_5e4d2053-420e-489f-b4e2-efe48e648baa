package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleGradeCapacityDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleGradeCapacityPageDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleGradeCapacityDynamicQuery;
import com.jinkosolar.scp.mps.domain.query.ModuleGradeCapacityQuery;
import com.jinkosolar.scp.mps.domain.save.ApprovalDTO;
import com.jinkosolar.scp.mps.domain.save.ProductPlanSaveDTO;
import com.jinkosolar.scp.mps.service.ModuleGradeCapacityDynamicService;
import com.jinkosolar.scp.mps.service.ModuleGradeCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 爬坡产能相关操作控制层
 *
 * <AUTHOR> 2024-04-22 14:30:28
 */
@RequestMapping(value = "/module-grade-capacity")
@RestController
@Api(value = "moduleGradeCapacity", tags = "爬坡产能相关操作控制层")
public class ModuleGradeCapacityController extends BaseController {
    @Autowired
    private ModuleGradeCapacityService moduleGradeCapacityService;
    @Autowired
    private ModuleGradeCapacityDynamicService moduleGradeCapacityDynamicService;

    @PostMapping("/versions/{type}")
    @ApiOperation(value = "查询版本号")
    @ApiParam(name = "type", value = "版型类型,1：爬坡，2：实验", required = true)
    public ResponseEntity<Results<List<String>>> versions(@PathVariable("type") Integer type) {
        return Results.createSuccessRes(moduleGradeCapacityService.versions(type));
    }

    @PostMapping("/list")
    @ApiOperation(value = "爬坡产能版本号查询")
    public ResponseEntity<Results<List<String>>> list() {
        return Results.createSuccessRes(moduleGradeCapacityService.list());
    }

    @PostMapping("/page")
    @ApiOperation(value = "爬坡产能按月分布分页查询")
    public ResponseEntity<Results<Page<ModuleGradeCapacityDTO>>> page(@RequestBody ModuleGradeCapacityQuery query) {
        return Results.createSuccessRes(moduleGradeCapacityService.page(query));
    }

    @PostMapping("/page/summary")
    @ApiOperation(value = "爬坡产能按年分布分页查询")
    public ResponseEntity<Results<Page<ModuleGradeCapacityPageDTO>>> pageSummary(@RequestBody ModuleGradeCapacityQuery query) {
        return Results.createSuccessRes(moduleGradeCapacityService.pageSummary(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "爬坡产能详情")
    public ResponseEntity<Results<ModuleGradeCapacityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleGradeCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增爬坡产能")
    public ResponseEntity<Results<ModuleGradeCapacityDTO>> insert(@RequestBody ModuleGradeCapacityDTO moduleGradeCapacityDTO) {
        validObject(moduleGradeCapacityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleGradeCapacityService.saveOrUpdate(moduleGradeCapacityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新爬坡产能")
    public ResponseEntity<Results<ModuleGradeCapacityDTO>> update(@RequestBody ModuleGradeCapacityDTO moduleGradeCapacityDTO) {
        validObject(moduleGradeCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleGradeCapacityService.saveOrUpdate(moduleGradeCapacityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除爬坡产能")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleGradeCapacityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出爬坡产能明细")
    @PostMapping("/export")
    public void export(@RequestBody ModuleGradeCapacityQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        moduleGradeCapacityService.export(query, response);
    }

    @ApiOperation(value = "导出爬坡产能汇总")
    @PostMapping("/export/summary")
    public void exportSummary(@RequestBody ModuleGradeCapacityQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        moduleGradeCapacityService.exportSummary(query, response);
    }

    @ApiOperation(value = "导入爬坡产能")
    @PostMapping("/import/{type}")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @PathVariable("type") Integer type,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleGradeCapacityService.importData(multipartFile, type, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "对接asprove")
    @PostMapping("/calculate")
    public ResponseEntity<Results<Boolean>> calculate(@RequestBody(required = false) ModuleGradeCapacityDynamicQuery query) {
        //CompletableFuture.runAsync(()->{
        //moduleGradeCapacityService.calculate();
        moduleGradeCapacityDynamicService.calculate(query);
        //});
        return Results.createSuccessRes();
    }


    @PostMapping("/doApproval")
    @ApiOperation(value = "审批操作")
    public ResponseEntity<Results<Object>> doApproval(@RequestBody ApprovalDTO query) {
        moduleGradeCapacityService.doApproval(query.getIds(), query.getApprovalType());
        return Results.createSuccessRes();
    }
}