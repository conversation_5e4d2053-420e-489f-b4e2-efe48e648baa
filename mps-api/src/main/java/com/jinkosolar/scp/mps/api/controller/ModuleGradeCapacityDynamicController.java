package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleGradeCapacityDynamicDTO;
import com.jinkosolar.scp.mps.domain.dto.OpenlineNumDynamicDTO;
import com.jinkosolar.scp.mps.domain.dto.ProductYieldThicknessDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleGradeCapacityDynamicQuery;
import com.jinkosolar.scp.mps.service.ModuleGradeCapacityDynamicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 爬坡产能-动态相关操作控制层
 * 
 * <AUTHOR> 2024-08-14 10:49:49
 */
@RequestMapping(value = "/module-grade-capacity-dynamic")
@RestController
@Api(value = "moduleGradeCapacityDynamic", tags = "爬坡产能-动态相关操作控制层")
@RequiredArgsConstructor  
public class ModuleGradeCapacityDynamicController extends BaseController {    
    private final ModuleGradeCapacityDynamicService moduleGradeCapacityDynamicService; 

    @PostMapping("/page")
    @ApiOperation(value = "爬坡产能-动态分页查询")
    public ResponseEntity<Results<Page<ModuleGradeCapacityDynamicDTO>>> page(@RequestBody ModuleGradeCapacityDynamicQuery query) {
        return Results.createSuccessRes(moduleGradeCapacityDynamicService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "爬坡产能-动态详情")
    public ResponseEntity<Results<ModuleGradeCapacityDynamicDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleGradeCapacityDynamicService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增爬坡产能-动态")
    public ResponseEntity<Results<Void>> insert(@RequestBody ModuleGradeCapacityDynamicDTO moduleGradeCapacityDynamicDTO) {
        validObject(moduleGradeCapacityDynamicDTO, ValidGroups.Insert.class);
        moduleGradeCapacityDynamicService.saveOrUpdate(moduleGradeCapacityDynamicDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新爬坡产能-动态")
    public ResponseEntity<Results<Void>> update(@RequestBody ModuleGradeCapacityDynamicDTO moduleGradeCapacityDynamicDTO) {
        validObject(moduleGradeCapacityDynamicDTO, ValidGroups.Update.class);
        moduleGradeCapacityDynamicService.saveOrUpdate(moduleGradeCapacityDynamicDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除爬坡产能-动态")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleGradeCapacityDynamicService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出爬坡产能-动态")
    @PostMapping("/export")
    public void export(@RequestBody ModuleGradeCapacityDynamicQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleGradeCapacityDynamicService.export(query, response);
    }

    @ApiOperation(value = "导入爬坡产能-动态")
    @PostMapping("/import/{type}")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara,
                                                               @PathVariable("type") Integer type,
                                                               @RequestPart("openlineNumDynamicDTO") Map<String, LocalDate> map) {

        ModuleGradeCapacityDynamicQuery moduleGradeCapacityDynamicQuery=new ModuleGradeCapacityDynamicQuery();
        moduleGradeCapacityDynamicQuery.setStartWorkTime(map.get("startDate"));
        moduleGradeCapacityDynamicQuery.setEndWorkTime(map.get("endDate"));
        ImportResultDTO importResultDTO = moduleGradeCapacityDynamicService.importData(multipartFile, excelPara,type,moduleGradeCapacityDynamicQuery);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @PostMapping("/saveBatch")
    @ApiOperation(value = "批量修改动态爬坡产能")
    public ResponseEntity<Results<Boolean>> saveBatch(@RequestBody ModuleGradeCapacityDynamicDTO moduleGradeCapacityDynamicDTO) {
        return Results.createSuccessRes(moduleGradeCapacityDynamicService.saveBatch(moduleGradeCapacityDynamicDTO));
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "批量删除动态爬坡产能")
    public ResponseEntity<Results<Object>> deleteBatch(@RequestBody  List<ModuleGradeCapacityDynamicDTO> moduleGradeCapacityDynamicDTOS) {
        moduleGradeCapacityDynamicService.deleteBatch(moduleGradeCapacityDynamicDTOS);
        return Results.createSuccessRes();
    }

}