package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleGradeCapacityDynamicDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleOemCapacityDynamicDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleOemCapacityDynamicQuery;
import com.jinkosolar.scp.mps.service.ModuleOemCapacityDynamicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件OEM产能-动态相关操作控制层
 * 
 * <AUTHOR> 2024-08-16 14:35:49
 */
@RequestMapping(value = "/module-oem-capacity-dynamic")
@RestController
@Api(value = "moduleOemCapacityDynamic", tags = "组件OEM产能-动态相关操作控制层")
@RequiredArgsConstructor  
public class ModuleOemCapacityDynamicController extends BaseController {    
    private final ModuleOemCapacityDynamicService moduleOemCapacityDynamicService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件OEM产能-动态分页查询")
    public ResponseEntity<Results<Page<ModuleOemCapacityDynamicDTO>>> page(@RequestBody ModuleOemCapacityDynamicQuery query) {
        return Results.createSuccessRes(moduleOemCapacityDynamicService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件OEM产能-动态详情")
    public ResponseEntity<Results<ModuleOemCapacityDynamicDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleOemCapacityDynamicService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件OEM产能-动态")
    public ResponseEntity<Results<Void>> insert(@RequestBody ModuleOemCapacityDynamicDTO moduleOemCapacityDynamicDTO) {
        validObject(moduleOemCapacityDynamicDTO, ValidGroups.Insert.class);
        moduleOemCapacityDynamicService.saveOrUpdate(moduleOemCapacityDynamicDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件OEM产能-动态")
    public ResponseEntity<Results<Void>> update(@RequestBody ModuleOemCapacityDynamicDTO moduleOemCapacityDynamicDTO) {
        validObject(moduleOemCapacityDynamicDTO, ValidGroups.Update.class);
        moduleOemCapacityDynamicService.saveOrUpdate(moduleOemCapacityDynamicDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件OEM产能-动态")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleOemCapacityDynamicService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件OEM产能-动态")
    @PostMapping("/export")
    public void export(@RequestBody ModuleOemCapacityDynamicQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleOemCapacityDynamicService.export(query, response);
    }

    @ApiOperation(value = "导入组件OEM产能-动态")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleOemCapacityDynamicService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/saveBatch")
    @ApiOperation(value = "批量修改动态爬坡产能")
    public ResponseEntity<Results<Boolean>> saveBatch(@RequestBody ModuleOemCapacityDynamicDTO moduleOemCapacityDynamicDTO) {
        return Results.createSuccessRes(moduleOemCapacityDynamicService.saveBatch(moduleOemCapacityDynamicDTO));
    }


    @PostMapping("/deleteBatch")
    @ApiOperation(value = "批量删除动态爬坡产能")
    public ResponseEntity<Results<Object>> deleteBatch(@RequestBody  List<ModuleOemCapacityDynamicDTO> moduleOemCapacityDynamicDTOS) {
        moduleOemCapacityDynamicService.deleteBatch(moduleOemCapacityDynamicDTOS);
        return Results.createSuccessRes();
    }
}