package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModulePlanAmendmentReportDTO;
import com.jinkosolar.scp.mps.domain.query.ModulePlanAmendmentReportQuery;
import com.jinkosolar.scp.mps.service.ModulePlanAmendmentReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件生产计划改单明细报表相关操作控制层
 * 
 * <AUTHOR> 2024-08-20 19:20:43
 */
@RequestMapping(value = "/module-plan-amendment-report")
@RestController
@Api(value = "modulePlanAmendmentReport", tags = "组件生产计划改单明细报表相关操作控制层")
@RequiredArgsConstructor  
public class ModulePlanAmendmentReportController extends BaseController {    
    private final ModulePlanAmendmentReportService modulePlanAmendmentReportService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件生产计划改单明细报表分页查询")
    public ResponseEntity<Results<Page<ModulePlanAmendmentReportDTO>>> page(@RequestBody ModulePlanAmendmentReportQuery query) {
        return Results.createSuccessRes(modulePlanAmendmentReportService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件生产计划改单明细报表详情")
    public ResponseEntity<Results<ModulePlanAmendmentReportDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(modulePlanAmendmentReportService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件生产计划改单明细报表")
    public ResponseEntity<Results<Void>> insert(@RequestBody ModulePlanAmendmentReportDTO modulePlanAmendmentReportDTO) {
        validObject(modulePlanAmendmentReportDTO, ValidGroups.Insert.class);
        modulePlanAmendmentReportService.saveOrUpdate(modulePlanAmendmentReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件生产计划改单明细报表")
    public ResponseEntity<Results<Void>> update(@RequestBody ModulePlanAmendmentReportDTO modulePlanAmendmentReportDTO) {
        validObject(modulePlanAmendmentReportDTO, ValidGroups.Update.class);
        modulePlanAmendmentReportService.saveOrUpdate(modulePlanAmendmentReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件生产计划改单明细报表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        modulePlanAmendmentReportService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件生产计划改单明细报表")
    @PostMapping("/export")
    public void export(@RequestBody ModulePlanAmendmentReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        modulePlanAmendmentReportService.export(query, response);
    }

    @ApiOperation(value = "导入组件生产计划改单明细报表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = modulePlanAmendmentReportService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/scpVersionList")
    @ApiOperation(value = "scp版本号下拉")
    public ResponseEntity<Results<List<String>>> scpVersionList() {
        return Results.createSuccessRes(modulePlanAmendmentReportService.scpVersionList());
    }
}