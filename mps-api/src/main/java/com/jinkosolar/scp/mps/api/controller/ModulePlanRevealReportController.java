package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModulePlanRevealReportDTO;
import com.jinkosolar.scp.mps.domain.query.ModulePlanRevealReportQuery;
import com.jinkosolar.scp.mps.service.ModulePlanRevealReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件生产计划兜底报表相关操作控制层
 * 
 * <AUTHOR> 2024-09-06 22:10:38
 */
@RequestMapping(value = "/module-plan-reveal-report")
@RestController
@Api(value = "modulePlanRevealReport", tags = "组件生产计划兜底报表相关操作控制层")
@RequiredArgsConstructor  
public class ModulePlanRevealReportController extends BaseController {    
    private final ModulePlanRevealReportService modulePlanRevealReportService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件生产计划兜底报表分页查询")
    public ResponseEntity<Results<Page<ModulePlanRevealReportDTO>>> page(@RequestBody ModulePlanRevealReportQuery query) {
        return Results.createSuccessRes(modulePlanRevealReportService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件生产计划兜底报表详情")
    public ResponseEntity<Results<ModulePlanRevealReportDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(modulePlanRevealReportService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件生产计划兜底报表")
    public ResponseEntity<Results<Void>> insert(@RequestBody ModulePlanRevealReportDTO modulePlanRevealReportDTO) {
        validObject(modulePlanRevealReportDTO, ValidGroups.Insert.class);
        modulePlanRevealReportService.saveOrUpdate(modulePlanRevealReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件生产计划兜底报表")
    public ResponseEntity<Results<Void>> update(@RequestBody ModulePlanRevealReportDTO modulePlanRevealReportDTO) {
        validObject(modulePlanRevealReportDTO, ValidGroups.Update.class);
        modulePlanRevealReportService.saveOrUpdate(modulePlanRevealReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件生产计划兜底报表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        modulePlanRevealReportService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件生产计划兜底报表")
    @PostMapping("/export")
    public void export(@RequestBody ModulePlanRevealReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        modulePlanRevealReportService.export(query, response);
    }

    @ApiOperation(value = "导入组件生产计划兜底报表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = modulePlanRevealReportService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}