package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityApsDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionEmptyCapacityApsQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionEmptyCapacityApsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件空产能计算传APS表相关操作控制层
 * 
 * <AUTHOR> 2024-06-26 10:54:10
 */
@RequestMapping(value = "/module-production-empty-capacity-aps")
@RestController
@Api(value = "moduleProductionEmptyCapacityAps", tags = "组件空产能计算传APS表相关操作控制层")
public class ModuleProductionEmptyCapacityApsController extends BaseController {
    @Autowired
    private ModuleProductionEmptyCapacityApsService moduleProductionEmptyCapacityApsService;

    @PostMapping("/page")
    @ApiOperation(value = "组件空产能计算传APS表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionEmptyCapacityApsDTO>>> page(@RequestBody ModuleProductionEmptyCapacityApsQuery query) {
        return Results.createSuccessRes(moduleProductionEmptyCapacityApsService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件空产能计算传APS表详情")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityApsDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionEmptyCapacityApsService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件空产能计算传APS表")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityApsDTO>> insert(@RequestBody ModuleProductionEmptyCapacityApsDTO moduleProductionEmptyCapacityApsDTO) {
        validObject(moduleProductionEmptyCapacityApsDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleProductionEmptyCapacityApsService.saveOrUpdate(moduleProductionEmptyCapacityApsDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件空产能计算传APS表")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityApsDTO>> update(@RequestBody ModuleProductionEmptyCapacityApsDTO moduleProductionEmptyCapacityApsDTO) {
        validObject(moduleProductionEmptyCapacityApsDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleProductionEmptyCapacityApsService.saveOrUpdate(moduleProductionEmptyCapacityApsDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件空产能计算传APS表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionEmptyCapacityApsService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件空产能计算传APS表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionEmptyCapacityApsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionEmptyCapacityApsService.export(query, response);
    }

    @ApiOperation(value = "导入组件空产能计算传APS表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleProductionEmptyCapacityApsService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/executeProductionCapacity")
    @ApiOperation(value = "执行组件排产空产能逻辑")
    public ResponseEntity<Results<Object>> executeProductionCapacity() {
        try {
            // 计算空产能逻辑
            Boolean result = moduleProductionEmptyCapacityApsService.executeProductionCapacity();
            // 推送同步表结构数据
            moduleProductionEmptyCapacityApsService.syncProductionCapacityAps();
            // 返回结果
            return Results.createAsprovaSuccessRes(result);
        }catch (Exception e){
            return Results.createAsprovaFailRes(e.toString(), "A0001");
        }
    }
}