package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionEmptyCapacityQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionEmptyCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组件排产空产能表相关操作控制层
 *
 * <AUTHOR> 2024-06-25 13:07:43
 */
@RequestMapping(value = "/module-production-empty-capacity")
@RestController
@Api(value = "moduleProductionEmptyCapacity", tags = "组件排产空产能表相关操作控制层")
public class ModuleProductionEmptyCapacityController extends BaseController {
    @Autowired
    private ModuleProductionEmptyCapacityService moduleProductionEmptyCapacityService;

    @PostMapping("/page")
    @ApiOperation(value = "组件排产空产能表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionEmptyCapacityDTO>>> page(@RequestBody ModuleProductionEmptyCapacityQuery query) {
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.page(query));
    }

    @PostMapping("/list")
    @ApiOperation(value = "组件排产空产能表分页查询")
    public ResponseEntity<Results<List<ModuleProductionEmptyCapacityDTO>>> list(@RequestBody ModuleProductionEmptyCapacityQuery query) {
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.list(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件排产空产能表详情")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件排产空产能表")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityDTO>> insert(@RequestBody ModuleProductionEmptyCapacityDTO moduleProductionEmptyCapacityDTO) {
        validObject(moduleProductionEmptyCapacityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.saveOrUpdate(moduleProductionEmptyCapacityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件排产空产能表")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityDTO>> update(@RequestBody ModuleProductionEmptyCapacityDTO moduleProductionEmptyCapacityDTO) {
        validObject(moduleProductionEmptyCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.saveOrUpdate(moduleProductionEmptyCapacityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件排产空产能表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionEmptyCapacityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件排产空产能表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionEmptyCapacityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionEmptyCapacityService.export(query, response);
    }

    @ApiOperation(value = "导入组件排产空产能表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleProductionEmptyCapacityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/queryTempExcessProduction4OrderQuery")
    @ApiOperation(value = "询单获取空产能数据", notes = "询单获取空产能数据")
    public ResponseEntity<Results<List<Map<String, String>>>> queryTempExcessProduction4OrderQuery() {
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.queryTempExcessProduction4OrderQuery());
    }

    @PostMapping("/findProductionEmptyCapacityByCondition")
    @ApiOperation(value = "获取空产能数据", notes = "获取空产能数据")
    public ResponseEntity<Results<List<ModuleProductionEmptyCapacityDTO>>> findProductionEmptyCapacityByCondition(@RequestBody ModuleProductionEmptyCapacityQuery query) {
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.findProductionEmptyCapacityByCondition(query));
    }

    @PostMapping("/pageList")
    @ApiOperation(value = "获取空产能数据", notes = "获取空产能数据")
    public ResponseEntity<Results<PageFeign<ModuleProductionEmptyCapacityDTO>>> pageList(@RequestBody ModuleProductionEmptyCapacityQuery query) {
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.pageList(query));
    }
}