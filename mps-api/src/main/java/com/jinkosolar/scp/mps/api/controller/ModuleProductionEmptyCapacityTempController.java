package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityTempDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionEmptyCapacityTempQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionEmptyCapacityTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件排产空产能临时表相关操作控制层
 * 
 * <AUTHOR> 2024-06-25 13:07:43
 */
@RequestMapping(value = "/module-production-empty-capacity-temp")
@RestController
@Api(value = "moduleProductionEmptyCapacityTempController", tags = "组件排产空产能表相关操作控制层")
public class ModuleProductionEmptyCapacityTempController extends BaseController {
    @Autowired
    private ModuleProductionEmptyCapacityTempService moduleProductionEmptyCapacityService;

    @PostMapping("/page")
    @ApiOperation(value = "组件排产空产能表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionEmptyCapacityTempDTO>>> page(@RequestBody ModuleProductionEmptyCapacityTempQuery query) {
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件排产空产能表详情")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityTempDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件排产空产能表")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityTempDTO>> insert(@RequestBody ModuleProductionEmptyCapacityTempDTO ModuleProductionEmptyCapacityTempDTO) {
        validObject(ModuleProductionEmptyCapacityTempDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.saveOrUpdate(ModuleProductionEmptyCapacityTempDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件排产空产能表")
    public ResponseEntity<Results<ModuleProductionEmptyCapacityTempDTO>> update(@RequestBody ModuleProductionEmptyCapacityTempDTO ModuleProductionEmptyCapacityTempDTO) {
        validObject(ModuleProductionEmptyCapacityTempDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleProductionEmptyCapacityService.saveOrUpdate(ModuleProductionEmptyCapacityTempDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件排产空产能表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionEmptyCapacityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件排产空产能表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionEmptyCapacityTempQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionEmptyCapacityService.export(query, response);
    }

    @ApiOperation(value = "导入组件排产空产能表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleProductionEmptyCapacityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}