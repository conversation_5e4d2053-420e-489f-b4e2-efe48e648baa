package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanCalendarDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanCalendarQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionPlanCalendarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件计划生产日历-正式表相关操作控制层
 * 
 * <AUTHOR> 2024-12-03 20:21:46
 */
@RequestMapping(value = "/module-production-plan-calendar")
@RestController
@Api(value = "moduleProductionPlanCalendar", tags = "组件计划生产日历-正式表相关操作控制层")
@RequiredArgsConstructor  
public class ModuleProductionPlanCalendarController extends BaseController {    
    private final ModuleProductionPlanCalendarService moduleProductionPlanCalendarService; 

    @PostMapping("/page")
    @ApiOperation(value = "组件计划生产日历-正式表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionPlanCalendarDTO>>> page(@RequestBody ModuleProductionPlanCalendarQuery query) {
        return Results.createSuccessRes(moduleProductionPlanCalendarService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件计划生产日历-正式表详情")
    public ResponseEntity<Results<ModuleProductionPlanCalendarDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionPlanCalendarService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件计划生产日历-正式表")
    public ResponseEntity<Results<Void>> insert(@RequestBody ModuleProductionPlanCalendarDTO moduleProductionPlanCalendarDTO) {
        validObject(moduleProductionPlanCalendarDTO, ValidGroups.Insert.class);
        moduleProductionPlanCalendarService.saveOrUpdate(moduleProductionPlanCalendarDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件计划生产日历-正式表")
    public ResponseEntity<Results<Void>> update(@RequestBody ModuleProductionPlanCalendarDTO moduleProductionPlanCalendarDTO) {
        validObject(moduleProductionPlanCalendarDTO, ValidGroups.Update.class);
        moduleProductionPlanCalendarService.saveOrUpdate(moduleProductionPlanCalendarDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件计划生产日历-正式表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionPlanCalendarService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件计划生产日历-正式表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionPlanCalendarQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionPlanCalendarService.export(query, response);
    }

    @ApiOperation(value = "导入组件计划生产日历-正式表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleProductionPlanCalendarService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}