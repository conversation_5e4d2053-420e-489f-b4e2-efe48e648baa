package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.constant.CommonConstant;
import com.jinkosolar.scp.mps.domain.constant.MpsAttrConstant;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.convert.ModuleProductionPlanDEConvert;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.dto.feign.LovLineExtDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlan;
import com.jinkosolar.scp.mps.domain.pojo.CheckResultPOJO;
import com.jinkosolar.scp.mps.domain.query.ModulePlanAmendmentReportProcessQuery;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanQuery;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanReportCompareQuery;
import com.jinkosolar.scp.mps.domain.query.SurplusQuantitativeAnalysisQuery;
import com.jinkosolar.scp.mps.domain.repository.ModuleProductionPlanRepository;
import com.jinkosolar.scp.mps.exception.CheckResultException;
import com.jinkosolar.scp.mps.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.jinkosolar.scp.mps.domain.constant.MpsLovConstant.MODULE_PRODUCTION_PLAN_PUBLISH_LOCK;

/**
 * 组件排产计划表相关操作控制层
 *
 * <AUTHOR> 2024-05-25 11:47:36
 */
@Slf4j
@RequestMapping(value = "/module-production-plan")
@RestController
@Api(value = "moduleProductionPlan", tags = "组件排产计划表相关操作控制层")
public class ModuleProductionPlanController extends BaseController {
    public static final String LOCK_IMPORT_KEY = "module-production-plan-importFile";

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    private AsyncService asyncService;

    @Autowired
    private SurplusQuantitativeAnalysisService surplusQuantitativeAnalysisService;

    @Autowired
    private ModuleProductionPlanService moduleProductionPlanService;

    @Autowired
    private ModuleProductionPlanForMrpService moduleProductionPlanForMrpService;

    @Autowired
    private ModuleProductionPlanRepository moduleProductionPlanRepository;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ProductInStockRecordService productInStockRecordService;

    @Autowired
    private SyncTableUtils syncTableUtils;

    @Autowired
    private ModuleEmailService moduleEmailService;

    @PostMapping("/page")
    @ApiOperation(value = "组件排产计划表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionPlanReportDTO>>> page(@RequestBody ModuleProductionPlanQuery query) {
        query.setPlanVersionRemark(null);
        return Results.createSuccessRes(moduleProductionPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件排产计划表详情")
    public ResponseEntity<Results<ModuleProductionPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件排产计划表")
    public ResponseEntity<Results<ModuleProductionPlanDTO>> insert(@RequestBody ModuleProductionPlanDTO moduleProductionPlanDTO) {
        validObject(moduleProductionPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleProductionPlanService.saveOrUpdate(moduleProductionPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件排产计划表")
    public ResponseEntity<Results<ModuleProductionPlanDTO>> update(@RequestBody ModuleProductionPlanDTO moduleProductionPlanDTO) {
        validObject(moduleProductionPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleProductionPlanService.saveOrUpdate(moduleProductionPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件排产计划表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件排产计划表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setPlanVersionRemark(null);
        moduleProductionPlanService.export(query, response);
    }

    @ApiOperation(value = "导入组件排产计划表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        RLock lock = redissonClient.getLock(LOCK_IMPORT_KEY);
        if (lock.tryLock()) {
            try {
                ImportResultDTO importResultDTO = moduleProductionPlanService.importData(multipartFile, excelPara);
                if (importResultDTO.getFailMessages().isEmpty()) {
                    return Results.createSuccessRes(importResultDTO);
                }
                return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
            } finally {
                lock.unlock();
            }
        }
        throw new BizException("mps.error.importingWait");
    }

    @ApiOperation(value = "属性列表")
    @PostMapping("/listColumn")
    public ResponseEntity<Results<List<String>>> listColumn(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.listColumn(query));
    }

    @ApiOperation(value = "同步临时表数据到正式表")
    @PostMapping("/sync")
    public ResponseEntity<Results<Boolean>> sync(@RequestBody ModuleProductionPlanQuery query) {
        try {
            log.info("start sync:{}", query.getModuleType());
            return Results.createAsprovaSuccessRes(moduleProductionPlanService.sync(query.getModuleType()));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("sync error");
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
    }

    @ApiOperation(value = "待发布数据列表")
    @PostMapping("/list/unPublish")
    public ResponseEntity<Results<List<ModuleProductionPlanViewDTO>>> listUnPublish(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.listUnPublish(query));
    }

    @ApiOperation(value = "待发布版本列表")
    @PostMapping("/list/prePublishVersion")
    public ResponseEntity<Results<List<ModuleProductionPlanViewDTO>>> prePublishVersion(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.prePublishVersion(query));
    }

    @ApiOperation(value = "发布ModulePlanAmendmentReportProcess")
    @PostMapping("/doModulePlanAmendmentReportProcess")
    public ResponseEntity<Results<List<ModuleProductionPlanViewDTO>>> doModulePlanAmendmentReportProcess(@RequestBody ModulePlanAmendmentReportProcessQuery query) {
        moduleProductionPlanService.doModulePlanAmendmentReportProcess(query.getFactoryCodes(), query.getBatchNos(), query.getPlanVersion());
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "发布临时表数据到正式表")
    @PostMapping("/publish")
    public ResponseEntity<Results<CheckResultPOJO>> publish(@RequestBody List<ModuleProductionPlanPublishDTO> planPublishList) {
        if (CollectionUtils.isNotEmpty(planPublishList)) {
            // 加分布式锁
            RLock lock = redissonClient.getLock(MODULE_PRODUCTION_PLAN_PUBLISH_LOCK);
            if (lock.tryLock()) {
                try {
                    String planVersion = moduleProductionPlanService.publish(planPublishList);
                    //同步发布数据到asprova物控表
                    moduleProductionPlanService.syncToAPS(planPublishList.get(0).getDomesticOversea(), planVersion);

                    CompletableFuture.runAsync(() -> {
                        try {//同步已投产未完工
                            productInStockRecordService.selectQuantity();
                            SyncTableDTO tableDTO = new SyncTableDTO();
                            List<String> tablesList = new ArrayList<String>();
                            tablesList.add(MpsLovConstant.MRP_PRODUCTION_UNFINISHED_PLAN);
                            tablesList.add(MpsLovConstant.MRP_REALTIME_INVENTORY);
                            tablesList.add(MpsLovConstant.MRP_PURCHASE_QUOTA_DISASSEMBLE);
                            tablesList.add(MpsLovConstant.MRP_DELIVERY_PROMISE);
                            tableDTO.setLovCodes(tablesList);
                            // 推送计算后的已投产未完工数据到APS
                            syncTableUtils.syncTables(tableDTO);
                            //异步计算中长期剩余可接单量
                            log.info("开始计算中长期剩余可接单量。。。。");
                            surplusQuantitativeAnalysisService.compute(new SurplusQuantitativeAnalysisQuery(planPublishList.get(0).getDomesticOversea()));
                        } catch (Exception e) {
                            log.info("同步已投产未完工数据到APS失败", e);
                        }
                    }, threadPoolExecutor);
                    //异步计算中长期剩余可接单量
                    //asyncService.syncTask(surplusQuantitativeAnalysisService, (service) -> service.compute(new SurplusQuantitativeAnalysisQuery(planPublishList.get(0).getDomesticOversea())));
                } catch (CheckResultException checkResultException) {
                    String message = checkResultException.getMessage();
                    CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
                    checkResultPOJO.setStatus(false);
                    checkResultPOJO.setMsgs(message);
                    return Results.createSuccessRes(checkResultPOJO);
                } finally {
                    lock.unlock();
                }
            } else {
                throw new BizException("正在运算中, 请稍后再试");
            }
        }

        CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
        checkResultPOJO.setStatus(true);
        return Results.createSuccessRes(checkResultPOJO);
    }


    @ApiOperation(value = "发布临时表数据到正式表")
    @PostMapping("/prepublish")
    public ResponseEntity<Results<CheckResultPOJO>> prepublish(@RequestBody ModuleProductionPlanPrePublishDTO prePublishDTO) {
        try {
            moduleProductionPlanService.prepublish(prePublishDTO.getPlanPublishList(), CommonConstant.PREPUBLISH_STATUS, prePublishDTO.getStartDate(), prePublishDTO.getEndDate());
        } catch (CheckResultException checkResultException) {
            String message = checkResultException.getMessage();
            CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
            checkResultPOJO.setStatus(false);
            checkResultPOJO.setMsgs(message);
            return Results.createSuccessRes(checkResultPOJO);
        }

        CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
        checkResultPOJO.setStatus(true);
        return Results.createSuccessRes(checkResultPOJO);
    }

    @ApiOperation(value = "预发布校验")
    @PostMapping("/prepublish/verify")
    public ResponseEntity<Results<CheckResultPOJO>> prepublishVerify(@RequestBody List<ModuleProductionPlanPublishDTO> planPublishList) {
        try {
            moduleProductionPlanService.prepublishVerify(planPublishList);
        } catch (CheckResultException checkResultException) {
            String message = checkResultException.getMessage();
            CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
            checkResultPOJO.setStatus(false);
            checkResultPOJO.setMsgs(message);
            return Results.createSuccessRes(checkResultPOJO);
        }
        CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
        checkResultPOJO.setStatus(true);
        return Results.createSuccessRes(checkResultPOJO);
    }


    @ApiOperation(value = "属性列表")
    @PostMapping("/list/versions")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> listVersions(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.listVersions(query));
    }

    @PostMapping("/history")
    @ApiOperation(value = "组件排产计划表历史详情")
    public ResponseEntity<Results<Page<ModuleProductionPlanDTO>>> history(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.pageHistory(query));
    }

    @PostMapping("/toSetDpLinesAttrValue")
    @ApiOperation(value = "组件排产计划表分页查询")
    public ResponseEntity<Results<Object>> toSetDpLinesAttrValue(@RequestBody ModuleProductionPlanDTO moduleProductionPlanDTO) {
        ModuleProductionPlan entity = ModuleProductionPlanDEConvert.INSTANCE.toEntity(moduleProductionPlanDTO);
        entity.setTenantId(null);
        List<ModuleProductionPlan> all = moduleProductionPlanRepository.findAll(Example.of(entity));
        moduleProductionPlanService.toSetDpLinesAttrValue(all);
        return Results.createSuccessRes();
    }

    @PostMapping("/test")
    @ApiOperation(value = "组件排产计划表分页查询")
    public ResponseEntity<Results<Object>> test(@RequestBody ModuleProductionPlanDTO moduleProductionPlanDTO) {
        Map<String, AttrTypeLineDTO> attrTypeLineBySourceColumn = AttrUtils.getAttrTypeLineBySourceColumn(MpsAttrConstant.DP_ATTR_CODE, MpsAttrConstant.BOM_MATERIAL_ACCESSORY);
        Map<String, AttrTypeLineDTO> attrTypeLineBySourceColumn1 = AttrUtils.getAttrTypeLineBySourceColumn1(MpsAttrConstant.DP_ATTR_CODE, MpsAttrConstant.BOM_MATERIAL_ACCESSORY);
        List<AttrTypeLineDTO> attrDP = AttrUtils.queryAttrTypeLinesByHeaderCode(MpsAttrConstant.DP_ATTR_CODE);
        List<AttrTypeLineDTO> attrMA = AttrUtils.queryAttrTypeLinesByHeaderCode(MpsAttrConstant.BOM_MATERIAL_ACCESSORY);

        System.out.println("111");
        return Results.createSuccessRes(attrTypeLineBySourceColumn1);
    }

    @PostMapping("/productionProposalGenerationBom")
    @ApiOperation(value = "组件排产计划表分页查询")
    public ResponseEntity<Results<Object>> productionProposalGenerationBom(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        List<PlanBomsDTO> planBomsDTOList = moduleProductionPlanService.productionProposalGenerationBom(ids);
        return Results.createSuccessRes(planBomsDTOList);
    }


    @PostMapping("/pageWithMaterialInfo")
    @ApiOperation(value = "组件排产计划表分页查询-增加动态信息")
    public ResponseEntity<Results<Page<ModuleProductionPlanForMaterialSchedulingDTO>>> pageWithMaterialInfo(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.pageWithMaterialInfo(query));
    }

    @ApiOperation(value = "导出组件排产计划表-增加动态信息")
    @PostMapping("/exportWithMaterialInfo")
    public void exportWithMaterialInfo(@RequestBody ModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionPlanService.exportWithMaterialInfo(query, response);
    }

    /**
     * 需求计划获取排产计划
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "需求计划获取排产计划")
    @PostMapping("/queryPlanList")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryPlanList(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.planDTOList(query));
    }

    /**
     * 组件排产发布版本对比报表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "组件排产发布版本对比报表")
    @PostMapping("/version/compare")
    public ResponseEntity<Results<List<ModuleProductionPlanReportCompareDTO>>> compareVersionDifferent(@RequestBody ModuleProductionPlanReportCompareQuery query) {
        query.setOptType("query");
        return Results.createSuccessRes(moduleProductionPlanService.compareVersionDifferent(query));
    }

    /**
     * 导出组件排产发布版本对比报表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "导出组件排产发布版本对比报表")
    @PostMapping("/version/compare/export")
    public void exportCompareVersionDifferent(@RequestBody ModuleProductionPlanReportCompareQuery query, HttpServletResponse response) {
        moduleProductionPlanService.exportCompareVersionDifferent(query, response);
    }

    /**
     * 组件规划及排产对比报表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "组件规划及排产对比报表")
    @PostMapping("/version/moduleCompare")
    public ResponseEntity<Results<List<Map<String, Object>>>> moduleCompareDifferent(@RequestBody ModuleProductionPlanReportCompareQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.moduleCompareDifferent(query));
    }

    /**
     * 导出组件规划及排产对比报表
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "导出组件规划及排产对比报表")
    @PostMapping("/version/moduleCompare/export")
    public void exportModuleCompareDifferent(@RequestBody ModuleProductionPlanReportCompareQuery query, HttpServletResponse response) {
        moduleProductionPlanService.exportModuleCompareDifferent(query, response);
    }

    @ApiOperation(value = "询单查询排查数据-用于询单资源回退")
    @PostMapping("/queryProductionPlan4OrderQuery")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryProductionPlan4OrderQuery(@RequestBody String sapOrderNo) {
        return Results.createSuccessRes(moduleProductionPlanService.queryProductionPlan4OrderQuery(sapOrderNo));
    }

    @ApiOperation(value = "询单查询排查数据-用于询单资源回退")
    @PostMapping("/queryProductionPlanBySapNo")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryProductionPlanBySapNo(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryProductionPlanBySapNo(query));
    }

    @ApiOperation(value = "询单查询排查数据-用户沉淀电池富余量")
    @PostMapping("/queryAllProductionPlan4OrderQuery")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryAllProductionPlan4OrderQuery() {
        return Results.createSuccessRes(moduleProductionPlanService.queryAllProductionPlan4OrderQuery());
    }

    @ApiOperation(value = "查询组件排除数据，大于等于当天")
    @PostMapping("/queryDpProductionPlan")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryDpProductionPlan(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryDpProductionPlan(query));
    }

    @ApiOperation(value = "查询工厂日期最大版本排产数据")
    @PostMapping("/queryMaxVersionPlanList")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryMaxVersionPlanList(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryMaxVersionPlanList(query));
    }

    @ApiOperation(value = "查询期间内各区域最大版本")
    @PostMapping("/queryMaxVersionByOversea")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryMaxVersionByOversea(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryMaxVersionByOversea(query));
    }

    @ApiOperation(value = "查询期间内各最大版本的排程计划")
    @PostMapping("/queryPlanByVersionQuery")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryPlanByVersionQuery(@RequestBody List<ModuleProductionPlanQuery> queryList) {
        return Results.createSuccessRes(moduleProductionPlanService.queryPlanByVersionQuery(queryList));
    }

    @ApiOperation(value = "查询物料推移各最大版本的排程计划")
    @PostMapping("/queryElapseMapByVersionQuery")
    public ResponseEntity<Results<Map<String, Map<String, BigDecimal>>>> queryElapseMapByVersionQuery(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryElapseMapByVersionQuery(query));
    }

    @ApiOperation(value = "查询期间内各区域正式版本最大版本")
    @PostMapping("/queryStatusVersionByOversea")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryStatusVersionByOversea(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryStatusVersionByOversea(query.getLatestFlag()));
    }

    @ApiOperation(value = "同步数据到aps")
    @PostMapping("/sync/aps")
    public ResponseEntity<Results<String>> syncToAPS(@RequestBody ModuleProductionPlanQuery query) {
        Assert.notNull(query.getDomesticOversea(), "传入的区域不能为空");
        try {
            moduleProductionPlanService.syncToAPS(query.getDomesticOversea().toString(), query.getPlanVersion());
        } catch (Exception e) {
            log.error("syncToAPS error:", e);
            return Results.createFailRes("syncToAPS error");
        }
        return Results.createSuccessRes("syncToAPS success");
    }

    @ApiOperation(value = "排产结果发布发布“未调整版本（仅供BOM创建")
    @PostMapping("/publish4Bom")
    public ResponseEntity<Results<CheckResultPOJO>> publish4Bom(@RequestBody List<ModuleProductionPlanPublishDTO> planPublishList) {
        try {
            moduleProductionPlanService.publish4Bom(planPublishList);
        } catch (CheckResultException checkResultException) {
            String message = checkResultException.getMessage();
            CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
            checkResultPOJO.setStatus(false);
            checkResultPOJO.setMsgs(message);
            return Results.createSuccessRes(checkResultPOJO);
        }

        CheckResultPOJO checkResultPOJO = new CheckResultPOJO();
        checkResultPOJO.setStatus(true);
        return Results.createSuccessRes(checkResultPOJO);
    }

    @ApiOperation(value = "获取最小的排产时间")
    @PostMapping("/queryMinPlanDateByFactoryAndHistoryVersion")
    public ResponseEntity<Results<Map<String, LocalDateTime>>> queryMinPlanDateByFactoryAndVersion(@RequestBody ModuleProductionPlanQuery query) {
        Map<String, LocalDateTime> result = moduleProductionPlanForMrpService.queryMinPlanDateByFactoryAndHistoryVersion(query);
        return Results.createSuccessRes(result);
    }

    @ApiOperation(value = "获取订单信息")
    @PostMapping("/queryOrderByFactoryAndHistoryVersion")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryOrderByFactoryAndHistoryVersion(@RequestBody ModuleProductionPlanQuery query) {
        List<ModuleProductionPlanDTO> result = moduleProductionPlanForMrpService.queryOrderByFactoryAndHistoryVersion(query);
        return Results.createSuccessRes(result);
    }

    @PostMapping("/sendSummaryEmail")
    @ApiOperation(value = "组件排产计划-发送汇总版邮件")
    public ResponseEntity<Results<Boolean>> sendSummaryEmail(@RequestBody ProductionSendEmailDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() -> {
                    try {
                        moduleProductionPlanService.sendSummaryEmail(productionSendEmailDTO);
                    } catch (Exception e) {
                        log.error("发送汇总版邮件失败", e);
                        throw new RuntimeException(e);
                    }
                }, threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }

    @PostMapping("/getTitle")
    @ApiOperation(value = "获取标题")
    public ResponseEntity<Results<String>> getTitle(@RequestBody Long domesticOversea) {
        return Results.createSuccessRes(moduleProductionPlanService.getTitle(domesticOversea));
    }

    @PostMapping("/getEmail")
    @ApiOperation(value = "获取收件人或抄送人")
    public ResponseEntity<Results<Map<String, List<LovLineExtDTO>>>> getEmail(@RequestBody SaveEmailDTO saveEmailDTO) {
        return Results.createSuccessRes(moduleProductionPlanService.getEmail(saveEmailDTO));
    }

    @PostMapping("/getDynamicColumn")
    @ApiOperation(value = "汇总版获取动态列")
    public ResponseEntity<Results<List<String>>> getDynamicColumn(@RequestBody ModuleProductionPlanQuery query) {
        ResponseEntity<Results<List<String>>> responseEntity = null;
        try {
            responseEntity = Results.createSuccessRes(moduleProductionPlanService.getDynamicColumn(query));
        } catch (Exception e) {
            throw e;
        }
        return responseEntity;
    }

    @PostMapping("/sendSingleExternalEmail")
    @ApiOperation(value = "组件排产计划-发送单基地外部版邮件")
    public ResponseEntity<Results<Boolean>> sendSingleExternalEmail(@RequestBody ProductionSendEmailDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() -> {
                    try {
                        moduleProductionPlanService.sendSingleExternalEmail(productionSendEmailDTO);
                    } catch (Exception e) {
                        log.error("sendSingleExternalEmail", e);
                        throw e;
                    }
                }, threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }

    @PostMapping("/getDynamicColumnSingleExternal")
    @ApiOperation(value = "外部版获取动态列")
    public ResponseEntity<Results<List<String>>> getDynamicColumnSingleExternal(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.getDynamicColumnSingleExternal(query));
    }

    @PostMapping("/getSingleExternalTitle")
    @ApiOperation(value = "外部版获取标题")
    public ResponseEntity<Results<String>> getSingleExternalTitle(@RequestBody Long baseId) {
        return Results.createSuccessRes(moduleEmailService.getTitle(baseId));
    }

    @PostMapping("/getContent")
    @ApiOperation(value = "外部版获取内容")
    public ResponseEntity<Results<String>> getContent(@RequestBody ProductionSendEmailDTO productionSendEmailDTO) {
        return Results.createSuccessRes(moduleProductionPlanService.getContent(productionSendEmailDTO));
    }

    @PostMapping("/getFactory")
    @ApiOperation(value = "外部版获取工厂")
    public ResponseEntity<Results<FactorysDTO>> getFactory(@RequestBody SaveEmailDTO saveEmailDTO) {
        return Results.createSuccessRes(moduleEmailService.getFactory(saveEmailDTO));
    }

    @PostMapping("/sendSingleInternalEmail")
    @ApiOperation(value = "组件排产计划-发送单基地内部版邮件")
    public ResponseEntity<Results<Boolean>> sendSingleInternalEmail(@RequestBody ProductionSendEmailDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() ->
                moduleProductionPlanService.sendSingleInternalEmail(productionSendEmailDTO), threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }

    @PostMapping("/getDynamicColumnSingleInternal")
    @ApiOperation(value = "内部版获取动态列")
    public ResponseEntity<Results<List<String>>> getDynamicColumnSingleInternal(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.getDynamicColumnSingleInternal(query));
    }

    @PostMapping("/getSingleInternalTitle")
    @ApiOperation(value = "内部版获取标题")
    public ResponseEntity<Results<String>> getSingleInternalTitle(@RequestBody Long baseId) {
        return Results.createSuccessRes(moduleEmailService.getInternalTitle(baseId));
    }

    @PostMapping("/getBase")
    @ApiOperation(value = "外部版获取基地")
    public ResponseEntity<Results<BasesDTO>> getBase(@RequestBody Long domesticOversea) {
        return Results.createSuccessRes(moduleEmailService.getBase(domesticOversea));
    }

    @ApiOperation(value = "最大排产结束时间查询")
    @PostMapping("/queryMaxScheduleEndDate")
    public ResponseEntity<Results<ModuleProductionPlanDTO>> queryMaxScheduleEndDate(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryMaxScheduleEndDate(query));
    }

    @ApiOperation(value = "根据批次查询最大排产结束时间")
    @PostMapping("/queryMaxScheduleEndDateByBatchNos")
    public ResponseEntity<Results<ModuleProductionPlanDTO>> queryMaxScheduleEndDateByBatchNoList(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryMaxScheduleEndDateByBatchNos(query));
    }

    @ApiOperation(value = "最大排产结束时间查询")
    @PostMapping("/list")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> list(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.list(query));
    }

    /**
     * 需求计划获取排产计划->提供mrp采购配额计算使用
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "需求计划获取排产计划->提供mrp采购配额计算使用")
    @PostMapping("/queryPlanListToMrp")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> queryPlanListToMrp(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanService.queryPlanListToMrp(query));
    }

    @ApiOperation(value = "查询已排产的需求")
    @PostMapping("/findPlannedDemand")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> findPlannedDemand() {
        return Results.createSuccessRes(moduleProductionPlanService.findPlannedDemand());
    }
    /*@PostMapping("/testImage")
    @ApiOperation(value = "外部版获取基地")
    public ResponseEntity<Results<String>> testImage(@RequestBody String domesticOversea){
        return Results.createSuccessRes(moduleProductionPlanService.getImageOutputStream(domesticOversea));
    }*/
}
