package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanBaseInfoMissingReportDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanOrderDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanReportDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanBaseInfoMissingReportQuery;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionPlanBaseInfoMissingService;
import com.jinkosolar.scp.mps.service.ModuleProductionPlanReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 组件排产计划报表相关操作控制层
 *
 * <AUTHOR> 2024-08-07 10:29:10
 */
@RequestMapping(value = "/module-production-plan-report")
@RestController
@Api(value = "moduleProductionPlanReport", tags = "组件排产计划报表相关操作控制层")
@RequiredArgsConstructor
public class ModuleProductionPlanReportController extends BaseController {
    private final ModuleProductionPlanReportService moduleProductionPlanReportService;

    private final ModuleProductionPlanBaseInfoMissingService moduleProductionPlanBaseInfoMissingService;

    @PostMapping("/page")
    @ApiOperation(value = "组件排产计划报表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionPlanReportDTO>>> page(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanReportService.page(query));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件排产计划报表")
    public ResponseEntity<Results<Object>> delete(@RequestBody  ModuleProductionPlanQuery query) {
        moduleProductionPlanReportService.deleteByVersion(query.getPlanVersion());
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件排产计划报表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionPlanBaseInfoMissingReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionPlanBaseInfoMissingService.export(query, response);
    }

    @ApiOperation(value = "生成组件排产计划报表")
    @PostMapping("/generate")
    public ResponseEntity<Results<Object>> generateReport(@RequestBody ModuleProductionPlanQuery query) throws Exception {
        moduleProductionPlanReportService.generateReport(query.getPlanVersion());
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "生成全部组件排产计划报表")
    @PostMapping("/generate/all")
    public ResponseEntity<Results<Object>> generateAllReport() throws Exception {
        moduleProductionPlanReportService.generateAllReport();
        return Results.createSuccessRes();
    }


    @PostMapping("/baseInfoMissingReport")
    @ApiOperation(value = "基础信息缺失报表")
    public ResponseEntity<Results<List<ModuleProductionPlanBaseInfoMissingReportDTO>>> baseInfoMissingReport(@RequestBody ModuleProductionPlanBaseInfoMissingReportQuery query) {
        return Results.createSuccessRes(moduleProductionPlanBaseInfoMissingService.list(query));
    }

    @PostMapping("/queryOrderLineListByPlanVersion")
    @ApiOperation(value = "查询排产版本订单行数据")
    public ResponseEntity<Results<List<ModuleProductionPlanOrderDTO>>> queryOrderLineListByPlanVersion(@RequestBody ModuleProductionPlanQuery query) {
        return Results.createSuccessRes(moduleProductionPlanReportService.queryOrderLineListByPlanVersion(query.getPlanVersion()));
    }

}