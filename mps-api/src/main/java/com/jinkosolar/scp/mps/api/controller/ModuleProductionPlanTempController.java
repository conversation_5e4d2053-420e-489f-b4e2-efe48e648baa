package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanTempDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanTempQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionPlanTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件排产计划临时表相关操作控制层
 * 
 * <AUTHOR> 2024-05-27 21:35:13
 */
@RequestMapping(value = "/module-production-plan-temp")
@RestController
@Api(value = "moduleProductionPlanTemp", tags = "组件排产计划临时表相关操作控制层")
public class ModuleProductionPlanTempController extends BaseController {
    @Autowired
    private ModuleProductionPlanTempService moduleProductionPlanTempService;

    @PostMapping("/page")
    @ApiOperation(value = "组件排产计划临时表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionPlanTempDTO>>> page(@RequestBody ModuleProductionPlanTempQuery query) {
        return Results.createSuccessRes(moduleProductionPlanTempService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件排产计划临时表详情")
    public ResponseEntity<Results<ModuleProductionPlanTempDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionPlanTempService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件排产计划临时表")
    public ResponseEntity<Results<ModuleProductionPlanTempDTO>> insert(@RequestBody ModuleProductionPlanTempDTO moduleProductionPlanTempDTO) {
        validObject(moduleProductionPlanTempDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleProductionPlanTempService.saveOrUpdate(moduleProductionPlanTempDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件排产计划临时表")
    public ResponseEntity<Results<ModuleProductionPlanTempDTO>> update(@RequestBody ModuleProductionPlanTempDTO moduleProductionPlanTempDTO) {
        validObject(moduleProductionPlanTempDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleProductionPlanTempService.saveOrUpdate(moduleProductionPlanTempDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件排产计划临时表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionPlanTempService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件排产计划临时表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionPlanTempQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionPlanTempService.export(query, response);
    }

    @ApiOperation(value = "导入组件排产计划临时表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleProductionPlanTempService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}