package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionSuggestionDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionSuggestionMakeDto;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionSuggestionQuery;
import com.jinkosolar.scp.mps.service.ModuleProductionSuggestionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产建议表相关操作控制层
 *
 * <AUTHOR> 2024-07-11 15:29:00
 */
@RequestMapping(value = "/module-production-suggestion")
@RestController
@Api(value = "moduleProductionSuggestion", tags = "生产建议表相关操作控制层")
@Slf4j
public class ModuleProductionSuggestionController extends BaseController {
    @Autowired
    private ModuleProductionSuggestionService moduleProductionSuggestionService;

    @PostMapping("/page")
    @ApiOperation(value = "生产建议表分页查询")
    public ResponseEntity<Results<Page<ModuleProductionSuggestionDTO>>> page(@RequestBody ModuleProductionSuggestionQuery query) {
        return Results.createSuccessRes(moduleProductionSuggestionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "生产建议表详情")
    public ResponseEntity<Results<ModuleProductionSuggestionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleProductionSuggestionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增生产建议表")
    public ResponseEntity<Results<ModuleProductionSuggestionDTO>> insert(@RequestBody ModuleProductionSuggestionDTO moduleProductionSuggestionDTO) {
        validObject(moduleProductionSuggestionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleProductionSuggestionService.saveOrUpdate(moduleProductionSuggestionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新生产建议表")
    public ResponseEntity<Results<ModuleProductionSuggestionDTO>> update(@RequestBody ModuleProductionSuggestionDTO moduleProductionSuggestionDTO) {
        validObject(moduleProductionSuggestionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleProductionSuggestionService.saveOrUpdate(moduleProductionSuggestionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除生产建议表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleProductionSuggestionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出生产建议表")
    @PostMapping("/export")
    public void export(@RequestBody ModuleProductionSuggestionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleProductionSuggestionService.export(query, response);
    }

    @ApiOperation(value = "导入生产建议表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleProductionSuggestionService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/generate")
    @ApiOperation(value = "生产建议生成")
    public ResponseEntity<Results<Page<ModuleProductionSuggestionDTO>>> generate(@RequestBody ModuleProductionSuggestionMakeDto dto) {
        moduleProductionSuggestionService.deleteNoSendSapData(dto.getFactory());
        try {
            moduleProductionSuggestionService.generate(dto);
        } catch (Exception e) {
            log.error("生产建议生成失败", e);
            e.printStackTrace();
        }
        return Results.createSuccessRes();
    }

    @PostMapping("/sync/sap")
    @ApiOperation(value = "scp同步到sap(包含bom)")
    public ResponseEntity<Results<Object>> syncToSAP(@RequestBody ModuleProductionSuggestionQuery query) {
        //生产建议下发ERP
        moduleProductionSuggestionService.planSynSapHaveBom(query);
        return Results.createSuccessRes();
    }

    @PostMapping("/versions")
    @ApiOperation(value = "获取版本号")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody ModuleProductionSuggestionQuery query) {
        return Results.createSuccessRes(moduleProductionSuggestionService.versions(query));
    }

    @PostMapping("/schedule")
    @ApiOperation(value = "定时生成生产建议")
    public ResponseEntity<Results<Object>> schedule() {
        moduleProductionSuggestionService.runScheduleTask();
        return Results.createSuccessRes();
    }

    @PostMapping("/getMaxDate")
    @ApiOperation(value = "获取时间卡控")
    public ResponseEntity<Results<LocalDate>> getMaxDate(@RequestBody Long factoryId) {
        return Results.createSuccessRes(moduleProductionSuggestionService.getMaxDate(factoryId));
    }
}