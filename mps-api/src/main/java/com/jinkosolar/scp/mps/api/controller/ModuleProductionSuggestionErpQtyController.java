package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionSuggestionErpQtyDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionSuggestionErpQtyQuery;
import com.jinkosolar.scp.mps.domain.save.ModuleProductionSuggestionErpQtySaveDTO;
import com.jinkosolar.scp.mps.service.ModuleProductionSuggestionErpQtyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 生产建议Erp下发数量 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-19 15:36:03
 */
@RestController
@RequestMapping("/module-production-suggestion-erp-qty")
@RequiredArgsConstructor
@Api(value = "module-production-suggestion-erp-qty", tags = "生产建议Erp下发数量操作")
public class ModuleProductionSuggestionErpQtyController {
    private final ModuleProductionSuggestionErpQtyService moduleProductionSuggestionErpQtyService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "生产建议Erp下发数量分页列表", notes = "获得生产建议Erp下发数量分页列表")
    public ResponseEntity<Results<Page<ModuleProductionSuggestionErpQtyDTO>>> queryByPage(@RequestBody ModuleProductionSuggestionErpQtyQuery query) {
        return Results.createSuccessRes(moduleProductionSuggestionErpQtyService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ModuleProductionSuggestionErpQtyDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(moduleProductionSuggestionErpQtyService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ModuleProductionSuggestionErpQtyDTO>> save(@Valid @RequestBody ModuleProductionSuggestionErpQtySaveDTO saveDTO) {
        return Results.createSuccessRes(moduleProductionSuggestionErpQtyService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        moduleProductionSuggestionErpQtyService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody ModuleProductionSuggestionErpQtyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        moduleProductionSuggestionErpQtyService.export(query, response);
    }

    /**
     * 批量逻辑删除数据
     *
     * @param query 入参
     * @return 删除是否成功
     */
    @PostMapping("/deleteByCondition")
    @ApiOperation(value = "根据订单号等删除数据")
    public ResponseEntity<Results<Object>> deleteByCondition(@RequestBody ModuleProductionSuggestionErpQtyQuery query) {
        moduleProductionSuggestionErpQtyService.deleteByCondition(query);
        return Results.createSuccessRes();
    }
}
