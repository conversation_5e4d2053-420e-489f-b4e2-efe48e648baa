package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.dto.ModuleTypeDTO;
import com.jinkosolar.scp.mps.domain.query.ModuleTypeQuery;
import com.jinkosolar.scp.mps.domain.save.ModuleTypeSaveDTO;
import com.jinkosolar.scp.mps.service.ModuleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件版型相关操作控制层
 *
 * <AUTHOR> 2024-05-06 11:44:45
 */
@RequestMapping(value = "/module-type")
@RestController
@Api(value = "moduleType", tags = "组件版型相关操作控制层")
public class ModuleTypeController extends BaseController {
    @Autowired
    private ModuleTypeService moduleTypeService;

    @PostMapping("/page")
    @ApiOperation(value = "组件版型分页查询")
    public ResponseEntity<Results<Page<ModuleTypeDTO>>> page(@RequestBody ModuleTypeQuery query) {
        return Results.createSuccessRes(moduleTypeService.page(query));
    }

    @PostMapping("/query/list")
    @ApiOperation(value = "组件版型集合查询")
    public ResponseEntity<Results<List<ModuleTypeDTO>>> queryList(@RequestBody ModuleTypeQuery query) {
        return Results.createSuccessRes(moduleTypeService.queryList(query));
    }

    @PostMapping("/queryList")
    @ApiOperation(value = "组件版型查询")
    public ResponseEntity<Results<List<ModuleTypeDTO>>> queryList() {
        return Results.createSuccessRes(moduleTypeService.queryList());
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件版型详情")
    public ResponseEntity<Results<ModuleTypeDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(moduleTypeService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/queryByFullName")
    @ApiOperation(value = "组件版型详情")
    public ResponseEntity<Results<ModuleTypeDTO>> queryByFullName(@RequestBody String fullname) {
        validObject(fullname);
        return Results.createSuccessRes(moduleTypeService.queryByFullName(fullname));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件版型")
    public ResponseEntity<Results<ModuleTypeDTO>> insert(@RequestBody ModuleTypeDTO moduleTypeDTO) {
        validObject(moduleTypeDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleTypeService.saveOrUpdate(moduleTypeDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件版型")
    public ResponseEntity<Results<ModuleTypeDTO>> update(@RequestBody ModuleTypeDTO moduleTypeDTO) {
        validObject(moduleTypeDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(moduleTypeService.saveOrUpdate(moduleTypeDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件版型")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        moduleTypeService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件版型")
    @PostMapping("/export")
    public void export(@RequestBody ModuleTypeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        moduleTypeService.export(query, response);
    }

    @ApiOperation(value = "导入组件版型")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = moduleTypeService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增组件版型")
    public ResponseEntity<Results<ModuleTypeDTO>> save(@RequestBody ModuleTypeSaveDTO dto) {
//        validObject(dto, ValidGroups.Insert.class);
        return Results.createSuccessRes(moduleTypeService.save(dto));
    }


    @PostMapping("/fullNameOptions")
    @ApiOperation(value = "新增组件版型")
    public ResponseEntity<Results<List<CommonOption>>> fullNameOptions() {
        return Results.createSuccessRes(moduleTypeService.fullNameOptions());
    }


    @PostMapping("/fullName")
    @ApiOperation(value = "新增组件版型")
    public ResponseEntity<Results<List<String>>> fullName() {
        return Results.createSuccessRes(moduleTypeService.fullName());
    }

    @PostMapping("/moduleNameOptions")
    @ApiOperation(value = "查询版型不带主栅下拉")
    public ResponseEntity<Results<List<CommonOption>>> moduleNameOptions() {
        return Results.createSuccessRes(moduleTypeService.moduleNameOptions());
    }

    @PostMapping("/list")
    @ApiOperation(value = "供应商信息接口列表查询")
    public ResponseEntity<Results<List<CommonOption>>> list(@RequestBody ModuleTypeQuery query) {
        return Results.createSuccessRes(moduleTypeService.list(query));
    }

    @PostMapping("/findByFullNames")
    @ApiOperation(value = "根据计划版型（主栅）查询")
    public ResponseEntity<Results<List<ModuleTypeDTO>>> findByFullNames(@RequestBody ModuleTypeQuery query) {
        return Results.createSuccessRes(moduleTypeService.findByFullNames(query.getFullNameList()));
    }
}
