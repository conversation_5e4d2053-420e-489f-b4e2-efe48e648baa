
package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.erp.WorkCenterRequestDTO;
import com.jinkosolar.scp.mps.domain.dto.MpsWorkCenterDTO;
import com.jinkosolar.scp.mps.domain.query.MpsWorkCenterQuery;
import com.jinkosolar.scp.mps.service.MpsWorkCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 工作中心主表相关操作控制层
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
@RequestMapping(value = "/work-center")
@RestController
@Api(value = "MpsWorkCenter", tags = "相关操作控制层")
public class MpsWorkCenterController extends BaseController {
    @Autowired
    private MpsWorkCenterService mpsWorkCenterService;
    @PostMapping("/page")
    @ApiOperation(value = "查询工作中心主表列表", notes = "查询工作中心主表列表")
    public ResponseEntity<Results<Page<MpsWorkCenterDTO>>> page(@RequestBody MpsWorkCenterQuery query) {
        return Results.createSuccessRes(mpsWorkCenterService.page(query));
    }
    @PostMapping("/list")
    @ApiOperation(value = "查询工作中心主表列表", notes = "查询工作中心主表列表")
    public ResponseEntity<Results<List<MpsWorkCenterDTO>>> list(@RequestBody MpsWorkCenterQuery query) {
        return Results.createSuccessRes(mpsWorkCenterService.list(query));
    }
    @PostMapping("/factoryList")
    @ApiOperation(value = "查询工作中心工厂代码下拉选", notes = "查询工作中心工厂代码下拉选")
    public ResponseEntity<Results<List<String>>> factoryList() {
        return Results.createSuccessRes(mpsWorkCenterService.factoryList());
    }
    @PostMapping("/detail")
    @ApiOperation(value = "查询工作中心主表详情", notes = "查询工作中心主表详情")
    @ApiImplicitParam(name = "id", value = "主键")
    public ResponseEntity<Results<MpsWorkCenterDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(mpsWorkCenterService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/save")
    @ApiOperation(value = "保存工作中心主表信息", notes = "保存工作中心主表信息")
    public ResponseEntity<Results<MpsWorkCenterDTO>> save(@Validated(ValidGroups.Insert.class) @RequestBody MpsWorkCenterDTO mpsWorkCenterDTO) {
        return Results.createSuccessRes(mpsWorkCenterService.saveOrUpdate(mpsWorkCenterDTO));
    }
    @PostMapping("/submit")
    @ApiOperation(value = "提交工作中心主表信息", notes = "提交工作中心主表信息")
    public ResponseEntity<Results<MpsWorkCenterDTO>> submit(@Validated(ValidGroups.Update.class) @RequestBody MpsWorkCenterDTO mpsWorkCenterDTO) {
        return Results.createSuccessRes(mpsWorkCenterService.saveOrUpdate(mpsWorkCenterDTO));
    }
    @PostMapping("/delete")
    @ApiOperation(value = "批量物理删除工作中心主表信息", notes = "批量物理删除工作中心主表信息")
    @ApiImplicitParam(name = "ids", value = "主键集合")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        mpsWorkCenterService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }
    @ApiOperation(value = "导出工作中心主表数据", notes = "导出工作中心主表数据")
    @PostMapping("/export")
    public void export(@RequestBody MpsWorkCenterQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        mpsWorkCenterService.export(query,response);
    }
    @ApiOperation(value = "导入工作中心主表数据", notes = "导入工作中心主表数据")
    @PostMapping("/import")
    public ResponseEntity<Results<Object>> importFile(ExcelPara excelPara, MultipartFile multipartFile) {
        mpsWorkCenterService.importData(multipartFile,excelPara);
        return Results.createSuccessRes();
    }
    @PostMapping("/sync")
    @ApiOperation(value = "同步工作中心", notes = "同步工作中心")
    public ResponseEntity<Results<List<MpsWorkCenterDTO>>> sync(@RequestBody WorkCenterRequestDTO.RequestInfo queryInfo) {
        return Results.createSuccessRes(mpsWorkCenterService.sync(queryInfo));
    }


    @PostMapping("/syncByUser")
    @ApiOperation(value = "同步工作中心", notes = "同步工作中心")
    public ResponseEntity<Results<List<MpsWorkCenterDTO>>> syncByUser(@RequestBody MpsWorkCenterQuery queryInfo) {
        return Results.createSuccessRes(mpsWorkCenterService.syncByUser(queryInfo));
    }
}
