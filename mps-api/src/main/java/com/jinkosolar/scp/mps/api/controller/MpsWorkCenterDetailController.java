
package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.BizException;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.MpsWorkCenterDetailDTO;
import com.jinkosolar.scp.mps.domain.query.MpsWorkCenterDetailQuery;
import com.jinkosolar.scp.mps.domain.query.MpsWorkCenterLovQuery;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionReleaseQuery;
import com.jinkosolar.scp.mps.service.MpsWorkCenterDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作中心详情相关操作控制层
 *
 * <AUTHOR>
 * @date 2022/4/24
 */
@RequestMapping(value = "/work-center-detail")
@RestController
@Api(value = "MpsWorkCenterDetail", tags = "相关操作控制层")
public class MpsWorkCenterDetailController extends BaseController {
    public static final String LOCK_IMPORT_KEY = "work-center-detail-importFile";
    @Autowired
    private MpsWorkCenterDetailService mpsWorkCenterDetailService;

    @Autowired
    private RedissonClient redissonClient;

    @PostMapping("/page")
    @ApiOperation(value = "查询工作中心详情列表", notes = "查询工作中心详情列表")
    public ResponseEntity<Results<Page<MpsWorkCenterDetailDTO>>> page(@RequestBody MpsWorkCenterDetailQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.page(query));
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询工作中心详情列表", notes = "查询工作中心详情列表")
    public ResponseEntity<Results<List<MpsWorkCenterDetailDTO>>> list(@RequestBody MpsWorkCenterDetailQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.list(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询工作中心详情详情", notes = "查询工作中心详情详情")
    @ApiImplicitParam(name = "id", value = "主键")
    public ResponseEntity<Results<MpsWorkCenterDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(mpsWorkCenterDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/detailByWorkCenter")
    @ApiOperation(value = "查询工作中心详情详情", notes = "查询工作中心详情详情")
    public ResponseEntity<Results<MpsWorkCenterDetailDTO>> detailByWorkCenter(@RequestBody MpsWorkCenterDetailQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.queryBy(query));
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存工作中心详情信息", notes = "保存工作中心详情信息")
    public ResponseEntity<Results<MpsWorkCenterDetailDTO>> save(@Validated(ValidGroups.Insert.class) @RequestBody MpsWorkCenterDetailDTO mpsWorkCenterDetailDTO) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.saveOrUpdate(mpsWorkCenterDetailDTO));
    }

    @PostMapping("/submit")
    @ApiOperation(value = "提交工作中心详情信息", notes = "提交工作中心详情信息")
    public ResponseEntity<Results<MpsWorkCenterDetailDTO>> submit(@Validated(ValidGroups.Update.class) @RequestBody MpsWorkCenterDetailDTO mpsWorkCenterDetailDTO) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.saveOrUpdate(mpsWorkCenterDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量物理删除工作中心详情信息", notes = "批量物理删除工作中心详情信息")
    @ApiImplicitParam(name = "ids", value = "主键集合")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(s -> Long.parseLong(s)).collect(Collectors.toList());
        mpsWorkCenterDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出工作中心详情数据", notes = "导出工作中心详情数据")
    @PostMapping("/export")
    public void export(@RequestBody MpsWorkCenterDetailQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        mpsWorkCenterDetailService.export(query, response);
    }

    @ApiOperation(value = "导入工作中心详情数据", notes = "导入工作中心详情数据")
    @PostMapping("/import")
    public ResponseEntity<Results<Object>> importFile(@RequestPart("excelPara") ExcelPara excelPara, @RequestPart("file") MultipartFile multipartFile) {
        RLock lock = redissonClient.getLock(LOCK_IMPORT_KEY);
        if (lock.tryLock()) {
            try {
                ImportResultDTO importResultDTO = mpsWorkCenterDetailService.importData(multipartFile, excelPara);
                if (importResultDTO.getFailMessages().isEmpty()) {
                    return Results.createSuccessRes(importResultDTO);
                }
                return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
            } finally {
                lock.unlock();
            }
        }
        throw new BizException("mps.error.importingWait");
    }


    @PostMapping("/syncByUser")
    @ApiOperation(value = "同步工作中心", notes = "同步工作中心")
    public ResponseEntity<Results<List<MpsWorkCenterDetailDTO>>> syncByUser(@RequestBody MpsWorkCenterDetailQuery queryInfo) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.syncByUser(queryInfo));
    }

    @PostMapping("/queryByWorkshopId")
    @ApiOperation(value = "通过车间查询层级", notes = "通过车间查询层级")
    @ApiImplicitParam(name = "id", value = "车间id")
    public ResponseEntity<Results<MpsWorkCenterDetailDTO>> queryByWorkshopId(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.queryByWorkshopId(Long.parseLong(idDTO.getId())));
    }


    @PostMapping("/queryFactoryIdByOverseaIdAndDivisionId")
    @ApiOperation(value = "通过车间查询层级", notes = "通过车间查询层级")
    @ApiImplicitParam(name = "id", value = "车间id")
    public ResponseEntity<Results<List<MpsWorkCenterDetailDTO>>> queryFactoryIdByOverseaIdAndDivisionId(@RequestBody MpsWorkCenterDetailQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.queryFactoryCodeByOverseaIdAndDivisionId(query));
    }

    @PostMapping("/queryLov")
    @ApiOperation(value = "通过车间查询层级", notes = "通过车间查询层级")
    @ApiImplicitParam(name = "id", value = "车间id")
    public ResponseEntity<Results<List<LovLineDTO>>> queryLov(@RequestBody MpsWorkCenterLovQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.queryLov(query));
    }

    @PostMapping("/factoryCodeList")
    @ApiOperation(value = "根据事业部编码查询工厂代码", notes = "根据事业部查询工厂代码")
    public ResponseEntity<Results<List<MpsWorkCenterDetailDTO>>> factoryCodeList(@RequestBody MpsWorkCenterDetailQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.factoryCodeList(query));
    }

    @PostMapping("/getFactoryDesc")
    @ApiOperation(value = "根据factoryCode查询工厂描述", notes = "根据事业部查询工厂代码")
    public ResponseEntity<Results<String>> getFactoryDesc(@RequestParam("factoryCode") String factoryCode) {
        MpsWorkCenterDetailQuery query = new MpsWorkCenterDetailQuery();
        query.setFactoryCode(factoryCode);
        List<MpsWorkCenterDetailDTO> list = mpsWorkCenterDetailService.factoryCodeList(query);
        if (CollectionUtils.isEmpty(list)) {
            return Results.createSuccessRes("");
        }
        return Results.createSuccessRes(list.stream().map(MpsWorkCenterDetailDTO::getFactoryDesc).findFirst().orElse(""));
    }

    @PostMapping("/workshopList")
    @ApiOperation(value = "根据事业部编码查询生产车间", notes = "根据事业部编码查询生产车间")
    public ResponseEntity<Results<List<MpsWorkCenterDetailDTO>>> workshopList(@RequestBody MpsWorkCenterDetailQuery query) {
        return Results.createSuccessRes(mpsWorkCenterDetailService.workshopList(query));
    }

    @PostMapping("/queryFactoryWithComponent")
    @ApiOperation(value = "查询组件类的工作中心列表")
    public ResponseEntity<Results<List<Map<String,String>>>> queryFactoryWithComponent() {
        return Results.createSuccessRes(mpsWorkCenterDetailService.queryFactoryWithComponent());
    }
}

