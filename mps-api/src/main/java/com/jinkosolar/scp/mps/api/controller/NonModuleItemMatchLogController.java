package com.jinkosolar.scp.mps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ibm.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jinkosolar.scp.mps.domain.query.NonModuleItemMatchLogQuery;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.jinkosolar.scp.mps.domain.save.NonModuleItemMatchLogSaveDTO;
import com.jinkosolar.scp.mps.domain.entity.NonModuleItemMatchLog;
import com.jinkosolar.scp.mps.domain.dto.NonModuleItemMatchLogDTO;
import com.jinkosolar.scp.mps.service.NonModuleItemMatchLogService;
import com.ibm.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * [说明]料号匹配历史记录表 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-07-30
 */
@RestController
@RequestMapping("/non-module-item-match-log")
@RequiredArgsConstructor
@Api(value = "non-module-item-match-log", tags = "料号匹配历史记录表操作")
public class NonModuleItemMatchLogController {
    private final NonModuleItemMatchLogService nonModuleItemMatchLogService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "料号匹配历史记录表分页列表", notes = "获得料号匹配历史记录表分页列表")
    public ResponseEntity<Results<Page<NonModuleItemMatchLogDTO>>> queryByPage(@RequestBody NonModuleItemMatchLogQuery query) {
        return Results.createSuccessRes(nonModuleItemMatchLogService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<NonModuleItemMatchLogDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(nonModuleItemMatchLogService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<NonModuleItemMatchLogDTO>> save(@Valid @RequestBody NonModuleItemMatchLogSaveDTO saveDTO) {
        return Results.createSuccessRes(nonModuleItemMatchLogService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        nonModuleItemMatchLogService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody NonModuleItemMatchLogQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            nonModuleItemMatchLogService.export(query, response);
    }
}
