package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.NonModuleOperatingRateDTO;
import com.jinkosolar.scp.mps.domain.query.NonModuleOperatingRateQuery;
import com.jinkosolar.scp.mps.service.NonModuleOperatingRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 非组件开工率基础数据相关操作控制层
 *
 * <AUTHOR> 2024-11-05 11:05:36
 */
@RequestMapping(value = "/non-module-operating-rate")
@RestController
@Api(value = "nonModuleOperatingRate", tags = "非组件开工率基础数据相关操作控制层")
@RequiredArgsConstructor
public class NonModuleOperatingRateController extends BaseController {
    private final NonModuleOperatingRateService nonModuleOperatingRateService;

    @PostMapping("/page")
    @ApiOperation(value = "非组件开工率基础数据分页查询")
    public ResponseEntity<Results<Page<Map<String, Object>>>> page(@RequestBody NonModuleOperatingRateQuery query) {
        return Results.createSuccessRes(nonModuleOperatingRateService.page(query));
    }


    @ApiOperation(value = "导出非组件开工率基础数据")
    @PostMapping("/export")
    public void export(@RequestBody NonModuleOperatingRateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleOperatingRateService.export(query, response);
    }

    @ApiOperation(value = "导入非组件开工率基础数据")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = nonModuleOperatingRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @PostMapping("/detail")
    @ApiOperation(value = "非组件开工率基础数据详情")
    public ResponseEntity<Results<NonModuleOperatingRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(nonModuleOperatingRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增非组件开工率基础数据")
    public ResponseEntity<Results<Void>> insert(@RequestBody NonModuleOperatingRateDTO nonModuleOperatingRateDTO) {
        validObject(nonModuleOperatingRateDTO, ValidGroups.Insert.class);
        nonModuleOperatingRateService.saveOrUpdate(nonModuleOperatingRateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新非组件开工率基础数据")
    public ResponseEntity<Results<Void>> update(@RequestBody NonModuleOperatingRateDTO nonModuleOperatingRateDTO) {
        validObject(nonModuleOperatingRateDTO, ValidGroups.Update.class);
        nonModuleOperatingRateService.saveOrUpdate(nonModuleOperatingRateDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除非组件开工率基础数据")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        nonModuleOperatingRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }


}