package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.dto.feign.LovLineExtDTO;
import com.jinkosolar.scp.mps.domain.excel.PullingSliceSignPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.query.*;
import com.jinkosolar.scp.mps.service.CellForecastService;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanService;
import com.jinkosolar.scp.mps.service.NonModuleProductionSuggestionService;
import com.jinkosolar.scp.mps.service.SlicePlanShiftService;
import com.jinkosolar.scp.mps.service.impl.email.NonModuleEmailServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 非组件排产计划相关操作控制层
 *
 * <AUTHOR> 2024-05-28 09:12:50
 */
@RequestMapping(value = "/non-module-production-plan")
@RestController
@Slf4j
@Api(value = "nonModuleProductionPlan", tags = "非组件排产计划相关操作控制层")
public class NonModuleProductionPlanController extends BaseController {
    @Autowired
    private NonModuleProductionPlanService nonModuleProductionPlanService;

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    private SlicePlanShiftService slicePlanShiftService;

    @Autowired
    private NonModuleProductionSuggestionService nonModuleProductionSuggestionService;

    @Autowired
    private NonModuleEmailServiceImpl nonModuleEmailService;
    @PostMapping("/page")
    @ApiOperation(value = "非组件排产计划分页查询")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> page(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "非组件排产计划详情")
    public ResponseEntity<Results<NonModuleProductionPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(nonModuleProductionPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增非组件排产计划")
    public ResponseEntity<Results<NonModuleProductionPlanDTO>> insert(@RequestBody NonModuleProductionPlanDTO nonModuleProductionPlanDTO) {
        validObject(nonModuleProductionPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(nonModuleProductionPlanService.saveOrUpdate(nonModuleProductionPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新非组件排产计划")
    public ResponseEntity<Results<NonModuleProductionPlanDTO>> update(@RequestBody NonModuleProductionPlanDTO nonModuleProductionPlanDTO) {
        validObject(nonModuleProductionPlanDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(nonModuleProductionPlanService.saveOrUpdate(nonModuleProductionPlanDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除非组件排产计划")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        nonModuleProductionPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出非组件排产计划")
    @PostMapping("/export")
    public void export(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionPlanService.export(query, response);
    }

    @ApiOperation(value = "导入非组件排产计划")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = nonModuleProductionPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @Autowired
    private CellForecastService cellForecastService;

    /**
     * 同步电池临时表数据到排产表数据
     */
    @ApiOperation(value = "同步电池临时表数据到排产表数据")
    @PostMapping("/syncCellProductionPlanData")
    public ResponseEntity<Results<Boolean>> syncCellProductionPlanData(@RequestBody NonModuleProductionPlanTempQuery query) {
        log.info("同步电池计划数据：query={}", JSONUtil.toJsonStr(query));
        try {

            return Results.createAsprovaSuccessRes(nonModuleProductionPlanService.syncCellProductionPlanData(query));
        } catch (Exception e) {
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.toString(), "A0001");
        }

    }

    @ApiOperation(value = "同步拉晶切片临时表数据到排产表数据")
    @PostMapping("/syncPullingSlicePlanData")
    public ResponseEntity<Results<Boolean>> syncPullingSlicePlanData(@RequestBody NonModuleProductionPlanTempQuery query) {
        try {
            return Results.createAsprovaSuccessRes(nonModuleProductionPlanService.syncPullingSlicePlanData(query));
        } catch (Exception e) {
            return Results.createAsprovaFailRes(e.toString(), "A0001");
        }

    }


    /**
     * 同步电池临时表数据到排产表数据
     */
    @ApiOperation(value = "同步拉晶临时表数据到排产表数据")
    @PostMapping("/syncWaferCrystalProductionPlanData")
    public ResponseEntity<Results<Boolean>> syncWaferCrystalProductionPlanData(@RequestBody WaferCrystalProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.syncWaferCrystalProductionPlanData(query));
    }

    /**
     * 获取电池排产表数据
     */
    @ApiOperation(value = "获取电池排产表数据")
    @PostMapping("/queryCellProductionPlanList")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> queryCellProductionPlanList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryCellProductionPlanList(query));
    }

    /**
     * 获取电池排产表数据
     */
    @ApiOperation(value = "获取电池排产表数据")
    @PostMapping("/cell/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryCellPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryCellPage(query));
    }

    @PostMapping("/cell/page/export")
    @ApiOperation(value = "导出")
    public void cellPageExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionPlanService.cellPageExport(query, response);
    }

    @ApiOperation(value = "库存预测表")
    @PostMapping("/inventory/forecasting/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> inventoryForecastingPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.inventoryForecastingPage(query));
    }

    @PostMapping("/inventory/forecasting/page/export")
    @ApiOperation(value = "库存预测表导出")
    public void inventoryForecastingPageExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.inventoryForecastingPageExport(query, response);
    }

    /**
     * 获取电池排产表数据
     */
    @ApiOperation(value = "拉晶计划会签报表查询")
    @PostMapping("/pulling/slice/sign/plan/report/list")
    public ResponseEntity<Results<List<PullingSliceSignPlanExcelDTO>>> pullingSliceSignPlanReportList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.pullingSliceSignPlanReportList(query));
    }

    @PostMapping("/pulling/slice/sign/plan/report/export")
    @ApiOperation(value = "拉晶计划会签报表导出")
    public void pullingSliceSignPlanReportListExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.pullingSliceSignPlanReportListExport(query, response);
    }

    @PostMapping("/getSlicePlanShiftDTOList")
    @ApiOperation(value = "国内切片排产推移")
    public ResponseEntity<Results<List<SlicePlanShiftHeadDTO>>> getSlicePlanShiftDTOList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(slicePlanShiftService.getSlicePlanShiftDTOList(query));
    }

    @PostMapping("/getSlicePlanShiftDTOList/export")
    @ApiOperation(value = "国内切片排产推移导出")
    public void getSlicePlanShiftDTOListExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        slicePlanShiftService.getSlicePlanShiftDTOListExport(query, response);
    }

    @ApiOperation(value = "电池排产月度计划")
    @PostMapping("/cell/monthPage")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryCellMonthPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryCellMonthPage(query));
    }

    /**
     * 获取拉晶切片排产计划
     */
    @ApiOperation(value = "获取拉晶切片排产计划")
    @PostMapping("/queryProductionPlanList")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> queryProductionPlanList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryProductionPlanList(query));
    }

    /**
     * 获取最新版本排产数据
     */
    @ApiOperation(value = "获取最新版本排产数据")
    @PostMapping("/last-production-plan")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> queryLastProductionPlan(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryLastProductionPlan(query));
    }

    /**
     * 获取最新版本排产数据-关联明细
     */
    @ApiOperation(value = "获取最新版本排产数据")
    @PostMapping("/last-production-plan2")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> queryLastProductionPlan2(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryLastProductionPlan2(query));
    }

    /**
     * 需求计划获取排产计划
     *
     * @param query
     * @return
     */
    @ApiOperation(value = "需求计划获取排产计划")
    @PostMapping("/findDpPlanList")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> findDpPlanList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.findDpPlanList(query));
    }

    /**
     * 需求计划获取排产计划
     *
     * @return
     */
    @ApiOperation(value = "获取aps版本号")
    @PostMapping("/findAPSPlanVersionList")
    public ResponseEntity<Results<List<String>>> findAPSPlanVersionList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.findAPSPlanVersionList(query));
    }

    /**
     * 获取电池生产计划下发分页列表
     */
    @ApiOperation(value = "获取电池生产计划下发分页数据")
    @PostMapping("/cell/productionSuggestion/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryCellProductionSuggestionPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.queryCellProductionSuggestionPage(query));
    }

    @PostMapping("/cell/productionSuggestion/export")
    @ApiOperation(value = "导出")
    public void queryCellProductionSuggestionExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        nonModuleProductionSuggestionService.queryCellProductionSuggestionExport(query, response);
    }

    @PostMapping("/oversea/slice/productionSuggestion/export")
    @ApiOperation(value = "导出")
    public void queryOverseaCellProductionSuggestionExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        nonModuleProductionSuggestionService.queryOverseaCellProductionSuggestionExport(query, response);
    }

    /**
     * 获取切片生产计划下发分页列表
     */
    @ApiOperation(value = "获取切片生产计划下发分页数据")
    @PostMapping("/slice/productionSuggestion/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> querySliceProductionSuggestionPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.querySliceProductionSuggestionPage(query));
    }


    @PostMapping("/slice/productionSuggestion/export")
    @ApiOperation(value = "切片生产计划下发导出")
    public void querySliceProductionSuggestionExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        nonModuleProductionSuggestionService.exportSliceProductionSuggestion(query, response);
    }

    /**
     * 获取海外切片生产计划下发分页列表
     */
    @ApiOperation(value = "获取切片生产计划下发分页数据")
    @PostMapping("/oversea/slice/productionSuggestion/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryOverseaSliceProductionSuggestionPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.queryOverseaSliceProductionSuggestionPage(query));
    }

    /**
     * 获取拉晶生产计划下发分页列表(4道工序)
     */
    @ApiOperation(value = "获取拉晶生产计划下发分页列表(4道工序)")
    @PostMapping("/crystal/productionSuggestion/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryCrystalProductionSuggestionPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.queryCrystalProductionSuggestionPage(query));
    }

    /**
     * 获取拉晶4道工序通用查生产建议下发分页列表
     */
    @ApiOperation(value = "获取拉晶4道工序通用查生产建议下发分页列表")
    @PostMapping("/crystal/queryCommonProductionSuggestionPage")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryCommonProductionSuggestionPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.queryCommonProductionSuggestionPage(query));
    }

    /**
     * 电池生产建议下发
     */
    @ApiOperation(value = "电池生产建议下发")
    @PostMapping("/cell/sendCellProductionSuggestion")
    public ResponseEntity<Results<Boolean>> sendCellProductionSuggestion(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.sendCellProductionSuggestion(query));
    }

    /**
     * 切片生产建议下发
     */
    @ApiOperation(value = "切片生产建议下发")
    @PostMapping("/slice/sendSliceProductionSuggestion")
    public ResponseEntity<Results<Boolean>> sendSliceProductionSuggestion(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.sendSliceProductionSuggestion(query));
    }

    /**
     * 海外切片生产建议下发
     */
    @ApiOperation(value = "海外切片生产建议下发")
    @PostMapping("/oversea/slice/sendSliceProductionSuggestion")
    public ResponseEntity<Results<Boolean>> sendOverseaSliceProductionSuggestion(@RequestBody List<NonModuleProductionPlanDTO> dtos) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.sendOverseaSliceProductionSuggestion(dtos));
    }

    /**
     * 拉晶-切方生产建议下发
     */
    @ApiOperation(value = "拉晶-切方生产建议下发")
    @PostMapping("/crystal/sendCrystalProductionSuggestion")
    public ResponseEntity<Results<Boolean>> sendCrystalProductionSuggestion(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.sendCrystalProductionSuggestion(query));
    }

    /**
     * 拉晶通用(4道工序)生产建议下发
     */
    @ApiOperation(value = "拉晶通用(4道工序)生产建议下发")
    @PostMapping("/crystal/sendCommonProductionSuggestionRequest")
    public ResponseEntity<Results<Boolean>> sendCommonProductionSuggestionRequest(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.sendCommonProductionSuggestionRequest(query));
    }

    /**
     * 周计划报表查询
     */
    @ApiOperation(value = "周计划报表查询")
    @PostMapping("/weeklyPlanReportList")
    public ResponseEntity<Results<List<WeeklyPlanReportDTO>>> weeklyPlanReportList(@RequestBody WeeklyPlanReportQuery query) {
        List<WeeklyPlanReportDTO> dataList = nonModuleProductionSuggestionService.weeklyPlanReportList(query);
        return Results.createSuccessRes(dataList);
    }

    /**
     * 周计划切片报表查询
     */
    @ApiOperation(value = "周计划切片报表查询（山西切片计划）")
    @PostMapping("/weeklySectionReportList")
    public ResponseEntity<Results<List<WeeklyPlanReportDTO>>> weeklySectionReportList(@RequestBody WeeklyPlanReportQuery query) {
        List<WeeklyPlanReportDTO> dataList = nonModuleProductionSuggestionService.weeklySectionReportList(query);
        return Results.createSuccessRes(dataList);
    }

    @ApiOperation(value = "周计划切片报表查询(国内切片计划）")
    @PostMapping("/weeklySectionReportListNew")
    public ResponseEntity<Results<List<WeeklyPlanReportDTO>>> weeklySectionReportListNew(@RequestBody WeeklyPlanReportQuery query) {
        List<WeeklyPlanReportDTO> dataList = nonModuleProductionSuggestionService.weeklySectionReportListNew(query);
        return Results.createSuccessRes(dataList);
    }

    /**
     * 周计划拉晶报表查询
     */
    @ApiOperation(value = "周计划拉晶报表查询")
    @PostMapping("/weeklyCrystalPlanReportList")
    public ResponseEntity<Results<List<WeeklyPlanReportDTO>>> weeklyCrystalPlanReportList(@RequestBody WeeklyPlanReportQuery query) {
        List<WeeklyPlanReportDTO> dataList = nonModuleProductionSuggestionService.weeklyCrystalPlanReportList(query);
        return Results.createSuccessRes(dataList);
    }

    /**
     * 周计划报表导出
     */
    @ApiOperation(value = "周计划切片报表导出(国内）")
    @PostMapping("/weeklySectionReportExportNew")
    public void weeklySectionReportExportNew(@RequestBody WeeklyPlanReportQuery query, HttpServletResponse response) {
        nonModuleProductionSuggestionService.weeklySectionReportExportNew(query, response);
    }

    @ApiOperation(value = "周计划切片报表导出")
    @PostMapping("/weeklySectionReportExport")
    public void weeklySectionReportExport(@RequestBody WeeklyPlanReportQuery query, HttpServletResponse response) {
        nonModuleProductionSuggestionService.weeklySectionReportExport(query, response);
    }

    /**
     * 周计划报表导出
     */
    @ApiOperation(value = "周计划拉晶报表导出")
    @PostMapping("/weeklyCrystalReportExport")
    public void weeklyCrystalReportExport(@RequestBody WeeklyPlanReportQuery query, HttpServletResponse response) {
        nonModuleProductionSuggestionService.weeklyCrystalReportExport(query, response);
    }

    /**
     * 周计划报表导出
     */
    @ApiOperation(value = "周计划报表导出")
    @PostMapping("/weeklyPlanReportExport")
    public void weeklyPlanReportExport(@RequestBody WeeklyPlanReportQuery query, HttpServletResponse response) {
        nonModuleProductionSuggestionService.weeklyPlanReportExport(query, response);
    }


    /**
     * 获取拉晶4道工序通用查生产建议下发版本
     */
    @ApiOperation(value = "获取拉晶4道工序通用查生产建议下发分页列表")
    @PostMapping("/crystal/queryCommonProductionSuggestionVersion")
    public ResponseEntity<Results<List<String>>> queryCommonProductionSuggestionVersion(@RequestBody NonModuleProductionPlanDTO dto) {
        return Results.createSuccessRes(nonModuleProductionSuggestionService.queryCommonProductionSuggestionVersion(dto.getWorkNum().toString()));
    }

    /**
     * 导出拉晶4道工序通用查生产建议下发逻辑
     */
    @ApiOperation(value = "导出拉晶4道工序通用查生产建议下发逻辑")
    @PostMapping("/crystal/exportCommonProductionSuggestion")
    public void exportCommonProductionSuggestion(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionSuggestionService.exportCommonProductionSuggestion(query, response);
    }

    @ApiOperation(value = "切片年度达成表查询")
    @PostMapping("/wafer/report/year/page")
    public ResponseEntity<Results<List<WaferPlanYearReportDto>>> waferReportYearPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.waferReportYearList(query));
    }

    @ApiOperation(value = "切片年度达成表导出")
    @PostMapping("/wafer/report/year/export")
    public void waferReportYearExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.waferReportYearExport(query, response);
    }

    @ApiOperation(value = "老基地拉晶产量及单产报表查询")
    @PostMapping("/oldBaseCrystalReport/list")
    public ResponseEntity<Results<List<NonModuleProductionPlanOldBaseCrystalReportDTO>>> queryOldBaseSliceOemPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryOldBaseCrystalPage(query));
    }

    @ApiOperation(value = "老基地拉晶产量及单产报表导出")
    @PostMapping("/oldBaseCrystalReport/export")
    public void oldBaseSliceOemExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.oldBaseCrystalExport(query, response);
    }

    @ApiOperation(value = "大片区产量及单产报表查询")
    @PostMapping("/oldBaseCrystalBigAreaReport/list")
    public ResponseEntity<Results<List<NonModuleProductionPlanOldBaseCrystalReportDTO>>> queryOldBaseCrystalBigAreaPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryOldBaseCrystalBigAreaPage(query));
    }

    @ApiOperation(value = "大片区产量及单产报表导出")
    @PostMapping("/oldBaseCrystalBigAreaReport/export")
    public void oldBaseCrystalBigAreaExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.oldBaseCrystalBigAreaExport(query, response);
    }

    /**
     * 拉晶终产量报表查询
     */
    @ApiOperation(value = "拉晶终产量报表查询")
    @PostMapping("/finalCrystalProdReportList")
    public ResponseEntity<Results<List<FinalProductionReportDTO>>> finalCrystalProdReportList(@RequestBody FinalProductionReportQuery query) {
        List<FinalProductionReportDTO> dataList = nonModuleProductionSuggestionService.finalCrystalProdReportList(query);
        return Results.createSuccessRes(dataList);
    }

    @ApiOperation(value = "拉晶终产量报表报表导出")
    @PostMapping("/finalCrystalProdReportList/export")
    public void finalCrystalProdReportListExport(@RequestBody FinalProductionReportQuery query, HttpServletResponse response) {
        nonModuleProductionSuggestionService.exportFinalCrystalProdReportList(query, response);
    }

    /**
     * 海外切片计划报表查询
     */
    @ApiOperation(value = "海外切片计划报表查询")
    @PostMapping("/slicePlanReportOverseaList")
    public ResponseEntity<Results<List<SlicePlanReportOverseaDTO>>> slicePlanReportOverseaList(@RequestBody FinalProductionReportQuery query) {
        List<SlicePlanReportOverseaDTO> dataList = nonModuleProductionPlanService.slicePlanReportOverseaList(query);
        return Results.createSuccessRes(dataList);
    }

    /**
     * 海外切片计划临时报表查询
     */
    @ApiOperation(value = "海外切片计划临时报表查询")
    @PostMapping("/slicePlanReportOverseaTempList")
    public ResponseEntity<Results<List<SlicePlanReportOverseaDTO>>> slicePlanReportOverseaTempList(@RequestBody FinalProductionReportQuery query) {
        List<SlicePlanReportOverseaDTO> dataList = nonModuleProductionPlanService.slicePlanReportOverseaTempList(query);
        return Results.createSuccessRes(dataList);
    }

    @ApiOperation(value = "海外切片计划报表导出")
    @PostMapping("/slicePlanReportOverseaTempList/export")
    public void slicePlanReportOverseaTempListExport(@RequestBody FinalProductionReportQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.exportSlicePlanReportOverseaTempList(query, response);
    }

    /**
     * 计算海外切片计划报表数据
     */
    @ApiOperation(value = "计算海外切片计划报表数据")
    @PostMapping("/slicePlanReportOverseaList/generate")
    public ResponseEntity<Results<List<SlicePlanReportOverseaDTO>>> generateSlicePlanReportOverseaList() {
        List<SlicePlanReportOverseaDTO> dataList = nonModuleProductionPlanService.generateSlicePlanReportOverseaList();
        return Results.createSuccessRes(dataList);
    }

    /**
     * 修改海外切片计划报表机台数数据
     */
    @ApiOperation(value = "修改海外切片计划报表机台数数据")
    @PostMapping("/slicePlanReportOverseaList/updateMachineQty")
    public ResponseEntity<Results<Boolean>> updateSlicePlanReportOverseaList(@RequestBody List<SlicePlanReportOverseaDTO> updateReportDataList) {
        return Results.createSuccessRes(nonModuleProductionPlanService.updateSlicePlanReportOverseaList(updateReportDataList));
    }

    /**
     * 修改海外切片计划报表厚度数据
     */
    @ApiOperation(value = "修改海外切片计划报表厚度数据")
    @PostMapping("/slicePlanReportOverseaList/updateThickness")
    public ResponseEntity<Results<Boolean>> updateSlicePlanReportOverseaThickness(@RequestBody List<SlicePlanReportOverseaUpdateDTO> updateReportDataList) {
        return Results.createSuccessRes(nonModuleProductionPlanService.updateSlicePlanReportOverseaThickness(updateReportDataList));
    }

    /**
     * 发布海外切片计划报表数据
     */
    @ApiOperation(value = "发布海外切片计划报表数据")
    @PostMapping("/slicePlanReportOverseaList/publish")
    public ResponseEntity<Results<Boolean>> publishSlicePlanReportOverseaList() {
        return Results.createSuccessRes(nonModuleProductionPlanService.publishSlicePlanReportOverseaList());
    }

    @ApiOperation(value = "海外切片计划报表导出")
    @PostMapping("/slicePlanReportOverseaList/export")
    public void slicePlanReportOverseaListExport(@RequestBody FinalProductionReportQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.exportSlicePlanReportOverseaList(query, response);
    }

    @ApiOperation(value = "老基地电池排产计划")
    @PostMapping("/cell/oldBasePage")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryOldBasePage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryOldBasePage(query));
    }

    @PostMapping("/cell/oldBasePage/export")
    @ApiOperation(value = "导出")
    public void cellOldBasePageExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionPlanService.cellOldBasePageExport(query, response);
    }

    @ApiOperation(value = "拉晶返棒计划报表查询")
    @PostMapping("/crystalBackToRod/list")
    public ResponseEntity<Results<List<CrystalBackToRodReportTypeDTO>>> queryCrystalBackToRodList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryCrystalBackToRodList(query));
    }

    @ApiOperation(value = "拉晶返棒计划报表导出")
    @PostMapping("/crystalBackToRod/export")
    public void crystalBackToRodExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.crystalBackToRodExport(query, response);
    }

    @PostMapping("/sendNotice")
    @ApiOperation(value = "发送晶彩通知")
    public ResponseEntity<Results<Boolean>> sendNotice(@RequestBody NonModuleProductionPlanQuery query) {
        nonModuleProductionPlanService.sendJingCaiNotice(query.getPlanVersion(), query.getModelType());
        return Results.createSuccessRes(true);
    }

    @ApiOperation(value = "年度预算返棒报表查询")
    @PostMapping("/crystalBackToRodYear/list")
    public ResponseEntity<Results<List<CrystalBackToRodYearReportTypeDTO>>> queryCrystalBackToRodYearList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryCrystalBackToRodYearList(query));
    }

    @ApiOperation(value = "年度预算返棒报表导出")
    @PostMapping("/crystalBackToRodYear/export")
    public void crystalBackToRodYearExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.crystalBackToRodYearExport(query, response);
    }

    /**
     *
     * 周计划报表查询
     * */
    @ApiOperation(value = "电池排产计划-发送邮件")
    @PostMapping("/sendCellEmail")
    public ResponseEntity<Results<Boolean>> sendCellEmail(@RequestBody NonModuleProductionSendEmailDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() ->
                nonModuleEmailService.sendCellEmail(productionSendEmailDTO), threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }

    /**
     *
     * 拉晶排产计划发送邮件
     * */
    @ApiOperation(value = "拉晶排产计划-发送邮件")
    @PostMapping("/sendCrystalEmail")
    public ResponseEntity<Results<Boolean>> sendCrystalEmail(@RequestBody NonModuleProductionSendEmailCrystalDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() ->
                nonModuleEmailService.sendCrystalEmail(productionSendEmailDTO), threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }

    /**
     *
     * 周计划报表查询
     * */
    @ApiOperation(value = "切片生产计划下发-发送邮件")
    @PostMapping("/sendSliceEmail")
    public ResponseEntity<Results<Boolean>> sendSliceEmail(@RequestBody NonModuleProductionSendEmailCrystalDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() ->
                nonModuleEmailService.sendSliceEmail(productionSendEmailDTO), threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }

    @ApiOperation(value = "(国内）切片生产计划下发-发送邮件")
    @PostMapping("/sendSliceEmailNew")
    public ResponseEntity<Results<Boolean>> sendSliceEmailNew(@RequestBody NonModuleProductionSendEmailCrystalDTO productionSendEmailDTO) {
        // 异步调用
        CompletableFuture.runAsync(() ->
                nonModuleEmailService.sendSliceEmailNew(productionSendEmailDTO), threadPoolExecutor
        );
        return Results.createSuccessRes(true);
    }
    /**
     *
     * 获取动态列
     * */
    @ApiOperation(value = "切片排产计划获取动态列")
    @PostMapping("/getSliceDynamicColumn")
    public ResponseEntity<Results<List<String>>> getSliceDynamicColumn(@RequestBody WeeklyPlanReportQuery query) {
        List<String> dataList = nonModuleEmailService.getSliceDynamicColumn(query);
        return Results.createSuccessRes(dataList);
    }

    /**
     *
     * 获取动态列
     * */
    @ApiOperation(value = "拉晶排产计划获取动态列")
    @PostMapping("/getCrystalDynamicColumn")
    public ResponseEntity<Results<List<String>>> getCrystalDynamicColumn(@RequestBody WeeklyPlanReportQuery query) {
        List<String> dataList = nonModuleEmailService.getCrystalDynamicColumn(query);
        return Results.createSuccessRes(dataList);
    }

    @PostMapping("/getFactory")
    @ApiOperation(value = "非组件获取工厂")
    public ResponseEntity<Results<List<LovLineDTO>>> getFactory(@RequestBody Long baseId){
        return Results.createSuccessRes(nonModuleEmailService.getFactory(baseId));
    }

    @PostMapping("/getEmail")
    @ApiOperation(value = "获取收件人或抄送人")
    public ResponseEntity<Results<Map<String,List<LovLineExtDTO>>>> getEmail(@RequestBody SaveEmailDTO saveEmailDTO){
        return Results.createSuccessRes(nonModuleEmailService.getEmail(saveEmailDTO));
    }

    /**
     * 获取电池排产表数据
     */
    @ApiOperation(value = "获取电池排产表数据")
    @PostMapping("/oldBasePage/getCellProductionPlanList")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> getCellProductionPlanList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.getCellProductionPlanList(query));
    }
}
