package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;

import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanTempDTO;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanTempQuery;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanTempService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 非组件排产临时表相关操作控制层
 * 
 * <AUTHOR> 2024-05-27 21:09:15
 */
@RequestMapping(value = "/non-module-production-plan-temp")
@RestController
@Api(value = "nonModuleProductionPlanTemp", tags = "非组件排产临时表相关操作控制层")
public class NonModuleProductionPlanTempController extends BaseController {
    @Autowired
    private NonModuleProductionPlanTempService nonModuleProductionPlanTempService;

    @PostMapping("/page")
    @ApiOperation(value = "非组件排产临时表分页查询")
    public ResponseEntity<Results<Page<NonModuleProductionPlanTempDTO>>> page(@RequestBody NonModuleProductionPlanTempQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanTempService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "非组件排产临时表详情")
    public ResponseEntity<Results<NonModuleProductionPlanTempDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(nonModuleProductionPlanTempService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增非组件排产临时表")
    public ResponseEntity<Results<NonModuleProductionPlanTempDTO>> insert(@RequestBody NonModuleProductionPlanTempDTO nonModuleProductionPlanTempDTO) {
        validObject(nonModuleProductionPlanTempDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(nonModuleProductionPlanTempService.saveOrUpdate(nonModuleProductionPlanTempDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新非组件排产临时表")
    public ResponseEntity<Results<NonModuleProductionPlanTempDTO>> update(@RequestBody NonModuleProductionPlanTempDTO nonModuleProductionPlanTempDTO) {
        validObject(nonModuleProductionPlanTempDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(nonModuleProductionPlanTempService.saveOrUpdate(nonModuleProductionPlanTempDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除非组件排产临时表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        nonModuleProductionPlanTempService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出非组件排产临时表")
    @PostMapping("/export")
    public void export(@RequestBody NonModuleProductionPlanTempQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionPlanTempService.export(query, response);
    }

    @ApiOperation(value = "导入非组件排产临时表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = nonModuleProductionPlanTempService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    /**
     *
     * 获取电池排产表的临时数据
     * */
    @ApiOperation(value = "获取电池排产表的临时数据")
    @PostMapping("/queryCellProductionPlanTemp")
    public ResponseEntity<Results<List<NonModuleProductionPlanTempDTO>>> queryCellProductionPlanTemp(@RequestBody NonModuleProductionPlanTempQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanTempService.queryCellProductionPlanTemp(query));
    }
    /***********************************************************************************************************************************/

    @ApiOperation(value = "老基地切片委外代工查询")
    @PostMapping("/oldBaseSliceOem/page")
    public ResponseEntity<Results<Page<NonModuleProductionPlanTempDTO>>> queryOldBaseSliceOemPage(@RequestBody NonModuleProductionPlanTempQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanTempService.queryOldBaseSliceOemPage(query));
    }

    @PostMapping("/oldBaseSliceOem/delete")
    @ApiOperation(value = "老基地切片委外代工删除")
    public ResponseEntity<Results<Object>> oldBaseSliceOemDelete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        nonModuleProductionPlanTempService.logicDeleteOldBaseSliceOemByIds(ids);
        return Results.createSuccessRes();
    }
    @ApiOperation(value = "老基地切片委外代工删除导出")
    @PostMapping("/oldBaseSliceOem/export")
    public void oldBaseSliceOemExport(@RequestBody NonModuleProductionPlanTempQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionPlanTempService.oldBaseSliceOemExport(query, response);
    }

    @ApiOperation(value = "老基地切片委外代工导入")
    @PostMapping("/oldBaseSliceOem/import")
    public ResponseEntity<Results<ImportResultDTO>> oldBaseSliceOemImportFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = nonModuleProductionPlanTempService.oldBaseSliceOemImportData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }



}