package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanTzDTO;
import com.jinkosolar.scp.mps.domain.query.CellForecastQuery;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanTzQuery;
import com.jinkosolar.scp.mps.domain.save.NonModuleProductionPlanTzEditDTO;
import com.jinkosolar.scp.mps.service.CellForecastService;
import com.jinkosolar.scp.mps.service.CellTransferPlanService;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanTzService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 非组件排产计划相关操作控制层
 *
 * <AUTHOR> 2024-05-28 09:12:50
 */
@RequestMapping(value = "/non-module-production-plan-tz")
@RestController
@Api(value = "nonModuleProductionPlan", tags = "电池排产可调整计划控制层")
public class NonModuleProductionPlanTzController extends BaseController {

    @Autowired
    private NonModuleProductionPlanTzService nonModuleProductionPlanTzService;


    @Autowired
    CellForecastService cellForecastService;


    @Autowired
    private CellTransferPlanService cellTransferPlanService;


    @ApiOperation(value = "老基地电池排产可调整计划")
    @PostMapping("/cell/oldBasePage")
    public ResponseEntity<Results<Page<NonModuleProductionPlanTzDTO>>> queryOldBasePage(@RequestBody NonModuleProductionPlanTzQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanTzService.queryOldBasePage(query));
    }


    @PostMapping("/cell/oldBasePage/export")
    @ApiOperation(value = "导出")
    public void cellOldBasePageExport(@RequestBody NonModuleProductionPlanTzQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        nonModuleProductionPlanTzService.cellOldBasePageExport(query, response);
    }

    @PostMapping("/cell/record/edit")
    @ApiOperation(value = "可调整计划编辑")
    public ResponseEntity<Results<Boolean>> editRecord(@RequestBody NonModuleProductionPlanTzEditDTO planTzEditDTO) {
        return Results.createSuccessRes(nonModuleProductionPlanTzService.editRecord(planTzEditDTO));
    }



    @PostMapping("/forecast/calculate")
    @ApiOperation(value = "基于调整版本计划版本计算国内电池产能")
    public ResponseEntity<Results<Boolean>> forecastCalculate() {
        CellForecastQuery cellForecastQuery = new CellForecastQuery();
        cellForecastQuery.setPlanType("GNDC");
        cellForecastQuery.setBasePlanTz(true);
        cellForecastService.cellForecast(cellForecastQuery);
        return Results.createSuccessRes(Boolean.TRUE);
    }


    @PostMapping("/calculate-transfer-plan")
    @ApiOperation(value = "基于转运计划计算并更新NonModuleProductionPlanTz的attribute8")
    public ResponseEntity<Results<Boolean>> calculateTransferPlan() {
        // 调用服务层执行转运计划计算逻辑
        nonModuleProductionPlanTzService.executeTransferPlanCalculation();
        return Results.createSuccessRes(Boolean.TRUE);
    }



}
