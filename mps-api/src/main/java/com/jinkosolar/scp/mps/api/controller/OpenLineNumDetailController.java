package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.OpenLineNumDetailDTO;
import com.jinkosolar.scp.mps.domain.query.OpenLineNumDetailQuery;
import com.jinkosolar.scp.mps.service.OpenLineNumDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调整开线数量明细表相关操作控制层
 * 
 * <AUTHOR> 2024-05-11 10:41:22
 */
@RequestMapping(value = "/open-line-num-detail")
@RestController
@Api(value = "openLineNumDetail", tags = "调整开线数量明细表相关操作控制层")
public class OpenLineNumDetailController extends BaseController {
    @Autowired
    private OpenLineNumDetailService openLineNumDetailService;

    @PostMapping("/page")
    @ApiOperation(value = "调整开线数量明细表分页查询")
    public ResponseEntity<Results<Page<OpenLineNumDetailDTO>>> page(@RequestBody OpenLineNumDetailQuery query) {
        return Results.createSuccessRes(openLineNumDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "调整开线数量明细表详情")
    public ResponseEntity<Results<OpenLineNumDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(openLineNumDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增调整开线数量明细表")
    public ResponseEntity<Results<OpenLineNumDetailDTO>> insert(@RequestBody OpenLineNumDetailDTO openLineNumDetailDTO) {
        validObject(openLineNumDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(openLineNumDetailService.saveOrUpdate(openLineNumDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新调整开线数量明细表")
    public ResponseEntity<Results<OpenLineNumDetailDTO>> update(@RequestBody OpenLineNumDetailDTO openLineNumDetailDTO) {
        validObject(openLineNumDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(openLineNumDetailService.saveOrUpdate(openLineNumDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除调整开线数量明细表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        openLineNumDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出调整开线数量明细表")
    @PostMapping("/export")
    public void export(@RequestBody OpenLineNumDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        openLineNumDetailService.export(query, response);
    }

    @ApiOperation(value = "导入调整开线数量明细表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = openLineNumDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}