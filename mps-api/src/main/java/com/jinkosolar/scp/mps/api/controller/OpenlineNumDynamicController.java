package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.OpenlineNumDynamicDTO;
import com.jinkosolar.scp.mps.domain.query.OpenlineNumDynamicQuery;
import com.jinkosolar.scp.mps.service.OpenlineNumDynamicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 调整开线数量动态展示相关操作控制层
 * 
 * <AUTHOR> 2024-07-03 08:59:33
 */
@RequestMapping(value = "/openline-num-dynamic")
@RestController
@Api(value = "openlineNumDynamic", tags = "调整开线数量动态展示相关操作控制层")
public class OpenlineNumDynamicController extends BaseController {
    @Autowired
    private OpenlineNumDynamicService openlineNumDynamicService;

    @PostMapping("/page")
    @ApiOperation(value = "调整开线数量动态展示分页查询")
    public ResponseEntity<Results<Page<Map<String, Object>>>> page(@RequestBody OpenlineNumDynamicQuery query) {
        return Results.createSuccessRes(openlineNumDynamicService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "调整开线数量动态展示详情")
    public ResponseEntity<Results<OpenlineNumDynamicDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(openlineNumDynamicService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增调整开线数量动态展示")
    public ResponseEntity<Results<OpenlineNumDynamicDTO>> insert(@RequestBody OpenlineNumDynamicDTO openlineNumDynamicDTO) {
        validObject(openlineNumDynamicDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(openlineNumDynamicService.saveOrUpdate(openlineNumDynamicDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新调整开线数量动态展示")
    public ResponseEntity<Results<OpenlineNumDynamicDTO>> update(@RequestBody OpenlineNumDynamicDTO openlineNumDynamicDTO) {
        validObject(openlineNumDynamicDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(openlineNumDynamicService.saveOrUpdate(openlineNumDynamicDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除调整开线数量动态展示")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        openlineNumDynamicService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出调整开线数量动态展示")
    @PostMapping("/export")
    public void export(@RequestBody OpenlineNumDynamicQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        openlineNumDynamicService.export(query, response);
    }

    @ApiOperation(value = "导入调整开线数量动态展示")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("openlineNumDynamicDTO") OpenlineNumDynamicDTO openlineNumDynamicDTO) {
        ImportResultDTO importResultDTO = openlineNumDynamicService.importData(multipartFile, openlineNumDynamicDTO);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "同步aps")
    @PostMapping("/syncTable")
    public ResponseEntity<Results<Object>> syncTable() {
        try{
            openlineNumDynamicService.syncTable();
        }catch (Exception e){
            e.printStackTrace();
            return Results.createFailRes();
        }
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "根据工厂获取起止日期")
    @PostMapping("/getDate")
    public ResponseEntity<Results<Map<String, LocalDate>>> getDate(@RequestBody Long factoryId) {
        return Results.createSuccessRes(openlineNumDynamicService.getDate(factoryId));
    }
}