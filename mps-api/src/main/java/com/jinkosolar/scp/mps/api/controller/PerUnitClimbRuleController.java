package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PerUnitClimbRuleDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.PerUnitClimbRuleQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.PerUnitClimbRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单产爬坡规则相关操作控制层
 *
 * <AUTHOR> 2024-07-18 11:30:08
 */
@RequestMapping(value = "/per-unit-climb-rule")
@RestController
@Api(value = "perUnitClimbRule", tags = "单产爬坡规则相关操作控制层")
public class PerUnitClimbRuleController extends BaseController {
    @Autowired
    private PerUnitClimbRuleService perUnitClimbRuleService;

    @PostMapping("/page")
    @ApiOperation(value = "单产爬坡规则分页查询")
    public ResponseEntity<Results<Page<PerUnitClimbRuleDTO>>> page(@RequestBody PerUnitClimbRuleQuery query) {
        return Results.createSuccessRes(perUnitClimbRuleService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "单产爬坡规则详情")
    public ResponseEntity<Results<PerUnitClimbRuleDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(perUnitClimbRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增单产爬坡规则")
    public ResponseEntity<Results<PerUnitClimbRuleDTO>> insert(@RequestBody PerUnitClimbRuleDTO perUnitClimbRuleDTO) {
        validObject(perUnitClimbRuleDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(perUnitClimbRuleService.saveOrUpdate(perUnitClimbRuleDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新单产爬坡规则")
    public ResponseEntity<Results<PerUnitClimbRuleDTO>> update(@RequestBody PerUnitClimbRuleDTO perUnitClimbRuleDTO) {
        validObject(perUnitClimbRuleDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(perUnitClimbRuleService.saveOrUpdate(perUnitClimbRuleDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除单产爬坡规则")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        perUnitClimbRuleService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出单产爬坡规则")
    @PostMapping("/export")
    public void export(@RequestBody PerUnitClimbRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        perUnitClimbRuleService.export(query, response);
    }

    @ApiOperation(value = "导入单产爬坡规则")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultExDTO importResultDTO = perUnitClimbRuleService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号-三个数据分类汇总")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(perUnitClimbRuleService.queryVersions(versionQuery));
    }

    @PostMapping("/type-versions")
    @ApiOperation(value = "查询版本号-三个数据分类对象")
    public ResponseEntity<Results<Map<Long,List<String>>>> queryDateTypeVersions() {
        return Results.createSuccessRes(perUnitClimbRuleService.queryDateTypeVersions());
    }
}
