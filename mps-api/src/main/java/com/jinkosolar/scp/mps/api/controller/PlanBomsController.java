package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PlanBomsDTO;
import com.jinkosolar.scp.mps.domain.query.PlanBomsQuery;
import com.jinkosolar.scp.mps.service.PlanBomsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件计划的标准BOM行相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 20:02:55
 */
@RequestMapping(value = "/plan-boms")
@RestController
@Api(value = "planBoms", tags = "组件计划的标准BOM行相关操作控制层")
public class PlanBomsController extends BaseController {
    @Autowired
    private PlanBomsService planBomsService;

    @PostMapping("/page")
    @ApiOperation(value = "组件计划的标准BOM行分页查询")
    public ResponseEntity<Results<Page<PlanBomsDTO>>> page(@RequestBody PlanBomsQuery query) {
        return Results.createSuccessRes(planBomsService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件计划的标准BOM行详情")
    public ResponseEntity<Results<PlanBomsDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(planBomsService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件计划的标准BOM行")
    public ResponseEntity<Results<PlanBomsDTO>> insert(@RequestBody PlanBomsDTO planBomsDTO) {
        validObject(planBomsDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(planBomsService.saveOrUpdate(planBomsDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件计划的标准BOM行")
    public ResponseEntity<Results<PlanBomsDTO>> update(@RequestBody PlanBomsDTO planBomsDTO) {
        validObject(planBomsDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(planBomsService.saveOrUpdate(planBomsDTO));
    }
    @PostMapping("/save-many")
    @ApiOperation(value = "更新组件计划的标准BOM行（批量）")
    public ResponseEntity<Results<PlanBomsDTO>> saveMany(@RequestBody List<PlanBomsDTO> dtos) {
        planBomsService.saveMany(dtos);
        return Results.createSuccessRes();
    }
    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件计划的标准BOM行")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        planBomsService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出组件计划的标准BOM行")
    @PostMapping("/export")
    public void export(@RequestBody PlanBomsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        planBomsService.export(query, response);
    }

    @ApiOperation(value = "导入组件计划的标准BOM行")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = planBomsService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}