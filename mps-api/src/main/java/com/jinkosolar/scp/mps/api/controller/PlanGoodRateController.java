package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PlanGoodRateDTO;
import com.jinkosolar.scp.mps.domain.query.PlanGoodRateQuery;
import com.jinkosolar.scp.mps.service.PlanGoodRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 计划良率表相关操作控制层
 *
 * <AUTHOR> 2024-04-19 14:33:40
 */
@RequestMapping(value = "/plan-good-rate")
@RestController
@Api(value = "planGoodRate", tags = "计划良率表相关操作控制层")
public class PlanGoodRateController extends BaseController {
    @Autowired
    private PlanGoodRateService planGoodRateService;

    @PostMapping("/page")
    @ApiOperation(value = "计划良率表分页查询")
    public ResponseEntity<Results<Page<PlanGoodRateDTO>>> page(@RequestBody PlanGoodRateQuery query) {
        return Results.createSuccessRes(planGoodRateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "计划良率表详情")
    public ResponseEntity<Results<PlanGoodRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(planGoodRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增计划良率表")
    public ResponseEntity<Results<PlanGoodRateDTO>> insert(@RequestBody PlanGoodRateDTO planGoodRateDTO) {
        validObject(planGoodRateDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(planGoodRateService.saveOrUpdate(planGoodRateDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新计划良率表")
    public ResponseEntity<Results<PlanGoodRateDTO>> update(@RequestBody PlanGoodRateDTO planGoodRateDTO) {
        validObject(planGoodRateDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(planGoodRateService.saveOrUpdate(planGoodRateDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除计划良率表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        planGoodRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出计划良率表")
    @PostMapping("/export")
    public void export(@RequestBody PlanGoodRateQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        planGoodRateService.export(query, response);
    }

    @ApiOperation(value = "导入计划良率表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = planGoodRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/queryPlanGoodRateByPlanVersion")
    @ApiOperation(value = "计划良率表分页查询")
    public ResponseEntity<Results<List<PlanGoodRateDTO>>> queryPlanGoodRateByPlanVersion(@RequestBody PlanGoodRateQuery query) {
        return Results.createSuccessRes(planGoodRateService.queryPlanGoodRateByPlanVersion(query));
    }

    @PostMapping("/findPlanGoodRateByCondition")
    @ApiOperation(value = "根据条件查询良率")
    public ResponseEntity<Results<List<PlanGoodRateDTO>>> findPlanGoodRateByCondition(@RequestBody PlanGoodRateQuery query) {
        return Results.createSuccessRes(planGoodRateService.findPlanGoodRateByCondition(query));
    }
}