package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanQuery;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 非组件料号匹配 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@RestController
@RequestMapping("/plan-item-match")
@RequiredArgsConstructor
@Api(value = "plan-item-match", tags = "非组件料号匹配")
public class PlanItemMatchController {
    private final NonModuleProductionPlanService nonModuleProductionPlanService;

    @PostMapping("/versions")
    @ApiOperation(value = "非组件料号匹配版本列表", notes = "非组件料号匹配版本列表")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.versions(query));
    }

    /**
     * 非组件料号匹配分页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "非组件料号匹配分页列表", notes = "非组件料号匹配分页列表")
    public ResponseEntity<Results<Page<NonModuleProductionPlanDTO>>> queryItemMatchByPage(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.queryItemMatchByPage(query));
    }

    /**
     * 非组件料号匹配列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "非组件料号匹配列表", notes = "非组件料号匹配列表")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> queryItemMatchByList(@RequestBody NonModuleProductionPlanQuery query) {
        Assert.notEmpty(query.getIdsDTO().getIds(), "传入的ids不能为空");
        List<Long> ids = query.getIdsDTO().getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        List<NonModuleProductionPlanDTO> list = nonModuleProductionPlanService.findListByIds(ids);
        return Results.createSuccessRes(list);
    }

    /**
     * 非组件料号匹配后同步结果数据
     */
    @PostMapping("/changeMatchCellPlanData")
    @ApiOperation(value = "电池料号匹配更新切换计划")
    public ResponseEntity<Results<Object>> changeMatchCellPlanData(@RequestBody List<NonModuleProductionPlanDTO> dtoList) {
        nonModuleProductionPlanService.changeMatchCellPlanData(dtoList);
        return Results.createSuccessRes(Results.SUCCESS_CODE);
    }

    /**
     * 非组件料号匹配后同步结果数据
     */
    @PostMapping("/changeDataByItemMatch")
    @ApiOperation(value = "非组件料号匹配后同步结果数据")
    public ResponseEntity<Results<Object>> changeDataByItemMatch(@RequestBody List<NonModuleProductionPlanDTO> dtoList) {
        nonModuleProductionPlanService.changeDataByItemMatch(dtoList);
        return Results.createSuccessRes();
    }

    /**
     * 非组件料号匹配后同步结果数据
     */
    @PostMapping("/batchSaveItemCodeByList")
    @ApiOperation(value = "非组件料号匹配批量选择料号保存")
    public ResponseEntity<Results<Object>> batchSaveItemCodeByList(@RequestBody List<NonModuleProductionPlanDTO> dtoList) {
        nonModuleProductionPlanService.batchSaveItemCodeByList(dtoList);
        dtoList = nonModuleProductionPlanService.planMatchListInfo(dtoList);
        return Results.createSuccessRes(dtoList);
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/matchItemSave")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<NonModuleProductionPlanDTO>> matchItemSave(@Valid @RequestBody NonModuleProductionPlanDTO saveDTO) {
        return Results.createSuccessRes(nonModuleProductionPlanService.matchItemSave(saveDTO));
    }

    /**
     * 非组件料号匹配批量发布
     */
    @PostMapping("/batchMatchItemConfirm")
    @ApiOperation(value = "非组件料号匹配批量发布")
    public ResponseEntity<Results<Object>> batchMatchItemConfirm(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        nonModuleProductionPlanService.barchMatchItemConfirm(ids);
        return Results.createSuccessRes();
    }

    /**
     * 非组件料号匹配批量撤回
     */
    @PostMapping("/barchMatchItemRevoke")
    @ApiOperation(value = "非组件料号匹配批量撤回")
    public ResponseEntity<Results<Object>> barchMatchItemRevoke(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        nonModuleProductionPlanService.barchMatchItemRevoke(ids);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        nonModuleProductionPlanService.exportQueryItemMatchByPage(query, response);
    }

    /**
     * 籽晶/酸洗 发布
     */
    @PostMapping("/batchMatchItemPublish")
    @ApiOperation(value = "籽晶/酸洗 发布")
    public ResponseEntity<Results<Object>> batchMatchItemPublish(@RequestBody List<NonModuleProductionPlanDTO> dtoList) {
        nonModuleProductionPlanService.batchMatchItemPublish(dtoList);
        return Results.createSuccessRes();
    }
}
