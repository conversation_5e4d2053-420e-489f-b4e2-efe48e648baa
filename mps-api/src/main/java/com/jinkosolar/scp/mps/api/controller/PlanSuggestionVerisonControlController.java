package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PlanSuggestionVerisonControlDTO;
import com.jinkosolar.scp.mps.domain.query.PlanSuggestionVerisonControlQuery;
import com.jinkosolar.scp.mps.service.PlanSuggestionVerisonControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产建议版本控制表相关操作控制层
 * 
 * <AUTHOR> 2024-07-11 16:28:21
 */
@RequestMapping(value = "/plan-suggestion-verison-control")
@RestController
@Api(value = "planSuggestionVerisonControl", tags = "生产建议版本控制表相关操作控制层")
public class PlanSuggestionVerisonControlController extends BaseController {
    @Autowired
    private PlanSuggestionVerisonControlService planSuggestionVerisonControlService;

    @PostMapping("/page")
    @ApiOperation(value = "生产建议版本控制表分页查询")
    public ResponseEntity<Results<Page<PlanSuggestionVerisonControlDTO>>> page(@RequestBody PlanSuggestionVerisonControlQuery query) {
        return Results.createSuccessRes(planSuggestionVerisonControlService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "生产建议版本控制表详情")
    public ResponseEntity<Results<PlanSuggestionVerisonControlDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(planSuggestionVerisonControlService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增生产建议版本控制表")
    public ResponseEntity<Results<PlanSuggestionVerisonControlDTO>> insert(@RequestBody PlanSuggestionVerisonControlDTO planSuggestionVerisonControlDTO) {
        validObject(planSuggestionVerisonControlDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(planSuggestionVerisonControlService.saveOrUpdate(planSuggestionVerisonControlDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新生产建议版本控制表")
    public ResponseEntity<Results<PlanSuggestionVerisonControlDTO>> update(@RequestBody PlanSuggestionVerisonControlDTO planSuggestionVerisonControlDTO) {
        validObject(planSuggestionVerisonControlDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(planSuggestionVerisonControlService.saveOrUpdate(planSuggestionVerisonControlDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除生产建议版本控制表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        planSuggestionVerisonControlService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出生产建议版本控制表")
    @PostMapping("/export")
    public void export(@RequestBody PlanSuggestionVerisonControlQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        planSuggestionVerisonControlService.export(query, response);
    }

    @ApiOperation(value = "导入生产建议版本控制表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = planSuggestionVerisonControlService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}