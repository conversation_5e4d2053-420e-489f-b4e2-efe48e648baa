package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanTempDTO;
import com.jinkosolar.scp.mps.domain.dto.OpenLineNumDetailDTO;
import com.jinkosolar.scp.mps.domain.query.OpenLineNumDetailQuery;
import com.jinkosolar.scp.mps.domain.query.ProductionPlanForSapQuery;
import com.jinkosolar.scp.mps.service.OpenLineNumDetailService;
import com.jinkosolar.scp.mps.service.PlanSynSapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/plan-syn-sap")
@Api(value = "plan-syn-sap", tags = "SCP同步给SAP相关操作控制层")
@Slf4j
@RequiredArgsConstructor
public class PlanSynSapController {
   private  final PlanSynSapService planSynSapService;

    @PostMapping("/getNoModuleProductionPlanData")
    @ApiOperation(value = "获取非组件计划数据")
    public ResponseEntity<Results<List<NonModuleProductionPlanTempDTO>>> getNoModuleProductionPlanData(@RequestBody ProductionPlanForSapQuery query) {
        return Results.createSuccessRes(planSynSapService.getNoModuleProductionPlanData(query));
    }
    @PostMapping("/getModuleProductionPlanData")
    @ApiOperation(value = "获取非组件计划数据")
    public ResponseEntity<Results<List<ModuleProductionPlanDTO>>> getModuleProductionPlanData(@RequestBody ProductionPlanForSapQuery query) {
        return Results.createSuccessRes(planSynSapService.getModuleProductionPlanData(query));
    }
    @PostMapping("/plan-syn-sap")
    @ApiOperation(value = "scp同步到sap")
    public ResponseEntity<Results< Object>> planSynSap( ) {
        //生产计划下发
         planSynSapService.planSynSap();
        return Results.createSuccessRes( );
    }
    @PostMapping("/plan-syn-sap-have-bom")
    @ApiOperation(value = "scp同步到sap(包含bom)")
    public ResponseEntity<Results< Object>> planSynSapHaveBom( ) {
        //生产建议下发ERP
        planSynSapService.planSynSapHaveBom();
        return Results.createSuccessRes( );
    }
}
