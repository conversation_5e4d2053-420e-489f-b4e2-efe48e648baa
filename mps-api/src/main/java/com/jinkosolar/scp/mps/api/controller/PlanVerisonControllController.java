package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PlanVerisonControllDTO;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanTempQuery;
import com.jinkosolar.scp.mps.domain.query.PlanVerisonControllQuery;
import com.jinkosolar.scp.mps.service.PlanVerisonControllService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 排产版本控制表相关操作控制层
 * 
 * <AUTHOR> 2024-06-05 14:12:05
 */
@RequestMapping(value = "/plan-verison-controll")
@RestController
@Api(value = "planVerisonControll", tags = "排产版本控制表相关操作控制层")
public class PlanVerisonControllController extends BaseController {
    @Autowired
    private PlanVerisonControllService planVerisonControllService;

    @PostMapping("/page")
    @ApiOperation(value = "排产版本控制表分页查询")
    public ResponseEntity<Results<Page<PlanVerisonControllDTO>>> page(@RequestBody PlanVerisonControllQuery query) {
        return Results.createSuccessRes(planVerisonControllService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "排产版本控制表详情")
    public ResponseEntity<Results<PlanVerisonControllDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(planVerisonControllService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增排产版本控制表")
    public ResponseEntity<Results<PlanVerisonControllDTO>> insert(@RequestBody PlanVerisonControllDTO planVerisonControllDTO) {
        validObject(planVerisonControllDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(planVerisonControllService.saveOrUpdate(planVerisonControllDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新排产版本控制表")
    public ResponseEntity<Results<PlanVerisonControllDTO>> update(@RequestBody PlanVerisonControllDTO planVerisonControllDTO) {
        validObject(planVerisonControllDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(planVerisonControllService.saveOrUpdate(planVerisonControllDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除排产版本控制表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        planVerisonControllService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出排产版本控制表")
    @PostMapping("/export")
    public void export(@RequestBody PlanVerisonControllQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        planVerisonControllService.export(query, response);
    }

    @ApiOperation(value = "导入排产版本控制表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = planVerisonControllService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/queryPlanVersionMax")
    @ApiOperation(value = "获取最新已确认发布的料号匹配版排产数据")
    public ResponseEntity<Results<PlanVerisonControllDTO>> queryPlanVersionMax(@RequestBody PlanVerisonControllQuery query) {
        return Results.createSuccessRes(planVerisonControllService.queryPlanVersionMax(query));
    }

    @PostMapping("/createVersion")
    @ApiOperation(value = "创建版本")
    public ResponseEntity<Results<PlanVerisonControllDTO>> createVersion(@RequestBody NonModuleProductionPlanTempQuery query) {
        return Results.createSuccessRes(planVerisonControllService.createPlanVersion(query));
    }


    @PostMapping("/queryPlanVersionByStatus")
    @ApiOperation(value = "获取最新排产版本数据")
    public ResponseEntity<Results<List<PlanVerisonControllDTO>>> queryPlanVersionByStatus(@RequestBody PlanVerisonControllQuery query) {
        return Results.createSuccessRes(planVerisonControllService.queryPlanVersionByStatus(query));
    }

    @PostMapping("/findLastVersionByDomesticOversea")
    @ApiOperation(value = "获取各个排产区域最大版本号")
    public ResponseEntity<Results<List<PlanVerisonControllDTO>>> findLastVersionByDomesticOversea(@RequestBody PlanVerisonControllQuery query) {
        return Results.createSuccessRes(planVerisonControllService.findLastVersionByDomesticOversea(query));
    }

    @PostMapping("/queryPlanVersionByStatusNew")
    @ApiOperation(value = "获取最新排产版本数据->选择status=Y，或者status=N 但是attribute3=Y的版本")
    public ResponseEntity<Results<List<PlanVerisonControllDTO>>> queryPlanVersionByStatusNew(@RequestBody PlanVerisonControllQuery query) {
        return Results.createSuccessRes(planVerisonControllService.queryPlanVersionByStatusNew(query));
    }

    @PostMapping("/findLastVersionByDomesticOverseaByMrpQuotaCalcul")
    @ApiOperation(value = "获取各个排产区域最大版本号,mrp采购配额计算使用")
    public ResponseEntity<Results<PlanVerisonControllDTO>> findLastVersionByDomesticOverseaByMrpQuotaCalcul(@RequestBody PlanVerisonControllQuery query) {
        return Results.createSuccessRes(planVerisonControllService.findLastVersionByDomesticOverseaByMrpQuotaCalcul(query));
    }

    @PostMapping("/queryPlanVersionByFactoryIds")
    @ApiOperation(value = "获取各个排产区域最大版本号,mrp采购配额计算使用")
    public ResponseEntity<Results<List<String>>> queryPlanVersionByFactoryIds(@RequestBody IdsDTO idsDTO) {
        return Results.createSuccessRes(planVerisonControllService.queryPlanVersionByFactoryIds(
                idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList())));
    }
}