package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerBaseShortDTO;
import com.jinkosolar.scp.mps.domain.query.PowerBaseShortQuery;
import com.jinkosolar.scp.mps.domain.save.PowerBaseShortSaveDTO;
import com.jinkosolar.scp.mps.service.PowerBaseShortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.stream.Collectors;

/**
 * 预测基准档位功率 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:07
 */
@RestController
@RequestMapping("/power-base-short")
@Api(value = "power-base-short", tags = "预测基准档位功率操作")
public class PowerBaseShortController {
    @Autowired
    PowerBaseShortService powerBaseShortService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "预测基准档位功率分页列表", notes = "获得预测基准档位功率分页列表")
    public ResponseEntity<Results<Page<PowerBaseShortDTO>>> queryByPage(@RequestBody PowerBaseShortQuery query) {
        return Results.createSuccessRes(powerBaseShortService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerBaseShortDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerBaseShortService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerBaseShortDTO>> save(@Valid @RequestBody PowerBaseShortSaveDTO saveDTO) {
        return Results.createSuccessRes(powerBaseShortService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerBaseShortService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 导入数据
     *
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "EX导入数据")
    public ResponseEntity<Results<Object>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws IOException {
        powerBaseShortService.importData(file,excelPara);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerBaseShortQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerBaseShortService.exportData(query, response);
    }

    @PostMapping("/powerBaseShortCopy")
    @ApiOperation(value = "复制")
    public ResponseEntity<Results<Object>> powerBaseShortCopy(@RequestBody PowerBaseShortDTO powerBaseShortDTO) {
        return Results.createSuccessRes("生成V" + powerBaseShortService.powerBaseShortCopy(powerBaseShortDTO) + "版本数据成功");
    }

    @PostMapping("/copyDataByMonth")
    @ApiOperation(value = "按月份复制数据")
    public ResponseEntity<Results<Object>> copyDataByMonth(@RequestBody PowerBaseShortDTO powerBaseShortDTO) {
        return Results.createSuccessRes(powerBaseShortService.copyDataByMonth(powerBaseShortDTO));
    }


}
