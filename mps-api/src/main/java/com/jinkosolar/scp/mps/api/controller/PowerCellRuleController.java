package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.ExcelUtils;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerCellRuleDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerCellRule;
import com.jinkosolar.scp.mps.domain.query.PowerCellRuleQuery;
import com.jinkosolar.scp.mps.domain.save.PowerCellRuleSaveDTO;
import com.jinkosolar.scp.mps.service.PowerCellRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池分类规则 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:19
 */
@RestController
@RequestMapping("/power-cell-rule")
@Api(value = "power-cell-rule", tags = "电池分类规则操作")
public class PowerCellRuleController {
    @Autowired
    PowerCellRuleService powerCellRuleService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池分类规则分页列表", notes = "获得电池分类规则分页列表")
    public ResponseEntity<Results<Page<PowerCellRule>>> queryByPage(@RequestBody PowerCellRuleQuery query) {
        return Results.createSuccessRes(powerCellRuleService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerCellRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerCellRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerCellRuleDTO>> save(@Valid @RequestBody PowerCellRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(powerCellRuleService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerCellRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 导入数据
     *
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "EX导入数据")
    public ResponseEntity<Results<Object>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws IOException {
        List<PowerCellRule> list = ExcelUtils.readExcel(file.getInputStream(), null, PowerCellRule.class, excelPara);
        powerCellRuleService.batchSave(list);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerCellRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        Page<PowerCellRule> page = powerCellRuleService.queryByPage(query);
        String sheet = "电池分类规则";

        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        List<List<Object>> objList = ExcelUtils.getList(page.getContent(), excelPara);
        ExcelUtils.exportEx(response, "电池分类规则", sheet, simpleHeader, objList);
    }
}
