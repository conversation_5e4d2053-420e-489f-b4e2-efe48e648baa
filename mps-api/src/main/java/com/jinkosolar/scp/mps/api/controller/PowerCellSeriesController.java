package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.dto.PowerCellSeriesDTO;
import com.jinkosolar.scp.mps.domain.query.PowerCellSeriesQuery;
import com.jinkosolar.scp.mps.service.PowerCellSeriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池片系列基表相关操作控制层
 *
 * <AUTHOR> 2024-05-14 10:52:04
 */
@RequestMapping(value = "/power-cell-series")
@RestController
@Api(value = "powerCellSeries", tags = "电池片系列基表相关操作控制层")
public class PowerCellSeriesController extends BaseController {
    @Autowired
    private PowerCellSeriesService powerCellSeriesService;

    @PostMapping("/page")
    @ApiOperation(value = "电池片系列基表分页查询")
    public ResponseEntity<Results<Page<PowerCellSeriesDTO>>> page(@RequestBody PowerCellSeriesQuery query) {
        return Results.createSuccessRes(powerCellSeriesService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池片系列基表详情")
    public ResponseEntity<Results<PowerCellSeriesDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerCellSeriesService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池片系列基表")
    public ResponseEntity<Results<PowerCellSeriesDTO>> insert(@RequestBody PowerCellSeriesDTO powerCellSeriesDTO) {
        validObject(powerCellSeriesDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerCellSeriesService.saveOrUpdate(powerCellSeriesDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池片系列基表")
    public ResponseEntity<Results<PowerCellSeriesDTO>> update(@RequestBody PowerCellSeriesDTO powerCellSeriesDTO) {
        validObject(powerCellSeriesDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerCellSeriesService.saveOrUpdate(powerCellSeriesDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池片系列基表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerCellSeriesService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池片系列基表")
    @PostMapping("/export")
    public void export(@RequestBody PowerCellSeriesQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerCellSeriesService.export(query, response);
    }

    @ApiOperation(value = "导入电池片系列基表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerCellSeriesService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "电池片系列描述")
    @PostMapping("/listDesc")
    public ResponseEntity<Results<List<CommonOption>>> listDesc(@RequestBody PowerCellSeriesQuery query) {
        return Results.createSuccessRes(powerCellSeriesService.listDesc(query));
    }
    @PostMapping("/archive")
    @ApiOperation(value = "批量归档")
    public ResponseEntity<Results<Object>> archive(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerCellSeriesService.archive(ids);
        return Results.createSuccessRes();
    }
}
