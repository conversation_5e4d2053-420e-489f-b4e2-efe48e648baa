package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerCellTypeDTO;
import com.jinkosolar.scp.mps.domain.query.PowerCellTypeQuery;
import com.jinkosolar.scp.mps.domain.save.PowerCellTypeSaveDTO;
import com.jinkosolar.scp.mps.service.PowerCellTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 产品系列对应电池类型 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-28 15:55:47
 */
@RestController
@RequestMapping("/power-cell-type")
@Api(value = "power-cell-type", tags = "产品系列对应电池类型操作")
public class PowerCellTypeController extends BaseController {
    @Autowired
    PowerCellTypeService powerCellTypeService;

    @PostMapping("/page")
    @ApiOperation(value = "产品系列对应电池类型分页列表", notes = "获得产品系列对应电池类型分页列表")
    public ResponseEntity<Results<Page<PowerCellTypeDTO>>> queryByPage(@RequestBody PowerCellTypeQuery query) {
        return Results.createSuccessRes(powerCellTypeService.queryByPage(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerCellTypeDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerCellTypeService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerCellTypeDTO>> save(@Valid @RequestBody PowerCellTypeSaveDTO saveDTO) {
        return Results.createSuccessRes(powerCellTypeService.save(saveDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerCellTypeService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerCellTypeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerCellTypeService.export(query, response);
    }

    @PostMapping("/lovDataSave")
    @ApiOperation(value = "Lov数据获取")
    public void lovDataSave() {
        powerCellTypeService.lovDataSave();
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(powerCellTypeService.importData(multipartFile, excelPara));
    }

    @PostMapping("/findAll")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<List<PowerCellTypeDTO>>> findAll() {
        return Results.createSuccessRes(powerCellTypeService.findAll());
    }
}
