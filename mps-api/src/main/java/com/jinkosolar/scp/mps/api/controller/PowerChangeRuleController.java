package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.ExcelUtils;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerChangeRuleDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerChangeRule;
import com.jinkosolar.scp.mps.domain.query.PowerChangeRuleQuery;
import com.jinkosolar.scp.mps.domain.save.PowerChangeRuleSaveDTO;
import com.jinkosolar.scp.mps.service.PowerChangeRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 材料变动规则 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:42
 */
@RestController
@RequestMapping("/power-change-rule")
@Api(value = "power-change-rule", tags = "材料变动规则操作")
public class PowerChangeRuleController {
    @Autowired
    PowerChangeRuleService powerChangeRuleService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "材料变动规则分页列表", notes = "获得材料变动规则分页列表")
    public ResponseEntity<Results<Page<PowerChangeRule>>> queryByPage(@RequestBody PowerChangeRuleQuery query) {
        return Results.createSuccessRes(powerChangeRuleService.queryByPage(query));
    }

    @PostMapping("/list")
    @ApiOperation(value = "材料变动规则分页列表", notes = "获得材料变动规则分页列表")
    public ResponseEntity<Results<List<PowerChangeRule>>> queryBylist(@RequestBody PowerChangeRuleQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(powerChangeRuleService.queryByPage(query).getContent());
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerChangeRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerChangeRuleService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerChangeRuleDTO>> save(@Valid @RequestBody PowerChangeRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(powerChangeRuleService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerChangeRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }


    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerChangeRuleQuery query, HttpServletResponse response) {
        List<PowerChangeRule> list = powerChangeRuleService.queryAll(query);
        String sheet = "功率转换规则";

        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        List<List<Object>> objList = ExcelUtils.getList(list, excelPara);
        ExcelUtils.exportEx(response, sheet, sheet, simpleHeader, objList);
    }

    /**
     * 导入数据
     *
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "EX导入数据")
    public ResponseEntity<Results<Object>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws IOException {
        List<PowerChangeRule> list = ExcelUtils.readExcel(file.getInputStream(), null, PowerChangeRule.class, excelPara);
        powerChangeRuleService.batchSave(list);
        return Results.createSuccessRes();
    }

}
