package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerCollectGapDTO;
import com.jinkosolar.scp.mps.domain.query.PowerCollectGapQuery;
import com.jinkosolar.scp.mps.domain.save.PowerCollectGapSaveDTO;
import com.jinkosolar.scp.mps.service.PowerCollectGapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率预测GAP汇总 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:32:47
 */
@RestController
@RequestMapping("/power-collect-gap")
@Api(value = "power-collect-gap", tags = "功率预测GAP汇总操作")
public class PowerCollectGapController {
    @Autowired
    PowerCollectGapService powerCollectGapService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "功率预测GAP汇总分页列表", notes = "获得功率预测GAP汇总分页列表")
    public ResponseEntity<Results<Page<PowerCollectGapDTO>>> queryByPage(@RequestBody PowerCollectGapQuery query) {
        return Results.createSuccessRes(powerCollectGapService.queryByPage(query));
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerCollectGapQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerCollectGapService.export(query, response);

    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerCollectGapDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerCollectGapService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerCollectGapDTO>> save(@Valid @RequestBody PowerCollectGapSaveDTO saveDTO) {
        return Results.createSuccessRes(powerCollectGapService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerCollectGapService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/doCaclGAP")
    @ApiOperation(value = "计算GAP")
    public ResponseEntity<Results<Object>> doCaclGAP(@RequestBody PowerCollectGapDTO powerCollectGapDTO) {
        powerCollectGapService.caclGAP(powerCollectGapDTO.getCellType(), powerCollectGapDTO.getMonth());
        return Results.createSuccessRes();
    }

    @PostMapping("/loadPowerCollectGap")
    @ApiOperation(value = "功率预测GAP汇总列表", notes = "获得功率预测GAP汇总列表")
    public ResponseEntity<Results<List<PowerCollectGapDTO>>> loadPowerCollectGap(@RequestBody PowerCollectGapQuery query) {
        return Results.createSuccessRes(powerCollectGapService.loadPowerCollectGap(query));
    }

    @PostMapping("/autoAdjustGap")
    public ResponseEntity<Results<List<PowerCollectGapDTO>>> autoAdjustGap(@RequestBody PowerCollectGapQuery query) {
        powerCollectGapService.autoAdjustGap(query.getMonth());
        return Results.createSuccessRes(null);
    }
}
