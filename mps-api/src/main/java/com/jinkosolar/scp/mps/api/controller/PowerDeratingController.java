package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PowerDeratingDTO;
import com.jinkosolar.scp.mps.domain.query.PowerDeratingQuery;
import com.jinkosolar.scp.mps.service.PowerDeratingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率落档基表相关操作控制层
 *
 * <AUTHOR> 2024-05-14 10:52:04
 */
@RequestMapping(value = "/power-derating")
@RestController
@Api(value = "powerDerating", tags = "功率落档基表相关操作控制层")
public class PowerDeratingController extends BaseController {
    @Autowired
    private PowerDeratingService powerDeratingService;

    @PostMapping("/page")
    @ApiOperation(value = "功率落档基表分页查询")
    public ResponseEntity<Results<Page<PowerDeratingDTO>>> page(@RequestBody PowerDeratingQuery query) {
        return Results.createSuccessRes(powerDeratingService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "功率落档基表详情")
    public ResponseEntity<Results<PowerDeratingDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerDeratingService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增功率落档基表")
    public ResponseEntity<Results<PowerDeratingDTO>> insert(@RequestBody PowerDeratingDTO powerDeratingDTO) {
        validObject(powerDeratingDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerDeratingService.saveOrUpdate(powerDeratingDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新功率落档基表")
    public ResponseEntity<Results<PowerDeratingDTO>> update(@RequestBody PowerDeratingDTO powerDeratingDTO) {
        validObject(powerDeratingDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerDeratingService.saveOrUpdate(powerDeratingDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除功率落档基表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerDeratingService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出功率落档基表")
    @PostMapping("/export")
    public void export(@RequestBody PowerDeratingQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerDeratingService.export(query, response);
    }

    @ApiOperation(value = "导入功率落档基表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerDeratingService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions() {
        return Results.createSuccessRes(powerDeratingService.versions());
    }

    @PostMapping("/findByVersion4OrderQuery")
    @ApiOperation(value = "询单用查询基础表版本")
    public ResponseEntity<Results<List<PowerDeratingDTO>>> findByVersion(@RequestBody String version) {
        return Results.createSuccessRes(powerDeratingService.findByVersion(version));
    }
}
