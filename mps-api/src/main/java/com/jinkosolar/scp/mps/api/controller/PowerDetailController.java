package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.query.MtlMixStatisticalReportQuery;
import com.jinkosolar.scp.mps.domain.query.PowerDetailQuery;
import com.jinkosolar.scp.mps.domain.save.PowerDetailSaveDTO;
import com.jinkosolar.scp.mps.service.PowerDetailService;
import com.jinkosolar.scp.mps.service.PowerDetailStatisticalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率预测明细 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:32:44
 */
@RestController
@RequestMapping("/power-detail")
@Api(value = "power-detail", tags = "功率预测明细操作")
@Slf4j
public class PowerDetailController {
    @Autowired
    private PowerDetailService powerDetailService;

    @Autowired
    private PowerDetailStatisticalService powerDetailStatisticalService;

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "功率预测明细分页列表", notes = "获得功率预测明细分页列表")
    public ResponseEntity<Results<Page<PowerDetailDTO>>> queryByPage(@RequestBody PowerDetailQuery query) {
        return Results.createSuccessRes(powerDetailService.queryByPage(query));
    }

    /**
     * 根据预测结果查询月度基础档位数据
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/loadPowerResultShortList")
    @ApiOperation(value = "根据预测结果查询月度基础档位数据", notes = "根据预测结果查询月度基础档位数据")
    public ResponseEntity<Results<List<PowerResultShortDTO>>> loadPowerResultShortList(@RequestBody PowerDetailQuery query) {
        return Results.createSuccessRes(powerDetailService.loadPowerShortResultList(query));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/loadPowerDetailListByDpid")
    @ApiOperation(value = "根据dpid获取预测明细", notes = "根据dpid获取预测明细")
    public ResponseEntity<Results<List<PowerDetailDTO>>> loadPowerDetailListByDpid(@RequestBody PowerDetailQuery query) {
        return Results.createSuccessRes(powerDetailService.loadPowerDetailListByDpid(query));
    }

    /**
     * 列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @ApiOperation(value = "列表查询", notes = "列表查询")
    public ResponseEntity<Results<List<PowerDetailDTO>>> list(@RequestBody PowerDetailQuery query) {
        return Results.createSuccessRes(powerDetailService.list(query));
    }

    /**
     * 根据dpid+月份获取最新版本的符合率
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/findPassPercent")
    @ApiOperation(value = "列表查询", notes = "列表查询")
    public ResponseEntity<Results<List<PowerDetailDTO>>> findPassPercent(@RequestBody PowerDetailQuery query) {
        return Results.createSuccessRes(powerDetailService.findPassPercent(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerDetailDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerDetailDTO>> save(@Valid @RequestBody PowerDetailSaveDTO saveDTO) {
        return Results.createSuccessRes(powerDetailService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerDetailService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerDetailService.export(query, response);

    }

    /**
     * 前端第一次调用isUpdOldVersion不传值
     * 后端返回0表示前端需要提示今天已保存版本是否将次版本覆盖
     * 用户选择是isUpdOldVersion传Y
     * 后端返回1表示保存版本成功
     *
     * @param query
     * @return
     */
    @PostMapping("/savePowerPredictVersion")
    @ApiOperation(value = "功率预测保存版本")
    public ResponseEntity<Results<Object>> savePowerPredictVersion(@RequestBody PowerDetailQuery query) {
        return Results.createSuccessRes(powerDetailService.savePowerPredictVersion(query));
    }

    @PostMapping("/doCaclPowerDetail")
    @ApiOperation(value = "功率预测计算")
    public ResponseEntity<Results<Object>> doCaclPowerDetail(@RequestBody PowerDetailQuery query) {
        String msg = powerDetailService.caclPowerDetailHandler(query);
        if (StringUtils.isNotBlank(msg)) {
            return Results.createSuccessRes(msg);
        }
        return Results.createFailRes(msg);
    }

    @PostMapping("/doRecalcPowerDetail")
    @ApiOperation(value = "功率预测手工调整")
    public ResponseEntity<Results<List<PowerDetailDTO>>> doRecalcPowerDetail(@RequestBody PowerDetailAdjustDTO powerDetailAdjustDTO) {
        return Results.createSuccessRes(powerDetailService.doRecalcPowerDetail(powerDetailAdjustDTO));
    }

    @PostMapping("/doRecalcPowerDetailUpdVersion")
    @ApiOperation(value = "功率预测手工调整版本")
    public ResponseEntity<Results<List<PowerDetailDTO>>> doRecalcPowerDetailUpdVersion(@RequestBody PowerDetailAdjustDTO powerDetailAdjustDTO) {
        return Results.createSuccessRes(powerDetailService.doRecalcPowerDetailUpdVersion(powerDetailAdjustDTO));
    }

    @PostMapping("/mtlMixStatisticalReport")
    @ApiOperation(value = "材料搭配统计报表")
    public ResponseEntity<Results<MtlMixStatisticalReportDTO>> mtlMixStatisticalReport(@RequestBody MtlMixStatisticalReportQuery query) {
        MtlMixStatisticalReportDTO data = powerDetailStatisticalService.mtlMixStatisticalReport(query);
        return Results.createSuccessRes(data);
    }

    @PostMapping("/mtlMixStatisticalReportExport")
    @ApiOperation(value = "材料搭配统计报表")
    public void mtlMixStatisticalReportExport(HttpServletResponse response, @RequestBody MtlMixStatisticalReportQuery query) throws IOException {
        powerDetailStatisticalService.export(response, query);
    }

    /**
     * 根据预测结果查询月度基础档位数据
     *
     * @return 查询结果
     */
    @PostMapping("/modulePassPercent")
    @ApiOperation(value = "组件功率符合率", notes = "根据预测结果查询月度基础档位数据")
    public ResponseEntity<Results<List<ModulePassPercentDTO>>> modulePassPercent(@RequestBody ModulePassPercentDTO modulePassPercentDTO) {
        return Results.createSuccessRes(powerDetailService.modulePassPercent(modulePassPercentDTO));
    }

    /**
     * 修改预测结果标记
     *
     * @return 查询结果
     */
    @PostMapping("/updFlagByDpGroupId")
    @ApiOperation(value = "修改预测结果标记", notes = "修改预测结果标记")
    public ResponseEntity<Results<List<Object>>> updFlagByDpGroupId(@RequestBody PowerDetailDTO powerDetailDTO) {
        powerDetailService.updFlagByDpGroupId(powerDetailDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/powerPredictIsInSuperBomReq")
    @ApiOperation(value = "超级bom校验")
    public ResponseEntity<Results<Object>> powerPredictIsInSuperBomReq(@RequestBody PowerDetailQuery query) {
        powerDetailService.powerPredictIsInSuperBomReq(query);
        return Results.createSuccessRes();
    }

    @PostMapping("/mergePowerDetailData")
    @ApiOperation(value = "合并导入的功率预测数据")
    public ResponseEntity<Results<Object>> mergePowerDetailData(@RequestBody PowerDetailQuery query) {
        powerDetailService.mergePowerDetailData(query);
        return Results.createSuccessRes();
    }

//    @PostMapping("/sendDpSendErrorEmail")
//    @ApiOperation(value = "发送邮件测试")
//    public ResponseEntity<Results<Object>> sendDpSendErrorEmail(@RequestBody PowerDetailQuery query) {
//        powerDetailService.sendDpSendErrorEmail(query.getApplicationId());
//        return Results.createSuccessRes();
//    }

}
