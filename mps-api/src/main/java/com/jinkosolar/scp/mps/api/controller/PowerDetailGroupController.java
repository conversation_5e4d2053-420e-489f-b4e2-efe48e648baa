package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerDetailGroupDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerDetailGroupQuery;
import com.jinkosolar.scp.mps.service.PowerDetailGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 *
 * @date 2022-09-13
 */
@RestController
@RequestMapping("/power-detail-group")
@Api(value = "power-detail-group", tags = "功率预测异步")
public class PowerDetailGroupController extends BaseController {
    @Autowired
    PowerDetailGroupService powerDetailGroupService;

    @PostMapping("/page")
    @ApiOperation(value = "调拨清单分页列表", notes = "获得调拨清单分页列表")
    public ResponseEntity<Results<Page<PowerDetailGroupDTO>>> queryByPage(@RequestBody PowerDetailGroupQuery query) {
        return Results.createSuccessRes(powerDetailGroupService.queryByPage(query));
    }
}
