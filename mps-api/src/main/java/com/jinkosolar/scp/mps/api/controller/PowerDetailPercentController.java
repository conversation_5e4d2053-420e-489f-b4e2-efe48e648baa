package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerDetailPercentDTO;
import com.jinkosolar.scp.mps.domain.query.PowerDetailExportQuery;
import com.jinkosolar.scp.mps.domain.query.PowerDetailPercentQuery;
import com.jinkosolar.scp.mps.domain.save.PowerDetailPercentSaveDTO;
import com.jinkosolar.scp.mps.service.PowerDetailPercentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @date 2022-12-13
 */
@RestController
@RequestMapping("/power-detail-percent")
@Api(value = "/power-detail-percent", tags = "")
public class PowerDetailPercentController {
    @Autowired
    private PowerDetailPercentService powerDetailPercentService;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页列表", notes = "分页列表")
    public ResponseEntity<Results<Page<PowerDetailPercentDTO>>> queryByPage(@RequestBody PowerDetailPercentQuery query) {
        return Results.createSuccessRes(powerDetailPercentService.queryByPage(query));
    }

     /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerDetailPercentDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerDetailPercentService.queryById(Long.parseLong(idDTO.getId())));
    }
    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerDetailPercentDTO>> save(@Valid @RequestBody PowerDetailPercentSaveDTO saveDTO) {
        return Results.createSuccessRes(powerDetailPercentService.save(saveDTO));
    }
    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerDetailPercentService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerDetailPercentQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerDetailPercentService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(powerDetailPercentService.importData(multipartFile, excelPara));
    }

    @PostMapping("/deleteByMonth")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> deleteByMonth(@RequestBody PowerDetailPercentDTO powerDetailPercentDTO) {
        powerDetailPercentService.deleteByMonth(powerDetailPercentDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/sendEmail")
    @ApiOperation(value = "组件功率符合率-发送邮件")
    public ResponseEntity<Results<Object>> sendEmail(@RequestBody PowerDetailExportQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerDetailPercentService.sendEmail(query);
        return Results.createSuccessRes();
    }
    @PostMapping("/checkEmail")
    @ApiOperation(value = "校验发送邮件")
    public ResponseEntity<Results<Object>> checkEmail(@RequestBody PowerDetailExportQuery query) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);

        return Results.createSuccessRes(powerDetailPercentService.checkEmail(query));
    }
}
