package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.dpf.base.core.util.StringUtils;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.DataColumn;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.ExcelUtils;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerEfficiencyDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerEfficiencyMainDTO;
import com.jinkosolar.scp.mps.domain.query.CellForecastQuery;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanTempQuery;
import com.jinkosolar.scp.mps.domain.query.PowerEfficiencyMainCustomUniqueQuery;
import com.jinkosolar.scp.mps.domain.query.PowerEfficiencyQuery;
import com.jinkosolar.scp.mps.domain.save.PowerEfficiencySaveDTO;
import com.jinkosolar.scp.mps.service.CellForecastService;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanService;
import com.jinkosolar.scp.mps.service.PowerEfficiencyMainService;
import com.jinkosolar.scp.mps.service.PowerEfficiencyService;
import com.jinkosolar.scp.mps.util.ExcelFillPercentageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * 全年效率值 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:13
 */
@RestController
@RequestMapping("/power-efficiency")
@Api(value = "power-efficiency", tags = "全年效率值操作")
public class PowerEfficiencyController {
    @Autowired
    private PowerEfficiencyService powerEfficiencyService;
    @Resource
    private PowerEfficiencyMainService powerEfficiencyMainService;
    @Resource
    private NonModuleProductionPlanService nonModuleProductionPlanService;

    @Resource
    private CellForecastService cellForecastService;

    @Resource
    @Qualifier("asyncTaskExecutor")
    ExecutorService threadPoolExecutor;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "全年效率值分页列表", notes = "获得全年效率值分页列表")
    public ResponseEntity<Results<List<PowerEfficiencyDTO>>> queryByPage(@RequestBody PowerEfficiencyQuery query) {
        return Results.createSuccessRes(powerEfficiencyService.queryWithRelations(query));
    }

    @PostMapping("/reportCompare")
    @ApiOperation(value = "电池效率预测对比报表", notes = "电池效率预测对比报表")
    public ResponseEntity<Results<List<PowerEfficiencyDTO>>> reportCompare(@RequestBody PowerEfficiencyQuery query) {
        return Results.createSuccessRes(powerEfficiencyService.reportCompare(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerEfficiencyDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerEfficiencyService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerEfficiencyDTO>> save(@Valid @RequestBody PowerEfficiencySaveDTO saveDTO) {
        return Results.createSuccessRes(powerEfficiencyService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerEfficiencyService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 导入数据
     *
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "EX导入数据")
    public ResponseEntity<Results<ImportResultDTO>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws IOException {
        ImportResultDTO importResultDTO= powerEfficiencyService.importMainData(file);
        if (importResultDTO.getFailMessages().isEmpty()) {
            powerEfficiencyMainService.syncApsTable();

            try {
                CompletableFuture.runAsync(() -> {
                    CellForecastQuery cellForecastQuery = new CellForecastQuery();
                    cellForecastService.cellForecast(cellForecastQuery);
                }, threadPoolExecutor);
            } catch (Exception e) {
//                log.info("电池产出预测自动计算刷新失败, planType:{}", query.getModelType());
            }
            /*NonModuleProductionPlanTempQuery nonModuleProductionPlanTempQuery = new NonModuleProductionPlanTempQuery();
            nonModuleProductionPlanTempQuery.setModelType("GNDC");
            nonModuleProductionPlanService.flushCellForecast(nonModuleProductionPlanTempQuery);*/
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerEfficiencyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<PowerEfficiencyDTO> list = powerEfficiencyService.queryWithRelations(query);
        if(CollectionUtils.isNotEmpty(list)) {
            //第一行是头,去掉第一行
            list.remove(0);
            ExcelPara excelPara = query.getExcelPara();
            List<DataColumn> oldColumnlist = excelPara.getColumns();
            List<DataColumn> newColumnlist =  new ArrayList<>();
            for (int i = 2;i<oldColumnlist.size();i++){
                int oldIndex= oldColumnlist.get(i).getIndex();
                oldColumnlist.get(i).setIndex(oldIndex-2);
                newColumnlist.add(oldColumnlist.get(i));
            }
            excelPara.setColumns(newColumnlist);

            String sheet = "效率分布";
            List<List<Object>> objList = ExcelUtils.getList(list, excelPara);
            ExcelUtils.exportEx(response, sheet, sheet, excelPara.getSimpleHeader(), objList, new ExcelFillPercentageUtils());
        }
    }

    @PostMapping("/exportReport")
    @ApiOperation(value = "导出对比报表")
    public void exportReport(@RequestBody PowerEfficiencyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        List<PowerEfficiencyDTO> list = powerEfficiencyService.reportCompare(query);
        if(CollectionUtils.isNotEmpty(list)) {
            //第一行是头,去掉第一行
            list.remove(0);
            ExcelPara excelPara = query.getExcelPara();
            String sheet = "效率分布";
            List<List<Object>> objList = ExcelUtils.getList(list, excelPara);
            ExcelUtils.exportEx(response, sheet, sheet, excelPara.getSimpleHeader(), objList, new ExcelFillPercentageUtils());
        }
    }

    /**
     * dp需求调用接口
     * 根据电池工厂id+电池车间id+产品id+年月查询mps_power_efficiency_main
     * 查到后再根据id作为mps_power_efficiency_relation的外键查询sub_title=22的数值
     * @param standardCellEfficiencys 筛选条件
     * @return 查询结果
     */
    @PostMapping("/queryPowerEffciencyListByDp")
    @ApiOperation(value = "全年效率值查询", notes = "全年效率值查询")
    public ResponseEntity<Results<List<PowerEfficiencyMainDTO.DpGroup>>> queryListByDp(@RequestBody List<BigDecimal> standardCellEfficiencys) {
        return Results.createSuccessRes(powerEfficiencyMainService.queryWithRelationsByDp(standardCellEfficiencys));
    }

    /**
     * 电池效率预测版本
     *
     * @return
     */
    @ApiOperation(value = "电池效率预测版本")
    @PostMapping("/findVersionList")
    public ResponseEntity<Results<List<String>>> findVersionList(@RequestBody PowerEfficiencyQuery query) {
        return Results.createSuccessRes(powerEfficiencyMainService.findVersionList(query));
    }

    @ApiOperation(value = "编辑电池预测效率保存")
    @PostMapping("/edit")
    public ResponseEntity<Results<Long>> edit(@RequestBody PowerEfficiencyMainCustomUniqueQuery query) {
        return Results.createSuccessRes(powerEfficiencyMainService.edit(query));
    }

}


