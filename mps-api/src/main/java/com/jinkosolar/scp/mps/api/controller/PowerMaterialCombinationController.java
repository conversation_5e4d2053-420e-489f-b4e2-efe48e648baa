package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.dto.PowerMaterialCombinationDTO;
import com.jinkosolar.scp.mps.domain.query.PowerMaterialCombinationQuery;
import com.jinkosolar.scp.mps.service.PowerMaterialCombinationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 材料搭配组合描述基表相关操作控制层
 *
 * <AUTHOR> 2024-05-14 10:52:04
 */
@RequestMapping(value = "/power-material-combination")
@RestController
@Api(value = "powerMaterialCombination", tags = "材料搭配组合描述基表相关操作控制层")
public class PowerMaterialCombinationController extends BaseController {
    @Autowired
    private PowerMaterialCombinationService powerMaterialCombinationService;

    @PostMapping("/page")
    @ApiOperation(value = "材料搭配组合描述基表分页查询")
    public ResponseEntity<Results<Page<PowerMaterialCombinationDTO>>> page(@RequestBody PowerMaterialCombinationQuery query) {
        return Results.createSuccessRes(powerMaterialCombinationService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "材料搭配组合描述基表详情")
    public ResponseEntity<Results<PowerMaterialCombinationDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerMaterialCombinationService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/getIds")
    @ApiOperation(value = "批量获取")
    public ResponseEntity<Results<List<CommonOption>>> getIds(@RequestBody IdsDTO idsDTO) {
        return Results.createSuccessRes(powerMaterialCombinationService.queryByIds(idsDTO));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增材料搭配组合描述基表")
    public ResponseEntity<Results<PowerMaterialCombinationDTO>> insert(@RequestBody PowerMaterialCombinationDTO powerMaterialCombinationDTO) {
        validObject(powerMaterialCombinationDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerMaterialCombinationService.saveOrUpdate(powerMaterialCombinationDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新材料搭配组合描述基表")
    public ResponseEntity<Results<PowerMaterialCombinationDTO>> update(@RequestBody PowerMaterialCombinationDTO powerMaterialCombinationDTO) {
        validObject(powerMaterialCombinationDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerMaterialCombinationService.saveOrUpdate(powerMaterialCombinationDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除材料搭配组合描述基表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerMaterialCombinationService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出材料搭配组合描述基表")
    @PostMapping("/export")
    public void export(@RequestBody PowerMaterialCombinationQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerMaterialCombinationService.export(query, response);
    }

    @ApiOperation(value = "导入材料搭配组合描述基表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerMaterialCombinationService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "材料搭配组合描述")
    @PostMapping("/listDesc")
    public ResponseEntity<Results<List<CommonOption>>> listDesc(@RequestBody PowerMaterialCombinationQuery query) {
        return Results.createSuccessRes(powerMaterialCombinationService.listDesc(query));
    }

    @ApiOperation(value = "投产方案描述")
    @PostMapping("/listProductionPlan")
    public ResponseEntity<Results<List<CommonOption>>> listProductionPlan(@RequestBody PowerMaterialCombinationQuery query) {
        return Results.createSuccessRes(powerMaterialCombinationService.listProductionPlan(query));
    }

    @PostMapping("/archive")
    @ApiOperation(value = "批量归档")
    public ResponseEntity<Results<Object>> archive(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerMaterialCombinationService.archive(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "投产方案描述")
    @PostMapping("/queryMaterialCombinationByCode")
    public ResponseEntity<Results<List<PowerMaterialCombinationDTO>>> queryMaterialCombinationByCode(@RequestBody String code) {
        return Results.createSuccessRes(powerMaterialCombinationService.queryMaterialCombinationByCode(code));
    }


    @ApiOperation(value = "投产方案编码获取投产方案描述")
    @PostMapping("/getTheProductionPlanDescription")
    public ResponseEntity<Results<Map<String,String>>> getTheProductionPlanDescription(@RequestBody List<String> code) {
        return Results.createSuccessRes(powerMaterialCombinationService.getTheProductionPlanDescription(code));
    }

    @ApiOperation(value = "根据条件查询数据")
    @PostMapping("/findByCondition")
    public ResponseEntity<Results<List<PowerMaterialCombinationDTO>>> findByCondition(@RequestBody PowerMaterialCombinationQuery query) {
        return Results.createSuccessRes(powerMaterialCombinationService.findByCondition(query));
    }

    @PostMapping("/powerMaterialOptions")
    @ApiOperation(value = "投产方案")
    public ResponseEntity<Results<List<CommonOption>>> powerMaterialOptions() {
        return Results.createSuccessRes(powerMaterialCombinationService.powerMaterialOptions());
    }
}
