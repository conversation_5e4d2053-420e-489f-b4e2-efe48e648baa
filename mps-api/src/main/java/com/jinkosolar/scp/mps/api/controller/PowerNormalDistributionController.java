package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.constant.PowerNormalDistributionConst;
import com.jinkosolar.scp.mps.domain.dto.PowerNormalDistributionDTO;
import com.jinkosolar.scp.mps.domain.query.PowerNormalDistributionQuery;
import com.jinkosolar.scp.mps.domain.save.PowerNormalDistributionSaveDTO;
import com.jinkosolar.scp.mps.service.PowerNormalDistributionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 正态分布计算与查询 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:01
 */
@RestController
@RequestMapping("/power-normal-distribution")
@Api(value = "power-normal-distribution", tags = "正态分布计算与查询操作")
public class PowerNormalDistributionController {
    @Autowired
    PowerNormalDistributionService powerNormalDistributionService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "正态分布计算与查询分页列表", notes = "获得正态分布计算与查询分页列表")
    public ResponseEntity<Results<List<PowerNormalDistributionDTO>>> queryByPage(@RequestBody PowerNormalDistributionQuery query) {
        return Results.createSuccessRes(powerNormalDistributionService.queryByPage(query, PowerNormalDistributionConst.PageQueryType.PAGE));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/loadPowerNormalDistributionDetailList")
    public ResponseEntity<Results<List<PowerNormalDistributionDTO>>> loadPowerNormalDistributionDetailList(@RequestBody PowerNormalDistributionQuery query) {
        return Results.createSuccessRes(powerNormalDistributionService.queryByPage(query, PowerNormalDistributionConst.PageQueryType.DETAIL));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerNormalDistributionDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerNormalDistributionService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerNormalDistributionDTO>> save(@Valid @RequestBody PowerNormalDistributionSaveDTO saveDTO) {
        return Results.createSuccessRes(powerNormalDistributionService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerNormalDistributionService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerNormalDistributionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerNormalDistributionService.export(query, response);

    }

    /**
     * 计算正态分布
     *
     * @return 计算正态分布
     */
    @PostMapping("/calcCurrMonthAndNextMonthNormalDistribution")
    @ApiOperation(value = "计算正态分布")
    public ResponseEntity<Results<Object>> calcCurrMonthAndNextMonthNormalDistribution(@RequestBody PowerNormalDistributionSaveDTO saveDTO) {
        powerNormalDistributionService.calcCurrMonthAndNextMonthNormalDistribution(saveDTO.getMonth());
        return Results.createSuccessRes();
    }
}



