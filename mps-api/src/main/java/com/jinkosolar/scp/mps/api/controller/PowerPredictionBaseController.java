package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionBaseDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionBaseDetailDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionReleaseDTO;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionBaseQuery;
import com.jinkosolar.scp.mps.service.PowerPredictionBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品功率预测基准相关操作控制层
 *
 * <AUTHOR> 2024-05-22 09:52:44
 */
@RequestMapping(value = "/power-prediction-base")
@RestController
@Api(value = "powerPredictionBase", tags = "产品功率预测基准相关操作控制层")
public class PowerPredictionBaseController extends BaseController {
    @Autowired
    private PowerPredictionBaseService powerPredictionBaseService;

    @PostMapping("/page")
    @ApiOperation(value = "产品功率预测基准分页查询")
    public ResponseEntity<Results<Page<PowerPredictionBaseDTO>>> page(@RequestBody PowerPredictionBaseQuery query) {
        return Results.createSuccessRes(powerPredictionBaseService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品功率预测基准详情")
    public ResponseEntity<Results<PowerPredictionBaseDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionBaseService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品功率预测基准")
    public ResponseEntity<Results<PowerPredictionBaseDTO>> insert(@RequestBody PowerPredictionBaseDTO powerPredictionBaseDTO) {
        validObject(powerPredictionBaseDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerPredictionBaseService.saveOrUpdate(powerPredictionBaseDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品功率预测基准")
    public ResponseEntity<Results<PowerPredictionBaseDTO>> update(@RequestBody PowerPredictionBaseDTO powerPredictionBaseDTO) {
        validObject(powerPredictionBaseDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerPredictionBaseService.saveOrUpdate(powerPredictionBaseDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品功率预测基准")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionBaseService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品功率预测基准")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionBaseQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionBaseService.export(query, response);
    }

    @ApiOperation(value = "导入产品功率预测基准")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionBaseService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "产品功率预测基准明细")
    @PostMapping("/details")
    public ResponseEntity<Results<List<PowerPredictionBaseDetailDTO>>> details(@RequestBody PowerPredictionBaseQuery query) {
        return Results.createSuccessRes(powerPredictionBaseService.listByPredictionBase(query));
    }

    @ApiOperation(value = "功率预测版本号正式发布版本号")
    @PostMapping("/releaseVersion")
    public ResponseEntity<Results<String>> getReleaseVersion() {
        return Results.createSuccessRes(powerPredictionBaseService.getReleaseVersion());
    }

    @ApiOperation(value = "判断今天是否已存在发版数据")
    @PostMapping("/existVersionData")
    public ResponseEntity<Results<Boolean>> existVersionData() {
        return Results.createSuccessRes(powerPredictionBaseService.existVersionData());
    }

    @ApiOperation(value = "功率预测版本号正式发布")
    @PostMapping(value = "/release",consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Results<Object>> release(@RequestPart("files") List<MultipartFile> multipartFiles,
                                                   @RequestParam("title") String title,
                                                   @RequestParam("version") String version,
                                                   @RequestParam("mailContent") String mailContent,
                                                   @RequestParam(value = "recipientNo") String recipientNo,
                                                   @RequestParam(value = "copyTo", required = false) String copyTo){
        PowerPredictionReleaseDTO predictionReleaseDTO=new PowerPredictionReleaseDTO();
        predictionReleaseDTO.setTitle(title);
        predictionReleaseDTO.setVersion(version);
        predictionReleaseDTO.setMailContent(mailContent);
        predictionReleaseDTO.setRecipientNo(recipientNo);
        predictionReleaseDTO.setCopyTo(copyTo);
        predictionReleaseDTO.setMultipartFiles(multipartFiles);
        powerPredictionBaseService.release(predictionReleaseDTO);
        return Results.createSuccessRes();
    }
}
