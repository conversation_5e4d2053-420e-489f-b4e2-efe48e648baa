package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionBaseDetailDTO;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionBaseDetailQuery;
import com.jinkosolar.scp.mps.service.PowerPredictionBaseDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品功率预测基准明细相关操作控制层
 *
 * <AUTHOR> 2024-05-22 15:40:53
 */
@RequestMapping(value = "/power-prediction-base-detail")
@RestController
@Api(value = "powerPredictionBaseDetail", tags = "产品功率预测基准明细相关操作控制层")
public class PowerPredictionBaseDetailController extends BaseController {
    @Autowired
    private PowerPredictionBaseDetailService powerPredictionBaseDetailService;

    @PostMapping("/page")
    @ApiOperation(value = "产品功率预测基准明细分页查询")
    public ResponseEntity<Results<Page<PowerPredictionBaseDetailDTO>>> page(@RequestBody PowerPredictionBaseDetailQuery query) {
        return Results.createSuccessRes(powerPredictionBaseDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品功率预测基准明细详情")
    public ResponseEntity<Results<PowerPredictionBaseDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionBaseDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品功率预测基准明细")
    public ResponseEntity<Results<PowerPredictionBaseDetailDTO>> insert(@RequestBody PowerPredictionBaseDetailDTO powerPredictionBaseDetailDTO) {
        validObject(powerPredictionBaseDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerPredictionBaseDetailService.saveOrUpdate(powerPredictionBaseDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品功率预测基准明细")
    public ResponseEntity<Results<PowerPredictionBaseDetailDTO>> update(@RequestBody PowerPredictionBaseDetailDTO powerPredictionBaseDetailDTO) {
        validObject(powerPredictionBaseDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerPredictionBaseDetailService.saveOrUpdate(powerPredictionBaseDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品功率预测基准明细")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionBaseDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品功率预测基准明细")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionBaseDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionBaseDetailService.export(query, response);
    }
}
