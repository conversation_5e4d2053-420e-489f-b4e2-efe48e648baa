package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.enums.TaskEnum;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionCellAllocation;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionCellAllocationQuery;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionDemandQuery;
import com.jinkosolar.scp.mps.service.LogService;
import com.jinkosolar.scp.mps.service.PowerPredictionCellAllocationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功率预测电池分配表相关操作控制层
 * 
 * <AUTHOR> 2024-08-20 13:12:44
 */
@RequestMapping(value = "/power-prediction-cell-allocation")
@RestController
@Api(value = "powerPredictionCellAllocation", tags = "功率预测电池分配表相关操作控制层")
@Slf4j
public class PowerPredictionCellAllocationController extends BaseController {
    @Autowired

    private PowerPredictionCellAllocationService powerPredictionCellAllocationService;

    @Autowired
    private LogService logService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ExecutorService threadPoolExecutor;

    @PostMapping("/page")
    @ApiOperation(value = "功率预测电池分配表分页查询")
    public ResponseEntity<Results<Page<PowerPredictionCellAllocationDTO>>> page(@RequestBody PowerPredictionCellAllocationQuery query) {
        return Results.createSuccessRes(powerPredictionCellAllocationService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "功率预测电池分配表详情")
    public ResponseEntity<Results<PowerPredictionCellAllocationDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionCellAllocationService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增功率预测电池分配表")
    public ResponseEntity<Results<Void>> insert(@RequestBody PowerPredictionCellAllocationDTO powerPredictionCellAllocationDTO) {
        validObject(powerPredictionCellAllocationDTO, ValidGroups.Insert.class);
        powerPredictionCellAllocationService.saveOrUpdate(powerPredictionCellAllocationDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新功率预测电池分配表")
    public ResponseEntity<Results<Void>> update(@RequestBody PowerPredictionCellAllocationDTO powerPredictionCellAllocationDTO) {
        validObject(powerPredictionCellAllocationDTO, ValidGroups.Update.class);
        powerPredictionCellAllocationService.saveOrUpdate(powerPredictionCellAllocationDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除功率预测电池分配表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionCellAllocationService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出功率预测电池分配表")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionCellAllocationQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionCellAllocationService.export(query, response);
    }

    @ApiOperation(value = "导入功率预测电池分配表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionCellAllocationService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    // 三、电池分配结果推移展示
    @PostMapping("/queryCellAllocationList")
    @ApiOperation(value = "电池分配结果推移展示")
    public ResponseEntity<Results<List<PowerPredictionCellAllocation>>> queryCellAllocationList(@RequestBody PowerPredictionCellAllocationQuery query) {
        return Results.createSuccessRes(powerPredictionCellAllocationService.queryCellAllocationList(query));
    }

    // 四、电池外购需求展示
    @PostMapping("/queryCellOverseaPurchaseList")
    @ApiOperation(value = "电池外购需求展示")
    public ResponseEntity<Results<PowerPredictionCellAllocationBaseDTO>> queryCellOverseaPurchaseList(@RequestBody PowerPredictionCellAllocationQuery query) {
        return Results.createSuccessRes(powerPredictionCellAllocationService.queryCellOverseaPurchaseList(query));
    }

    // 五、自产电池发货需求展示
    @PostMapping("/queryCellProductPurchaseList")
    @ApiOperation(value = "自产电池发货需求展示")
    public ResponseEntity<Results<PowerPredictionCellAllocationBaseDTO>> queryCellProductPurchaseList(@RequestBody PowerPredictionCellAllocationQuery query) {
        return Results.createSuccessRes(powerPredictionCellAllocationService.queryCellProductPurchaseList(query));
    }

    @ApiOperation(value = "导出自产电池发货需求表")
    @PostMapping("/exportCellProductPurchase")
    public void exportCellProductPurchase(@RequestBody PowerPredictionCellAllocationQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionCellAllocationService.exportCellProductPurchase(query, response);
    }

    @ApiOperation(value = "导出电池外购需求表")
    @PostMapping("/exportCellOverseaPurchase")
    public void exportCellOverseaPurchase(@RequestBody PowerPredictionCellAllocationQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionCellAllocationService.exportCellOverseaPurchase(query, response);
    }

    // 执行电池分配计算逻辑
    @PostMapping("/executeCellAllocation")
    @ApiOperation(value = "执行电池分配计算逻辑")
    public ResponseEntity<Results<Boolean>> executeCellAllocation(@RequestBody PowerPredictionDemandQuery query) {


        PowerPredictionSyncRestDTO restDTO=new PowerPredictionSyncRestDTO();
        restDTO.setSuccess(false);


        List<String> checkTaskNames = Arrays.asList(TaskEnum.CELL_ALLOCATION.getCode(), TaskEnum.SYNC_DATA.getCode(),  TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode());

        List<ScheduledTaskLinesDTO> taskLinesDTOS= logService.getRunningTasksByName(checkTaskNames);

        if(!CollectionUtil.isEmpty(taskLinesDTOS)){
            log.error("数据库中判断有同步任务正在进行中,请稍后再试");
            return Results.createFailRes("有[电池分配任务,功率效率计算,电池分配计算]正在进行中,请稍后再试");
        }

        ScheduledTaskLinesDTO scheduledTaskLinesDTO=ScheduledTaskLinesDTO.init();

        if(Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.CELL_ALLOCATION.getCode()))
                || Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode()))
                || Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.SYNC_DATA.getCode())) ){
            log.error("redis中判断有电池分配任务正在进行中,请稍后再试");
            return Results.createFailRes("有[电池分配任务,功率效率计算,电池分配计算]正在进行中,请稍后再试");


        }else{
            scheduledTaskLinesDTO.setTaskName(TaskEnum.CELL_ALLOCATION.getCode());
            scheduledTaskLinesDTO.setTaskDesc(TaskEnum.CELL_ALLOCATION.getDesc());
            scheduledTaskLinesDTO.setTaskNumber(LocalDateTimeUtil.format(LocalDateTime.now(),"yyyyMMddHHmmss"));
            logService.saveTask(scheduledTaskLinesDTO);
            redisTemplate.opsForValue().set(TaskEnum.CELL_ALLOCATION.getCode(), "1", 1, TimeUnit.HOURS);
        }

        CompletableFuture.runAsync(() -> {
            try {
                powerPredictionCellAllocationService.executeCellAllocation(query);
                logService.completeTask(TaskEnum.CELL_ALLOCATION.getCode(), scheduledTaskLinesDTO.getTaskNumber(), ScheduleTaskStatusEnum.SUCCESS, "执行成功");
                log.info("info scheduledTaskLinesDTO.getTaskNumber()",scheduledTaskLinesDTO.getTaskNumber());

            } catch (Exception ex) {
                log.error("执行电池分配计算逻辑失败", ex);
                log.info("error scheduledTaskLinesDTO.getTaskNumber()={}",scheduledTaskLinesDTO.getTaskNumber());
                logService.completeTask(TaskEnum.CELL_ALLOCATION.getCode(), scheduledTaskLinesDTO.getTaskNumber(), ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(ex));
            } finally {
                redisTemplate.delete(TaskEnum.CELL_ALLOCATION.getCode());
            }
        }, threadPoolExecutor);


        restDTO.setSuccess(true);
        restDTO.setMsg("提交电池分配任务成功,任务批次号:"+scheduledTaskLinesDTO.getTaskNumber());

        return Results.createSuccessRes(Boolean.TRUE);


    }
}