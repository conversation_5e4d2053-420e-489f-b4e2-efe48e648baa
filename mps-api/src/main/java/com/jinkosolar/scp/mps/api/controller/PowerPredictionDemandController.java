package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.enums.TaskEnum;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemand;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionSupply;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionDemandQuery;
import com.jinkosolar.scp.mps.service.LogService;
import com.jinkosolar.scp.mps.service.PowerPredictionDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 功率预测&电池分配_需求整合表相关操作控制层
 * 
 * <AUTHOR> 2024-08-05 16:18:07
 */
@RequestMapping(value = "/power-prediction-demand")
@RestController
@Api(value = "powerPredictionDemand", tags = "功率预测&电池分配_需求整合表相关操作控制层")
@Slf4j
public class PowerPredictionDemandController extends BaseController {


    @Autowired
    private PowerPredictionDemandService powerPredictionDemandService;

    @Autowired
    private LogService logService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ExecutorService threadPoolExecutor;


    @PostMapping("/page")
    @ApiOperation(value = "功率预测&电池分配_需求整合表分页查询")
    public ResponseEntity<Results<Page<PowerPredictionDemandDTO>>> page(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "功率预测&电池分配_需求整合表详情")
    public ResponseEntity<Results<PowerPredictionDemandDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionDemandService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增功率预测&电池分配_需求整合表")
    public ResponseEntity<Results<PowerPredictionDemandDTO>> insert(@RequestBody PowerPredictionDemandDTO powerPredictionDemandDTO) {
        validObject(powerPredictionDemandDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerPredictionDemandService.saveOrUpdate(powerPredictionDemandDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新功率预测&电池分配_需求整合表")
    public ResponseEntity<Results<PowerPredictionDemandDTO>> update(@RequestBody PowerPredictionDemandDTO powerPredictionDemandDTO) {
        validObject(powerPredictionDemandDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerPredictionDemandService.saveOrUpdate(powerPredictionDemandDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除功率预测&电池分配_需求整合表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionDemandService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出功率预测&电池分配_需求整合表")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionDemandQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionDemandService.export(query, response);
    }

    @ApiOperation(value = "导入功率预测&电池分配_需求整合表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionDemandService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/executePowerPredictionDemand")
    @ApiOperation(value = "执行功率预测")
    public ResponseEntity<Results<Boolean>> executePowerPredictionDemand(@RequestBody PowerPredictionDemandQuery query) {

        PowerPredictionSyncRestDTO restDTO=new PowerPredictionSyncRestDTO();
        restDTO.setSuccess(false);

        List<String> checkTaskNames = Arrays.asList(TaskEnum.CELL_ALLOCATION.getCode(), TaskEnum.SYNC_DATA.getCode(),  TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode());

        List<ScheduledTaskLinesDTO> taskLinesDTOS= logService.getRunningTasksByName(checkTaskNames);

        if(!CollectionUtil.isEmpty(taskLinesDTOS)){
            log.error("数据库中判断有同步任务正在进行中,请稍后再试");
            return Results.createFailRes("有[电池分配或功率效率或电池分配]正在计算中,请稍后再试");
        }

        ScheduledTaskLinesDTO scheduledTaskLinesDTO=ScheduledTaskLinesDTO.init();

        if(Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.CELL_ALLOCATION.getCode()))
                || Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode()))
                || Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.SYNC_DATA.getCode())) ){
            log.error("redis中判断有电池分配任务正在进行中,请稍后再试");
            return Results.createFailRes("有[电池分配或功率效率或电池分配]正在计算中,请稍后再试");

        }else{
            scheduledTaskLinesDTO.setTaskName(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode());
            scheduledTaskLinesDTO.setTaskDesc(TaskEnum.POWER_EFFICIENCY_CALCULATION.getDesc());
            scheduledTaskLinesDTO.setTaskNumber(LocalDateTimeUtil.format(LocalDateTime.now(),"yyyyMMddHHmmss"));
            logService.saveTask(scheduledTaskLinesDTO);
            redisTemplate.opsForValue().set(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode(), "1", 1, TimeUnit.HOURS);
        }

        CompletableFuture.runAsync(() -> {
            try {
                powerPredictionDemandService.executePowerPredictionDemand(query);
                logService.completeTask(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode(), scheduledTaskLinesDTO.getTaskNumber(), ScheduleTaskStatusEnum.SUCCESS, "执行成功");
                log.info("info scheduledTaskLinesDTO.getTaskNumber()",scheduledTaskLinesDTO.getTaskNumber());

            } catch (Exception ex) {
                log.error("执行功率预测逻辑失败", ex);
                log.info("error scheduledTaskLinesDTO.getTaskNumber()={}",scheduledTaskLinesDTO.getTaskNumber());
                logService.completeTask(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode(), scheduledTaskLinesDTO.getTaskNumber(), ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(ex));
            } finally {
                redisTemplate.delete(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode());
            }
        }, threadPoolExecutor);


        restDTO.setSuccess(true);
        restDTO.setMsg("提交同步任务成功,任务批次号:"+scheduledTaskLinesDTO.getTaskNumber());

        return Results.createSuccessRes(Boolean.TRUE);




    }

    @PostMapping("/computationCellAllocationByIdList")
    @ApiOperation(value = "根据排产行id计算电池分配比例")
    public ResponseEntity<Results<List<PowerPredictionDemand>>> computationCellAllocationByIdList(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandService.computationCellAllocationByIdList(query.getIdList()));
    }

    @PostMapping("/computationCellAllocationMiniDTOByIdList")
    @ApiOperation(value = "根据排产行id计算电池分配结果")
    public ResponseEntity<Results<List<PowerPredictionDemandMiniDTO>>> computationCellAllocationMiniDTOByIdList(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandService.computationCellAllocationMiniDTOByIdList(query.getIdList()));
    }

    @PostMapping("/batchSavePowerPredictionDemand")
    @ApiOperation(value = "保存需求数据")
    public ResponseEntity<Results<List<PowerPredictionDemand>>> batchSavePowerPredictionDemand(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandService.batchSavePowerPredictionDemand(query));
    }
}