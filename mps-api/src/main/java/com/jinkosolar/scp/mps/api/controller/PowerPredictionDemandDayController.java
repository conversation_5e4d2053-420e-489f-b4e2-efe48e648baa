package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandDayDTO;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionDemandDayQuery;
import com.jinkosolar.scp.mps.service.PowerPredictionDemandDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率预测&电池分配_需求日整表相关操作控制层
 * 
 * <AUTHOR> 2024-08-20 13:33:49
 */
@RequestMapping(value = "/power-prediction-demand-day")
@RestController
@Api(value = "powerPredictionDemandDay", tags = "功率预测&电池分配_需求日整表相关操作控制层")
@RequiredArgsConstructor  
public class PowerPredictionDemandDayController extends BaseController {    
    private final PowerPredictionDemandDayService powerPredictionDemandDayService; 

    @PostMapping("/page")
    @ApiOperation(value = "功率预测&电池分配_需求日整表分页查询")
    public ResponseEntity<Results<Page<PowerPredictionDemandDayDTO>>> page(@RequestBody PowerPredictionDemandDayQuery query) {
        return Results.createSuccessRes(powerPredictionDemandDayService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "功率预测&电池分配_需求日整表详情")
    public ResponseEntity<Results<PowerPredictionDemandDayDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionDemandDayService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增功率预测&电池分配_需求日整表")
    public ResponseEntity<Results<Void>> insert(@RequestBody PowerPredictionDemandDayDTO powerPredictionDemandDayDTO) {
        validObject(powerPredictionDemandDayDTO, ValidGroups.Insert.class);
        powerPredictionDemandDayService.saveOrUpdate(powerPredictionDemandDayDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新功率预测&电池分配_需求日整表")
    public ResponseEntity<Results<Void>> update(@RequestBody PowerPredictionDemandDayDTO powerPredictionDemandDayDTO) {
        validObject(powerPredictionDemandDayDTO, ValidGroups.Update.class);
        powerPredictionDemandDayService.saveOrUpdate(powerPredictionDemandDayDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除功率预测&电池分配_需求日整表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionDemandDayService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出功率预测&电池分配_需求日整表")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionDemandDayQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionDemandDayService.export(query, response);
    }

    @ApiOperation(value = "导入功率预测&电池分配_需求日整表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionDemandDayService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/queryPropertiesList")
    @ApiOperation(value = "功率预测&电池分配_需求日整表列表")
    public ResponseEntity<Results<List<PowerPredictionDemandDayDTO>>> queryPredictionDemandDayPropertiesList(@RequestBody PowerPredictionDemandDayQuery query) {
        return Results.createSuccessRes(powerPredictionDemandDayService.queryPredictionDemandDayPropertiesList(query));
    }
}