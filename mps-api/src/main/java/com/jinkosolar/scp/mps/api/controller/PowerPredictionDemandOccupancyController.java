package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ModuleTypeDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandBaseDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandOccupancyBaseDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandOccupancyDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemandDay;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionDemandOccupancyQuery;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionDemandQuery;
import com.jinkosolar.scp.mps.service.PowerPredictionDemandOccupancyService;
import com.jinkosolar.scp.mps.service.PowerPredictionDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功率预测需求占用表相关操作控制层
 * 
 * <AUTHOR> 2024-08-15 11:00:46
 */
@RequestMapping(value = "/power-prediction-demand-occupancy")
@RestController
@Api(value = "powerPredictionDemandOccupancy", tags = "功率预测需求占用表相关操作控制层")
@RequiredArgsConstructor  
public class PowerPredictionDemandOccupancyController extends BaseController {    
    private final PowerPredictionDemandOccupancyService powerPredictionDemandOccupancyService;

    private final PowerPredictionDemandService powerPredictionDemandService;

    @PostMapping("/page")
    @ApiOperation(value = "功率预测需求占用表分页查询")
    public ResponseEntity<Results<Page<PowerPredictionDemandOccupancyDTO>>> page(@RequestBody PowerPredictionDemandOccupancyQuery query) {
        return Results.createSuccessRes(powerPredictionDemandOccupancyService.page(query));
    }

    @PostMapping("/queryDemandOccupancyList")
    @ApiOperation(value = "功率预测查询尺寸库存推移展示")
    public ResponseEntity<Results<PowerPredictionDemandOccupancyBaseDTO>> queryDemandOccupancyList(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandOccupancyService.queryDemandOccupancyList(query));
    }

    @PostMapping("/queryDemandOccupancyMonthList")
    @ApiOperation(value = "功率预测月推移数据展示")
    public ResponseEntity<Results<PowerPredictionDemandOccupancyBaseDTO>> queryDemandOccupancyMonthList(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandOccupancyService.queryDemandOccupancyMonthList(query));
    }

    @PostMapping("/queryDemandOccupancyDetail")
    @ApiOperation(value = "功率预测查询推移相关明细数据")
    public ResponseEntity<Results<PowerPredictionDemandBaseDTO>> queryDemandOccupancyDetail(@RequestBody PowerPredictionDemandQuery query) {
        return Results.createSuccessRes(powerPredictionDemandOccupancyService.queryDemandOccupancyDetail(query));
    }
    @PostMapping("/detail")
    @ApiOperation(value = "功率预测需求占用表详情")
    public ResponseEntity<Results<PowerPredictionDemandOccupancyDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionDemandOccupancyService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增功率预测需求占用表")
    public ResponseEntity<Results<Void>> insert(@RequestBody PowerPredictionDemandOccupancyDTO powerPredictionDemandOccupancyDTO) {
        validObject(powerPredictionDemandOccupancyDTO, ValidGroups.Insert.class);
        //powerPredictionDemandOccupancyService.saveOrUpdate(powerPredictionDemandOccupancyDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新功率预测需求占用表")
    public ResponseEntity<Results<Void>> update(@RequestBody PowerPredictionDemandDay powerPredictionDemandDay) {
        // validObject(powerPredictionDemandOccupancyDTO, ValidGroups.Update.class);
        powerPredictionDemandOccupancyService.saveOrUpdate(powerPredictionDemandDay);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除功率预测需求占用表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionDemandOccupancyService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出功率预测需求占用表")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionDemandQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionDemandOccupancyService.export(query, response);
    }

    @ApiOperation(value = "导出功率预测需求占用_对应明细表")
    @PostMapping("/exportDetail")
    public void exportDetail(@RequestBody PowerPredictionDemandQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionDemandOccupancyService.exportDetail(query, response);
    }

    @ApiOperation(value = "导入功率预测需求占用表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionDemandOccupancyService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "导出功率预测月度占用表")
    @PostMapping("/exportMonthData")
    public void exportMonthData(@RequestBody PowerPredictionDemandQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionDemandOccupancyService.export(query, response);
    }

    @PostMapping("/queryOddEven")
    @ApiOperation(value = "各尺寸库存推移等页面单双玻查询下拉列表")
    public ResponseEntity<Results<List<ModuleTypeDTO>>> queryOddEven() {
        return Results.createSuccessRes(powerPredictionDemandOccupancyService.queryOddEven());
    }
}