package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionReleaseDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionReleaseMESDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionReleaseMESResponseDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerReleaseDTO;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionReleaseQuery;
import com.jinkosolar.scp.mps.service.PowerPredictionReleaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品功率预测版本相关操作控制层
 *
 * <AUTHOR> 2024-05-23 10:03:06
 */
@RequestMapping(value = "/power-prediction-release")
@RestController
@Api(value = "powerPredictionRelease", tags = "产品功率预测版本相关操作控制层")
public class PowerPredictionReleaseController extends BaseController {
    @Autowired
    private PowerPredictionReleaseService powerPredictionReleaseService;

    @PostMapping("/page")
    @ApiOperation(value = "产品功率预测版本分页查询")
    public ResponseEntity<Results<Page<PowerPredictionReleaseDTO>>> page(@RequestBody PowerPredictionReleaseQuery query) {
        return Results.createSuccessRes(powerPredictionReleaseService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品功率预测版本详情")
    public ResponseEntity<Results<PowerPredictionReleaseDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionReleaseService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品功率预测版本")
    public ResponseEntity<Results<PowerPredictionReleaseDTO>> insert(@RequestBody PowerPredictionReleaseDTO powerPredictionReleaseDTO) {
        validObject(powerPredictionReleaseDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerPredictionReleaseService.saveOrUpdate(powerPredictionReleaseDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品功率预测版本")
    public ResponseEntity<Results<PowerPredictionReleaseDTO>> update(@RequestBody PowerPredictionReleaseDTO powerPredictionReleaseDTO) {
        validObject(powerPredictionReleaseDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerPredictionReleaseService.saveOrUpdate(powerPredictionReleaseDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品功率预测版本")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionReleaseService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品功率预测版本")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionReleaseQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionReleaseService.export(query, response);
    }

    @ApiOperation(value = "导入产品功率预测版本")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionReleaseService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "电池片系列描述列表")
    @PostMapping("/cellSeriesDesc")
    public ResponseEntity<Results<List<String>>> cellSeriesDesc() {
        return Results.createSuccessRes(powerPredictionReleaseService.cellSeriesDesc());
    }

    @ApiOperation(value = "片串列表")
    @PostMapping("/cellString")
    public ResponseEntity<Results<List<String>>> cellString() {
        return Results.createSuccessRes(powerPredictionReleaseService.cellString());
    }

    @ApiOperation(value = "材料搭配组合描述列表")
    @PostMapping("/materialCombinationDesc")
    public ResponseEntity<Results<List<String>>> materialCombinationDesc() {
        return Results.createSuccessRes(powerPredictionReleaseService.materialCombinationDesc());
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions() {
        return Results.createSuccessRes(powerPredictionReleaseService.versions());
    }

    @PostMapping("/queryInfoForQueryOrder")
    @ApiOperation(value = "询单相关信息业务查询接口")
    public ResponseEntity<Results<List<PowerPredictionReleaseDTO>>> queryInfoForQueryOrder(@RequestBody PowerPredictionReleaseQuery query) {
        return Results.createSuccessRes(powerPredictionReleaseService.queryInfoForQueryOrder(query));
    }

    @PostMapping("/queryInfoForQueryOrderByPerson")
    @ApiOperation(value = "询单相关信息业务查询接口")
    public ResponseEntity<Results<List<PowerPredictionReleaseDTO>>> queryInfoForQueryOrderByPerson(@RequestBody PowerPredictionReleaseQuery query) {
        return Results.createSuccessRes(powerPredictionReleaseService.queryInfoForQueryOrderByPerson(query));
    }

    @PostMapping("/queryMaterialCombinationDescByVersion")
    @ApiOperation(value = "询单相关信息业务查询接口")
    public ResponseEntity<Results<List<Map<String, String>>>> queryMaterialCombinationDescByVersion(@RequestBody PowerPredictionReleaseQuery query) {
        return Results.createSuccessRes(powerPredictionReleaseService.queryMaterialCombinationDescByVersion(query));
    }

    @PostMapping("/queryLastVersion")
    @ApiOperation(value = "询单相关信息业务查询接口")
    public ResponseEntity<Results<String>> queryLastVersion() {
        return Results.createSuccessRes(powerPredictionReleaseService.queryLastVersion());
    }

    @PostMapping("/findPowerPredictionRelease")
    @ApiOperation(value = "获取最新版本下的功率预测数据和落档信息")
    public ResponseEntity<Results<List<PowerReleaseDTO>>> findPowerPredictionRelease() {
        return Results.createSuccessRes(powerPredictionReleaseService.findPowerPredictionRelease());
    }

    @PostMapping("/findPowerPredictionReleaseByParams")
    @ApiOperation(value = "获取最新版本下的功率预测数据")
    public ResponseEntity<Results<List<PowerPredictionReleaseDTO>>> findPowerPredictionReleaseByParams(@RequestBody PowerPredictionReleaseQuery query) {
        return Results.createSuccessRes(powerPredictionReleaseService.findPowerPredictionReleaseByParams(query));
    }

    @PostMapping("/findPowerPredictionReleaseByQuery")
    @ApiOperation(value = "根据query获取功率预测数据和落档信息")
    public ResponseEntity<Results<List<PowerReleaseDTO>>> findPowerPredictionReleaseByQuery(@RequestBody PowerPredictionReleaseQuery query) {
        return Results.createSuccessRes(powerPredictionReleaseService.findPowerPredictionReleaseByQuery(query));
    }

    @JipFeignLog
    @PostMapping("/queryPowerPredictionReleaseByMes")
    @ApiOperation(value = "MES系统获取功率预测数据和落档信息")
    public PowerPredictionReleaseMESResponseDTO queryPowerPredictionReleaseByMes(@RequestBody PowerPredictionReleaseMESDTO releaseDto) {
        PowerPredictionReleaseMESResponseDTO responseDTO = new PowerPredictionReleaseMESResponseDTO();
        try{
            responseDTO.setReleasePageList(powerPredictionReleaseService.queryPowerPredictionReleaseByMes(releaseDto));
            responseDTO.setEtReturn(JipResponseData.success().getEtReturn());
        } catch (Exception ex) {
            responseDTO.setReleasePageList(null);
            responseDTO.setEtReturn(JipResponseData.error("queryPowerPredictionReleaseByMes system error : " + ex).getEtReturn());
        }
        return responseDTO;
    }
}
