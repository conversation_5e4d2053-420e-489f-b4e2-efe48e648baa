package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.BizException;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.enums.TaskEnum;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionSupplyDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionSyncRestDTO;
import com.jinkosolar.scp.mps.domain.dto.ScheduleTaskStatusEnum;
import com.jinkosolar.scp.mps.domain.dto.ScheduledTaskLinesDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionSupply;
import com.jinkosolar.scp.mps.domain.query.CellForecastQuery;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionDemandQuery;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionSupplyQuery;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import com.jinkosolar.scp.mps.service.LogService;
import com.jinkosolar.scp.mps.service.PowerPredictionSupplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_FORMAT;
import static cn.hutool.core.date.DatePattern.PURE_DATETIME_MS_FORMAT;


/**
 * 功率预测&电池分配_供应数据表相关操作控制层
 * 
 * <AUTHOR> 2024-08-05 16:18:07
 */
@RequestMapping(value = "/power-prediction-supply")
@RestController
@Api(value = "powerPredictionSupply", tags = "功率预测&电池分配_供应数据表相关操作控制层")
@Slf4j
public class PowerPredictionSupplyController extends BaseController {   
    
    @Autowired
    private PowerPredictionSupplyService powerPredictionSupplyService;

    @Autowired
    private LogService logService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ExecutorService threadPoolExecutor;

    @PostMapping("/page")
    @ApiOperation(value = "功率预测&电池分配_供应数据表分页查询")
    public ResponseEntity<Results<Page<PowerPredictionSupplyDTO>>> page(@RequestBody PowerPredictionSupplyQuery query) {
        return Results.createSuccessRes(powerPredictionSupplyService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "功率预测&电池分配_供应数据表详情")
    public ResponseEntity<Results<PowerPredictionSupplyDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(powerPredictionSupplyService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增功率预测&电池分配_供应数据表")
    public ResponseEntity<Results<PowerPredictionSupplyDTO>> insert(@RequestBody PowerPredictionSupplyDTO powerPredictionSupplyDTO) {
        validObject(powerPredictionSupplyDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerPredictionSupplyService.saveOrUpdate(powerPredictionSupplyDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新功率预测&电池分配_供应数据表")
    public ResponseEntity<Results<PowerPredictionSupplyDTO>> update(@RequestBody PowerPredictionSupplyDTO powerPredictionSupplyDTO) {
        validObject(powerPredictionSupplyDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(powerPredictionSupplyService.saveOrUpdate(powerPredictionSupplyDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除功率预测&电池分配_供应数据表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        powerPredictionSupplyService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出功率预测&电池分配_供应数据表")
    @PostMapping("/export")
    public void export(@RequestBody PowerPredictionSupplyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        powerPredictionSupplyService.export(query, response);
    }

    @ApiOperation(value = "导入功率预测&电池分配_供应数据表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = powerPredictionSupplyService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/executeSavePowerPredictionSupply")
    @ApiOperation(value = "执行库存供应数据汇总保存逻辑")
    public ResponseEntity<Results<PowerPredictionSyncRestDTO>> executeSavePowerPredictionSupply(@RequestBody PowerPredictionDemandQuery query) {


        PowerPredictionSyncRestDTO  restDTO=new PowerPredictionSyncRestDTO();
        restDTO.setSuccess(false);

        List<String> checkTaskNames = Arrays.asList(TaskEnum.CELL_ALLOCATION.getCode(), TaskEnum.SYNC_DATA.getCode(),  TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode());

        List<ScheduledTaskLinesDTO> taskLinesDTOS= logService.getRunningTasksByName(checkTaskNames);

        if(!CollectionUtil.isEmpty(taskLinesDTOS)){
            log.error("数据库中判断有同步任务正在进行中,请稍后再试");
            return Results.createFailRes("有[电池分配或功率效率或电池分配]正在计算中,请稍后再试");
        }

        ScheduledTaskLinesDTO scheduledTaskLinesDTO=ScheduledTaskLinesDTO.init();

        if(Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.CELL_ALLOCATION.getCode()))
                || Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.POWER_EFFICIENCY_CALCULATION.getCode()))
                || Boolean.TRUE.equals(redisTemplate.hasKey(TaskEnum.SYNC_DATA.getCode())) ){
            log.error("redis中判断有电池分配任务正在进行中,请稍后再试");
            return Results.createFailRes("有[电池分配或功率效率或电池分配]正在计算中,请稍后再试");

        }else{
            scheduledTaskLinesDTO.setTaskName(TaskEnum.SYNC_DATA.getCode());
            scheduledTaskLinesDTO.setTaskDesc(TaskEnum.SYNC_DATA.getDesc());
            scheduledTaskLinesDTO.setTaskNumber(LocalDateTimeUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
            logService.saveTask(scheduledTaskLinesDTO);
            redisTemplate.opsForValue().set(TaskEnum.SYNC_DATA.getCode(), "1", 1, TimeUnit.HOURS);
        }

        CompletableFuture.runAsync(() -> {
            try {
                List<PowerPredictionSupply> supplyList = powerPredictionSupplyService.getPowerPredictionSupply(query);
                powerPredictionSupplyService.executeSavePowerPredictionSupply(query, supplyList);
                logService.completeTask(TaskEnum.SYNC_DATA.getCode(), scheduledTaskLinesDTO.getTaskNumber(), ScheduleTaskStatusEnum.SUCCESS, "执行成功");
                log.info("info scheduledTaskLinesDTO.getTaskNumber()",scheduledTaskLinesDTO.getTaskNumber());

            } catch (Exception ex) {
                log.error("执行库存供应数据汇总保存逻辑失败", ex);
                log.info("error scheduledTaskLinesDTO.getTaskNumber()={}",scheduledTaskLinesDTO.getTaskNumber());
                logService.completeTask(TaskEnum.SYNC_DATA.getCode(), scheduledTaskLinesDTO.getTaskNumber(), ScheduleTaskStatusEnum.ERROR, logService.getStackTraceAsString(ex));
            } finally {
                redisTemplate.delete(TaskEnum.SYNC_DATA.getCode());
            }
        }, threadPoolExecutor);
        
        restDTO.setSuccess(true);
        restDTO.setMsg("提交同步任务成功,任务批次号:"+scheduledTaskLinesDTO.getTaskNumber());
        return Results.createSuccessRes(restDTO);



    }



    @PostMapping("/pageList")
    @ApiOperation(value = "分页数据")
    public ResponseEntity<Results<PageFeign<PowerPredictionSupplyDTO>>> pageList(@RequestBody PowerPredictionSupplyQuery query) {
        return Results.createSuccessRes(powerPredictionSupplyService.pageList(query));
    }
}