package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerResultShortDTO;
import com.jinkosolar.scp.mps.domain.query.PowerResultShortQuery;
import com.jinkosolar.scp.mps.domain.save.PowerResultShortSaveDTO;
import com.jinkosolar.scp.mps.domain.util.RedisLockUtil;
import com.jinkosolar.scp.mps.feign.SystemFeign;
import com.jinkosolar.scp.mps.service.PowerBaseShortService;
import com.jinkosolar.scp.mps.service.PowerResultShortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基础档位表（月度）查询 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:04
 */
@Slf4j
@RestController
@RequestMapping("/power-result-short")
@Api(value = "power-result-short", tags = "基础档位表（月度）查询操作")
public class PowerResultShortController {
    @Autowired
    PowerResultShortService powerResultShortService;

    @Autowired
    PowerBaseShortService powerBaseShortService;

    @Autowired
    SystemFeign systemFeign;

    @Autowired
    RedisLockUtil redisLock;

    @PostMapping("/queryGroupByPage")
    @ApiOperation(value = "基础档位表（月度）查询汇总分页列表", notes = "获得基础档位表（月度）查询汇总分页列表")
    public ResponseEntity<Results<Page<PowerResultShortDTO>>> queryGroupByPage(@RequestBody PowerResultShortQuery query) {
        return Results.createSuccessRes(powerResultShortService.queryGroupByPage(query));
    }

    @PostMapping("/loadPowerResultShortList")
    @ApiOperation(value = "基础档位表（月度）查询汇总详情列表", notes = "获得基础档位表（月度）查询汇总详情列表")
    public ResponseEntity<Results<List<PowerResultShortDTO>>> loadPowerResultShortList(@RequestBody PowerResultShortQuery query) {
        return Results.createSuccessRes(powerResultShortService.loadPowerResultShortList(query));
    }

    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "基础档位表（月度）查询分页列表", notes = "获得基础档位表（月度）查询分页列表")
    public ResponseEntity<Results<Page<PowerResultShortDTO>>> queryByPage(@RequestBody PowerResultShortQuery query) {
        return Results.createSuccessRes(powerResultShortService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerResultShortDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerResultShortService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerResultShortDTO>> save(@Valid @RequestBody PowerResultShortSaveDTO saveDTO) {
        return Results.createSuccessRes(powerResultShortService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerResultShortService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 重新计算功率正态分布数据
     */
    @PostMapping("/powerShortForecast")
    @ApiOperation(value = "重新计算月度基础档位")
    public ResponseEntity<Results<Object>> powerShortForecast(@RequestBody PowerResultShortDTO powerResultShortDTO) {
        powerResultShortService.powerShortCacl(powerResultShortDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody PowerResultShortQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        powerResultShortService.export(query, response);

    }

    @PostMapping("/loadPowerResultShortVersion")
    @ApiOperation(value = "根据dpGroupId获取月度基础档位版本列表")
    public ResponseEntity<Results<List<Map<String,Object>>>> loadPowerResultShortVersion(@RequestBody PowerResultShortQuery query) {
        return Results.createSuccessRes(powerResultShortService.loadPowerResultShortVersion(query));
    }

    @PostMapping("/getPowerResultShortAttrItem")
    @ApiOperation(value = "根据dpGroupId和版本获取月度基础档位列表")
    public ResponseEntity<Results<PowerResultShortDTO>> getPowerResultShortAttrItem(@RequestBody PowerResultShortQuery query) {
        return Results.createSuccessRes(powerResultShortService.getPowerResultShortAttrItem(query));
    }
}
