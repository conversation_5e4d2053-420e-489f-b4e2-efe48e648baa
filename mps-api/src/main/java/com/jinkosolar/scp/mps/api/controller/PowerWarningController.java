package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PowerWarningDTO;
import com.jinkosolar.scp.mps.domain.query.PowerWarningQuery;
import com.jinkosolar.scp.mps.domain.save.PowerWarningSaveDTO;
import com.jinkosolar.scp.mps.service.PowerWarningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 功率预测预警清单 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:32:50
 */
@RestController
@RequestMapping("/power-warning")
@Api(value = "power-warning", tags = "功率预测预警清单操作")
public class PowerWarningController {
    @Autowired
    PowerWarningService powerWarningService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "功率预测预警清单分页列表", notes = "获得功率预测预警清单分页列表")
    public ResponseEntity<Results<Page<PowerWarningDTO>>> queryByPage(@RequestBody PowerWarningQuery query) {
        return Results.createSuccessRes(powerWarningService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<PowerWarningDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(powerWarningService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<PowerWarningDTO>> save(@Valid @RequestBody PowerWarningSaveDTO saveDTO) {
        return Results.createSuccessRes(powerWarningService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        powerWarningService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }
}
