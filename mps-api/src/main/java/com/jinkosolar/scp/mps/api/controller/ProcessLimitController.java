package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitDTO;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitInfo4OrderQueryDTO;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitItemDTO;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitViewDTO;
import com.jinkosolar.scp.mps.domain.query.ProcessLimitQuery;
import com.jinkosolar.scp.mps.domain.save.ProcessLimitSaveDTO;
import com.jinkosolar.scp.mps.domain.dto.OaApproveDTO;
import com.jinkosolar.scp.mps.service.ProcessLimitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工艺限制主表相关操作控制层
 *
 * <AUTHOR> 2024-05-24 17:54:59
 */
@RequestMapping(value = "/process-limit")
@RestController
@Api(value = "processLimit", tags = "工艺限制主表相关操作控制层")
public class ProcessLimitController extends BaseController {
    @Autowired
    private ProcessLimitService processLimitService;

    @PostMapping("/page")
    @ApiOperation(value = "工艺限制主表分页查询")
    public ResponseEntity<Results<Page<ProcessLimitViewDTO>>> page(@RequestBody ProcessLimitQuery query) {
        return Results.createSuccessRes(processLimitService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "工艺限制主表详情")
    public ResponseEntity<Results<ProcessLimitDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(processLimitService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增工艺限制主表")
    public ResponseEntity<Results<ProcessLimitSaveDTO>> save(@RequestBody ProcessLimitSaveDTO processLimitDTO) {
        validObject(processLimitDTO, ValidGroups.Insert.class);
        ProcessLimitSaveDTO resultDTO = processLimitService.saveProcessLimit(processLimitDTO);
        return Results.createSuccessRes(resultDTO);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除工艺限制主表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        processLimitService.deleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出工艺限制主表")
    @PostMapping("/export")
    public void export(@RequestBody ProcessLimitQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        processLimitService.export(query, response);
    }

    @ApiOperation(value = "导入工艺限制主表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = processLimitService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "查询所有符合条件的限制信息-询单用")
    @PostMapping("/queryProcessLimit4OrderQuery")
    public ResponseEntity<Results<List<ProcessLimitInfo4OrderQueryDTO>>> queryProcessLimit4OrderQuery(@RequestBody ProcessLimitItemDTO dto) {
        return Results.createSuccessRes(processLimitService.queryProcessLimit4OrderQuery(dto));
    }

    @ApiOperation(value = "提交产品工艺限制申请到OA系统")
    @PostMapping("/oa/submit")
    public ResponseEntity<Results<Boolean>> submitToOA(@RequestBody ProcessLimitSaveDTO processLimitDTO) {
        processLimitService.submitToOA(processLimitDTO);
        return Results.createSuccessRes(true);
    }

    @ApiOperation(value = "更新OA单据状态")
    @PostMapping("/oa/status")
    public ResponseEntity<Results<Boolean>> updateOAStatus(@RequestBody OaApproveDTO approveDTO) {
        try {
            processLimitService.updateOAStatus(approveDTO);
            return Results.createAsprovaSuccessRes(true);
        } catch (Exception e) {
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
    }

    @ApiOperation(value = "撤回采购申请")
    @PostMapping("/oa/cancel")
    public ResponseEntity<Results<Boolean>> cancel(@RequestBody OaApproveDTO approveDTO) {
        try {
            return Results.createAsprovaSuccessRes(processLimitService.cancel(approveDTO));
        } catch (Exception e) {
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
    }

    @ApiOperation(value = "根据条件查询工艺限制")
    @PostMapping("/findProcessLimitByCondition")
    public ResponseEntity<Results<List<ProcessLimitInfo4OrderQueryDTO>>> findProcessLimitByCondition(@RequestBody ProcessLimitQuery query) {
        return Results.createSuccessRes(processLimitService.findProcessLimitByCondition(query));
    }

    @ApiOperation(value = "工艺限制临时限制到期前发送晶彩消息提醒")
    @PostMapping("/send/notice")
    public ResponseEntity<Results<Boolean>> sendNotice(@RequestBody ProcessLimitQuery query) {
        try {
            return Results.createAsprovaSuccessRes(processLimitService.sendJingCaiNotice());
        } catch (Exception e) {
            return Results.createAsprovaFailRes(e.getMessage(), "A0001");
        }
    }
}