package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.save.ProcessLimitItemSaveDTO;
import com.jinkosolar.scp.mps.service.ProcessLimitItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工艺限制明细表相关操作控制层
 * 
 * <AUTHOR> 2024-05-24 17:55:02
 */
@RequestMapping(value = "/process-limit-item")
@RestController
@Api(value = "processLimitItem", tags = "工艺限制明细表相关操作控制层")
public class ProcessLimitItemController extends BaseController {
    @Autowired
    private ProcessLimitItemService processLimitItemService;

    @PostMapping("/save")
    @ApiOperation(value = "新增产品工艺限制明细表")
    public ResponseEntity<Results<ProcessLimitItemSaveDTO>> save(@RequestBody ProcessLimitItemSaveDTO processLimitItemDTO) {
        validObject(processLimitItemDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(processLimitItemService.saveProcessLimitItem(processLimitItemDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品工艺限制明细表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> lineIds = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        processLimitItemService.batchDeleteByLineIds(lineIds);
        return Results.createSuccessRes();
    }
}