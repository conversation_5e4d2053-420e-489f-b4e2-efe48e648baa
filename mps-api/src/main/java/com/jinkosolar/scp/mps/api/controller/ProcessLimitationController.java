package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitationDTO;
import com.jinkosolar.scp.mps.domain.query.ProcessLimitationQuery;
import com.jinkosolar.scp.mps.service.ProcessLimitationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工艺限制相关操作控制层
 * 
 * <AUTHOR> 2024-05-13 09:41:00
 */
@RequestMapping(value = "/process-limitation")
@RestController
@Api(value = "processLimitation", tags = "工艺限制相关操作控制层")
public class ProcessLimitationController extends BaseController {
    @Autowired
    private ProcessLimitationService processLimitationService;

    @PostMapping("/page")
    @ApiOperation(value = "工艺限制分页查询")
    public ResponseEntity<Results<Page<ProcessLimitationDTO>>> page(@RequestBody ProcessLimitationQuery query) {
        return Results.createSuccessRes(processLimitationService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "工艺限制详情")
    public ResponseEntity<Results<ProcessLimitationDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(processLimitationService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增工艺限制")
    public ResponseEntity<Results<ProcessLimitationDTO>> save(@RequestBody ProcessLimitationDTO processLimitationDTO) {
        validObject(processLimitationDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(processLimitationService.saveOrUpdate(processLimitationDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除工艺限制")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        processLimitationService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出工艺限制")
    @PostMapping("/export")
    public void export(@RequestBody ProcessLimitationQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        processLimitationService.export(query, response);
    }

    @ApiOperation(value = "导入工艺限制")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = processLimitationService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}