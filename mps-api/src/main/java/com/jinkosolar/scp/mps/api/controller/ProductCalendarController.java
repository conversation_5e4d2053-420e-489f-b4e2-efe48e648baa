package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.ProductCalendarDTO;
import com.jinkosolar.scp.mps.domain.query.ProductCalendarQuery;
import com.jinkosolar.scp.mps.domain.save.ProductCalendarSaveDTO;
import com.jinkosolar.scp.mps.service.ProductCalendarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;


@Api(value = "ProductCalendar", tags = "管理后台 - 生产日历")
@RestController
@RequestMapping("/ProductCalendar")
@Validated
public class ProductCalendarController {

    @Resource
    private ProductCalendarService productCalendarService;

    @PostMapping("/save")
    @ApiOperation(value = "创建生产日历")
    public ResponseEntity<Results<ProductCalendarDTO>> createProductCalendar(@Valid @RequestBody ProductCalendarSaveDTO createReqVO) {
        return Results.createSuccessRes(productCalendarService.save(createReqVO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新生产日历")
    public ResponseEntity<Results<ProductCalendarDTO>> updateProductCalendar(@Valid @RequestBody ProductCalendarSaveDTO updateReqVO) {
        return Results.createSuccessRes(productCalendarService.save(updateReqVO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除生产日历")
    @ApiParam(name = "id", value = "编号", required = true)
    public ResponseEntity<Results<Boolean>> deleteProductCalendar(@RequestBody IdsDTO idsDTO) {
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productCalendarService.logicDeleteByIds(ids);
        return Results.createSuccessRes(true);
    }

    @PostMapping("/get")
    @ApiOperation(value = "获得生产日历")
    @ApiParam(name = "id", value = "编号", required = true)
    public ResponseEntity<Results<ProductCalendarDTO>> getProductCalendar(@RequestBody IdDTO idDTO) {
        ProductCalendarDTO ProductCalendar = productCalendarService.queryById(Long.valueOf(idDTO.getId()));
        return Results.createSuccessRes(ProductCalendar);
    }

    @PostMapping("/page")
    @ApiOperation(value = "获得生产日历分页")
    public ResponseEntity<Results<Page<ProductCalendarDTO>>> getProductCalendarPage(@RequestBody ProductCalendarQuery pageReqVO) {
        Page<ProductCalendarDTO> pageResult = productCalendarService.page(pageReqVO);
        return Results.createSuccessRes(pageResult);
    }

    @PostMapping("/export-excel")
    @ApiOperation(value = "导出生产日历 Excel")
    public void exportProductCalendarExcel(@RequestBody @Valid ProductCalendarQuery pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(GlobalConstant.max_page_size);
        productCalendarService.export(pageReqVO, response);
    }

    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<ImportResultDTO>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productCalendarService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

}