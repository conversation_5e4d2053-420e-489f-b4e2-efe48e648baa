package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.SyncTableUtils;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mrp.ProductInStockRecordRequest;
import com.jinkosolar.scp.jip.api.dto.mrp.ProductInstockRecordResponse;
import com.jinkosolar.scp.jip.api.service.SyncProductInStockRecordService;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.dto.ProductInStockRecordDTO;
import com.jinkosolar.scp.mps.domain.query.ProductInStockRecordQuery;
import com.jinkosolar.scp.mps.service.ProductInStockRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产入库记录表相关操作控制层
 *
 * <AUTHOR> 2024-05-20 18:34:39
 */
@RequestMapping(value = "/product-in-stock-record")
@RestController
@Api(value = "productInStockRecord", tags = "生产入库记录表相关操作控制层")
public class ProductInStockRecordController extends BaseController {
    @Autowired
    private ProductInStockRecordService productInStockRecordService;

    @Autowired
    private SyncProductInStockRecordService syncProductInStockRecordService;
    @Autowired
    private SyncTableUtils syncTableUtils;


    @PostMapping("/syncProductInStockRecord")
    @ApiOperation(value = "工厂采购入库记录表分页查询")
    public JipResponseData syncProductInStockRecord(@RequestParam(required = false,value = "date") String date) {
        productInStockRecordService.sync(date);
        return JipResponseData.success();
    }

    @PostMapping("/selectQuantity")
    @ApiOperation(value = "实投数量扣减")
    public void selectQuantity() {
        productInStockRecordService.selectQuantity();
        SyncTableDTO tableDTO = new SyncTableDTO();
        List<String> tablesList = new ArrayList<String>();
        tablesList.add(MpsLovConstant.MRP_PRODUCTION_UNFINISHED_PLAN);
        tableDTO.setLovCodes(tablesList);
        // 推送计算后的已投产未完工数据到APS
        syncTableUtils.syncTables(tableDTO);
    }

    @PostMapping("/sync")
    @ApiOperation(value = "同步接口外部调用")
    public ResponseEntity<Results<Object>> productInStockRecordSync() {
        return Results.createSuccessRes();
    }

    @PostMapping("/page")
    @ApiOperation(value = "生产入库记录表分页查询")
    public ResponseEntity<Results<Page<ProductInStockRecordDTO>>> page(@RequestBody ProductInStockRecordQuery query) {
        return Results.createSuccessRes(productInStockRecordService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "生产入库记录表详情")
    public ResponseEntity<Results<ProductInStockRecordDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productInStockRecordService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增生产入库记录表")
    public ResponseEntity<Results<ProductInStockRecordDTO>> insert(@RequestBody ProductInStockRecordDTO productInStockRecordDTO) {
        validObject(productInStockRecordDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(productInStockRecordService.saveOrUpdate(productInStockRecordDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新生产入库记录表")
    public ResponseEntity<Results<ProductInStockRecordDTO>> update(@RequestBody ProductInStockRecordDTO productInStockRecordDTO) {
        validObject(productInStockRecordDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(productInStockRecordService.saveOrUpdate(productInStockRecordDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除生产入库记录表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productInStockRecordService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出生产入库记录表")
    @PostMapping("/export")
    public void export(@RequestBody ProductInStockRecordQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productInStockRecordService.export(query, response);
    }


    @ApiOperation(value = "导入生产入库记录表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productInStockRecordService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}
