package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ProductDescDTO;
import com.jinkosolar.scp.mps.domain.dto.ProductMaterialCombinationMappingDTO;
import com.jinkosolar.scp.mps.domain.query.ProductDescQuery;
import com.jinkosolar.scp.mps.domain.query.ProductMaterialCombinationMappingQuery;
import com.jinkosolar.scp.mps.service.ProductDescService;
import com.jinkosolar.scp.mps.service.ProductMaterialCombinationMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 投产方案对应的材料搭配对照关系相关操作控制层
 * 
 * <AUTHOR> 2024-05-29 10:35:32
 */
@RequestMapping(value = "/product-material-combination-mapping")
@RestController
@Api(value = "productMaterialCombinationMapping", tags = "投产方案对应的材料搭配对照关系相关操作控制层")
public class ProductMaterialCombinationMappingController extends BaseController {
    @Autowired
    private ProductMaterialCombinationMappingService productMaterialCombinationMappingService;

    @Autowired
    private ProductDescService productDescService;

    @PostMapping("/page/desc")
    @ApiOperation(value = "投产方案描述分页")
    public ResponseEntity<Results<Page<ProductDescDTO>>> page(@RequestBody ProductDescQuery query){
        return Results.createSuccessRes(productDescService.page(query));
    }

    @PostMapping("/page")
    @ApiOperation(value = "投产方案对应的材料搭配对照关系分页查询")
    public ResponseEntity<Results<Page<ProductMaterialCombinationMappingDTO>>> page(@RequestBody ProductMaterialCombinationMappingQuery query) {
        return Results.createSuccessRes(productMaterialCombinationMappingService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "投产方案对应的材料搭配对照关系详情")
    public ResponseEntity<Results<ProductMaterialCombinationMappingDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productMaterialCombinationMappingService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增投产方案对应的材料搭配对照关系")
    public ResponseEntity<Results<ProductMaterialCombinationMappingDTO>> insert(@RequestBody ProductMaterialCombinationMappingDTO productMaterialCombinationMappingDTO) {
        validObject(productMaterialCombinationMappingDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(productMaterialCombinationMappingService.saveOrUpdate(productMaterialCombinationMappingDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新投产方案对应的材料搭配对照关系")
    public ResponseEntity<Results<ProductMaterialCombinationMappingDTO>> update(@RequestBody ProductMaterialCombinationMappingDTO productMaterialCombinationMappingDTO) {
        validObject(productMaterialCombinationMappingDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(productMaterialCombinationMappingService.saveOrUpdate(productMaterialCombinationMappingDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除投产方案对应的材料搭配对照关系")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productMaterialCombinationMappingService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出投产方案对应的材料搭配对照关系")
    @PostMapping("/export")
    public void export(@RequestBody ProductMaterialCombinationMappingQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productMaterialCombinationMappingService.export(query, response);
    }

    @ApiOperation(value = "导入投产方案对应的材料搭配对照关系")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productMaterialCombinationMappingService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "选择投产方案对应的材料搭配对照关系描述")
    @PostMapping("/queryProductMaterialCombinationMappingDesc")
    public  ResponseEntity<Results<List<ProductMaterialCombinationMappingDTO>>> queryProductMaterialCombinationMappingDesc(@RequestBody ProductMaterialCombinationMappingQuery query) {
        return Results.createSuccessRes(productMaterialCombinationMappingService.queryProductMaterialCombinationMappingDesc(query));
    }
}