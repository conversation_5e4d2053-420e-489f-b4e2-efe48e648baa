package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ProductPlanLineDTO;
import com.jinkosolar.scp.mps.domain.query.ProductPlanLineQuery;
import com.jinkosolar.scp.mps.service.ProductPlanLineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * APS排产计划行相关操作控制层
 * 
 * <AUTHOR> 2024-05-10 16:37:16
 */
@RequestMapping(value = "/product-plan-line")
@RestController
@Api(value = "productPlanLine", tags = "APS排产计划行相关操作控制层")
public class ProductPlanLineController extends BaseController {
    @Autowired
    private ProductPlanLineService productPlanLineService;

    @PostMapping("/page")
    @ApiOperation(value = "APS排产计划行分页查询")
    public ResponseEntity<Results<Page<ProductPlanLineDTO>>> page(@RequestBody ProductPlanLineQuery query) {
        return Results.createSuccessRes(productPlanLineService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "APS排产计划行详情")
    public ResponseEntity<Results<ProductPlanLineDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productPlanLineService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增APS排产计划行")
    public ResponseEntity<Results<ProductPlanLineDTO>> insert(@RequestBody ProductPlanLineDTO productPlanLineDTO) {
        validObject(productPlanLineDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(productPlanLineService.saveOrUpdate(productPlanLineDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新APS排产计划行")
    public ResponseEntity<Results<ProductPlanLineDTO>> update(@RequestBody ProductPlanLineDTO productPlanLineDTO) {
        validObject(productPlanLineDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(productPlanLineService.saveOrUpdate(productPlanLineDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除APS排产计划行")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productPlanLineService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出APS排产计划行")
    @PostMapping("/export")
    public void export(@RequestBody ProductPlanLineQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productPlanLineService.export(query, response);
    }

    @ApiOperation(value = "导入APS排产计划行")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productPlanLineService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}