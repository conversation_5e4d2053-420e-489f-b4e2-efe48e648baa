package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.CapacityMonthToReportDTO;
import com.jinkosolar.scp.mps.domain.dto.ProductPlanLineDetailDTO;
import com.jinkosolar.scp.mps.domain.query.ProductPlanLineDetailQuery;
import com.jinkosolar.scp.mps.service.MpsWorkCenterDetailService;
import com.jinkosolar.scp.mps.service.ProductPlanLineDetailService;
import com.jinkosolar.scp.mps.service.SingleModuleWattageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 生产计划明细行(到天)相关操作控制层
 *
 * <AUTHOR> 2024-06-25 15:43:43
 */
@RequestMapping(value = "/product-plan-line-detail")
@RestController
@Api(value = "productPlanLineDetail", tags = "生产计划明细行(到天)相关操作控制层")
public class ProductPlanLineDetailController extends BaseController {
    @Autowired
    private ProductPlanLineDetailService productPlanLineDetailService;
    @Resource
    private MpsWorkCenterDetailService mpsWorkCenterDetailService;
    @Resource
    private SingleModuleWattageService singleModuleWattageService;

    @PostMapping("/page")
    @ApiOperation(value = "生产计划明细行(到天)分页查询")
    public ResponseEntity<Results<Page<ProductPlanLineDetailDTO>>> page(@RequestBody ProductPlanLineDetailQuery query) {
        return Results.createSuccessRes(productPlanLineDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "生产计划明细行(到天)详情")
    public ResponseEntity<Results<ProductPlanLineDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productPlanLineDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增生产计划明细行(到天)")
    public ResponseEntity<Results<ProductPlanLineDetailDTO>> insert(@RequestBody ProductPlanLineDetailDTO productPlanLineDetailDTO) {
        validObject(productPlanLineDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(productPlanLineDetailService.saveOrUpdate(productPlanLineDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新生产计划明细行(到天)")
    public ResponseEntity<Results<ProductPlanLineDetailDTO>> update(@RequestBody ProductPlanLineDetailDTO productPlanLineDetailDTO) {
        validObject(productPlanLineDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(productPlanLineDetailService.saveOrUpdate(productPlanLineDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除生产计划明细行(到天)")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productPlanLineDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出生产计划明细行(到天)")
    @PostMapping("/export")
    public void export(@RequestBody ProductPlanLineDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productPlanLineDetailService.export(query, response);
    }

    @ApiOperation(value = "导入生产计划明细行(到天)")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productPlanLineDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/findCapacityMonth/page")
    @ApiOperation(value = "生产计划产能月度汇总分页查询")
    public ResponseEntity<Results<Page<ProductPlanLineDetailDTO>>> findCapacityMonth(@RequestBody ProductPlanLineDetailQuery query) {
        return Results.createSuccessRes(productPlanLineDetailService.findCapacityMonth(query));
    }


    @ApiOperation(value = "生产计划产能月度汇总导出")
    @PostMapping("/findCapacityMonth/export")
    public void exportDataPlan(@RequestBody ProductPlanLineDetailQuery query, HttpServletResponse response) {

        List<CapacityMonthToReportDTO> reportDTOS = new ArrayList<>();
        //查询车间描述
        Map<String, String> workCenterDescMap = mpsWorkCenterDetailService.findWorkCenterDesc();
        //查询最新版本瓦数信息
        String version = singleModuleWattageService.findMaxVersion();
        Map<String, BigDecimal> singleModuleWattageMap = singleModuleWattageService.findSingleModuleWattage(version);
        //查询工厂信息
        List<String> list = productPlanLineDetailService.findFactoryCode();
        List<String> filterList = new ArrayList<>();
        if (!StringUtils.isEmpty(query.getFactoryCode())) {
            filterList.add(query.getFactoryCode());
        } else {
            filterList = list;
        }

        List<Future<List<CapacityMonthToReportDTO>>> futureList = new ArrayList<>();
        //已工厂维度 创建子线程
        filterList.stream().forEach(factory -> {
            futureList.add(productPlanLineDetailService.exportDataPlan(query, workCenterDescMap, singleModuleWattageMap, version, factory));

        });
        //查询任务执行的结果
        for (Future<?> future : futureList) {
            while (true) {
                try {
                    if (future.isDone() && !future.isCancelled()) {
                        List<CapacityMonthToReportDTO> result = (List<CapacityMonthToReportDTO>) future.get();//获取结果
                        reportDTOS.addAll(result);
                        break;//当前future获取结果完毕，跳出while
                    } else {
                        Thread.sleep(1);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

            }
        }
        String fileName = "产能月汇总报表";
        List<List<Object>> excelData = ExcelUtils.getList(reportDTOS, query.getExcelPara(), CapacityMonthToReportDTO::getMonthValueMap);
        ExcelUtils.exportEx(response, fileName, fileName, query.getExcelPara().getSimpleHeader(), excelData);
    }
}