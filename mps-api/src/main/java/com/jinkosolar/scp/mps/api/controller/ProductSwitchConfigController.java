package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ProductSwitchConfigDTO;
import com.jinkosolar.scp.mps.domain.query.ProductSwitchConfigQuery;
import com.jinkosolar.scp.mps.service.ProductSwitchConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品切换计划维护相关操作控制层
 * 
 * <AUTHOR> 2025-01-06 09:48:37
 */
@RequestMapping(value = "/product-switch-config")
@RestController
@Api(value = "productSwitchConfig", tags = "产品切换计划维护相关操作控制层")
@RequiredArgsConstructor  
public class ProductSwitchConfigController extends BaseController {    
    private final ProductSwitchConfigService productSwitchConfigService; 

    @PostMapping("/page")
    @ApiOperation(value = "产品切换计划维护分页查询")
    public ResponseEntity<Results<Page<ProductSwitchConfigDTO>>> page(@RequestBody ProductSwitchConfigQuery query) {
        return Results.createSuccessRes(productSwitchConfigService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品切换计划维护详情")
    public ResponseEntity<Results<ProductSwitchConfigDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productSwitchConfigService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品切换计划维护")
    public ResponseEntity<Results<Void>> insert(@RequestBody ProductSwitchConfigDTO productSwitchConfigDTO) {
        validObject(productSwitchConfigDTO, ValidGroups.Insert.class);
        productSwitchConfigService.saveOrUpdate(productSwitchConfigDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品切换计划维护")
    public ResponseEntity<Results<Void>> update(@RequestBody ProductSwitchConfigDTO productSwitchConfigDTO) {
        validObject(productSwitchConfigDTO, ValidGroups.Update.class);
        productSwitchConfigService.saveOrUpdate(productSwitchConfigDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品切换计划维护")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productSwitchConfigService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品切换计划维护")
    @PostMapping("/export")
    public void export(@RequestBody ProductSwitchConfigQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productSwitchConfigService.export(query, response);
    }

    @ApiOperation(value = "导入产品切换计划维护")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productSwitchConfigService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/sendNotice")
    @ApiOperation(value = "批量删除产品切换计划维护")
    public ResponseEntity<Results<Void>> sendJinkoNotice(@RequestBody List<ProductSwitchConfigDTO> productSwitchConfigList) {
        Assert.notEmpty(productSwitchConfigList, "传入的数据不能为空");
        productSwitchConfigService.sendJinkoNotice(productSwitchConfigList);
        return Results.createSuccessRes();
    }
}