package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.ProductYieldThicknessDTO;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanTempQuery;
import com.jinkosolar.scp.mps.domain.query.ProductYieldThicknessQuery;
import com.jinkosolar.scp.mps.domain.util.Constant;
import com.jinkosolar.scp.mps.service.ProductYieldThicknessService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品良率厚度信息表相关操作控制层
 * 
 * <AUTHOR> 2024-07-24 19:46:16
 */
@RequestMapping(value = "/product-yield-thickness")
@RestController
@Api(value = "productYieldThickness", tags = "产品良率厚度信息表相关操作控制层")
public class ProductYieldThicknessController extends BaseController {    
    private final ProductYieldThicknessService productYieldThicknessService;
    @Autowired
    private SyncTableUtils syncTableUtils;


    public ProductYieldThicknessController(ProductYieldThicknessService productYieldThicknessService) {
        this.productYieldThicknessService = productYieldThicknessService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "产品良率厚度信息表分页查询")
    public ResponseEntity<Results<Page<ProductYieldThicknessDTO>>> page(@RequestBody ProductYieldThicknessQuery query) {
        return Results.createSuccessRes(productYieldThicknessService.page(query));
    }
    @PostMapping("/query-for-dp")
    @ApiOperation(value = "产品良率厚度信息给DP使用")
    public ResponseEntity<Results<List<ProductYieldThicknessDTO>>> queryForDp(@RequestBody ProductYieldThicknessQuery query) {
        return Results.createSuccessRes(productYieldThicknessService.queryForDp(query));
    }
    @PostMapping("/detail")
    @ApiOperation(value = "产品良率厚度信息表详情")
    public ResponseEntity<Results<ProductYieldThicknessDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productYieldThicknessService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品良率厚度信息表")
    public ResponseEntity<Results<ProductYieldThicknessDTO>> insert(@RequestBody ProductYieldThicknessDTO productYieldThicknessDTO) {
        validObject(productYieldThicknessDTO, ValidGroups.Insert.class);
        ProductYieldThicknessDTO result = productYieldThicknessService.saveOrUpdate(productYieldThicknessDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }


    @PostMapping("/insertAll")
    @ApiOperation(value = "初始化产品良率厚度信息表")
    public  ResponseEntity<Results<Boolean>> insertAll(@RequestBody ProductYieldThicknessDTO productYieldThicknessDTO ) {
        Boolean result=productYieldThicknessService.insertAll(productYieldThicknessDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/saveBatch")
    @ApiOperation(value = "修改产品良率厚度信息表")
    public  ResponseEntity<Results<Boolean>> saveBatch(@RequestBody ProductYieldThicknessDTO productYieldThicknessDTO) {
        Boolean result=productYieldThicknessService.saveBatch(productYieldThicknessDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/deleteBatch")
    @ApiOperation(value = "批量删除产品良率厚度信息表")
    public ResponseEntity<Results<Object>> deleteBatch(@RequestBody  List<ProductYieldThicknessDTO> productYieldThicknessDTOList) {
        productYieldThicknessService.deleteBatch(productYieldThicknessDTOList);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes();
    }



    @PostMapping("/update")
    @ApiOperation(value = "更新产品良率厚度信息表")
    public ResponseEntity<Results<ProductYieldThicknessDTO>> update(@RequestBody ProductYieldThicknessDTO productYieldThicknessDTO) {
        validObject(productYieldThicknessDTO, ValidGroups.Update.class);
        ProductYieldThicknessDTO result = productYieldThicknessService.saveOrUpdate(productYieldThicknessDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品良率厚度信息表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productYieldThicknessService.logicDeleteByIds(ids);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品良率厚度信息表")
    @PostMapping("/export")
    public void export(@RequestBody ProductYieldThicknessQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productYieldThicknessService.export(query, response);
    }

    @ApiOperation(value = "导入产品良率厚度信息表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productYieldThicknessService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    /**
     *
     * 更新排产表晶棒投入量字段
     * */
    @ApiOperation(value = "更新排产表晶棒投入量字段")
    @PostMapping("/syncUpdateProductionVolume")
    public ResponseEntity<Results<Boolean>> syncUpdateProductionVolume() {
        try {
            return Results.createAsprovaSuccessRes(productYieldThicknessService.syncUpdateProductionVolume());
        }catch (Exception e){
            return Results.createAsprovaFailRes(e.toString(), "A0001");
        }
    }


    @PostMapping("/countKgNum")
    @ApiOperation(value = "计算每KG数据")
    public ResponseEntity<Results<Object>> countKgNum(@RequestBody IdDTO idDTO) {
        productYieldThicknessService.countKgNum(idDTO.getId());
        return Results.createSuccessRes();
    }

    public void syncToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> list = new ArrayList<>();
        list.add(Constant.MPS_PRODUCT_YIELD_THICKNESS);
        syncTableDTO.setLovCodes(list);
        syncTableUtils.syncTables(syncTableDTO);
    }
}