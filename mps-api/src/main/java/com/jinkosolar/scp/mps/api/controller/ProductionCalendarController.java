package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.ProductionCalendarDTO;
import com.jinkosolar.scp.mps.domain.query.ProductionCalendarQuery;
import com.jinkosolar.scp.mps.domain.util.Constant;
import com.jinkosolar.scp.mps.service.ProductionCalendarService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 生产日历相关操作控制层
 * 
 * <AUTHOR> 2024-07-10 13:35:52
 */
@RequestMapping(value = "/production-calendar")
@RestController
@Api(value = "productionCalendar", tags = "生产日历相关操作控制层")
public class ProductionCalendarController extends BaseController {
    @Autowired
    private ProductionCalendarService productionCalendarService;
    @Autowired
    private SyncTableUtils syncTableUtils;

    @PostMapping("/page")
    @ApiOperation(value = "生产日历分页查询")
    public ResponseEntity<Results<Page<ProductionCalendarDTO>>> page(@RequestBody ProductionCalendarQuery query) {
        return Results.createSuccessRes(productionCalendarService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "生产日历详情")
    public ResponseEntity<Results<ProductionCalendarDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(productionCalendarService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增生产日历")
    public ResponseEntity<Results<ProductionCalendarDTO>> insert(@RequestBody ProductionCalendarDTO productionCalendarDTO) {
        validObject(productionCalendarDTO, ValidGroups.Insert.class);
        ProductionCalendarDTO result = productionCalendarService.saveOrUpdate(productionCalendarDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新生产日历")
    public ResponseEntity<Results<ProductionCalendarDTO>> update(@RequestBody ProductionCalendarDTO productionCalendarDTO) {
        validObject(productionCalendarDTO, ValidGroups.Update.class);
        ProductionCalendarDTO result = productionCalendarService.saveOrUpdate(productionCalendarDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除生产日历")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        productionCalendarService.logicDeleteByIds(ids);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出生产日历")
    @PostMapping("/export")
    public void export(@RequestBody ProductionCalendarQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        productionCalendarService.export(query, response);
    }

    @ApiOperation(value = "导入生产日历")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = productionCalendarService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    public void syncToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> list = new ArrayList<>();
        list.add(Constant.MPS_PRODUCTION_CALENDAR);
        syncTableDTO.setLovCodes(list);
        syncTableUtils.syncTables(syncTableDTO);
    }
}