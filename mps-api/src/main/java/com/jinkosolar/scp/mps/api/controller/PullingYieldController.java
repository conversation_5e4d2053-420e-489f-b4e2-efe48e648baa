package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.PullingYieldDTO;
import com.jinkosolar.scp.mps.domain.query.PullingYieldQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.PullingYieldService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
/**
 * 拉晶A级良率相关操作控制层
 * 
 * <AUTHOR> 2024-07-05 11:14:39
 */
@RequestMapping(value = "/pulling-yield")
@RestController
@Api(value = "pullingYield", tags = "拉晶A级良率相关操作控制层")
public class PullingYieldController extends BaseController {
    @Autowired
    private PullingYieldService pullingYieldService;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶A级良率分页查询")
    public ResponseEntity<Results<Page<PullingYieldDTO>>> page(@RequestBody PullingYieldQuery query) {
        return Results.createSuccessRes(pullingYieldService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶A级良率详情")
    public ResponseEntity<Results<PullingYieldDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(pullingYieldService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶A级良率")
    public ResponseEntity<Results<PullingYieldDTO>> insert(@RequestBody PullingYieldDTO pullingYieldDTO) {
        validObject(pullingYieldDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(pullingYieldService.saveOrUpdate(pullingYieldDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶A级良率")
    public ResponseEntity<Results<PullingYieldDTO>> update(@RequestBody PullingYieldDTO pullingYieldDTO) {
        validObject(pullingYieldDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(pullingYieldService.saveOrUpdate(pullingYieldDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶A级良率")
    public ResponseEntity<Results<Object>> delete(List<PullingYieldDTO> pullingYieldDTO) {
        validObject(pullingYieldDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(pullingYieldService.deleteByDto((List<PullingYieldDTO>) pullingYieldDTO));
    }

    @ApiOperation(value = "导出拉晶A级良率")
    @PostMapping("/export")
    public void export(@RequestBody PullingYieldQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        pullingYieldService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶A级良率")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>>importFile(@RequestPart("file") MultipartFile multipartFile,
                                                              @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = pullingYieldService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            pullingYieldService.syncToAPS();
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(pullingYieldService.findVersion(versionQuery));
    }

}