package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.query.PowerPredictionReleaseQuery;
import com.jinkosolar.scp.mps.service.PowerPredictionReleaseService;
import com.jinkosolar.scp.mps.service.ReReleaseVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 重发布版本相关操作控制层
 *
 * <AUTHOR> 2024-05-23 10:03:06
 */
@RequestMapping(value = "/re-release-version")
@RestController
@Api(value = "reReleaseVersion", tags = "重发布版本相关操作控制层")
public class ReReleaseVersionController extends BaseController {
    @Autowired
    private ReReleaseVersionService reReleaseVersionService;

    @JipFeignLog
    @PostMapping("/queryReReleaseVersionByMes")
    @ApiOperation(value = "MES系统获取重发布版本数据")
    public ReReleaseVersionMESResponseDTO queryReReleaseVersionByMes(@RequestBody PowerPredictionReleaseMESDTO query) {
        ReReleaseVersionMESResponseDTO responseDTO = new ReReleaseVersionMESResponseDTO();
        try{
            responseDTO.setReleasePageList(reReleaseVersionService.page(query));
            responseDTO.setEtReturn(JipResponseData.success().getEtReturn());
        } catch (Exception ex) {
            responseDTO.setReleasePageList(null);
            responseDTO.setEtReturn(JipResponseData.error("queryReReleaseVersionByMes system error : " + ex).getEtReturn());
        }
        return responseDTO;
    }

    /*@PostMapping("/insert")
    @ApiOperation(value = "新增产品功率预测版本")
    public ResponseEntity<Results<ReReleaseVersionDTO>> insert(@RequestBody PowerPredictionReleaseDTO powerPredictionReleaseDTO) {
        validObject(powerPredictionReleaseDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(powerPredictionReleaseService.saveOrUpdate(powerPredictionReleaseDTO));
    }*/
}
