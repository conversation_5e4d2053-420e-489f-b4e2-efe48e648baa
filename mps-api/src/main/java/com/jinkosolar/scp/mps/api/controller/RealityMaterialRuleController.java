package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.RealityMaterialRuleDTO;
import com.jinkosolar.scp.mps.domain.query.RealityMaterialRuleQuery;
import com.jinkosolar.scp.mps.domain.save.RealityMaterialRuleSaveDTO;
import com.jinkosolar.scp.mps.service.RealityMaterialRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 预测结果实际用料规则
 *
 * <AUTHOR>
 * @date 2022-9-30
 */
@RestController
@RequestMapping("/reality-material-rule")
@Api(value = "/reality-material-rule", tags = "预测结果实际用料规则")
public class RealityMaterialRuleController {
    @Autowired
    private RealityMaterialRuleService realityMaterialRuleService;
    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "预测结果实际用料规则分页列表", notes = "预测结果实际用料规则分页列表")
    public ResponseEntity<Results<Page<RealityMaterialRuleDTO>>> queryByPage(@RequestBody RealityMaterialRuleQuery query) {
        return Results.createSuccessRes(realityMaterialRuleService.queryByPage(query));
    }

     /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<RealityMaterialRuleDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(realityMaterialRuleService.queryById(Long.parseLong(idDTO.getId())));
    }
    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<RealityMaterialRuleDTO>> save(@Valid @RequestBody RealityMaterialRuleSaveDTO saveDTO) {
        return Results.createSuccessRes(realityMaterialRuleService.save(saveDTO));
    }
    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        realityMaterialRuleService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }
    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody RealityMaterialRuleQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        realityMaterialRuleService.export(query, response);
    }
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile multipartFile,
                                                      @RequestPart("excelPara") ExcelPara excelPara) {
        return Results.createSuccessRes(realityMaterialRuleService.importData(multipartFile, excelPara));
    }
}
