package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.LovUtils;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BaseBatteryTakeoutDTO;
import com.jinkosolar.scp.mps.domain.dto.RealtimeInventoryDTO;
import com.jinkosolar.scp.mps.domain.query.RealtimeInventoryQuery;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.feign.SystemFeign;
import com.jinkosolar.scp.mps.service.RealtimeInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实时库存表相关操作控制层
 *
 * <AUTHOR> 2024-05-09 17:10:18
 */
@RequestMapping(value = "/realtime-inventory")
@RestController
@Api(value = "realtimeInventory", tags = "实时库存表相关操作控制层")
public class RealtimeInventoryController extends BaseController {


    @Autowired
    private RealtimeInventoryService realtimeInventoryService;


    @Autowired
    SystemFeign systemFeign;

    @PostMapping("/batteryFacotrys")
    @ApiOperation(value = "查询电池工厂字典")
    ResponseEntity<Results<LovHeaderDTO>> queryBatteryFacotrys(@RequestBody LovLineQuery lovLineQuery){

        LovHeaderDTO lovHeaderDTO= realtimeInventoryService.queryBatteryFactorys(lovLineQuery);
        return Results.createSuccessRes(lovHeaderDTO);


    }



    @PostMapping("/page")
    @ApiOperation(value = "实时库存表分页查询")
    public ResponseEntity<Results<Page<RealtimeInventoryDTO>>> page(@RequestBody RealtimeInventoryQuery query) {
        return Results.createSuccessRes(realtimeInventoryService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "实时库存表详情")
    public ResponseEntity<Results<RealtimeInventoryDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(realtimeInventoryService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增实时库存表")
    public ResponseEntity<Results<RealtimeInventoryDTO>> insert(@RequestBody RealtimeInventoryDTO realtimeInventoryDTO) {
        validObject(realtimeInventoryDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(realtimeInventoryService.saveOrUpdate(realtimeInventoryDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新实时库存表")
    public ResponseEntity<Results<RealtimeInventoryDTO>> update(@RequestBody RealtimeInventoryDTO realtimeInventoryDTO) {
        validObject(realtimeInventoryDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(realtimeInventoryService.saveOrUpdate(realtimeInventoryDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除实时库存表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        realtimeInventoryService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出实时库存表")
    @PostMapping("/export")
    public void export(@RequestBody RealtimeInventoryQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        realtimeInventoryService.export(query, response);
    }

    @ApiOperation(value = "导入实时库存表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = realtimeInventoryService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/sync")
    @ApiOperation(value = "同步接口外部调用")
    public ResponseEntity<Results<Object>> realtimeSync() {
        realtimeInventoryService.sync(null,false);
        return Results.createSuccessRes();
    }

    @PostMapping("/baseBatteryTakeoutPage")
    @ApiOperation(value = "基地电池外卖分页查询")
    public ResponseEntity<Results<Page<BaseBatteryTakeoutDTO>>> baseBatteryTakeoutPage(@RequestBody RealtimeInventoryQuery query) {
        return Results.createSuccessRes(realtimeInventoryService.baseBatteryTakeoutPage(query));
    }

    @ApiOperation(value = "基地电池外卖导出")
    @PostMapping("/baseBatteryTakeoutExpory")
    public void baseBatteryTakeoutExpory(@RequestBody RealtimeInventoryQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        realtimeInventoryService.baseBatteryTakeoutExpory(query, response);
    }
}
