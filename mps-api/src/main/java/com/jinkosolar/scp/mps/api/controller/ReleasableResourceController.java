package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.ReleasableResourceDTO;
import com.jinkosolar.scp.mps.domain.query.ReleasableResourceQuery;
import com.jinkosolar.scp.mps.service.ReleasableResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 可排资源相关操作控制层
 * 
 * <AUTHOR> 2024-07-29 15:16:17
 */
@RequestMapping(value = "/releasable-resource")
@RestController
@Api(value = "releasableResource", tags = "可排资源相关操作控制层")
public class ReleasableResourceController extends BaseController {    
    private final ReleasableResourceService releasableResourceService;

    public ReleasableResourceController(ReleasableResourceService releasableResourceService) {
        this.releasableResourceService = releasableResourceService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "可排资源分页查询")
    public ResponseEntity<Results<Page<ReleasableResourceDTO>>> page(@RequestBody ReleasableResourceQuery query) {
        return Results.createSuccessRes(releasableResourceService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "可排资源详情")
    public ResponseEntity<Results<ReleasableResourceDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(releasableResourceService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody ReleasableResourceDTO releasableResourceDTO) {
        return Results.createSuccessRes(releasableResourceService.getVersionNumberByDataType(releasableResourceDTO));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增可排资源")
    public ResponseEntity<Results<ReleasableResourceDTO>> insert(@RequestBody ReleasableResourceDTO releasableResourceDTO) {
        validObject(releasableResourceDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(releasableResourceService.saveOrUpdate(releasableResourceDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新可排资源")
    public ResponseEntity<Results<ReleasableResourceDTO>> update(@RequestBody ReleasableResourceDTO releasableResourceDTO) {
        validObject(releasableResourceDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(releasableResourceService.saveOrUpdate(releasableResourceDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除可排资源")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        releasableResourceService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出可排资源")
    @PostMapping("/export")
    public void export(@RequestBody ReleasableResourceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        releasableResourceService.export(query, response);
    }

    @ApiOperation(value = "导入可排资源")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = releasableResourceService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}