package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
//import com.jinkosolar.scp.bom.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.dto.ReleasePowerApsDTO;
import com.jinkosolar.scp.mps.domain.dto.feign.SendDto;
import com.jinkosolar.scp.mps.domain.query.ReleasePowerApsQuery;
import com.jinkosolar.scp.mps.service.ReleasePowerApsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 释放功率管理相关操作控制层
 *
 * <AUTHOR> 2024-04-29 11:47:33
 */
@RequestMapping(value = "/release-power")
@RestController
@Api(value = "releasePower", tags = "释放功率管理相关操作控制层")
public class ReleasePowerApsController extends BaseController {
    @Autowired
    private ReleasePowerApsService releasePowerApsService;

    @PostMapping("/page")
    @ApiOperation(value = "释放功率管理分页查询")
    public ResponseEntity<Results<Page<ReleasePowerApsDTO>>> page(@RequestBody ReleasePowerApsQuery query) {
        return Results.createSuccessRes(releasePowerApsService.page(query));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增释放功率管理")
    public ResponseEntity<Results<ReleasePowerApsDTO>> insert(@RequestBody ReleasePowerApsDTO dto) {
        validObject(dto, ValidGroups.Insert.class);

        return Results.createSuccessRes(releasePowerApsService.saveOrUpdate(dto));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新释放功率")
    public ResponseEntity<Results<ReleasePowerApsDTO>> update(@RequestBody ReleasePowerApsDTO dto) {
        validObject(dto, ValidGroups.Update.class);

        return Results.createSuccessRes(releasePowerApsService.saveOrUpdate(dto));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除释放功率管理")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        //修改人
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        releasePowerApsService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出释放功率管理")
    @PostMapping("/export")
    public void export(@RequestBody ReleasePowerApsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        releasePowerApsService.export(query, response);
    }

    @PostMapping("/sapOrderNoList")
    @ApiOperation(value = "销售订单号下拉选项")
    public ResponseEntity<Results<List<String>>> sapOrderNoList() {
        return Results.createSuccessRes(releasePowerApsService.sapOrderNoList());
    }

    //行号下拉
    @PostMapping("/sapLineIdList")
    @ApiOperation(value = "行号下拉选项")
    public ResponseEntity<Results<List<String>>> sapLineIdList(@RequestParam("sapOrderNo") String sapOrderNo) {
        return Results.createSuccessRes(releasePowerApsService.sapLineIdList(sapOrderNo));
    }

    @ApiOperation(value = "导入技术释放明细")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = releasePowerApsService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            pushData();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/pushingData")
    @ApiOperation(value = "推送技术释放明细数据")
    public ResponseEntity<Results<Boolean>> pushData() {
        releasePowerApsService.syncReleasePowerAps();
        return Results.createSuccessRes(true);
    }
}
