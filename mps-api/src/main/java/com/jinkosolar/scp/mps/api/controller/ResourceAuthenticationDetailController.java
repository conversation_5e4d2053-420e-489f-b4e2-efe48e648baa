package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ResourceAuthenticationDetailDTO;
import com.jinkosolar.scp.mps.domain.query.ResourceAuthenticationDetailQuery;
import com.jinkosolar.scp.mps.service.ResourceAuthenticationDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资源认证明细相关操作控制层
 * 
 * <AUTHOR> 2024-09-25 10:03:02
 */
@RequestMapping(value = "/resource-authentication-detail")
@RestController
@Api(value = "resourceAuthenticationDetail", tags = "资源认证明细相关操作控制层")
@RequiredArgsConstructor  
public class ResourceAuthenticationDetailController extends BaseController {    
    private final ResourceAuthenticationDetailService resourceAuthenticationDetailService; 

    @PostMapping("/page")
    @ApiOperation(value = "资源认证明细分页查询")
    public ResponseEntity<Results<Page<ResourceAuthenticationDetailDTO>>> page(@RequestBody ResourceAuthenticationDetailQuery query) {
        return Results.createSuccessRes(resourceAuthenticationDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "资源认证明细详情")
    public ResponseEntity<Results<ResourceAuthenticationDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(resourceAuthenticationDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增资源认证明细")
    public ResponseEntity<Results<Void>> insert(@RequestBody ResourceAuthenticationDetailDTO resourceAuthenticationDetailDTO) {
        validObject(resourceAuthenticationDetailDTO, ValidGroups.Insert.class);
        resourceAuthenticationDetailService.saveOrUpdate(resourceAuthenticationDetailDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新资源认证明细")
    public ResponseEntity<Results<Void>> update(@RequestBody ResourceAuthenticationDetailDTO resourceAuthenticationDetailDTO) {
        validObject(resourceAuthenticationDetailDTO, ValidGroups.Update.class);
        resourceAuthenticationDetailService.saveOrUpdate(resourceAuthenticationDetailDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除资源认证明细")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        resourceAuthenticationDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出资源认证明细")
    @PostMapping("/export")
    public void export(@RequestBody ResourceAuthenticationDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        resourceAuthenticationDetailService.export(query, response);
    }

    @ApiOperation(value = "导入资源认证明细")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = resourceAuthenticationDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}