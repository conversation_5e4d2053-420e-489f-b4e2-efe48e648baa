package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.dto.RetentionDeductionDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.RetentionDeductionQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.RetentionDeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 留埚率扣减相关操作控制层
 *
 * <AUTHOR> 2024-07-19 10:36:38
 */
@RequestMapping(value = "/retention-deduction")
@RestController
@Api(value = "retentionDeduction", tags = "留埚率扣减相关操作控制层")
public class RetentionDeductionController extends BaseController {
    private final RetentionDeductionService retentionDeductionService;

    public RetentionDeductionController(RetentionDeductionService retentionDeductionService) {
        this.retentionDeductionService = retentionDeductionService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "留埚率扣减分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody RetentionDeductionQuery query) {
        return Results.createSuccessRes(retentionDeductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "留埚率扣减详情")
    public ResponseEntity<Results<RetentionDeductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(retentionDeductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增留埚率扣减")
    public ResponseEntity<Results<RetentionDeductionDTO>> insert(@RequestBody RetentionDeductionDTO retentionDeductionDTO) {
        validObject(retentionDeductionDTO, ValidGroups.Insert.class);
        retentionDeductionService.saveOrUpdate(retentionDeductionDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新留埚率扣减")
    public ResponseEntity<Results<RetentionDeductionDTO>> update(@RequestBody RetentionDeductionDTO retentionDeductionDTO) {
        validObject(retentionDeductionDTO, ValidGroups.Update.class);
        retentionDeductionService.saveOrUpdate(retentionDeductionDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除留埚率扣减")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        retentionDeductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出留埚率扣减")
    @PostMapping("/export")
    public void export(@RequestBody RetentionDeductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        retentionDeductionService.export(query, response);
    }

    @ApiOperation(value = "导入留埚率扣减")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile) {
        ImportResultExDTO importResultDTO = retentionDeductionService.importData(multipartFile);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号-三个数据分类汇总")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(retentionDeductionService.queryVersions(versionQuery));
    }

    @PostMapping("/type-versions")
    @ApiOperation(value = "查询版本号-三个数据分类对象")
    public ResponseEntity<Results<Map<Long,List<String>>>> queryDateTypeVersions() {
        return Results.createSuccessRes(retentionDeductionService.queryDateTypeVersions());
    }
}
