package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.RetentionDeductionDetailDTO;
import com.jinkosolar.scp.mps.domain.query.RetentionDeductionDetailQuery;
import com.jinkosolar.scp.mps.service.RetentionDeductionDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 留埚率扣减详情相关操作控制层
 * 
 * <AUTHOR> 2024-07-19 10:37:47
 */
@RequestMapping(value = "/retention-deduction-detail")
@RestController
@Api(value = "retentionDeductionDetail", tags = "留埚率扣减详情相关操作控制层")
public class RetentionDeductionDetailController extends BaseController {    
    private final RetentionDeductionDetailService retentionDeductionDetailService;

    public RetentionDeductionDetailController(RetentionDeductionDetailService retentionDeductionDetailService) {
        this.retentionDeductionDetailService = retentionDeductionDetailService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "留埚率扣减详情分页查询")
    public ResponseEntity<Results<Page<RetentionDeductionDetailDTO>>> page(@RequestBody RetentionDeductionDetailQuery query) {
        return Results.createSuccessRes(retentionDeductionDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "留埚率扣减详情详情")
    public ResponseEntity<Results<RetentionDeductionDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(retentionDeductionDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增留埚率扣减详情")
    public ResponseEntity<Results<RetentionDeductionDetailDTO>> insert(@RequestBody RetentionDeductionDetailDTO retentionDeductionDetailDTO) {
        validObject(retentionDeductionDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(retentionDeductionDetailService.saveOrUpdate(retentionDeductionDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新留埚率扣减详情")
    public ResponseEntity<Results<RetentionDeductionDetailDTO>> update(@RequestBody RetentionDeductionDetailDTO retentionDeductionDetailDTO) {
        validObject(retentionDeductionDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(retentionDeductionDetailService.saveOrUpdate(retentionDeductionDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除留埚率扣减详情")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        retentionDeductionDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出留埚率扣减详情")
    @PostMapping("/export")
    public void export(@RequestBody RetentionDeductionDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        retentionDeductionDetailService.export(query, response);
    }

    @ApiOperation(value = "导入留埚率扣减详情")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = retentionDeductionDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}
