package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.RiskSlackInventoryDTO;
import com.jinkosolar.scp.mps.domain.query.RiskSlackInventoryQuery;
import com.jinkosolar.scp.mps.service.RiskSlackInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/risk-slack-inventory")
@Api(value = "risk-slack-inventory", tags = "呆滞风险库存")
public class RiskSlackInventoryController extends BaseController {

    @Autowired
    RiskSlackInventoryService riskSlackInventoryService;

    @PostMapping("/page")
    @ApiOperation(value = "列表")
    public ResponseEntity<Results<Page<RiskSlackInventoryDTO>>> listRiskSlackInventory(@RequestBody RiskSlackInventoryQuery riskSlackInventoryQuery) {
        Page<RiskSlackInventoryDTO> page = riskSlackInventoryService.listRiskSlackInventory(riskSlackInventoryQuery);
        return Results.createSuccessRes(page);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Boolean>> batchDelete(@RequestBody IdsDTO idsDTO) {
        riskSlackInventoryService.deleteRiskSlackInventory(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes(Boolean.TRUE);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody RiskSlackInventoryQuery riskSlackInventoryQuery, HttpServletResponse response) {
        riskSlackInventoryQuery.setPageNumber(1);
        riskSlackInventoryQuery.setPageSize(GlobalConstant.max_page_size);
        riskSlackInventoryService.exportRiskSlackInventoryData(riskSlackInventoryQuery, response);
    }

    /**
     * 导入数据
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入数据")
    public ResponseEntity<Results<Boolean>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws Exception {
        riskSlackInventoryService.importData(file, excelPara);
        return Results.createSuccessRes(Boolean.TRUE);
    }



}
