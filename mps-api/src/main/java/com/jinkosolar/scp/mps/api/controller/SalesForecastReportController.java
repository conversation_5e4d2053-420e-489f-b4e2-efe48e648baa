package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SalesForecastReportBaseDTO;
import com.jinkosolar.scp.mps.domain.dto.SalesForecastReportDTO;
import com.jinkosolar.scp.mps.domain.query.SalesForecastReportQuery;
import com.jinkosolar.scp.mps.service.SalesForecastReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售预测报表相关操作控制层
 * 
 * <AUTHOR> 2024-11-05 13:12:48
 */
@RequestMapping(value = "/sales-forecast-report")
@RestController
@Api(value = "salesForecastReport", tags = "销售预测报表相关操作控制层")
@RequiredArgsConstructor  
public class SalesForecastReportController extends BaseController {    
    private final SalesForecastReportService salesForecastReportService; 

    @PostMapping("/page")
    @ApiOperation(value = "销售预测报表分页查询")
    public ResponseEntity<Results<Page<SalesForecastReportDTO>>> page(@RequestBody SalesForecastReportQuery query) {
        return Results.createSuccessRes(salesForecastReportService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "销售预测报表详情")
    public ResponseEntity<Results<SalesForecastReportDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(salesForecastReportService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增销售预测报表")
    public ResponseEntity<Results<Void>> insert(@RequestBody SalesForecastReportDTO salesForecastReportDTO) {
        validObject(salesForecastReportDTO, ValidGroups.Insert.class);
        salesForecastReportService.saveOrUpdate(salesForecastReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新销售预测报表")
    public ResponseEntity<Results<Void>> update(@RequestBody SalesForecastReportDTO salesForecastReportDTO) {
        validObject(salesForecastReportDTO, ValidGroups.Update.class);
        salesForecastReportService.saveOrUpdate(salesForecastReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除销售预测报表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        salesForecastReportService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出销售预测报表")
    @PostMapping("/export")
    public void export(@RequestBody SalesForecastReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        salesForecastReportService.export(query, response);
    }

    @ApiOperation(value = "导入销售预测报表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = salesForecastReportService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @ApiOperation(value = "销售预测需求计算")
    @PostMapping("/calculate")
    public ResponseEntity<Results<Void>> calculate(@RequestBody SalesForecastReportQuery query) {
        salesForecastReportService.calculate(query);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "销售预测需求匹配（尺寸）")
    @PostMapping("/salesForecastSizeReport")
    public ResponseEntity<Results<List<SalesForecastReportBaseDTO>>> salesForecastSizeReport(@RequestBody SalesForecastReportQuery query) {
        return Results.createSuccessRes(salesForecastReportService.querySalesForecastSizeReportList(query));
    }

    @ApiOperation(value = "销售预测需求匹配（尺寸）导出")
    @PostMapping("/salesForecastSizeReportExport")
    public void salesForecastSizeReportExport(@RequestBody SalesForecastReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        salesForecastReportService.salesForecastSizeReportExport(query, response);
    }

    @ApiOperation(value = "销售预测需求匹配（版型）")
    @PostMapping("/salesForecastModuleReport")
    public ResponseEntity<Results<List<SalesForecastReportBaseDTO>>> salesForecastModuleReport(@RequestBody SalesForecastReportQuery query) {
        return Results.createSuccessRes(salesForecastReportService.salesForecastModuleReport(query));
    }

    @ApiOperation(value = "销售预测需求匹配（版型）导出")
    @PostMapping("/salesForecastModuleReportExport")
    public void salesForecastModuleReportExport(@RequestBody SalesForecastReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        salesForecastReportService.salesForecastModuleReportExport(query, response);
    }

}