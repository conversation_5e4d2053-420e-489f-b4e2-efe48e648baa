package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SalesForecastReportPlanDTO;
import com.jinkosolar.scp.mps.domain.query.SalesForecastReportPlanQuery;
import com.jinkosolar.scp.mps.service.SalesForecastReportPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 中长期_销售预测需求匹配_定线规划表相关操作控制层
 * 
 * <AUTHOR> 2024-11-20 14:53:03
 */
@RequestMapping(value = "/sales-forecast-report-plan")
@RestController
@Api(value = "salesForecastReportPlan", tags = "中长期_销售预测需求匹配_定线规划表相关操作控制层")
@RequiredArgsConstructor  
public class SalesForecastReportPlanController extends BaseController {    
    private final SalesForecastReportPlanService salesForecastReportPlanService; 


}