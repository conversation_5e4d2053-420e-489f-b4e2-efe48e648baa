package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.BatteryDeliveryPlanDayDTO;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.dto.SelfBatteryAllotPlanDTO;
import com.jinkosolar.scp.mps.domain.query.SelfBatteryAllotPlanQuery;
import com.jinkosolar.scp.mps.service.SelfBatteryAllotPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自产电池调拨计划相关操作控制层
 * 
 * <AUTHOR> 2024-08-22 14:46:34
 */
@RequestMapping(value = "/self-battery-allot-plan")
@RestController
@Api(value = "selfBatteryAllotPlan", tags = "自产电池调拨计划相关操作控制层")
@RequiredArgsConstructor  
public class SelfBatteryAllotPlanController extends BaseController {    
    private final SelfBatteryAllotPlanService selfBatteryAllotPlanService; 

    @PostMapping("/page")
    @ApiOperation(value = "自产电池调拨计划分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody SelfBatteryAllotPlanQuery query) {
        return Results.createSuccessRes(selfBatteryAllotPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "自产电池调拨计划详情")
    public ResponseEntity<Results<SelfBatteryAllotPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(selfBatteryAllotPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增自产电池调拨计划")
    public ResponseEntity<Results<Void>> insert(@RequestBody SelfBatteryAllotPlanDTO selfBatteryAllotPlanDTO) {
        validObject(selfBatteryAllotPlanDTO, ValidGroups.Insert.class);
        selfBatteryAllotPlanService.saveOrUpdate(selfBatteryAllotPlanDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新自产电池调拨计划")
    public ResponseEntity<Results<Void>> update(@RequestBody SelfBatteryAllotPlanDTO selfBatteryAllotPlanDTO) {
        validObject(selfBatteryAllotPlanDTO, ValidGroups.Update.class);
        selfBatteryAllotPlanService.saveOrUpdate(selfBatteryAllotPlanDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除自产电池调拨计划")
    public ResponseEntity<Results<Void>> delete(@RequestBody  List<SelfBatteryAllotPlanDTO> selfBatteryAllotPlanDTOList) {
        selfBatteryAllotPlanService.deleteByGroupCondition(selfBatteryAllotPlanDTOList);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出自产电池调拨计划")
    @PostMapping("/export")
    public void export(@RequestBody SelfBatteryAllotPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        selfBatteryAllotPlanService.export(query, response);
    }

    @ApiOperation(value = "导入自产电池调拨计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = selfBatteryAllotPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}