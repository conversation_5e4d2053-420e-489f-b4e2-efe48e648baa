package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SiliconProductionRatioDTO;
import com.jinkosolar.scp.mps.domain.query.SiliconProductionRatioQuery;
import com.jinkosolar.scp.mps.service.SiliconProductionRatioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 硅料月投产比例相关操作控制层
 * 
 * <AUTHOR> 2024-11-08 15:11:37
 */
@RequestMapping(value = "/silicon-production-ratio")
@RestController
@Api(value = "siliconProductionRatio", tags = "硅料月投产比例相关操作控制层")
@RequiredArgsConstructor  
public class SiliconProductionRatioController extends BaseController {    
    private final SiliconProductionRatioService siliconProductionRatioService; 

    @PostMapping("/page")
    @ApiOperation(value = "硅料月投产比例分页查询")
    public ResponseEntity<Results<Page<SiliconProductionRatioDTO>>> page(@RequestBody SiliconProductionRatioQuery query) {
        return Results.createSuccessRes(siliconProductionRatioService.page(query));
    }

    @ApiOperation(value = "导出硅料月投产比例")
    @PostMapping("/export")
    public void export(@RequestBody SiliconProductionRatioQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        siliconProductionRatioService.export(query, response);
    }

    @ApiOperation(value = "导入硅料月投产比例")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = siliconProductionRatioService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "硅料月投产比例详情")
    public ResponseEntity<Results<SiliconProductionRatioDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(siliconProductionRatioService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增硅料月投产比例")
    public ResponseEntity<Results<Void>> insert(@RequestBody SiliconProductionRatioDTO siliconProductionRatioDTO) {
        validObject(siliconProductionRatioDTO, ValidGroups.Insert.class);
        siliconProductionRatioService.saveOrUpdate(siliconProductionRatioDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新硅料月投产比例")
    public ResponseEntity<Results<Void>> update(@RequestBody SiliconProductionRatioDTO siliconProductionRatioDTO) {
        validObject(siliconProductionRatioDTO, ValidGroups.Update.class);
        siliconProductionRatioService.saveOrUpdate(siliconProductionRatioDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除硅料月投产比例")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        siliconProductionRatioService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(siliconProductionRatioService.queryVersions(dataType.getId()));
    }
}