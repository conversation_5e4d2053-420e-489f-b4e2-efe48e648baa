package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.SiliconeColorContinuityDTO;
import com.jinkosolar.scp.mps.domain.query.SiliconeColorContinuityQuery;
import com.jinkosolar.scp.mps.service.SiliconeColorContinuityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件定线规划表相关操作控制层
 *
 * <AUTHOR> 2024-04-19 10:12:03
 */
@RequestMapping(value = "/silicone-color-continuity")
@RestController
@Api(value = "siliconeColorContinuity", tags = "硅胶颜色连续性表相关操作控制层")
public class SiliconeColorContinuityController extends BaseController {
    @Autowired
    private SiliconeColorContinuityService siliconeColorContinuityService;

    @PostMapping("/page")
    @ApiOperation(value = "硅胶颜色连续性表分页查询")
    public ResponseEntity<Results<Page<SiliconeColorContinuityDTO>>> page(@RequestBody SiliconeColorContinuityQuery query) {
        return Results.createSuccessRes(siliconeColorContinuityService.page(query));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增硅胶颜色连续性表")
    public ResponseEntity<Results<SiliconeColorContinuityDTO>> insert(@RequestBody SiliconeColorContinuityDTO siliconeColorContinuityDTO) {
        validObject(siliconeColorContinuityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(siliconeColorContinuityService.saveOrUpdate(siliconeColorContinuityDTO));
    }

    @ApiOperation(value = "导出硅胶颜色连续性表")
    @PostMapping("/export")
    public void export(@RequestBody SiliconeColorContinuityQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        siliconeColorContinuityService.export(query, response);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件定线规划表")
    public ResponseEntity<Results<SiliconeColorContinuityDTO>> update(@RequestBody SiliconeColorContinuityDTO siliconeColorContinuityDTO) {
        validObject(siliconeColorContinuityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(siliconeColorContinuityService.saveOrUpdate(siliconeColorContinuityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件定线规划表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        siliconeColorContinuityService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导入硅胶颜色连续性表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {

        ImportResultDTO importResultDTO = siliconeColorContinuityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}