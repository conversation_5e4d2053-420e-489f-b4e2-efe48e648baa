package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.CommonOption;
import com.jinkosolar.scp.mps.domain.dto.SingleModuleWattageDTO;
import com.jinkosolar.scp.mps.domain.dto.VendorEfficiencyDTO;
import com.jinkosolar.scp.mps.domain.query.SingleModuleWattageQuery;
import com.jinkosolar.scp.mps.service.SingleModuleWattageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 组件单块瓦数表相关操作控制层
 *
 * <AUTHOR> 2024-07-22 14:35:35
 */
@RequestMapping(value = "/single-module-wattage")
@RestController
@Api(value = "singleModuleWattage", tags = "组件单块瓦数表相关操作控制层")
public class SingleModuleWattageController extends BaseController {
    private final SingleModuleWattageService singleModuleWattageService;

    public SingleModuleWattageController(SingleModuleWattageService singleModuleWattageService) {
        this.singleModuleWattageService = singleModuleWattageService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "组件单块瓦数表分页查询")
    public ResponseEntity<Results<Page<SingleModuleWattageDTO>>> page(@RequestBody SingleModuleWattageQuery query) {
        return Results.createSuccessRes(singleModuleWattageService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "组件单块瓦数表详情")
    public ResponseEntity<Results<SingleModuleWattageDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(singleModuleWattageService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增组件单块瓦数表")
    public ResponseEntity<Results<SingleModuleWattageDTO>> insert(@RequestBody SingleModuleWattageDTO singleModuleWattageDTO) {
        validObject(singleModuleWattageDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(singleModuleWattageService.saveOrUpdate(singleModuleWattageDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新组件单块瓦数表")
    public ResponseEntity<Results<SingleModuleWattageDTO>> update(@RequestBody SingleModuleWattageDTO singleModuleWattageDTO) {
        validObject(singleModuleWattageDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(singleModuleWattageService.saveOrUpdate(singleModuleWattageDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除组件单块瓦数表")
    public ResponseEntity<Results<Object>> delete(@RequestBody List<SingleModuleWattageDTO> singleModuleWattageDTO) {
        validObject(singleModuleWattageDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(singleModuleWattageService.deleteByDto((List<SingleModuleWattageDTO>) singleModuleWattageDTO));
    }

    @ApiOperation(value = "导出组件单块瓦数表")
    @PostMapping("/export")
    public void export(@RequestBody SingleModuleWattageQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        singleModuleWattageService.export(query, response);
    }

    @ApiOperation(value = "导入组件单块瓦数表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = singleModuleWattageService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody SingleModuleWattageQuery query) {
        return Results.createSuccessRes(singleModuleWattageService.findVersion(query));
    }

    @PostMapping("/years")
    @ApiOperation(value = "查询年份")
    public ResponseEntity<Results<List<String>>> years() {
        return Results.createSuccessRes(singleModuleWattageService.findYear());
    }

    @PostMapping("/planVersion")
    @ApiOperation(value = "查询计划版型")
    public ResponseEntity<Results<List<CommonOption>>> planVersions() {
    return Results.createSuccessRes(singleModuleWattageService.findPlanVersion());
    }

    @PostMapping("/findAllByAreaListAndYearList")
    @ApiOperation(value = "查询瓦数-区域集合和年份集合")
    public ResponseEntity<Results<List<SingleModuleWattageDTO>>> findAllByAreaListAndYearList(@RequestBody SingleModuleWattageQuery query) {
        return Results.createSuccessRes(singleModuleWattageService.findAllByAreaListAndYearList(query));
    }


}