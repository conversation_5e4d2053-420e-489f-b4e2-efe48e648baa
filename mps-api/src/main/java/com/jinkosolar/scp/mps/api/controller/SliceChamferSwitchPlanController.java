package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SliceChamferSwitchPlanDTO;
import com.jinkosolar.scp.mps.domain.query.SliceChamferSwitchPlanQuery;
import com.jinkosolar.scp.mps.service.SliceChamferSwitchPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶倒角切换计划相关操作控制层
 * 
 * <AUTHOR> 2024-11-15 11:01:07
 */
@RequestMapping(value = "/slice-chamfer-switch-plan")
@RestController
@Api(value = "sliceChamferSwitchPlan", tags = "拉晶倒角切换计划相关操作控制层")
@RequiredArgsConstructor  
public class SliceChamferSwitchPlanController extends BaseController {    
    private final SliceChamferSwitchPlanService sliceChamferSwitchPlanService; 

    @PostMapping("/page")
    @ApiOperation(value = "拉晶倒角切换计划分页查询")
    public ResponseEntity<Results<Page<SliceChamferSwitchPlanDTO>>> page(@RequestBody SliceChamferSwitchPlanQuery query) {
        return Results.createSuccessRes(sliceChamferSwitchPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶倒角切换计划详情")
    public ResponseEntity<Results<SliceChamferSwitchPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(sliceChamferSwitchPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增拉晶倒角切换计划")
    public ResponseEntity<Results<Void>> save(@RequestBody SliceChamferSwitchPlanDTO sliceChamferSwitchPlanDTO) {
        validObject(sliceChamferSwitchPlanDTO, ValidGroups.Insert.class);
        sliceChamferSwitchPlanService.saveOrUpdate(sliceChamferSwitchPlanDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶倒角切换计划")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        sliceChamferSwitchPlanService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶倒角切换计划")
    @PostMapping("/export")
    public void export(@RequestBody SliceChamferSwitchPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        sliceChamferSwitchPlanService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶倒角切换计划")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = sliceChamferSwitchPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //8760 倒角切换计划----导入数据后同步导APS数据库
            sliceChamferSwitchPlanService.syncToAps();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "拉晶倒角切换计划版本查询")
    public ResponseEntity<Results<List<String>>> versionList(@RequestBody SliceChamferSwitchPlanQuery query) {
        return Results.createSuccessRes(sliceChamferSwitchPlanService.versionList(query));
    }

    @PostMapping("/sync")
    @ApiOperation(value = "同步拉晶倒角切换计划到aps")
    public ResponseEntity<Results<Boolean>> syncToAps() {
        return Results.createSuccessRes(sliceChamferSwitchPlanService.syncToAps());
    }
}