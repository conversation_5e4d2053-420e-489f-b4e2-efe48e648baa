package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SliceOpeningInventoryOverseaDTO;
import com.jinkosolar.scp.mps.domain.query.SliceOpeningInventoryOverseaQuery;
import com.jinkosolar.scp.mps.service.SliceOpeningInventoryOverseaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 切片海外期初库存相关操作控制层
 *
 * <AUTHOR> 2024-11-07 16:58:21
 */
@RequestMapping(value = "/slice-opening-inventory-oversea")
@RestController
@Api(value = "sliceOpeningInventoryOversea", tags = "切片海外期初库存相关操作控制层")
@RequiredArgsConstructor
public class SliceOpeningInventoryOverseaController extends BaseController {
    private final SliceOpeningInventoryOverseaService sliceOpeningInventoryOverseaService;

    @PostMapping("/page")
    @ApiOperation(value = "切片海外期初库存分页查询")
    public ResponseEntity<Results<Page<SliceOpeningInventoryOverseaDTO>>> page(@RequestBody SliceOpeningInventoryOverseaQuery query) {
        return Results.createSuccessRes(sliceOpeningInventoryOverseaService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "切片海外期初库存详情")
    public ResponseEntity<Results<SliceOpeningInventoryOverseaDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(sliceOpeningInventoryOverseaService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增切片海外期初库存")
    public ResponseEntity<Results<Void>> save(@RequestBody SliceOpeningInventoryOverseaDTO sliceOpeningInventoryOverseaDTO) {
        validObject(sliceOpeningInventoryOverseaDTO, ValidGroups.Insert.class);
        sliceOpeningInventoryOverseaService.saveOrUpdate(sliceOpeningInventoryOverseaDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除切片海外期初库存")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        sliceOpeningInventoryOverseaService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出切片海外期初库存")
    @PostMapping("/export")
    public void export(@RequestBody SliceOpeningInventoryOverseaQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        sliceOpeningInventoryOverseaService.export(query, response);
    }

    @ApiOperation(value = "导入切片海外期初库存")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = sliceOpeningInventoryOverseaService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/calculate")
    @ApiOperation(value = "计算切片海外期初库存")
    public ResponseEntity<Results<Boolean>> calculate() {
        sliceOpeningInventoryOverseaService.calculate();
        return Results.createSuccessRes();
    }

    @PostMapping("/calculate/bar")
    @ApiOperation(value = "计算方棒海外期初库存")
    public ResponseEntity<Results<Boolean>> calculateBar() {
        sliceOpeningInventoryOverseaService.calculateBar();
        return Results.createSuccessRes(true);
    }

    @PostMapping("/flush")
    @ApiOperation(value = "刷新切片和方棒海外期初库存")
    public ResponseEntity<Results<Boolean>> flush() {
        sliceOpeningInventoryOverseaService.calculate();
        sliceOpeningInventoryOverseaService.calculateBar();
        return Results.createSuccessRes(true);
    }
}
