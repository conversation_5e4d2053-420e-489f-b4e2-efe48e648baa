package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.PageColumnDto;
import com.jinkosolar.scp.mps.domain.query.SliceProductionPlanReportQuery;
import com.jinkosolar.scp.mps.service.SliceProductionPlanReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 切片产量跟进报表相关操作控制层
 *
 * <AUTHOR> 2024-08-22 14:46:34
 */
@RequestMapping(value = "/slice-production-plan-report")
@RestController
@Api(value = "sliceProductionPlanReport", tags = "自产电池调拨计划相关操作控制层")
@RequiredArgsConstructor
public class SliceProductionPlanReportController extends BaseController {
    private final SliceProductionPlanReportService sliceProductionPlanReportService;

    @PostMapping("/page")
    @ApiOperation(value = "切片产量跟进报表分页查询")
    public ResponseEntity<Results<PageColumnDto>> page(@RequestBody SliceProductionPlanReportQuery query) {
        return Results.createSuccessRes(sliceProductionPlanReportService.page(query));
    }

    @ApiOperation(value = "导出切片产量跟进报表")
    @PostMapping("/export")
    public void export(@RequestBody SliceProductionPlanReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        sliceProductionPlanReportService.export(query, response);
    }

}