package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncSlicingYieldRateRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncSlicingYieldRateResponse;
import com.jinkosolar.scp.jip.api.dto.mes.SyncslicingLineInventoryRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncslicingLineInventoryResponse;
import com.jinkosolar.scp.jip.api.service.SyncSlicingLineInventoryService;
import com.jinkosolar.scp.mps.domain.dto.SlicingLineInventoryDTO;
import com.jinkosolar.scp.mps.domain.query.SlicingLineInventoryQuery;
import com.jinkosolar.scp.mps.service.SlicingLineInventoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 切片线边在制相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 15:26:40
 */
@RequestMapping(value = "/slicing-line-inventory")
@RestController
@Api(value = "slicingLineInventory", tags = "切片线边在制相关操作控制层")
public class SlicingLineInventoryController extends BaseController {
    @Autowired
    private SlicingLineInventoryService slicingLineInventoryService;
    @Autowired
    private SyncSlicingLineInventoryService syncSlicingLineInventoryService;

    @PostMapping("/page")
    @ApiOperation(value = "切片线边在制分页查询")
    public ResponseEntity<Results<Page<SlicingLineInventoryDTO>>> page(@RequestBody SlicingLineInventoryQuery query) {
        return Results.createSuccessRes(slicingLineInventoryService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "切片线边在制详情")
    public ResponseEntity<Results<SlicingLineInventoryDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(slicingLineInventoryService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增切片线边在制")
    public ResponseEntity<Results<SlicingLineInventoryDTO>> insert(@RequestBody SlicingLineInventoryDTO slicingLineInventoryDTO) {
        validObject(slicingLineInventoryDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(slicingLineInventoryService.saveOrUpdate(slicingLineInventoryDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新切片线边在制")
    public ResponseEntity<Results<SlicingLineInventoryDTO>> update(@RequestBody SlicingLineInventoryDTO slicingLineInventoryDTO) {
        validObject(slicingLineInventoryDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(slicingLineInventoryService.saveOrUpdate(slicingLineInventoryDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除切片线边在制")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        slicingLineInventoryService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出切片线边在制")
    @PostMapping("/export")
    public void export(@RequestBody SlicingLineInventoryQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        slicingLineInventoryService.export(query, response);
    }

    @ApiOperation(value = "导入切片线边在制")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = slicingLineInventoryService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncSlicingLineInventory")
    @ApiOperation(value = "切片在制")
    public JipResponseData syncSlicingLineInventory() {

        slicingLineInventoryService.sync();
        return JipResponseData.success();
    }
    @PostMapping("/queryListBySiteList")
    @ApiOperation(value = "dp需求获取切片自制")
    public ResponseEntity<Results<List<SlicingLineInventoryDTO>>> queryListBySiteList(@RequestBody SlicingLineInventoryQuery query) {
        return Results.createSuccessRes(slicingLineInventoryService.queryListBySiteList(query));
    }
}