package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncCurrentWorkshopStatusRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncCurrentWorkshopStatusResponse;
import com.jinkosolar.scp.jip.api.dto.mes.SyncSlicingYieldRateRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncSlicingYieldRateResponse;
import com.jinkosolar.scp.jip.api.service.SyncSlicingYieldRateService;
import com.jinkosolar.scp.mps.domain.dto.SlicingYieldRateDTO;
import com.jinkosolar.scp.mps.domain.query.SlicingYieldRateQuery;
import com.jinkosolar.scp.mps.service.SlicingYieldRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 切片良率相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 15:26:39
 */
@RequestMapping(value = "/slicing-yield-rate")
@RestController
@Api(value = "slicingYieldRate", tags = "切片良率相关操作控制层")
public class SlicingYieldRateController extends BaseController {
    @Autowired
    private SlicingYieldRateService slicingYieldRateService;
    @Autowired
    private SyncSlicingYieldRateService syncSlicingYieldRateService;

    @PostMapping("/page")
    @ApiOperation(value = "切片良率分页查询")
    public ResponseEntity<Results<Page<SlicingYieldRateDTO>>> page(@RequestBody SlicingYieldRateQuery query) {
        return Results.createSuccessRes(slicingYieldRateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "切片良率详情")
    public ResponseEntity<Results<SlicingYieldRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(slicingYieldRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增切片良率")
    public ResponseEntity<Results<SlicingYieldRateDTO>> insert(@RequestBody SlicingYieldRateDTO slicingYieldRateDTO) {
        validObject(slicingYieldRateDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(slicingYieldRateService.saveOrUpdate(slicingYieldRateDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新切片良率")
    public ResponseEntity<Results<SlicingYieldRateDTO>> update(@RequestBody SlicingYieldRateDTO slicingYieldRateDTO) {
        validObject(slicingYieldRateDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(slicingYieldRateService.saveOrUpdate(slicingYieldRateDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除切片良率")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        slicingYieldRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出切片良率")
    @PostMapping("/export")
    public void export(@RequestBody SlicingYieldRateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        slicingYieldRateService.export(query, response);
    }

    @ApiOperation(value = "导入切片良率")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = slicingYieldRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncSlicingYieldRate")
    @ApiOperation(value = "切片良率")
    public JipResponseData syncSlicingYieldRate() {

        slicingYieldRateService.sync();
        return JipResponseData.success();
    }
}