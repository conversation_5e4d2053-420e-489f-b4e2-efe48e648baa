package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.SpecialMaterialRequirementDTO;
import com.jinkosolar.scp.mps.domain.query.SpecialMaterialRequirementQuery;
import com.jinkosolar.scp.mps.service.SpecialMaterialRequirementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/special-material-requirement")
@Api(value = "special-material-requirement", tags = "特殊材料要求操作")
public class SpecialMaterialRequirementController extends BaseController {

    @Autowired
    private SpecialMaterialRequirementService specialMaterialRequirementService;

    @PostMapping("/createSpecialMaterialRequirementData")
    @ApiOperation(value = "生成数据")
    public ResponseEntity<Results<Boolean>> createSpecialMaterialRequirementData(@RequestBody SpecialMaterialRequirementQuery specialMaterialRequirementQuery) {
        specialMaterialRequirementService.createSpecialMaterialRequirementData(specialMaterialRequirementQuery);
        return Results.createSuccessRes(Boolean.TRUE);
    }

    @PostMapping("/page")
    @ApiOperation(value = "列表")
    public ResponseEntity<Results<Page<SpecialMaterialRequirementDTO>>> listSpecialMaterialRequirement(@RequestBody SpecialMaterialRequirementQuery specialMaterialRequirementQuery) {
        Page<SpecialMaterialRequirementDTO> specialMaterialRequirementDTOS = specialMaterialRequirementService.listSpecialMaterialRequirement(specialMaterialRequirementQuery);
        return Results.createSuccessRes(specialMaterialRequirementDTOS);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Boolean>> batchDelete(@RequestBody IdsDTO idsDTO) {
        specialMaterialRequirementService.deleteSpecialMaterialRequirement(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes(Boolean.TRUE);
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SpecialMaterialRequirementQuery specialMaterialRequirementQuery, HttpServletResponse response) {
        specialMaterialRequirementQuery.setPageNumber(1);
        specialMaterialRequirementQuery.setPageSize(GlobalConstant.max_page_size);
        specialMaterialRequirementService.exportSpecialMaterialRequirementData(specialMaterialRequirementQuery, response);
    }

    /**
     * 导入数据
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "导入数据")
    public ResponseEntity<Results<Boolean>> importData(@RequestPart(value = "file") MultipartFile file, @RequestPart(value = "excelPara") ExcelPara excelPara) throws Exception {
        specialMaterialRequirementService.importData(file, excelPara);
        return Results.createSuccessRes(Boolean.TRUE);
    }

}
