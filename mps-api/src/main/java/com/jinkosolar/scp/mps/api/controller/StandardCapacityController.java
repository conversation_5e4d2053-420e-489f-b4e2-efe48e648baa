package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StandardCapacityDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.StandardCapacityQuery;
import com.jinkosolar.scp.mps.domain.query.VersionQuery;
import com.jinkosolar.scp.mps.service.StandardCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标准产能相关操作控制层
 * 
 * <AUTHOR> 2024-07-04 15:25:18
 */
@RequestMapping(value = "/standard-capacity")
@RestController
@Api(value = "standardCapacity", tags = "标准产能相关操作控制层")
public class StandardCapacityController extends BaseController {
    @Autowired
    private StandardCapacityService standardCapacityService;

    @PostMapping("/page")
    @ApiOperation(value = "标准产能分页查询")
    public ResponseEntity<Results<Page<StandardCapacityDTO>>> page(@RequestBody StandardCapacityQuery query) {
        return Results.createSuccessRes(standardCapacityService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "标准产能详情")
    public ResponseEntity<Results<StandardCapacityDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(standardCapacityService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增标准产能")
    public ResponseEntity<Results<StandardCapacityDTO>> insert(@RequestBody StandardCapacityDTO standardCapacityDTO) {
        validObject(standardCapacityDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(standardCapacityService.saveOrUpdate(standardCapacityDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新标准产能")
    public ResponseEntity<Results<StandardCapacityDTO>> update(@RequestBody StandardCapacityDTO standardCapacityDTO) {
        validObject(standardCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(standardCapacityService.saveOrUpdate(standardCapacityDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除标准产能")
    public ResponseEntity<Results<Object>> delete(@RequestBody List<StandardCapacityDTO> standardCapacityDTO) {
        validObject(standardCapacityDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(standardCapacityService.deleteByDto((List<StandardCapacityDTO>) standardCapacityDTO));
    }

    @ApiOperation(value = "导出标准产能")
    @PostMapping("/export")
    public void export(@RequestBody StandardCapacityQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        standardCapacityService.export(query, response);
    }

    @ApiOperation(value = "导入标准产能")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                     @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = standardCapacityService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions(@RequestBody VersionQuery versionQuery) {
        return Results.createSuccessRes(standardCapacityService.findVersion(versionQuery));
    }
}