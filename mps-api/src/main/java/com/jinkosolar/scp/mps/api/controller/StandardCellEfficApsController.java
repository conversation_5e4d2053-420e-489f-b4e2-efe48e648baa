package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.dpf.common.response.RestResponse;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StandardCellEfficApsDTO;
import com.jinkosolar.scp.mps.domain.query.StandardCellEfficApsQuery;
import com.jinkosolar.scp.mps.service.StandardCellEfficApsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标准电池效率待计算表Aps相关操作控制层
 * 
 * <AUTHOR> 2024-05-31 14:02:15
 */
@RequestMapping(value = "/standard-cell-effic-aps")
@RestController
@Api(value = "standardCellEfficAps", tags = "标准电池效率待计算表Aps相关操作控制层")
public class StandardCellEfficApsController extends BaseController {
    @Autowired
    private StandardCellEfficApsService standardCellEfficApsService;

    @PostMapping("/page")
    @ApiOperation(value = "标准电池效率待计算表Aps分页查询")
    public ResponseEntity<Results<Page<StandardCellEfficApsDTO>>> page(@RequestBody StandardCellEfficApsQuery query) {
        return Results.createSuccessRes(standardCellEfficApsService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "标准电池效率待计算表Aps详情")
    public ResponseEntity<Results<StandardCellEfficApsDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(standardCellEfficApsService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增标准电池效率待计算表Aps")
    public ResponseEntity<Results<StandardCellEfficApsDTO>> insert(@RequestBody StandardCellEfficApsDTO standardCellEfficApsDTO) {
        validObject(standardCellEfficApsDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(standardCellEfficApsService.saveOrUpdate(standardCellEfficApsDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新标准电池效率待计算表Aps")
    public ResponseEntity<Results<StandardCellEfficApsDTO>> update(@RequestBody StandardCellEfficApsDTO standardCellEfficApsDTO) {
        validObject(standardCellEfficApsDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(standardCellEfficApsService.saveOrUpdate(standardCellEfficApsDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除标准电池效率待计算表Aps")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        standardCellEfficApsService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出标准电池效率待计算表Aps")
    @PostMapping("/export")
    public void export(@RequestBody StandardCellEfficApsQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        standardCellEfficApsService.export(query, response);
    }

    @ApiOperation(value = "导入标准电池效率待计算表Aps")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = standardCellEfficApsService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @ApiOperation(value = "接收处理APS，处理标准电池效率算逻辑")
    @PostMapping("/executeStandardCellEfficiencyData")
    public ResponseEntity<Results<Boolean>> executeStandardCellEfficiencyData(@RequestBody StandardCellEfficApsQuery query) {
        try {
            return Results.createAsprovaSuccessRes(standardCellEfficApsService.executeStandardCellEfficiencyData(query.getModelType()));
        }catch (Exception e){
            e.printStackTrace();
            return Results.createAsprovaFailRes(e.toString(), "A0001");
        }
    }
}