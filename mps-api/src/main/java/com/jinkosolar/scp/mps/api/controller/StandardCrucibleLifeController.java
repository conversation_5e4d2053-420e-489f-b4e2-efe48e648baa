package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.ReleasableResourceDTO;
import com.jinkosolar.scp.mps.domain.dto.StandardCrucibleLifeDTO;
import com.jinkosolar.scp.mps.domain.query.StandardCrucibleLifeQuery;
import com.jinkosolar.scp.mps.service.StandardCrucibleLifeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标准坩埚寿命相关操作控制层
 * 
 * <AUTHOR> 2024-07-30 19:28:18
 */
@RequestMapping(value = "/standard-crucible-life")
@RestController
@Api(value = "standardCrucibleLife", tags = "标准坩埚寿命相关操作控制层")
public class StandardCrucibleLifeController extends BaseController {    
    private final StandardCrucibleLifeService standardCrucibleLifeService;

    public StandardCrucibleLifeController(StandardCrucibleLifeService standardCrucibleLifeService) {
        this.standardCrucibleLifeService = standardCrucibleLifeService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "标准坩埚寿命分页查询")
    public ResponseEntity<Results<Page<StandardCrucibleLifeDTO>>> page(@RequestBody StandardCrucibleLifeQuery query) {
        return Results.createSuccessRes(standardCrucibleLifeService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "标准坩埚寿命详情")
    public ResponseEntity<Results<StandardCrucibleLifeDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(standardCrucibleLifeService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody StandardCrucibleLifeDTO standardCrucibleLifeDTO) {
        return Results.createSuccessRes(standardCrucibleLifeService.getVersionNumberByDataType(standardCrucibleLifeDTO));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增标准坩埚寿命")
    public ResponseEntity<Results<StandardCrucibleLifeDTO>> insert(@RequestBody StandardCrucibleLifeDTO standardCrucibleLifeDTO) {
        validObject(standardCrucibleLifeDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(standardCrucibleLifeService.saveOrUpdate(standardCrucibleLifeDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新标准坩埚寿命")
    public ResponseEntity<Results<StandardCrucibleLifeDTO>> update(@RequestBody StandardCrucibleLifeDTO standardCrucibleLifeDTO) {
        validObject(standardCrucibleLifeDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(standardCrucibleLifeService.saveOrUpdate(standardCrucibleLifeDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除标准坩埚寿命")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        standardCrucibleLifeService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出标准坩埚寿命")
    @PostMapping("/export")
    public void export(@RequestBody StandardCrucibleLifeQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        standardCrucibleLifeService.export(query, response);
    }

    @ApiOperation(value = "导入标准坩埚寿命")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = standardCrucibleLifeService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}