package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StandardPreUnitClimbDTO;
import com.jinkosolar.scp.mps.domain.query.StandardPreUnitClimbQuery;
import com.jinkosolar.scp.mps.service.StandardPreUnitClimbService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 国内老基地标准单产爬坡相关操作控制层
 *
 * <AUTHOR> 2024-10-21 19:40:18
 */
@RequestMapping(value = "/standard-pre-unit-climb")
@RestController
@Api(value = "standardPreUnitClimb", tags = "国内老基地标准单产爬坡相关操作控制层")
@RequiredArgsConstructor
public class StandardPreUnitClimbController extends BaseController {
    private final StandardPreUnitClimbService standardPreUnitClimbService;

    @PostMapping("/page")
    @ApiOperation(value = "国内老基地标准单产爬坡分页查询")
    // public ResponseEntity<Results<Page<StandardPreUnitClimbDTO>>> page(@RequestBody StandardPreUnitClimbQuery query) {
    public ResponseEntity<Results<Page<Map<String, Object>>>> page(@RequestBody StandardPreUnitClimbQuery query) {
        return Results.createSuccessRes(standardPreUnitClimbService.page(query));
    }

    @ApiOperation(value = "导出国内老基地标准单产爬坡")
    @PostMapping("/export")
    public void export(@RequestBody StandardPreUnitClimbQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        standardPreUnitClimbService.export(query, response);
    }

    @ApiOperation(value = "导入国内老基地标准单产爬坡")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = standardPreUnitClimbService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除国内老基地标准单产爬坡")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        standardPreUnitClimbService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @PostMapping("/detail")
    @ApiOperation(value = "国内老基地标准单产爬坡详情")
    public ResponseEntity<Results<StandardPreUnitClimbDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(standardPreUnitClimbService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增国内老基地标准单产爬坡")
    public ResponseEntity<Results<Void>> insert(@RequestBody StandardPreUnitClimbDTO standardPreUnitClimbDTO) {
        validObject(standardPreUnitClimbDTO, ValidGroups.Insert.class);
        standardPreUnitClimbService.saveOrUpdate(standardPreUnitClimbDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新国内老基地标准单产爬坡")
    public ResponseEntity<Results<Void>> update(@RequestBody StandardPreUnitClimbDTO standardPreUnitClimbDTO) {
        validObject(standardPreUnitClimbDTO, ValidGroups.Update.class);
        standardPreUnitClimbService.saveOrUpdate(standardPreUnitClimbDTO);
        return Results.createSuccessRes();
    }

}