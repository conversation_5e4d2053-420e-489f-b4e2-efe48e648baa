package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StockFromTransitDTO;
import com.jinkosolar.scp.mps.domain.query.StockFromTransitQuery;
import com.jinkosolar.scp.mps.service.StockFromTransitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ERP调拨在途相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 15:47:02
 */
@RequestMapping(value = "/stock-from-transit")
@RestController
@Api(value = "stockFromTransit", tags = "ERP调拨在途相关操作控制层")
public class StockFromTransitController extends BaseController {
    @Autowired
    private StockFromTransitService stockFromTransitService;

    @PostMapping("/page")
    @ApiOperation(value = "ERP调拨在途分页查询")
    public ResponseEntity<Results<Page<StockFromTransitDTO>>> page(@RequestBody StockFromTransitQuery query) {
        return Results.createSuccessRes(stockFromTransitService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "ERP调拨在途详情")
    public ResponseEntity<Results<StockFromTransitDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(stockFromTransitService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增ERP调拨在途")
    public ResponseEntity<Results<StockFromTransitDTO>> insert(@RequestBody StockFromTransitDTO stockFromTransitDTO) {
        validObject(stockFromTransitDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(stockFromTransitService.saveOrUpdate(stockFromTransitDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新ERP调拨在途")
    public ResponseEntity<Results<StockFromTransitDTO>> update(@RequestBody StockFromTransitDTO stockFromTransitDTO) {
        validObject(stockFromTransitDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(stockFromTransitService.saveOrUpdate(stockFromTransitDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除ERP调拨在途")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        stockFromTransitService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出ERP调拨在途")
    @PostMapping("/export")
    public void export(@RequestBody StockFromTransitQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        stockFromTransitService.export(query, response);
    }

    @ApiOperation(value = "导入ERP调拨在途")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = stockFromTransitService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
    @PostMapping("/queryStockFromTransit")
    @ApiOperation(value = "需求调用ERP调拨在途")
    public ResponseEntity<Results<List<StockFromTransitDTO>>> queryStockFromTransit(@RequestBody StockFromTransitQuery query) {
        return Results.createSuccessRes(stockFromTransitService.queryStockFromTransit(query));
    }
}