package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StockInTransitDTO;
import com.jinkosolar.scp.mps.domain.query.StockInTransitQuery;
import com.jinkosolar.scp.mps.service.StockInTransitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调拨在途数据相关操作控制层
 * 
 * <AUTHOR> 2024-07-09 11:29:13
 */
@RequestMapping(value = "/stock-in-transit")
@RestController
@Api(value = "stockInTransit", tags = "调拨在途数据相关操作控制层")
public class StockInTransitController extends BaseController {
    @Autowired
    private StockInTransitService stockInTransitService;

    @PostMapping("/page")
    @ApiOperation(value = "调拨在途数据分页查询")
    public ResponseEntity<Results<Page<StockInTransitDTO>>> page(@RequestBody StockInTransitQuery query) {
        return Results.createSuccessRes(stockInTransitService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "调拨在途数据详情")
    public ResponseEntity<Results<StockInTransitDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(stockInTransitService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增调拨在途数据")
    public ResponseEntity<Results<StockInTransitDTO>> insert(@RequestBody StockInTransitDTO stockInTransitDTO) {
        validObject(stockInTransitDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(stockInTransitService.saveOrUpdate(stockInTransitDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新调拨在途数据")
    public ResponseEntity<Results<StockInTransitDTO>> update(@RequestBody StockInTransitDTO stockInTransitDTO) {
        validObject(stockInTransitDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(stockInTransitService.saveOrUpdate(stockInTransitDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除调拨在途数据")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        stockInTransitService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出调拨在途数据")
    @PostMapping("/export")
    public void export(@RequestBody StockInTransitQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        stockInTransitService.export(query, response);
    }

    @ApiOperation(value = "导入调拨在途数据")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = stockInTransitService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}