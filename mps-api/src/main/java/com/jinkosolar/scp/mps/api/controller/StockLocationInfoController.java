package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StockLocationInfoDTO;
import com.jinkosolar.scp.mps.domain.query.StockLocationInfoQuery;
import com.jinkosolar.scp.mps.service.StockLocationInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存地点基本信息维护相关操作控制层
 * 
 * <AUTHOR> 2024-10-14 09:08:27
 */
@RequestMapping(value = "/stockLocationBaseInfo")
@RestController
@Api(value = "stockLocationInfo", tags = "库存地点基本信息维护相关操作控制层")
@RequiredArgsConstructor
public class StockLocationInfoController extends BaseController {

    private final StockLocationInfoService stockLocationInfoService; 

    @PostMapping("/page")
    @ApiOperation(value = "库存地点基本信息维护分页查询")
    public ResponseEntity<Results<Page<StockLocationInfoDTO>>> page(@RequestBody StockLocationInfoQuery query) {
        return Results.createSuccessRes(stockLocationInfoService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "库存地点基本信息维护详情")
    public ResponseEntity<Results<StockLocationInfoDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(stockLocationInfoService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增库存地点基本信息维护")
    public ResponseEntity<Results<Void>> insert(@RequestBody StockLocationInfoDTO stockLocationInfoDTO) {
        validObject(stockLocationInfoDTO, ValidGroups.Insert.class);
        stockLocationInfoService.saveOrUpdate(stockLocationInfoDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新库存地点基本信息维护")
    public ResponseEntity<Results<Void>> update(@RequestBody StockLocationInfoDTO stockLocationInfoDTO) {
        validObject(stockLocationInfoDTO, ValidGroups.Update.class);
        stockLocationInfoService.saveOrUpdate(stockLocationInfoDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除库存地点基本信息维护")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        stockLocationInfoService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出库存地点基本信息维护")
    @PostMapping("/export")
    public void export(@RequestBody StockLocationInfoQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        stockLocationInfoService.export(query, response);
    }

    @ApiOperation(value = "导入库存地点基本信息维护")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = stockLocationInfoService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}