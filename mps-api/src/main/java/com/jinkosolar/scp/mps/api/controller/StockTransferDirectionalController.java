package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.StockTransferDirectionalDTO;
import com.jinkosolar.scp.mps.domain.query.StockTransferDirectionalQuery;
import com.jinkosolar.scp.mps.service.StockTransferDirectionalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MPS自产电池调拨计划定向非定向转换相关操作控制层
 * 
 * <AUTHOR> 2024-09-11 19:28:01
 */
@RequestMapping(value = "/stock-transfer-directional")
@RestController
@Api(value = "stockTransferDirectional", tags = "MPS自产电池调拨计划定向非定向转换相关操作控制层")
@RequiredArgsConstructor  
public class StockTransferDirectionalController extends BaseController {    
    private final StockTransferDirectionalService stockTransferDirectionalService; 

    @PostMapping("/page")
    @ApiOperation(value = "MPS自产电池调拨计划定向非定向转换分页查询")
    public ResponseEntity<Results<Page<StockTransferDirectionalDTO>>> page(@RequestBody StockTransferDirectionalQuery query) {
        return Results.createSuccessRes(stockTransferDirectionalService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "MPS自产电池调拨计划定向非定向转换详情")
    public ResponseEntity<Results<StockTransferDirectionalDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(stockTransferDirectionalService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增MPS自产电池调拨计划定向非定向转换")
    public ResponseEntity<Results<Void>> insert(@RequestBody StockTransferDirectionalDTO stockTransferDirectionalDTO) {
        validObject(stockTransferDirectionalDTO, ValidGroups.Insert.class);
        stockTransferDirectionalService.saveOrUpdate(stockTransferDirectionalDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新MPS自产电池调拨计划定向非定向转换")
    public ResponseEntity<Results<Void>> update(@RequestBody StockTransferDirectionalDTO stockTransferDirectionalDTO) {
        validObject(stockTransferDirectionalDTO, ValidGroups.Update.class);
        stockTransferDirectionalService.saveOrUpdate(stockTransferDirectionalDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除MPS自产电池调拨计划定向非定向转换")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        stockTransferDirectionalService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出MPS自产电池调拨计划定向非定向转换")
    @PostMapping("/export")
    public void export(@RequestBody StockTransferDirectionalQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        stockTransferDirectionalService.export(query, response);
    }

    @ApiOperation(value = "导入MPS自产电池调拨计划定向非定向转换")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = stockTransferDirectionalService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}