package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SupplementReduceInvestmentReportDTO;
import com.jinkosolar.scp.mps.domain.query.SupplementReduceInvestmentReportQuery;
import com.jinkosolar.scp.mps.service.SupplementReduceInvestmentReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;                            
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 补减投报表相关操作控制层
 * 
 * <AUTHOR> 2024-09-30 19:51:08
 */
@RequestMapping(value = "/supplement-reduce-investment-report")
@RestController
@Api(value = "supplementReduceInvestmentReport", tags = "补减投报表相关操作控制层")
@RequiredArgsConstructor  
public class SupplementReduceInvestmentReportController extends BaseController {    
    private final SupplementReduceInvestmentReportService supplementReduceInvestmentReportService; 

    @PostMapping("/page")
    @ApiOperation(value = "补减投报表分页查询")
    public ResponseEntity<Results<Page<SupplementReduceInvestmentReportDTO>>> page(@RequestBody SupplementReduceInvestmentReportQuery query) {
        return Results.createSuccessRes(supplementReduceInvestmentReportService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "补减投报表详情")
    public ResponseEntity<Results<SupplementReduceInvestmentReportDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(supplementReduceInvestmentReportService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增补减投报表")
    public ResponseEntity<Results<Void>> insert(@RequestBody SupplementReduceInvestmentReportDTO supplementReduceInvestmentReportDTO) {
        validObject(supplementReduceInvestmentReportDTO, ValidGroups.Insert.class);
        supplementReduceInvestmentReportService.saveOrUpdate(supplementReduceInvestmentReportDTO);
        return Results.createSuccessRes();
    }


    @PostMapping("/update")
    @ApiOperation(value = "更新补减投报表")
    public ResponseEntity<Results<Void>> update(@RequestBody SupplementReduceInvestmentReportDTO supplementReduceInvestmentReportDTO) {
        validObject(supplementReduceInvestmentReportDTO, ValidGroups.Update.class);
        supplementReduceInvestmentReportService.saveOrUpdate(supplementReduceInvestmentReportDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除补减投报表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        supplementReduceInvestmentReportService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出补减投报表")
    @PostMapping("/export")
    public void export(@RequestBody SupplementReduceInvestmentReportQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        supplementReduceInvestmentReportService.export(query, response);
    }

    @ApiOperation(value = "导入补减投报表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = supplementReduceInvestmentReportService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncInvestmentReport")
    @ApiOperation(value = "新增补减投报表生成逻辑")
    public ResponseEntity<Results<Void>> syncInvestmentReport(@RequestBody SupplementReduceInvestmentReportQuery query) {
        boolean rest = supplementReduceInvestmentReportService.syncInvestmentReport();
        return Results.createSuccessRes();
    }
}