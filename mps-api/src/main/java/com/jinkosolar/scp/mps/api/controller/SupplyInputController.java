package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.MessageHelper;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SupplyInputDTO;
import com.jinkosolar.scp.mps.domain.query.SupplyInputQuery;
import com.jinkosolar.scp.mps.service.SupplyInputService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 补投相关操作控制层
 *
 * <AUTHOR> 2024-04-24 14:55:20
 */
@RequestMapping(value = "/supply-input")
@RestController
@Api(value = "supplyInput", tags = "补投相关操作控制层")
public class SupplyInputController extends BaseController {
    @Autowired
    private SupplyInputService supplyInputService;

    @PostMapping("/page")
    @ApiOperation(value = "补投分页查询")
    public ResponseEntity<Results<Page<SupplyInputDTO>>> page(@RequestBody SupplyInputQuery query) {
        return Results.createSuccessRes(supplyInputService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "补投详情")
    public ResponseEntity<Results<SupplyInputDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(supplyInputService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增补投")
    public ResponseEntity<Results<SupplyInputDTO>> insert(@RequestBody SupplyInputDTO supplyInputDTO) {
        validObject(supplyInputDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(supplyInputService.saveOrUpdate(supplyInputDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新补投")
    public ResponseEntity<Results<SupplyInputDTO>> update(@RequestBody SupplyInputDTO supplyInputDTO) {
        validObject(supplyInputDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(supplyInputService.saveOrUpdate(supplyInputDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除补投")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        supplyInputService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出补投")
    @PostMapping("/export")
    public void export(@RequestBody SupplyInputQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        supplyInputService.export(query, response);
    }

    @ApiOperation(value = "导入补投")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestParam("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = supplyInputService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/updateList")
    @ApiOperation(value = "批量更新")
    public ResponseEntity<Results<Object>> updateList(@RequestBody List<SupplyInputDTO> SupplyInputDTOs) {
        supplyInputService.updateList(SupplyInputDTOs);
        return Results.createSuccessRes();
    }

    @PostMapping("/syncSupplyInput")
    @ApiOperation(value = "推送补投数据")
    public ResponseEntity<Results<Object>> syncSupplyInput(@RequestBody SupplyInputQuery query) {
        supplyInputService.syncSupplyInput(query);
        return Results.createSuccessRes();
    }
}