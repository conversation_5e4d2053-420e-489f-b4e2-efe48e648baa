package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.SurplusQuantitativeAnalysisDTO;
import com.jinkosolar.scp.mps.domain.query.SurplusQuantitativeAnalysisQuery;
import com.jinkosolar.scp.mps.domain.save.SurplusQuantitativeAnalysisSaveDTO;
import com.jinkosolar.scp.mps.service.SurplusQuantitativeAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 剩余可接单量分析 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 08:02:10
 */
@RestController
@RequestMapping("/surplus-quantitative-analysis")
@RequiredArgsConstructor
@Api(value = "surplus-quantitative-analysis", tags = "剩余可接单量分析操作")
public class SurplusQuantitativeAnalysisController {
    private final SurplusQuantitativeAnalysisService surplusQuantitativeAnalysisService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "剩余可接单量分析分页列表", notes = "获得剩余可接单量分析分页列表")
    public ResponseEntity<Results<Page<SurplusQuantitativeAnalysisDTO>>> queryByPage(@RequestBody SurplusQuantitativeAnalysisQuery query) {
        return Results.createSuccessRes(surplusQuantitativeAnalysisService.queryByPage(query));
    }


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/planLayoutPage")
    @ApiOperation(value = "剩余可接单量分析分页列表", notes = "获得剩余可接单量分析分页列表")
    public ResponseEntity<Results<Page<SurplusQuantitativeAnalysisDTO>>> planLayoutPage(@RequestBody SurplusQuantitativeAnalysisQuery query) {
        return Results.createSuccessRes(surplusQuantitativeAnalysisService.planLayoutPage(query));
    }

    /**
     * 根据条件查询
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/findByCondition")
    @ApiOperation(value = "剩余可接单量分析列表", notes = "剩余可接单量分析列表")
    public ResponseEntity<Results<List<SurplusQuantitativeAnalysisDTO>>> findByCondition(@RequestBody SurplusQuantitativeAnalysisQuery query) {
        return Results.createSuccessRes(surplusQuantitativeAnalysisService.findByCondition(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<SurplusQuantitativeAnalysisDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(surplusQuantitativeAnalysisService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 计算数据
     *
     * @param query 筛选条件
     * @return
     */
    @PostMapping("/compute")
    @ApiOperation(value = "计算数据")
    public ResponseEntity<Results<Object>> compute(@RequestBody SurplusQuantitativeAnalysisQuery query) {
        surplusQuantitativeAnalysisService.compute(query);
        return Results.createSuccessRes();
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<SurplusQuantitativeAnalysisDTO>> save(@Valid @RequestBody SurplusQuantitativeAnalysisSaveDTO saveDTO) {
        return Results.createSuccessRes(surplusQuantitativeAnalysisService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        surplusQuantitativeAnalysisService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody SurplusQuantitativeAnalysisQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        surplusQuantitativeAnalysisService.export(query, response);
    }


    @PostMapping("/planLayoutExport")
    @ApiOperation(value = "导出")
    public void planLayoutExport(@RequestBody SurplusQuantitativeAnalysisQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        surplusQuantitativeAnalysisService.planLayoutExport(query, response);
    }
}
