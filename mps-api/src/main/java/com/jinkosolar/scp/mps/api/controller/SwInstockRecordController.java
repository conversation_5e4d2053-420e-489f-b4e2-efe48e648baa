package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mps.SyncSWTwoHourOutPutRecordRequest;
import com.jinkosolar.scp.jip.api.dto.mps.SyncSWTwoHourOutPutRecordResponse;
import com.jinkosolar.scp.jip.api.dto.mrp.ProductInStockRecordRequest;
import com.jinkosolar.scp.jip.api.dto.mrp.ProductInstockRecordResponse;
import com.jinkosolar.scp.jip.api.service.SyncProductInStockRecordService;
import com.jinkosolar.scp.jip.api.service.SyncSWTwoHourOutPutRecordService;
import com.jinkosolar.scp.mps.domain.dto.SwInstockRecordDTO;
import com.jinkosolar.scp.mps.domain.query.SwInstockRecordQuery;
import com.jinkosolar.scp.mps.service.SwInstockRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 丝网2小时产量表相关操作控制层
 * 
 * <AUTHOR> 2024-05-21 15:11:33
 */
@RequestMapping(value = "/sw-instock-record")
@RestController
@Api(value = "swInstockRecord", tags = "丝网2小时产量表相关操作控制层")
public class SwInstockRecordController extends BaseController {
    @Autowired
    private SwInstockRecordService swInstockRecordService;

    @Autowired
    private SyncSWTwoHourOutPutRecordService syncSWTwoHourOutPutRecordService;

    @PostMapping("/page")
    @ApiOperation(value = "丝网2小时产量表分页查询")
    public ResponseEntity<Results<Page<SwInstockRecordDTO>>> page(@RequestBody SwInstockRecordQuery query) {
        return Results.createSuccessRes(swInstockRecordService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "丝网2小时产量表详情")
    public ResponseEntity<Results<SwInstockRecordDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(swInstockRecordService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增丝网2小时产量表")
    public ResponseEntity<Results<SwInstockRecordDTO>> insert(@RequestBody SwInstockRecordDTO swInstockRecordDTO) {
        validObject(swInstockRecordDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(swInstockRecordService.saveOrUpdate(swInstockRecordDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新丝网2小时产量表")
    public ResponseEntity<Results<SwInstockRecordDTO>> update(@RequestBody SwInstockRecordDTO swInstockRecordDTO) {
        validObject(swInstockRecordDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(swInstockRecordService.saveOrUpdate(swInstockRecordDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除丝网2小时产量表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        swInstockRecordService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出丝网2小时产量表")
    @PostMapping("/export")
    public void export(@RequestBody SwInstockRecordQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        swInstockRecordService.export(query, response);
    }

    @ApiOperation(value = "导入丝网2小时产量表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = swInstockRecordService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/syncSwTwoHourInStockRecord")
    @ApiOperation(value = "丝网2小时产量入库同步")
    public JipResponseData syncSwTwoHourOutPutInStockRecord(@RequestBody SyncSWTwoHourOutPutRecordRequest query) {
        SyncSWTwoHourOutPutRecordResponse responseDTO=syncSWTwoHourOutPutRecordService.getSWTwoHourOutPutList(query);
        //TODO 这里要处理数据的存储
        swInstockRecordService.save(responseDTO.getInfoList());
        return JipResponseData.success();

    }
}