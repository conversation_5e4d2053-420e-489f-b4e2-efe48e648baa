package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SwitchLossDataDTO;
import com.jinkosolar.scp.mps.domain.query.SwitchLossDataQuery;
import com.jinkosolar.scp.mps.service.SwitchLossDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电池-切换损失数据表相关操作控制层
 * 
 * <AUTHOR> 2024-05-13 07:15:05
 */
@RequestMapping(value = "/switch-loss-data")
@RestController
@Api(value = "switchLossData", tags = "电池-切换损失数据表相关操作控制层")
public class SwitchLossDataController extends BaseController {
    @Autowired
    private SwitchLossDataService switchLossDataService;

    @PostMapping("/page")
    @ApiOperation(value = "电池-切换损失数据表分页查询")
    public ResponseEntity<Results<Page<SwitchLossDataDTO>>> page(@RequestBody SwitchLossDataQuery query) {
        return Results.createSuccessRes(switchLossDataService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "电池-切换损失数据表详情")
    public ResponseEntity<Results<SwitchLossDataDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(switchLossDataService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增电池-切换损失数据表")
    public ResponseEntity<Results<SwitchLossDataDTO>> insert(@RequestBody SwitchLossDataDTO switchLossDataDTO) {
        validObject(switchLossDataDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(switchLossDataService.saveOrUpdate(switchLossDataDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新电池-切换损失数据表")
    public ResponseEntity<Results<SwitchLossDataDTO>> update(@RequestBody SwitchLossDataDTO switchLossDataDTO) {
        validObject(switchLossDataDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(switchLossDataService.saveOrUpdate(switchLossDataDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除电池-切换损失数据表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        switchLossDataService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出电池-切换损失数据表")
    @PostMapping("/export")
    public void export(@RequestBody SwitchLossDataQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        switchLossDataService.export(query, response);
    }

    @ApiOperation(value = "导入电池-切换损失数据表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = switchLossDataService.importData(multipartFile, excelPara);
        switchLossDataService.syncApsTable();
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions() {
        return Results.createSuccessRes(switchLossDataService.top10Versions());
    }
}