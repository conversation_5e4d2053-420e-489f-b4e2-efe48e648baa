package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.SwitchLossDataDetailDTO;
import com.jinkosolar.scp.mps.domain.query.SwitchLossDataDetailQuery;
import com.jinkosolar.scp.mps.service.SwitchLossDataDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 切换损失数据明细表相关操作控制层
 * 
 * <AUTHOR> 2024-06-12 10:24:57
 */
@RequestMapping(value = "/switch-loss-data-detail")
@RestController
@Api(value = "switchLossDataDetail", tags = "切换损失数据明细表相关操作控制层")
public class SwitchLossDataDetailController extends BaseController {
    @Autowired
    private SwitchLossDataDetailService switchLossDataDetailService;

    @PostMapping("/page")
    @ApiOperation(value = "切换损失数据明细表分页查询")
    public ResponseEntity<Results<Page<SwitchLossDataDetailDTO>>> page(@RequestBody SwitchLossDataDetailQuery query) {
        return Results.createSuccessRes(switchLossDataDetailService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "切换损失数据明细表详情")
    public ResponseEntity<Results<SwitchLossDataDetailDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(switchLossDataDetailService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增切换损失数据明细表")
    public ResponseEntity<Results<SwitchLossDataDetailDTO>> insert(@RequestBody SwitchLossDataDetailDTO switchLossDataDetailDTO) {
        validObject(switchLossDataDetailDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(switchLossDataDetailService.saveOrUpdate(switchLossDataDetailDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新切换损失数据明细表")
    public ResponseEntity<Results<SwitchLossDataDetailDTO>> update(@RequestBody SwitchLossDataDetailDTO switchLossDataDetailDTO) {
        validObject(switchLossDataDetailDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(switchLossDataDetailService.saveOrUpdate(switchLossDataDetailDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除切换损失数据明细表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        switchLossDataDetailService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出切换损失数据明细表")
    @PostMapping("/export")
    public void export(@RequestBody SwitchLossDataDetailQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        switchLossDataDetailService.export(query, response);
    }

    @ApiOperation(value = "导入切换损失数据明细表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = switchLossDataDetailService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}