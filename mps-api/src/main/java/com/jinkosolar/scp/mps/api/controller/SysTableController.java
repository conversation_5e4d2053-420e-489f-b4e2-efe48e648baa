package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.service.ItemCollocationResultService;
import com.jinkosolar.scp.mps.service.MdaFactoryItemDetailService;
import com.jinkosolar.scp.mps.service.ResourceAuthenticationDetailService;
import com.jinkosolar.scp.mps.service.ResourceItemDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping(value = "/sync")
@RestController
@Api(value = "dpPulledCrystalDemand")
public class SysTableController {

    @Resource
    private ResourceAuthenticationDetailService resourceAuthenticationDetailService;

    @Resource
    private ItemCollocationResultService itemCollocationResultService;

    @Resource
    private ResourceItemDetailService resourceItemDetailService;

    @Resource
    private MdaFactoryItemDetailService mdaFactoryItemDetailService;

    @ApiOperation(value = "同步资源认证明细")
    @PostMapping("/syncResource")
    public ResponseEntity<Results<Void>> syncResource() {
        resourceAuthenticationDetailService.sysResourceData();
        return Results.createSuccessRes();
    }


    @ApiOperation(value = "MRP物料搭结果表")
    @PostMapping("/syncItmResult")
    public ResponseEntity<Results<Void>> syncItmResult() {
        itemCollocationResultService.sysResourceData();
        return Results.createSuccessRes();
    }


    @ApiOperation(value = "资源物料限制明细表")
    @PostMapping("/syncResourceItem")
    public ResponseEntity<Results<Void>> syncResourceItem() {
        resourceItemDetailService.sysResourceData();
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "MDA差异限定物料厂家明细")
    @PostMapping("/syncMdaItem")
    public ResponseEntity<Results<Void>> syncMdaItem() {
        mdaFactoryItemDetailService.sysResourceData();
        return Results.createSuccessRes();
    }


}
