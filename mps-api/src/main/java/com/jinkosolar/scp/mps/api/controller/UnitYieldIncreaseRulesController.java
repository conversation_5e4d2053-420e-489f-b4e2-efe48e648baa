package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.UnitYieldIncreaseRulesDTO;
import com.jinkosolar.scp.mps.domain.query.UnitYieldIncreaseRulesQuery;
import com.jinkosolar.scp.mps.service.UnitYieldIncreaseRulesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 单产提升规则相关操作控制层
 * 
 * <AUTHOR> 2024-07-30 19:28:18
 */
@RequestMapping(value = "/unit-yield-increase-rules")
@RestController
@Api(value = "unitYieldIncreaseRules", tags = "单产提升规则相关操作控制层")
public class UnitYieldIncreaseRulesController extends BaseController {    
    private final UnitYieldIncreaseRulesService unitYieldIncreaseRulesService;

    public UnitYieldIncreaseRulesController(UnitYieldIncreaseRulesService unitYieldIncreaseRulesService) {
        this.unitYieldIncreaseRulesService = unitYieldIncreaseRulesService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "单产提升规则分页查询")
    public ResponseEntity<Results<Page<UnitYieldIncreaseRulesDTO>>> page(@RequestBody UnitYieldIncreaseRulesQuery query) {
        return Results.createSuccessRes(unitYieldIncreaseRulesService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "单产提升规则详情")
    public ResponseEntity<Results<UnitYieldIncreaseRulesDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(unitYieldIncreaseRulesService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody UnitYieldIncreaseRulesDTO unitYieldIncreaseRulesDTO) {
        return Results.createSuccessRes(unitYieldIncreaseRulesService.getVersionNumberByDataType(unitYieldIncreaseRulesDTO));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增单产提升规则")
    public ResponseEntity<Results<UnitYieldIncreaseRulesDTO>> insert(@RequestBody UnitYieldIncreaseRulesDTO unitYieldIncreaseRulesDTO) {
        validObject(unitYieldIncreaseRulesDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(unitYieldIncreaseRulesService.saveOrUpdate(unitYieldIncreaseRulesDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新单产提升规则")
    public ResponseEntity<Results<UnitYieldIncreaseRulesDTO>> update(@RequestBody UnitYieldIncreaseRulesDTO unitYieldIncreaseRulesDTO) {
        validObject(unitYieldIncreaseRulesDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(unitYieldIncreaseRulesService.saveOrUpdate(unitYieldIncreaseRulesDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除单产提升规则")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        unitYieldIncreaseRulesService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出单产提升规则")
    @PostMapping("/export")
    public void export(@RequestBody UnitYieldIncreaseRulesQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        unitYieldIncreaseRulesService.export(query, response);
    }

    @ApiOperation(value = "导入单产提升规则")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = unitYieldIncreaseRulesService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}