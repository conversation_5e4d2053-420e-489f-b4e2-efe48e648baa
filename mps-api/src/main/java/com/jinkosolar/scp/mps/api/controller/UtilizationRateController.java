package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.UtilizationRateDTO;
import com.jinkosolar.scp.mps.domain.query.UtilizationRateLovQuery;
import com.jinkosolar.scp.mps.domain.query.UtilizationRateQuery;
import com.jinkosolar.scp.mps.service.UtilizationRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 利用率相关操作控制层
 *
 * <AUTHOR> 2024-04-24 11:00:19
 */
@RequestMapping(value = "/utilization-rate")
@RestController
@Api(value = "utilizationRate", tags = "利用率相关操作控制层")
public class UtilizationRateController extends BaseController {
    @Autowired
    private UtilizationRateService utilizationRateService;

    @PostMapping("/page")
    @ApiOperation(value = "利用率分页查询")
    public ResponseEntity<Results<Page<UtilizationRateDTO>>> page(@RequestBody UtilizationRateQuery query) {
        return Results.createSuccessRes(utilizationRateService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "利用率详情")
    public ResponseEntity<Results<UtilizationRateDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(utilizationRateService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增利用率")
    public ResponseEntity<Results<UtilizationRateDTO>> insert(@RequestBody UtilizationRateDTO utilizationRateDTO) {
        validObject(utilizationRateDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(utilizationRateService.saveOrUpdate(utilizationRateDTO));
    }

    @PostMapping("/insertList")
    @ApiOperation(value = "批量新增利用率")
    public ResponseEntity<Results<Object>> insertList(@RequestBody List<UtilizationRateDTO> utilizationRateDTOs) {
        validObject(utilizationRateDTOs, ValidGroups.Insert.class);
        return Results.createSuccessRes(utilizationRateService.saveList(utilizationRateDTOs));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新利用率")
    public ResponseEntity<Results<UtilizationRateDTO>> update(@RequestBody UtilizationRateDTO utilizationRateDTO) {
        validObject(utilizationRateDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(utilizationRateService.saveOrUpdate(utilizationRateDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除利用率")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        utilizationRateService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出利用率")
    @PostMapping("/export")
    public void export(@RequestBody UtilizationRateQuery query, HttpServletResponse response) {
        query.setPageSize(Integer.MAX_VALUE);
        utilizationRateService.export(query, response);
    }

    @ApiOperation(value = "导入利用率")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestParam("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = utilizationRateService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/queryLov")
    @ApiOperation(value = "通过大类查询类型", notes = "通过大类查询类型")
    @ApiImplicitParam(name = "parentRateType", value = "大类")
    public ResponseEntity<Results<List<LovLineDTO>>> queryLov(@RequestBody UtilizationRateLovQuery query) {
        return Results.createSuccessRes(utilizationRateService.queryLov(query));
    }
}