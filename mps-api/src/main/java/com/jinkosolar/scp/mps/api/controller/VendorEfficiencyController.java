package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.mps.domain.dto.PullingYieldDTO;
import com.jinkosolar.scp.mps.domain.dto.VendorEfficiencyDTO;
import com.jinkosolar.scp.mps.domain.dto.VendorEfficiencyToMesRequest;
import com.jinkosolar.scp.mps.domain.dto.VendorEfficiencyToMesResponse;
import com.jinkosolar.scp.mps.domain.query.VendorEfficiencyQuery;
import com.jinkosolar.scp.mps.service.VendorEfficiencyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 厂家档位对照相关操作控制层
 * 
 * <AUTHOR> 2024-07-26 11:45:19
 */
@RequestMapping(value = "/vendor-efficiency")
@RestController
@Api(value = "vendorEfficiency", tags = "厂家档位对照相关操作控制层")
public class VendorEfficiencyController extends BaseController {    
    private final VendorEfficiencyService vendorEfficiencyService;

    public VendorEfficiencyController(VendorEfficiencyService vendorEfficiencyService) {
        this.vendorEfficiencyService = vendorEfficiencyService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "厂家档位对照分页查询")
    public ResponseEntity<Results<Page<VendorEfficiencyDTO>>> page(@RequestBody VendorEfficiencyQuery query) {
        return Results.createSuccessRes(vendorEfficiencyService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "厂家档位对照详情")
    public ResponseEntity<Results<VendorEfficiencyDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(vendorEfficiencyService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增厂家档位对照")
    public ResponseEntity<Results<VendorEfficiencyDTO>> insert(@RequestBody VendorEfficiencyDTO vendorEfficiencyDTO) {
        validObject(vendorEfficiencyDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(vendorEfficiencyService.saveOrUpdate(vendorEfficiencyDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新厂家档位对照")
    public ResponseEntity<Results<VendorEfficiencyDTO>> update(@RequestBody VendorEfficiencyDTO vendorEfficiencyDTO) {
        validObject(vendorEfficiencyDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(vendorEfficiencyService.saveOrUpdate(vendorEfficiencyDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除厂家档位对照")
    public ResponseEntity<Results<Object>> delete(@RequestBody List<VendorEfficiencyDTO> vendorEfficiencyDTO) {
        validObject(vendorEfficiencyDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(vendorEfficiencyService.deleteByDto((List<VendorEfficiencyDTO>) vendorEfficiencyDTO));
    }

    @PostMapping("/deleteHistory")
    @ApiOperation(value = "批量删除厂家档位对照历史")
    public ResponseEntity<Results<Object>> deleteHistory(List<VendorEfficiencyDTO> vendorEfficiencyDTO) {
        validObject(vendorEfficiencyDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(vendorEfficiencyService.deleteByHistroyDto((List<VendorEfficiencyDTO>) vendorEfficiencyDTO));
    }


    @ApiOperation(value = "导出厂家档位对照")
    @PostMapping("/export")
    public void export(@RequestBody VendorEfficiencyQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        vendorEfficiencyService.export(query, response);
    }

    @ApiOperation(value = "导入厂家档位对照")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = vendorEfficiencyService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @ApiOperation(value = "导入厂家档位对照")
    @PostMapping("/importHistory")
    public ResponseEntity<Results<ImportResultDTO>> importFileHistory(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = vendorEfficiencyService.importDataHistory(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }


    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> versions() {
        return Results.createSuccessRes(vendorEfficiencyService.findVersion());
    }

    @PostMapping("/publish")
    @ApiOperation(value = "发布厂家挡位对照")
    public ResponseEntity<Results<Object>> publish() {
        vendorEfficiencyService.publish();
        return Results.createSuccessRes();
    }

    @PostMapping("/getVendorEfficiencyToMes")
    @ApiOperation(value = "MES主动查询-厂家档位对照历史版本表仅给最新版本，且‘供应商效率标准’有值的数据")
    public VendorEfficiencyToMesResponse getVendorEfficiencyToMes(@RequestBody VendorEfficiencyToMesRequest query) {
        VendorEfficiencyQuery vendorEfficiencyQuery= new VendorEfficiencyQuery();
        if(null != query.getInput().getPageNumber()){
            vendorEfficiencyQuery.setPageNumber(query.getInput().getPageNumber());
        }
        if(null != query.getInput().getPageSize()){
            vendorEfficiencyQuery.setPageSize(query.getInput().getPageSize());
        }
        return vendorEfficiencyService.getVendorEfficiencyToMes(vendorEfficiencyQuery);
    }

}