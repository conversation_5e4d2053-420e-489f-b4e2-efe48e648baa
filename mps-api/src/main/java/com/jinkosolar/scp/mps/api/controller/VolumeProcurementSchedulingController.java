package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.VolumeProcurementSchedulingDTO;
import com.jinkosolar.scp.mps.domain.query.VolumeProcurementSchedulingQuery;
import com.jinkosolar.scp.mps.service.VolumeProcurementSchedulingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 综合采购招标量和商务外发排程量表相关操作控制层
 *
 * <AUTHOR> 2024-11-06 18:29:27
 */
@RequestMapping(value = "/volume-procurement-scheduling")
@RestController
@Api(value = "volumeProcurementScheduling", tags = "综合采购招标量和商务外发排程量表相关操作控制层")
@RequiredArgsConstructor
public class VolumeProcurementSchedulingController extends BaseController {
    private final VolumeProcurementSchedulingService volumeProcurementSchedulingService;

    @PostMapping("/page")
    @ApiOperation(value = "综合采购招标量和商务外发排程量表分页查询")
    public ResponseEntity<Results<Page<VolumeProcurementSchedulingDTO>>> page(@RequestBody VolumeProcurementSchedulingQuery query) {
        return Results.createSuccessRes(volumeProcurementSchedulingService.page(query));
    }

    @ApiOperation(value = "导出综合采购招标量和商务外发排程量表")
    @PostMapping("/export")
    public void export(@RequestBody VolumeProcurementSchedulingQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        volumeProcurementSchedulingService.export(query, response);
    }

    @ApiOperation(value = "导入综合采购招标量和商务外发排程量表")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara,
                                                               @RequestParam("typeCode") String typeCode) {
        //typeCode ComprehensiveProcurementBiddingVolume BusinessOutboundSchedulingVolume
        ImportResultDTO importResultDTO = volumeProcurementSchedulingService.importData(multipartFile, excelPara,typeCode);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }
        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "综合采购招标量和商务外发排程量表详情")
    public ResponseEntity<Results<VolumeProcurementSchedulingDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(volumeProcurementSchedulingService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增综合采购招标量和商务外发排程量表")
    public ResponseEntity<Results<Void>> insert(@RequestBody VolumeProcurementSchedulingDTO volumeProcurementSchedulingDTO) {
        validObject(volumeProcurementSchedulingDTO, ValidGroups.Insert.class);
        volumeProcurementSchedulingService.saveOrUpdate(volumeProcurementSchedulingDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新综合采购招标量和商务外发排程量表")
    public ResponseEntity<Results<Void>> update(@RequestBody VolumeProcurementSchedulingDTO volumeProcurementSchedulingDTO) {
        validObject(volumeProcurementSchedulingDTO, ValidGroups.Update.class);
        volumeProcurementSchedulingService.saveOrUpdate(volumeProcurementSchedulingDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除综合采购招标量和商务外发排程量表")
    public ResponseEntity<Results<Void>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        volumeProcurementSchedulingService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }


}