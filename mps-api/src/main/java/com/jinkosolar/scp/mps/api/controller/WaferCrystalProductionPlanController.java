package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.WaferCrystalProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.query.WaferCrystalProductionPlanQuery;
import com.jinkosolar.scp.mps.domain.save.DynamicColumnSaveDTO;
import com.jinkosolar.scp.mps.service.WaferCrystalProductionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 大基地计划上传相关操作控制层
 *
 * <AUTHOR> 2024-05-20 14:05:22
 */
@RequestMapping(value = "/wafer-crystal-production-plan")
@RestController
@Api(value = "waferCrystalProductionPlan", tags = "大基地计划上传相关操作控制层")
public class WaferCrystalProductionPlanController extends BaseController {
    @Autowired
    private WaferCrystalProductionPlanService waferCrystalProductionPlanService;

    @PostMapping("/page")
    @ApiOperation(value = "大基地计划上传分页查询")
    public ResponseEntity<Results<Page<WaferCrystalProductionPlanDTO>>> page(@RequestBody WaferCrystalProductionPlanQuery query) {
        return Results.createSuccessRes(waferCrystalProductionPlanService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "大基地计划上传详情")
    public ResponseEntity<Results<WaferCrystalProductionPlanDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(waferCrystalProductionPlanService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增大基地计划上传")
    public ResponseEntity<Results<WaferCrystalProductionPlanDTO>> insert(@RequestBody WaferCrystalProductionPlanDTO waferCrystalProductionPlanDTO) {
        validObject(waferCrystalProductionPlanDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(waferCrystalProductionPlanService.saveOrUpdate(waferCrystalProductionPlanDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新大基地计划上传")
    public ResponseEntity<Results<Object>> update(@RequestBody WaferCrystalProductionPlanDTO waferCrystalProductionPlanDTO) {
        validObject(waferCrystalProductionPlanDTO, ValidGroups.Update.class);
        waferCrystalProductionPlanService.update(waferCrystalProductionPlanDTO);
        return Results.createSuccessRes();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除大基地计划上传")
    public ResponseEntity<Results<Object>> delete(@RequestBody List<WaferCrystalProductionPlanDTO> list) {
        waferCrystalProductionPlanService.deleteAll(list);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出大基地计划上传")
    @PostMapping("/export")
    public void export(@RequestBody WaferCrystalProductionPlanQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        waferCrystalProductionPlanService.export(query, response);
    }

    @ApiOperation(value = "导入大基地计划上传")
    @PostMapping("/import")
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = waferCrystalProductionPlanService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

}
