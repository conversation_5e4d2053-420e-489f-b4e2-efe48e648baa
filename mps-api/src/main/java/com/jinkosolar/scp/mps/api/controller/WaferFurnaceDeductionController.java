package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.WaferFurnaceDeductionDTO;
import com.jinkosolar.scp.mps.domain.dto.system.ImportResultExDTO;
import com.jinkosolar.scp.mps.domain.query.WaferFurnaceDeductionQuery;
import com.jinkosolar.scp.mps.service.WaferFurnaceDeductionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 炉型扣减表相关操作控制层
 * 
 * <AUTHOR> 2024-07-26 15:16:33
 */
@RequestMapping(value = "/wafer-furnace-deduction")
@RestController
@Api(value = "waferFurnaceDeduction", tags = "炉型扣减表相关操作控制层")
public class WaferFurnaceDeductionController extends BaseController {    
    private final WaferFurnaceDeductionService waferFurnaceDeductionService;

    public WaferFurnaceDeductionController(WaferFurnaceDeductionService waferFurnaceDeductionService) {
        this.waferFurnaceDeductionService = waferFurnaceDeductionService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "炉型扣减表分页查询")
    public ResponseEntity<Results<Page<WaferFurnaceDeductionDTO>>> page(@RequestBody WaferFurnaceDeductionQuery query) {
        return Results.createSuccessRes(waferFurnaceDeductionService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "炉型扣减表详情")
    public ResponseEntity<Results<WaferFurnaceDeductionDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(waferFurnaceDeductionService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增炉型扣减表")
    public ResponseEntity<Results<WaferFurnaceDeductionDTO>> insert(@RequestBody WaferFurnaceDeductionDTO waferFurnaceDeductionDTO) {
        validObject(waferFurnaceDeductionDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(waferFurnaceDeductionService.saveOrUpdate(waferFurnaceDeductionDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新炉型扣减表")
    public ResponseEntity<Results<WaferFurnaceDeductionDTO>> update(@RequestBody WaferFurnaceDeductionDTO waferFurnaceDeductionDTO) {
        validObject(waferFurnaceDeductionDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(waferFurnaceDeductionService.saveOrUpdate(waferFurnaceDeductionDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除炉型扣减表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        waferFurnaceDeductionService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出炉型扣减表")
    @PostMapping("/export")
    public void export(@RequestBody WaferFurnaceDeductionQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        waferFurnaceDeductionService.export(query, response);
    }

    @ApiOperation(value = "导入炉型扣减表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultExDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                                 @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultExDTO importResultDTO = waferFurnaceDeductionService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @PostMapping("/versions")
    @ApiOperation(value = "查询版本号")
    public ResponseEntity<Results<List<String>>> queryVersions(@RequestBody IdDTO dataType) {
        return Results.createSuccessRes(waferFurnaceDeductionService.queryVersions(dataType.getId()));
    }
}