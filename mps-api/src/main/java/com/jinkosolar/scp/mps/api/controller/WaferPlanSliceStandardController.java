package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.WaferPlanSliceStandardDTO;
import com.jinkosolar.scp.mps.domain.query.WaferPlanSliceStandardQuery;
import com.jinkosolar.scp.mps.domain.util.Constant;
import com.jinkosolar.scp.mps.service.WaferPlanSliceStandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品切片标准单产表相关操作控制层
 * 
 * <AUTHOR> 2024-07-23 15:02:25
 */
@RequestMapping(value = "/wafer-plan-slice-standard")
@RestController
@Api(value = "waferPlanSliceStandard", tags = "产品切片标准单产表相关操作控制层")
public class WaferPlanSliceStandardController extends BaseController {    
    private final WaferPlanSliceStandardService waferPlanSliceStandardService;
    @Autowired
    private SyncTableUtils syncTableUtils;

    public WaferPlanSliceStandardController(WaferPlanSliceStandardService waferPlanSliceStandardService) {
        this.waferPlanSliceStandardService = waferPlanSliceStandardService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "产品切片标准单产表分页查询")
    public ResponseEntity<Results<Page<WaferPlanSliceStandardDTO>>> page(@RequestBody WaferPlanSliceStandardQuery query) {
        return Results.createSuccessRes(waferPlanSliceStandardService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "产品切片标准单产表详情")
    public ResponseEntity<Results<WaferPlanSliceStandardDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(waferPlanSliceStandardService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增产品切片标准单产表")
    public ResponseEntity<Results<WaferPlanSliceStandardDTO>> insert(@RequestBody WaferPlanSliceStandardDTO waferPlanSliceStandardDTO) {
        validObject(waferPlanSliceStandardDTO, ValidGroups.Insert.class);
        WaferPlanSliceStandardDTO result =waferPlanSliceStandardService.saveOrUpdate(waferPlanSliceStandardDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新产品切片标准单产表")
    public ResponseEntity<Results<WaferPlanSliceStandardDTO>> update(@RequestBody WaferPlanSliceStandardDTO waferPlanSliceStandardDTO) {
        validObject(waferPlanSliceStandardDTO, ValidGroups.Update.class);
        WaferPlanSliceStandardDTO result = waferPlanSliceStandardService.saveOrUpdate(waferPlanSliceStandardDTO);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes(result);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除产品切片标准单产表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        waferPlanSliceStandardService.logicDeleteByIds(ids);
        //同步数据
        syncToAPS();
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出产品切片标准单产表")
    @PostMapping("/export")
    public void export(@RequestBody WaferPlanSliceStandardQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        waferPlanSliceStandardService.export(query, response);
    }

    @ApiOperation(value = "导入产品切片标准单产表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = waferPlanSliceStandardService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            //同步数据
            syncToAPS();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    public void syncToAPS() {
        SyncTableDTO syncTableDTO = new SyncTableDTO();
        List<String> listTable = new ArrayList<>();
        listTable.add(Constant.MPS_WAFER_PLAN_SLICE_STANDARD);
        syncTableDTO.setLovCodes(listTable);
        syncTableUtils.syncTables(syncTableDTO);
    }
}