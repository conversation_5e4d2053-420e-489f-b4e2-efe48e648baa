package com.jinkosolar.scp.mps.api.controller;


import com.ibm.scp.common.api.base.GlobalConstant;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.WeekAchievementRateDTO;
import com.jinkosolar.scp.mps.domain.entity.WeekDate;
import com.jinkosolar.scp.mps.domain.query.WeekAchievementRateQuery;
import com.jinkosolar.scp.mps.service.WeekAchievementRateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/weekAchievementRate")
@Api(value = "weekAchievementRate", tags = "周度达成率页面")
public class WeekAchievementRateController {

    @Autowired
    WeekAchievementRateService weekAchievementRateService;

    /**
     * 页列表，按照车间区分
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/workShopPage")
    @ApiOperation(value = "周度（车间）达成率分页列表", notes = "周度（车间）达成率分页列表")
    public ResponseEntity<Results<Page<WeekAchievementRateDTO>>> queryWorkShopPage(@RequestBody WeekAchievementRateQuery query) {
        return Results.createSuccessRes(weekAchievementRateService.queryWorkShopPage(query));
    }

    /**
     * 页列表，按照基地区分
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/basePlacePage")
    @ApiOperation(value = "周度（基地）达成率分页列表", notes = "周度（基地）达成率分页列表")
    public ResponseEntity<Results<Page<WeekAchievementRateDTO>>> queryBasePlacePage(@RequestBody WeekAchievementRateQuery query) {
        return Results.createSuccessRes(weekAchievementRateService.queryBasePlacePage(query));
    }

    /**
     * 页列表，按照产品系列区分
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/productSeriesPage")
    @ApiOperation(value = "周度（产品系列）达成率分页列表", notes = "周度（产品系列）达成率分页列表")
    public ResponseEntity<Results<Page<WeekAchievementRateDTO>>> queryProductSeriesPage(@RequestBody WeekAchievementRateQuery query) {
        return Results.createSuccessRes(weekAchievementRateService.queryProductSeriesPage(query));
    }

    /**
     * 获取年份所有月份
     */
    @PostMapping("/getDate")
    @ApiOperation(value = "获取年份所有月份", notes = "获取年份所有月份")
    public ResponseEntity<Results<List<WeekDate>>> getDate(@RequestBody WeekAchievementRateQuery query) {
        return Results.createSuccessRes(weekAchievementRateService.getDate(query));
    }

    /**
     * 刷新重新计算数据
     */
    @PostMapping("/flush")
    @ApiOperation(value = "周度达成率分页列表", notes = "周度达成率分页列表")
    public ResponseEntity<Results<Void>> flushDate(@RequestBody WeekAchievementRateQuery query) {
        weekAchievementRateService.flushDate(query);
        return Results.createSuccessRes();
    }


    /**
     * 获取所有版本
     */
    @PostMapping("/getVersion")
    @ApiOperation(value = "获取所有版本", notes = "获取所有版本")
    public ResponseEntity<Results<List<String>>> getVersion(@RequestBody WeekAchievementRateQuery query) {
        return Results.createSuccessRes(weekAchievementRateService.getVersion(query));
    }


    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody WeekAchievementRateQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        weekAchievementRateService.export(query, response);
    }


}
