package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.DateTypeVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.WorkCenterCorrespondenceDTO;
import com.jinkosolar.scp.mps.domain.query.WorkCenterCorrespondenceQuery;
import com.jinkosolar.scp.mps.service.WorkCenterCorrespondenceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶工作中心对应关系相关操作控制层
 * 
 * <AUTHOR> 2024-07-29 20:34:50
 */
@RequestMapping(value = "/work-center-correspondence")
@RestController
@Api(value = "workCenterCorrespondence", tags = "拉晶工作中心对应关系相关操作控制层")
public class WorkCenterCorrespondenceController extends BaseController {    
    private final WorkCenterCorrespondenceService workCenterCorrespondenceService;

    public WorkCenterCorrespondenceController(WorkCenterCorrespondenceService workCenterCorrespondenceService) {
        this.workCenterCorrespondenceService = workCenterCorrespondenceService;
    }

    @PostMapping("/page")
    @ApiOperation(value = "拉晶工作中心对应关系分页查询")
    public ResponseEntity<Results<Page<WorkCenterCorrespondenceDTO>>> page(@RequestBody WorkCenterCorrespondenceQuery query) {
        return Results.createSuccessRes(workCenterCorrespondenceService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶工作中心对应关系详情")
    public ResponseEntity<Results<WorkCenterCorrespondenceDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(workCenterCorrespondenceService.queryById(Long.parseLong(idDTO.getId())));
    }
    @PostMapping("/getVersionNumberByDataType")
    @ApiOperation(value = "根据数据类型找版本号")
    public ResponseEntity<Results<List<String>>> getVersionNumberByDataType(@RequestBody WorkCenterCorrespondenceDTO workCenterCorrespondenceDTO) {
        return Results.createSuccessRes(workCenterCorrespondenceService.getVersionNumberByDataType(workCenterCorrespondenceDTO));
    }
    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶工作中心对应关系")
    public ResponseEntity<Results<WorkCenterCorrespondenceDTO>> insert(@RequestBody WorkCenterCorrespondenceDTO workCenterCorrespondenceDTO) {
        validObject(workCenterCorrespondenceDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(workCenterCorrespondenceService.saveOrUpdate(workCenterCorrespondenceDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶工作中心对应关系")
    public ResponseEntity<Results<WorkCenterCorrespondenceDTO>> update(@RequestBody WorkCenterCorrespondenceDTO workCenterCorrespondenceDTO) {
        validObject(workCenterCorrespondenceDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(workCenterCorrespondenceService.saveOrUpdate(workCenterCorrespondenceDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶工作中心对应关系")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        workCenterCorrespondenceService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶工作中心对应关系")
    @PostMapping("/export")
    public void export(@RequestBody WorkCenterCorrespondenceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        workCenterCorrespondenceService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶工作中心对应关系")
    @PostMapping("/import")    
    public ResponseEntity<Results<DateTypeVersionDTO>> importFile(@RequestPart("file") MultipartFile multipartFile,
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        DateTypeVersionDTO importResultDTO = workCenterCorrespondenceService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            // 成功同步apsTable
            workCenterCorrespondenceService.syncApsTable();
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}