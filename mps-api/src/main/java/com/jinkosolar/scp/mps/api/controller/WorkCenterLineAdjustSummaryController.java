package com.jinkosolar.scp.mps.api.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ibm.scp.common.api.base.GlobalConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.jinkosolar.scp.mps.domain.query.WorkCenterLineAdjustSummaryQuery;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.jinkosolar.scp.mps.domain.save.WorkCenterLineAdjustSummarySaveDTO;
import com.jinkosolar.scp.mps.domain.entity.WorkCenterLineAdjustSummary;
import com.jinkosolar.scp.mps.domain.dto.WorkCenterLineAdjustSummaryDTO;
import com.jinkosolar.scp.mps.service.WorkCenterLineAdjustSummaryService;
import com.ibm.scp.common.api.util.Results;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * [说明]工作中心调整开线汇总表 前端控制器
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@RestController
@RequestMapping("/work-center-line-adjust-summary")
@RequiredArgsConstructor
@Api(value = "work-center-line-adjust-summary", tags = "工作中心调整开线汇总表操作")
public class WorkCenterLineAdjustSummaryController {
    private final WorkCenterLineAdjustSummaryService workCenterLineAdjustSummaryService;


    /**
     * 页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "工作中心调整开线汇总表分页列表", notes = "获得工作中心调整开线汇总表分页列表")
    public ResponseEntity<Results<Page<WorkCenterLineAdjustSummaryDTO>>> queryByPage(@RequestBody WorkCenterLineAdjustSummaryQuery query) {
        return Results.createSuccessRes(workCenterLineAdjustSummaryService.queryByPage(query));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<WorkCenterLineAdjustSummaryDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(workCenterLineAdjustSummaryService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<WorkCenterLineAdjustSummaryDTO>> save(@Valid @RequestBody WorkCenterLineAdjustSummarySaveDTO saveDTO) {
        return Results.createSuccessRes(workCenterLineAdjustSummaryService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        workCenterLineAdjustSummaryService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody WorkCenterLineAdjustSummaryQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
            workCenterLineAdjustSummaryService.export(query, response);
    }
}
