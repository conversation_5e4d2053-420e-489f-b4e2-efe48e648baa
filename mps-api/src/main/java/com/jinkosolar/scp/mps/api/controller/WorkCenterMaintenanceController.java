package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.dto.WorkCenterMaintenanceDTO;
import com.jinkosolar.scp.mps.domain.query.WorkCenterMaintenanceQuery;
import com.jinkosolar.scp.mps.service.WorkCenterMaintenanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工作中心维护(停机，停电，保养)相关操作控制层
 * 
 * <AUTHOR> 2024-05-13 09:40:16
 */
@RequestMapping(value = "/work-center-maintenance")
@RestController
@Api(value = "workCenterMaintenance", tags = "工作中心维护(停机，停电，保养)相关操作控制层")
public class WorkCenterMaintenanceController extends BaseController {
    @Autowired
    private WorkCenterMaintenanceService workCenterMaintenanceService;

    @PostMapping("/page")
    @ApiOperation(value = "工作中心维护(停机，停电，保养)分页查询")
    public ResponseEntity<Results<Page<WorkCenterMaintenanceDTO>>> page(@RequestBody WorkCenterMaintenanceQuery query) {
        return Results.createSuccessRes(workCenterMaintenanceService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "工作中心维护(停机，停电，保养)详情")
    public ResponseEntity<Results<WorkCenterMaintenanceDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(workCenterMaintenanceService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增工作中心维护(停机，停电，保养)")
    public ResponseEntity<Results<WorkCenterMaintenanceDTO>> insert(@Valid @RequestBody WorkCenterMaintenanceDTO workCenterMaintenanceDTO) {
        validObject(workCenterMaintenanceDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(workCenterMaintenanceService.saveOrUpdate(workCenterMaintenanceDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新工作中心维护(停机，停电，保养)")
    public ResponseEntity<Results<WorkCenterMaintenanceDTO>> update(@Valid @RequestBody WorkCenterMaintenanceDTO workCenterMaintenanceDTO) {
        validObject(workCenterMaintenanceDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(workCenterMaintenanceService.saveOrUpdate(workCenterMaintenanceDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除工作中心维护(停机，停电，保养)")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        workCenterMaintenanceService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出工作中心维护(停机，停电，保养)")
    @PostMapping("/export")
    public void export(@RequestBody WorkCenterMaintenanceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        workCenterMaintenanceService.export(query, response);
    }

    @ApiOperation(value = "导入工作中心维护(停机，停电，保养)")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = workCenterMaintenanceService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}