package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;                             
import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.base.IdDTO;
import com.ibm.scp.common.api.base.IdsDTO;
import com.ibm.scp.common.api.base.ImportResultDTO;
import com.ibm.scp.common.api.util.MessageHelper;                             
import com.ibm.scp.common.api.util.ExcelPara;                             
import com.ibm.scp.common.api.util.Results;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.annotation.JipFeignLog;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusResponse;
import com.jinkosolar.scp.jip.api.dto.mes.SyncWorkInProgressRequest;
import com.jinkosolar.scp.jip.api.dto.mes.SyncWorkInProgressResponse;
import com.jinkosolar.scp.jip.api.service.SyncWorkInProgressService;
import com.jinkosolar.scp.mps.domain.dto.SyncEquipmentInventoryLifespanDTO;
import com.jinkosolar.scp.mps.domain.dto.WorkInProgressDTO;
import com.jinkosolar.scp.mps.domain.query.WorkInProgressQuery;
import com.jinkosolar.scp.mps.service.WorkInProgressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 拉晶线边在制品表相关操作控制层
 * 
 * <AUTHOR> 2024-07-11 14:00:06
 */
@RequestMapping(value = "/work-in-progress")
@RestController
@Api(value = "workInProgress", tags = "拉晶线边在制品表相关操作控制层")
public class WorkInProgressController extends BaseController {
    @Autowired
    private WorkInProgressService workInProgressService;
    @Autowired
    private SyncWorkInProgressService syncWorkInProgressService;

    @PostMapping("/page")
    @ApiOperation(value = "拉晶线边在制品表分页查询")
    public ResponseEntity<Results<Page<WorkInProgressDTO>>> page(@RequestBody WorkInProgressQuery query) {
        return Results.createSuccessRes(workInProgressService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "拉晶线边在制品表详情")
    public ResponseEntity<Results<WorkInProgressDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(workInProgressService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增拉晶线边在制品表")
    public ResponseEntity<Results<WorkInProgressDTO>> insert(@RequestBody WorkInProgressDTO workInProgressDTO) {
        validObject(workInProgressDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(workInProgressService.saveOrUpdate(workInProgressDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新拉晶线边在制品表")
    public ResponseEntity<Results<WorkInProgressDTO>> update(@RequestBody WorkInProgressDTO workInProgressDTO) {
        validObject(workInProgressDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(workInProgressService.saveOrUpdate(workInProgressDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除拉晶线边在制品表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        workInProgressService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出拉晶线边在制品表")
    @PostMapping("/export")
    public void export(@RequestBody WorkInProgressQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        workInProgressService.export(query, response);
    }

    @ApiOperation(value = "导入拉晶线边在制品表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = workInProgressService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }

    @JipFeignLog
    @PostMapping("/syncWorkInProgress")
    @ApiOperation(value = "拉晶线边在制品接口")
    public JipResponseData syncWorkInProgress() throws SQLException {
        workInProgressService.sync();
        return JipResponseData.success();
    }
}