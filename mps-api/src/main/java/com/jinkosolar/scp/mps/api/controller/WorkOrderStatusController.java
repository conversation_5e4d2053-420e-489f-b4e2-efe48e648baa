package com.jinkosolar.scp.mps.api.controller;

import cn.hutool.json.JSONUtil;
import com.ibm.scp.common.api.base.*;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.dto.WorkOrderStatusDTO;
import com.jinkosolar.scp.mps.domain.query.WorkOrderStatusQuery;
import com.jinkosolar.scp.mps.domain.repository.ModuleProductionPlanRepository;
import com.jinkosolar.scp.mps.service.WorkOrderStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  工单状态表相关操作控制层
 * 
 * <AUTHOR> 2024-07-10 09:55:57
 */
@RequestMapping(value = "/work-order-status")
@RestController
@Api(value = "workOrderStatus", tags = " 工单状态表相关操作控制层")
public class WorkOrderStatusController extends BaseController {
    @Autowired
    private WorkOrderStatusService workOrderStatusService;

    @Autowired
    private ModuleProductionPlanRepository moduleProductionPlanRepository;

    @PostMapping("/page")
    @ApiOperation(value = " 工单状态表分页查询")
    public ResponseEntity<Results<Page<WorkOrderStatusDTO>>> page(@RequestBody WorkOrderStatusQuery query) {
        return Results.createSuccessRes(workOrderStatusService.page(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = " 工单状态表详情")
    public ResponseEntity<Results<WorkOrderStatusDTO>> detail(@RequestBody IdDTO idDTO) {
        validObject(idDTO);
        return Results.createSuccessRes(workOrderStatusService.queryById(Long.parseLong(idDTO.getId())));
    }

    @PostMapping("/insert")
    @ApiOperation(value = "新增 工单状态表")
    public ResponseEntity<Results<WorkOrderStatusDTO>> insert(@RequestBody WorkOrderStatusDTO workOrderStatusDTO) {
        validObject(workOrderStatusDTO, ValidGroups.Insert.class);
        return Results.createSuccessRes(workOrderStatusService.saveOrUpdate(workOrderStatusDTO));
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新 工单状态表")
    public ResponseEntity<Results<WorkOrderStatusDTO>> update(@RequestBody WorkOrderStatusDTO workOrderStatusDTO) {
        validObject(workOrderStatusDTO, ValidGroups.Update.class);
        return Results.createSuccessRes(workOrderStatusService.saveOrUpdate(workOrderStatusDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量删除 工单状态表")
    public ResponseEntity<Results<Object>> delete(@RequestBody IdsDTO idsDTO) {
        Assert.notEmpty(idsDTO.getIds(), "传入的ids不能为空");
        List<Long> ids = idsDTO.getIds().stream().map(IdDTO::getId).map(Long::parseLong).collect(Collectors.toList());
        workOrderStatusService.logicDeleteByIds(ids);
        return Results.createSuccessRes();
    }

    @ApiOperation(value = "导出 工单状态表")
    @PostMapping("/export")
    public void export(@RequestBody WorkOrderStatusQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        workOrderStatusService.export(query, response);
    }

    @ApiOperation(value = "导入 工单状态表")
    @PostMapping("/import")    
    public ResponseEntity<Results<ImportResultDTO>> importFile(@RequestPart("file") MultipartFile multipartFile, 
                                                               @RequestPart("excelPara") ExcelPara excelPara) {
        ImportResultDTO importResultDTO = workOrderStatusService.importData(multipartFile, excelPara);
        if (importResultDTO.getFailMessages().isEmpty()) {
            return Results.createSuccessRes(importResultDTO);
        }

        return Results.createImportFailRes(JSONUtil.toJsonStr(importResultDTO.getFailMessages()));
    }
}