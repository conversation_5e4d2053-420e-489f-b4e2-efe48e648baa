package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.jinkosolar.scp.mps.domain.dto.sap.SapWorkOrderStatusParamDto;
import com.jinkosolar.scp.mps.domain.dto.sap.SapWorkOrderStatusResponseDto;
import com.jinkosolar.scp.mps.service.WorkOrderStatusService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  工单状态表相关操作控制层
 *
 * <AUTHOR> 2024-07-10 09:55:57
 */
@RequestMapping(value = "/work-order-status-sap")
@RestController
@Slf4j
public class WorkOrderStatusForSapController  extends BaseController{
    @Autowired
    private WorkOrderStatusService workOrderStatusService;

    @PostMapping("/save")
    @ApiOperation(value = "sap回填工单状态记录")
    public SapWorkOrderStatusResponseDto save(@RequestBody SapWorkOrderStatusParamDto param) {
          return    workOrderStatusService.saveForSap(param);
    }

}
