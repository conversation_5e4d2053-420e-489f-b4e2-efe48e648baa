package com.jinkosolar.scp.mps.api.controller;

import com.ibm.scp.common.api.base.BaseController;
import com.ibm.scp.common.api.util.Results;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanVersionDiffDTO;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanQuery;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 计划版本报表控制层
 * 
 * <AUTHOR> 2024-05-13 13:56:39
 */
@RequestMapping(value = "/planVersionReport")
@RestController
@Api(value = "planVersionReportController", tags = "计划版本报表控制层")
public class planVersionReportController extends BaseController {
    @Autowired
    private NonModuleProductionPlanService nonModuleProductionPlanService;

    @PostMapping("/list")
    @ApiOperation(value = "查询报表列表")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> list(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.planVersionReportList(query));
    }


    @PostMapping("/cellPlan/version/diff/list")
    @ApiOperation(value = "电池查询版本差异报表")
    public ResponseEntity<Results<List<CellProductionPlanVersionDTO>>> cellPlanVersionDiffList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.cellPlanVersionDiffList(query));
    }


    @PostMapping("/cellPlan/version/diff/export")
    @ApiOperation(value = "电池查询版本差异报表导出")
    public void cellPlanVersionDiffExport(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.cellPlanVersionDiffExport(query,response);
    }


    @PostMapping("/convertList")
    @ApiOperation(value = "查询折算报表列表")
    public ResponseEntity<Results<List<NonModuleProductionPlanDTO>>> convertList(@RequestBody NonModuleProductionPlanQuery query) {
        return Results.createSuccessRes(nonModuleProductionPlanService.planVersionReportConvertList(query));
    }

    @ApiOperation(value = "导出报表")
    @PostMapping("/export")
    public void export(@RequestBody NonModuleProductionPlanQuery query, HttpServletResponse response) {
        nonModuleProductionPlanService.exportPlanVersion(query, response);
    }

}