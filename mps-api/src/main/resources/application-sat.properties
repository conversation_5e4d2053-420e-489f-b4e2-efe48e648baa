##############################################################################
spring.redis.host = ************
spring.redis.port = 32007
spring.redis.database= 13
spring.redis.password = ENC(PKUlK4KWKJR69dntnZQ6ajIZX6ugadsAf0WTQEp0CWo=)
spring.datasource.druid.url = ENC(zrFZMsVAkHLbv0FELGdiMcy3B0kfipj4zPMpykPQRLN+H9TG9nuqXvZU4kX9tRHXjjCeRrbmMHddHfjgTa4dKyedURaboIAdwU3w19TL0YxOEfM2a+7c5AF3oRzCQw2lOsSHsW7Qy4GmCHCh8v9OOvC+15hQpCRW7Ku29QCZqIKPm14BIPJk2YNW+cVn8Jm0NIriaM2tze/5bnUIb4HUFYCvo8EbqPnTGy7y7D72lfuhwN7dIiOVPgRLcDaaFbAn6ht2E2c2eEWG0WC8KkFKegfcGOk/RvNAY4BfRe5iofRtaQC751ZSmz/9pe07zelb525KMhj8WPhSweeTwJ5bSsq/Y2vbS9ja3mfNp1Uc0RdKgvpeTE5CV9Q7twgWD0EBZF8GvsvkAxo85y2STGT3ulfTkvUj+yzNhKIDarE9FfMYN5237OCk3mXO8f0k5weXPdCY96vF/ZUX80F12K2M8PBbn/xSWNpjnjFeYUr1he1Yx6WoJo5i636j5yhBDfzUcprJOAXgE3YPG03l3PYHnUOY2bKQ0qbHhOqr9o6Lh1KVXo7gCUS3bWNa0p2rSc0VVbtp/p6NuWywJCVLMaM9nkN1kPKDAufU)
##############################################################################
jinkosolar.jip.url = http://*************:8000/JIP
jinkosolar.jip.clientId = APP112
jinkosolar.jip.jwtKey = SCP-5WN8DAK0
jinkosolar.jip.jwtSecret = ENC(hF1qaO07ZijZ6nVkARsjRLqapEg0/DMxdXvistz+HC5sgtd3UnhdfotZlNhSYh+XzcOOfT/I3DQ=)
jinkosolar.jip.version = 2.0.0

jinkosolar.asprova.url = http://************:8081
asprova.db.url = **********************************************************
asprova.db.user = SA
asprova.db.password = Jk@123

rocketmq.name-server = ************:32003
rocketmq.producer.group=scp-mps-api
##############################################################################
repeat.login=1
templateId=309615
security.oauth2.client.client-id = scp-mps-api
security.oauth2.client.client-secret = ENC(1fzpccKwlqsxokWYfFCKGCHXTTGF7L0aQuoJTc/72QJWPLVn3IjOxnRJ9RZ0Z0bQ)
oauth.url = http://oauth.jinkosolar.com
security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
security.oauth2.resource.preferTokenInfo = false
security.oauth2.client.scope = read,write,trust
##############################################################################
oss.io.accessKey = ENC(/atjLAH8zAFqj2CwOhHbyrCJFm9Qv7MN83S9TLWMhbY2NTI5ceh2Bw==)
oss.io.bucket: scp-test
oss.io.endpoint: http://************:9001
oss.io.secretKey: ENC(5V+qZujLJYQ/2nnIWofRZPZf1FD+JH8bx+88msegS3LCkoEeXz8ZsUT0Wifr79T/VavW+i8Bxxd5wJjnUt/wjA==)
oss.io.allowFiles: .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,.doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.aac,.mp4
oss.io.expireSecond: 60
##############################################################################
scp.super.token = 1
jinkosolar.env = sat
env.gateway.url = https://scp-gateway-sat.jinkosolar.com
env.page.url = https://scp-sat.jinkosolar.com

jinkosolar.file.fileUrl = https://scp-gateway-sat.jinkosolar.com/scp-system-api/file/download/