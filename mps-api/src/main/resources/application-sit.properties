spring.application.name = scp-mps-api
##############################################################################
spring.redis.host = ************
spring.redis.port = 32007
spring.redis.password = ENC(i3M53UbyZqgqiDg/iFD+xvf6l0hin1QsPE74vuBx+nM=)
spring.datasource.druid.url = ENC(Z04U3mMCYcDakLhvFjxOFE+iDrMUzQeenlillJAGcx7vuk3iV16N+tZAG7vvtVcXYiLAkSGvcvcN/b/2pUxgqD/YrlL7vnzXw6vxgKRPwsRTmADTvdWeHLRgPsvMIJJ5q4gFX4p9xEU6AxBPyGy0nHUoZzL+wACxBvK0ykVKcV58ev8IR1Iv5fIcb1EvLYDO2V47VWQB3Dr/1+uQdA7qsz1PtFhYTJF/19NgXijkNjcrXfWQyQVOay8qJIN3VGzcBfNoHr+ZcGHtmun21pzwJ44lRfr06ZK3qmMOB4JlkXp7+8LdiNCibGD6HdDyFwDC2FFLH6GLFD783RKADE4srZWy2DwtCL1HWjyFWDVyqjcpg6G3yRyXahReilNjsVqGbSjhJyxKtRoFpnSHUagfraSFNe+X0Pvk3t0Eycn/ypFonOF5xmeCxDzva0ygcOR2cpFmiWXNXw+iAvVJiN1/bQFa8gUqNptbjlZjE9cKo3ekGFXTlY03GnyzDDHm8Mi5BAtHQNdkV+1k3wRfL7PtIuw36hHQvptMFaQx3RBNctc=)
##############################################################################
jinkosolar.jip.url = http://*************:8000/JIP
jinkosolar.jip.clientId = APP112
jinkosolar.jip.jwtKey = SCP-5WN8DAK0
jinkosolar.jip.jwtSecret = ENC(FcngedQXM/+GCCIqdeSm2QvVnuGFyaQR3+ShHk9/9Yi3F7MCwyEv6QwmEpsFvbAqCl8sPk2Ra50=)
jinkosolar.jip.version = 6.5.0

jinkosolar.asprova.url = http://************:8081
asprova.db.url = ***********************************************************
asprova.db.user = SA
asprova.db.password = Jk@123

rocketmq.name-server = ************:32003
rocketmq.producer.group=scp-mps-api
##############################################################################
repeat.login=1
templateId=309615
security.oauth2.client.client-id = scp-mps-api
security.oauth2.client.client-secret = ENC(Ff/6mvMRp61gB/K2dLmSmrWIf3pxFpEJ206H/ugyG4MJts6asDjVincw0tZI6AYD)
oauth.url = http://oauth.jinkosolar.com
security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
security.oauth2.resource.preferTokenInfo = false
security.oauth2.client.scope = read,write,trust
##############################################################################
oss.io.accessKey = ENC(zZx7/Dp0nPPFc7sI6RM9uCZ6RamlLHIfX+COYa4ZtK7AZozTcljs9w==)
oss.io.bucket: scp-test
oss.io.endpoint: http://************:9001
oss.io.secretKey: ENC(honcTdIX0/p5qxeXue/EFadV0uJSUB3MgHjKz3uG4IcNJa+kZyg/IWlJVZGq/YjNf9OnXQwK3EgKKuYu9QWVuA==)
oss.io.allowFiles: .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,.doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.aac,.mp4
oss.io.expireSecond: 60
##############################################################################
scp.super.token = 1
jinkosolar.env = sit
env.gateway.url = https://scp-gateway-sit.jinkosolar.com