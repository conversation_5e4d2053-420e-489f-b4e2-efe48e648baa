spring.application.name = scp-mps-api
##############################################################################
spring.redis.host = ************
spring.redis.port = 32007
spring.redis.database=14
spring.redis.password = ENC(i3M53UbyZqgqiDg/iFD+xvf6l0hin1QsPE74vuBx+nM=)
spring.datasource.druid.url = ENC(5QNfb5oWnwAd9H8Eb50eJUFGvcWy0ktUBJLaFmfhLWh34/VQNa+u+hh1FWYvZafv88Or+yeLzecHekMBS7NpvMGHeyFCuXM3U8HTjZdq8q5AT1kV1E6e8SD2Ie0T0REnG20wbG515NCjtefr3vPAGancpPpsm+hwAV16r995jy0o4Jr0rrJIes0PEEbyz72JsN6/1CWrtzXBanAXRgH4AqfjV7OmPsTyhDY5q1uSJkx3ptNjK5Nlo0IYPRd7erLY9TLe+Ay2OIB8jsST9jwBUxjA4Dz3r3ciNuewnsSESWzTKduMceF4MgGuokIHNelmy/IMhhJfu3Qk9PzSr+UjLeDNAcJbuyPdOnlv/54NoozBVjkiLcYwE5Y4OpwzBxsKCFu8+MTKny+fsVdrdAnMHe/iACZCA9ChyDX/s7s2o6ScJi3y4Gv1TY4mVzVuHi0BGiyKnl7/ud4aJsQAUGnJcGaqM7cCee/XwnWCvXTRzO7JrqumzrDGwjJJebVUbcquR8hDH6rCwpywiObdW6YcztkpmmOwB8mMunvvEJYC7aEwXfKGQse/zG9XV1nJeMYTl8p3GgVnDE8ibdiEr4aJbmj8DVxpqOKv)
##############################################################################
jinkosolar.jip.url = http://*************:8000/JIP
jinkosolar.jip.clientId = APP112
jinkosolar.jip.jwtKey = SCP-5WN8DAK0
jinkosolar.jip.jwtSecret = ENC(FcngedQXM/+GCCIqdeSm2QvVnuGFyaQR3+ShHk9/9Yi3F7MCwyEv6QwmEpsFvbAqCl8sPk2Ra50=)
jinkosolar.jip.version = 6.5.0

jinkosolar.asprova.url = http://************:8081
asprova.db.url = ***********************************************************
asprova.db.user = SA
asprova.db.password = Jk@123

rocketmq.name-server = ************:32003
rocketmq.producer.group=scp-mps-api
##############################################################################
repeat.login=1
templateId=309615
security.oauth2.client.client-id = scp-mps-api
security.oauth2.client.client-secret = ENC(Ff/6mvMRp61gB/K2dLmSmrWIf3pxFpEJ206H/ugyG4MJts6asDjVincw0tZI6AYD)
oauth.url = http://oauth.jinkosolar.com
security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
security.oauth2.resource.preferTokenInfo = false
security.oauth2.client.scope = read,write,trust
##############################################################################
oss.io.accessKey = ENC(zZx7/Dp0nPPFc7sI6RM9uCZ6RamlLHIfX+COYa4ZtK7AZozTcljs9w==)
oss.io.bucket: scp-test
oss.io.endpoint: http://************:9001
oss.io.secretKey: ENC(honcTdIX0/p5qxeXue/EFadV0uJSUB3MgHjKz3uG4IcNJa+kZyg/IWlJVZGq/YjNf9OnXQwK3EgKKuYu9QWVuA==)
oss.io.allowFiles: .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,.doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.aac,.mp4
oss.io.expireSecond: 60
##############################################################################
scp.super.token = 1
jinkosolar.env = sit
