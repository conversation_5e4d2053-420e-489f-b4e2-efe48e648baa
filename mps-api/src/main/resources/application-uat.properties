##############################################################################
spring.redis.host = ************
spring.redis.port = 32007
spring.redis.database= 12
spring.redis.password = ENC(XUcxS+nIPpu48Z5fDmgiSQhWbW0xJpDxGjKG3sTtEbg=)
spring.datasource.druid.url = ENC(QHQGm+7b63I6htnQLNv8BDJsWDQsSSLlNJLCnnwMQwCWNHxo9tVP0ULXfRXKobb0XjvZXc7zkWMZR4O/KmAs6vcX9oGPSS7jUX9ngGUZ+ZfQxId6J1qBFA0QelFCeGl8RjBkMKtD6SM/33o0wwtvMWWh3Bfb/q9KNvG9oflxrCcYQgOiA47vZH2agoh/FJ88wX7Ncoj0/Oke/vmrASVFZDi0lb/2Ua7892+o2i0e3D5yxhHxmIynXcj4caMO8Xnz8GjVe8RsPurp7QTNztPDkhJBOBrv+BUITGbBlM7wxNP3zl6XkkCgo3NaeFE9uiZTIh2Oic4jPNU0ODF+yqWU8h1e+mOssiTUNkLy3yJ8chVCupX4UvUxtcPXCBrgMW+PEHuF7PYo58FuTvCMGbYXFIsc/lfDNF9nFidA25u7v/S+hrVggduLYj4M8iscAKMw2el8G0FDVaG7gb3JfipYaP/yEmJpf5UFCNgD34juOUIAJzf1Dt6T7Dp6bvUI3A8A4pthdYKf90ElaMAF7P3uxU+kpedUJGFgom1Yl42U3Jr5u+51l4hf2A==)
##############################################################################
jinkosolar.jip.url = http://*************:8000/JIP
jinkosolar.jip.clientId = APP112
jinkosolar.jip.jwtKey = SCP-5WN8DAK0
jinkosolar.jip.jwtSecret = ENC(5+Ii3sp8TzmRhkKbEtgpGhxb4xHs4vBmYspCYKf8WHQPkXcA/zLEzrOekpAD1pih7yF4/Wn+Hs4=)
jinkosolar.jip.version = 2.0.0

jinkosolar.asprova.url = http://************:8081
asprova.db.url = **********************************************************
asprova.db.user = SA
asprova.db.password = Jk@123

rocketmq.name-server = ************:32003
rocketmq.producer.group=scp-mps-api
##############################################################################
repeat.login=1
templateId=309615
security.oauth2.client.client-id = scp-mps-api
security.oauth2.client.client-secret = ENC(1fzpccKwlqsxokWYfFCKGCHXTTGF7L0aQuoJTc/72QJWPLVn3IjOxnRJ9RZ0Z0bQ)
oauth.url = http://oauth.jinkosolar.com
security.oauth2.resource.userInfoUri = ${oauth.url}/oauthserver/me
security.oauth2.client.accessTokenUri = ${oauth.url}/oauthserver/oauth/token
security.oauth2.client.userAuthorizationUri = ${oauth.url}/oauthserver/oauth/authorize
security.oauth2.resource.preferTokenInfo = false
security.oauth2.client.scope = read,write,trust
##############################################################################
oss.io.accessKey = ENC(/atjLAH8zAFqj2CwOhHbyrCJFm9Qv7MN83S9TLWMhbY2NTI5ceh2Bw==)
oss.io.bucket: scp-test
oss.io.endpoint: http://************:9001
oss.io.secretKey: ENC(5V+qZujLJYQ/2nnIWofRZPZf1FD+JH8bx+88msegS3LCkoEeXz8ZsUT0Wifr79T/VavW+i8Bxxd5wJjnUt/wjA==)
oss.io.allowFiles: .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,.doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.aac,.mp4
oss.io.expireSecond: 60
##############################################################################
scp.super.token = 1
jinkosolar.env = uat
env.gateway.url = https://scp-gateway-uat.jinkosolar.com
