spring.profiles.active = jdbc,cloud,redis
metadata.name = mps-api
metadata.namespace = sit-environment
management.endpoint.health.enabled=true
spring.session.store-type = none
spring.jpa.show-sql = true
##############################################################################
feign.client.config.default.connectTimeout = 6000000
feign.client.config.default.readTimeout = 6000000

scan.base.package = com.jinkosolar.scp
entity.jpa.package = com.jinkosolar.scp
entity.scan.package = com.jinkosolar.scp
#jasypt?????
jasypt.encryptor.password= $RFV5tgb
jasypt.encryptor.algorithm= PBEWithMD5AndDES
EUREKA_SERVER = ********************************************/eureka/

########## ??StatViewServlet???????????Druid????? ##########
# ??StatViewServlet
spring.datasource.druid.stat-view-servlet.enabled=true
# ??????????????????????/druid/index.html
spring.datasource.druid.stat-view-servlet.url-pattern=/druid\/*
# ?????????,????
spring.datasource.druid.stat-view-servlet.reset-enable=false
# ???????????
spring.datasource.druid.stat-view-servlet.login-username=admin
# ??????????
spring.datasource.druid.stat-view-servlet.login-password=admin
# ??????????allow????????????????
#spring.datasource.druid.stat-view-servlet.allow=127.0.0.1
# ????????deny???allow????deny???????allow?????????
#spring.datasource.druid.stat-view-servlet.deny=
########## ??WebStatFilter?????web??????? ##########
# ?? StatFilter
spring.datasource.druid.web-stat-filter.enabled=true
# ????url
spring.datasource.druid.web-stat-filter.url-pattern=\/*
# ????????url
spring.datasource.druid.web-stat-filter.exclusions=/druid\/*,*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico
# ??session????
spring.datasource.druid.web-stat-filter.session-stat-enable=true
# session?????,??1000
spring.datasource.druid.web-stat-filter.session-stat-max-count=1000
spring.datasource.druid.initial-size=8
spring.datasource.druid.min-idle=8
spring.datasource.druid.max-active=50
# ?????????????
spring.datasource.druid.max-wait=60000
# ???????????????????????????????
spring.datasource.druid.time-between-eviction-runs-millis=60000
# ??????????????????????
spring.datasource.druid.min-evictable-idle-time-millis=30000
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
# ??PSCache??????????PSCache???
spring.datasource.druid.pool-prepared-statements=true
spring.datasource.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.druid.filters=stat,wall
# Spring ?????aop ???????????jdbc?????
#spring.datasource.druid.aop-patterns= com.admin.controller.*,com.front.controller.*
# ??????
#spring.datasource.druid.remove-abandoned=true
#spring.datasource.druid.remove-abandoned-timeout-millis=600000
#spring.datasource.druid.log-abandoned=true

logging.level.com.netflix=warn
logging.level.org.apache.rocketmq=warn