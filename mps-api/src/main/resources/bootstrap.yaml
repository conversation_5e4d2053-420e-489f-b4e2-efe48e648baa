eureka:
  instance:
    prefer-ip-address: true
    metadata-map:
      user.name: ${spring.security.user.name}
      user.password: ${spring.security.user.password}
      management.context-path: ${server.servlet.context-path}/actuator
      security.enabled: ${spring.security.enabled:false}
      router.attribute: ${spring.router.attribute:}
    health-check-url-path: /actuator/health
    status-page-url-path: /actuator/info
  client:
    serviceUrl:
      defaultZone: ${eureka:********************************************/eureka/}
    registryFetchIntervalSeconds: 10
    disable-delta: true
spring:
  lifecycle:
    timeout-per-shutdown-phase: 120s
  main:
    allow-bean-definition-overriding: true
  application:
    name: scp-mps-api
  zipkin:
    locator:
      discovery:
        enabled: true
    enabled: true
    sender:
      type: web
  cloud:
    kubernetes:
      client:
        trust-certs: true
        namespace: ${KUBERNETES_ENV:dev-environment}
      config:
        enabled: true
        namespace: ${spring.cloud.kubernetes.client.namespace}
        name: ${spring.application.name:${KUBERNETES_APP:}}
      reload:
        enabled: false
  security:
    endpoints: info1
    user:
      name: ${USER_NAME:admin}
      password: ${PASSWORD:G2ABnnRz1Ydzk5hS}
server:
  port: 7012
  shutdown: graceful
  compression:
    enabled: false
  servlet:
    context-path: /${spring.application.name}
management:
  endpoint:
    health:
      show-details: always
    restart:
      enabled: true
  endpoints:
    jmx:
      exposure:
        include: "*"
    web:
      exposure:
        include: "*"