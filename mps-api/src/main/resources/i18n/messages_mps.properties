sys.error.fileId.notNull=\u6587\u4EF6ID\u4E0D\u80FD\u4E3A\u7A7A
sys.error.bizKey.notBlank=\u4E1A\u52A1\u4E3B\u952E\u4E0D\u80FD\u4E3A\u7A7A
sys.error.bizType.notBlank=\u4E1A\u52A1\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
sys.error.fileType.notSupport=\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301
mps.fullProduction.conventional.export.fileName=\u5E38\u89C4\u6EE1\u4EA7\u4EA7\u80FD
mps.fullProduction.special.export.fileName=\u7279\u6B8A\u6EE1\u4EA7\u4EA7\u80FD
mps.error.importingWait=\u6B63\u5728\u5BFC\u5165\u4E2D,\u8BF7\u7A0D\u7B49
mps.error.umatchedProcessStep=\u5DE5\u6BB5\u5FC5\u987B\u662F\u62C9\u6676\u6216\u8005\u5207\u7247
mps.error.dataException=\u6570\u636E\u5F02\u5E38
mps.error.productionFamilyErr=\u4EA7\u54C1\u65CF\u7684\u503C\u9519\u8BEF---{0}
mps.error.installTypeErr=\u6A2A\u7AD6\u88C5\u7684\u503C\u9519\u8BEF---{0}
mps.error.isOverseaErr=\u56FD\u5185/\u6D77\u5916\u7684\u503C\u9519\u8BEF---{0}
mps.error.typeErr=\u7C7B\u578B\u7684\u503C\u9519\u8BEF---{0}
mps.error.startDateErr=\u5F00\u59CB\u65E5\u671F\u7684\u503C\u9519\u8BEF---
mps.error.completeDateErr=\u7ED3\u675F\u65E5\u671F\u7684\u503C\u9519\u8BEF---
mps.error.emptyFieldAttriValueErr=\u5B57\u6BB5\u5C5E\u6027\u503C\u4E3A\u7A7A
mps.error.attriErr=\u5C5E\u6027\u9519\u8BEF
mps.error.atrriOfFiledErr={0}\u5B57\u6BB5\u5C5E\u6027\u503C\u5B58\u5728\u9519\u8BEF--->{1}
mps.error.sameWorkCenterAndPlanVersionCodeAndMainGridIdAndSupplyUsa=\u5DE5\u4F5C\u4E2D\u5FC3+\u7248\u578B+\u4E3B\u6805\u6570+\u662F\u5426\u4F9B\u7F8E \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u786E\u8BA4
mps.error.sameWorkCenterAndPlanVersionCodeAndMainGridIdWithDetail=\u540C\u5DE5\u4F5C\u4E2D\u5FC3{0}+\u540C\u7248\u578B{1}+\u540C\u4E3B\u6805\u91CD\u590D{2}\uFF0C\u8BF7\u68C0\u67E5
mps.error.multiDataInExcelWithDetial=excel\u4E2D\u5B58\u5728\u91CD\u590D\u6570\u636E\uFF08\u8BA4\u8BC1\u5206\u7C7B:{0}/\u8BA4\u8BC1\u56FD\u5BB6:{1}/\u4EA7\u54C1\u65CF:{2}\uFF09
mps.error.umatchedCat=\u8BA4\u8BC1\u5206\u7C7B\u8F93\u5165\u4E0D\u5339\u914D
mps.error.emptyCat=\u8BA4\u8BC1\u5206\u7C7B\u4E0D\u53EF\u4E3A\u7A7A
mps.error.unmatchedCertificationCountry=\u8BA4\u8BC1\u56FD\u5BB6\u8F93\u5165\u4E0D\u5339\u914D
mps.error.emptyCertificationCountry=\u8BA4\u8BC1\u56FD\u5BB6\u4E0D\u53EF\u4E3A\u7A7A
mps.error.umatchedProductionFamily=\u4EA7\u54C1\u65CF\u8F93\u5165\u4E0D\u5339\u914D
mps.error.emptyProductionFamily=\u4EA7\u54C1\u65CF\u4E0D\u53EF\u4E3A\u7A7A
mps.error.certificationStartGearErr=\u8BA4\u8BC1\u8D77\u59CB\u6863\u4F4D\u8F93\u5165\u6709\u8BEF
mps.error.certificationStartGearTooLong=\u8BA4\u8BC1\u8D77\u59CB\u6863\u4F4D\u8F93\u5165\u957F\u5EA6\u8FC7\u957F
mps.error.emptyCertificationStartGear=\u8BA4\u8BC1\u8D77\u59CB\u6863\u4F4D\u4E0D\u53EF\u4E3A\u7A7A
mps.error.certificationEndGearErr=\u8BA4\u8BC1\u7EC8\u6B62\u6863\u4F4D\u8F93\u5165\u6709\u8BEF
mps.error.certificationEndGearTooLong=\u8BA4\u8BC1\u7EC8\u6B62\u6863\u4F4D\u8F93\u5165\u957F\u5EA6\u8FC7\u957F
mps.error.emptyCertificationEndGear=\u8BA4\u8BC1\u7EC8\u6B62\u6863\u4F4D\u4E0D\u53EF\u4E3A\u7A7A
mps.error.moduleTypeSaveErr=\u7EC4\u4EF6\u7248\u578B\u4FDD\u5B58\u5F02\u5E38
mps.error.productModelNoExist=\u4EA7\u54C1\u578B\u53F7\u4E0D\u5B58\u5728
mps.error.duplicateInSameWorkshopOrFactory=\u6570\u636E\u9519\u8BEF:\u540C\u4E00\u4E2A\u5DE5\u4F5C\u4E2D\u5FC3\u8F66\u95F4\u6216\u5DE5\u5382\u4E0D\u80FD\u91CD\u590D
mps.error.multiData=\u5DF2\u5B58\u5728\u91CD\u590D\u7684\u6570\u636E
mps.error.workShopInfoNoExist=\u672A\u67E5\u8BE2\u5230\u8F66\u95F4\u5BF9\u5E94\u7684\u7EC4\u7EC7\u4FE1\u606F
mps.error.multiWorkShopInfo=\u8F66\u95F4\u5BF9\u5E94\u7684\u7EC4\u7EC7\u4FE1\u606F\u67E5\u8BE2\u5230\u591A\u4E2A\uFF0C\u8BF7\u786E\u8BA4
mps.error.lovForWorkShopIdNotFound=\u8F66\u95F4\u7F16\u7801\u672A\u627E\u5230lov\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4
mps.error.processNoExist=\u5DE5\u5E8F\u4E0D\u5B58\u5728, value=
mps.error.matchedItemNumButMapNoExsit=\u6599\u53F7\u5339\u914D.\u6620\u5C04\u5173\u7CFB\u4E0D\u5B58\u5728
mps.error.arrtCodeNotMaintained=\u6392\u4EA7\u5C5E\u6027ATTR_CODE\u672A\u7EF4\u62A4
mps.error.matchedItemNumNotSelected=\u5339\u914D\u6599\u53F7\u672A\u9009\u62E9
mps.error.publishedCantModify=\u5DF2\u53D1\u5E03,\u4E0D\u5141\u8BB8\u4FEE\u6539
mps.error.umatchedItemNum=\u5B58\u5728\u672A\u5339\u914D\u6599\u53F7\u6570\u636E,\u4E0D\u80FD\u53D1\u5E03
mps.error.emptyFactorySet=\u5DE5\u5382\u96C6\u5408\u4E0D\u80FD\u4E3A\u7A7A
mps.error.emptySourceType=\u6765\u6E90\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
mps.error.duplicateExcel=Excel\u5B58\u5728\u91CD\u590D
mps.error.headerSumLessThanOne=\u6548\u7387\u503C\u6807\u9898\u76F8\u52A0\u9700\u8981\u5927\u4E8E1
mps.error.dataExistAddFail=\u65B0\u589E\u5931\u8D25\uFF0C\u6570\u636E\u5DF2\u5B58\u5728
mps.error.dataExistUpdateFail=\u66F4\u65B0\u5931\u8D25\uFF0C\u6570\u636E\u5DF2\u5B58\u5728
mps.error.productionfamilyInputErr=\u4EA7\u54C1\u65CF\u8F93\u5165\u6709\u8BEF
mps.error.emptyDpID=dpID\u4E3A\u7A7A
mps.error.emptyPower=\u529F\u7387\u4E3A\u7A7A
mps.error.duplicateDataInImportFile=\u5BFC\u5165\u7684\u6587\u4EF6\u4E2D\u5B58\u5728\u91CD\u590D\u6570\u636E
mps.error.convertLovFacotryFail=\u8F6C\u6362Lov\u5DE5\u5382\u5931\u8D25!
mps.error.convertLovWorkCenterFail=\u8F6C\u6362Lov\u5DE5\u4F5C\u4E2D\u5FC3\u5931\u8D25!
mps.error.convertLovCellSizeFail=\u8F6C\u6362Lov\u7535\u6C60\u5C3A\u5BF8\u5931\u8D25!
mps.error.convertLovMainGridNumFail=\u8F6C\u6362Lov\u4E3B\u6805\u6570\u5931\u8D25!
mps.error.convertLovCrystalTypeFail=\u8F6C\u6362Lov\u6676\u4F53\u7C7B\u578B\u5931\u8D25!
mps.error.convertLovSealTypeFail=\u8F6C\u6362Lov\u5355\u53CC\u5C01\u5931\u8D25!
mps.error.convertLovCellProductionTypeFail=\u8F6C\u6362Lov\u7535\u6C60\u4EA7\u54C1\u7C7B\u578B\u5931\u8D25!
mps.error.convertLovWeldingFail=\u8F6C\u6362Lov\u7269\u63A7\u6307\u5B9A\u710A\u63A5\u5931\u8D25!
mps.error.convertLovBusBarFail=\u8F6C\u6362Lov\u7269\u63A7\u6307\u5B9A\u6C47\u6D41\u6761\u5931\u8D25!
mps.error.convertLovRearCoverTypeFail=\u8F6C\u6362Lov\u5355\u53CC\u73BB SYS.REAR_COVER_TYPE\u5931\u8D25!
mps.error.dataErr=\u6570\u636E\u5B58\u5728\u95EE\u9898
mps.error.invalidDPGroupId=DPGroupId\u5B57\u6BB5\u4E3A\u4E00\u957F\u4E32\u6574\u6570\uFF0C\u8BF7\u68C0\u67E5\u6570\u636E\u683C\u5F0Fa'a
mps.error.duplicateData=\u4EE5\u4E0B\u6570\u636E\u5B58\u5728\u91CD\u590D : {0}
mps.error.duplicateDataInExcel=Excel\u4E2D\u5B58\u5728\u91CD\u590D\u6570\u636E\uFF0C\u8BF7\u68C0\u67E5!
mps.error.invalidMonth=\u6708\u4EFD\u7684\u503C\u9519\u8BEF---{0},\u65E5\u671F\u6848\u4F8B 202212
mps.error.excelHeaderNoExist=Excel\u8868\u5934[{0}]\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u5BFC\u5165\uFF01
mps.error.invalidDate=\u4E0D\u652F\u6301\u7684\u65E5\u671F\u683C\u5F0F:
mps.error.valErr=\u9519\u8BEF\u7684\u6570\u5B57:
mps.error.factoryAndWorkCenterAndVersionAndYearNoUnique=\u5DE5\u5382+\u5DE5\u4F5C\u4E2D\u5FC3+\u7248\u672C+\u5E74\u4EFD \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u786E\u8BA4
mps.error.dataNull=\u6570\u636E\u4E3Anull
mps.error.inventoryOrganizationErr=\u5E93\u5B58\u7EC4\u7EC7\u7684\u503C\u9519\u8BEF---{0}
mps.error.erpOfInventoryOrganizationErr=\u5E93\u5B58\u7EC4\u7EC7\u5BF9\u5E94 erp\u7684\u503C\u4E0D\u5B58\u5728---{0}
mps.error.productTypeErr=\u4EA7\u54C1\u7C7B\u578B\u7684\u503C\u9519\u8BEF---{0}
mps.error.basePlaceErr=\u751F\u4EA7\u57FA\u5730\u7684\u503C\u9519\u8BEF---{0}
mps.error.workShopErr=\u751F\u4EA7\u8F66\u95F4\u7684\u503C\u9519\u8BEF---{0}
mps.error.workUnitErr=\u751F\u4EA7\u5355\u5143\u7684\u503C\u9519\u8BEF---{0}
mps.error.cellTypeErr=\u7535\u6C60\u7C7B\u578B\u7684\u503C\u9519\u8BEF---{0}
mps.error.supplyTypeErr=\u4F9B\u5E94\u7C7B\u578B\u7684\u503C\u9519\u8BEF---{0}
mps.error.supplierErr=\u4F9B\u5E94\u65B9\u7684\u503C\u9519\u8BEF---{0}
mps.error.statusErr=\u72B6\u6001\u7684\u503C\u9519\u8BEF---{0}
mps.error.noDataInTheMonth={0}\u6708\u6682\u65E0\u6570\u636E\uFF01
mps.error.workshopNameAndIsOverseaNameNoUnique=\u552F\u4E00\u6027\u6821\u9A8C \u8F66\u95F4:  {0}  \u56FD\u5185/\u6D77\u5916:  {1}  \u9519\u8BEF
mps.error.workCenterAndMaintenaceAreaAndFromTimeAndToTimeNoUnique=\u5DE5\u4F5C\u4E2D\u5FC3+\u533A\u57DF+\u5F00\u59CB\u65F6\u95F4+\u7ED3\u675F\u65F6\u95F4 \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u786E\u8BA4
mps.error.businessTypeErr=\u573A\u666F\u7684\u503C\u9519\u8BEF---{0}
mps.error.typeToConvertErr=\u8F6C\u6362\u7C7B\u578B\u7684\u503C\u9519\u8BEF---{0}
mps.error.attriAffectedByPowerErr=\u529F\u7387\u5F71\u54CD\u5C5E\u6027\u7684\u503C\u9519\u8BEF---{0}
mps.error.moduleTypeNoExist=\u8BA1\u5212\u7248\u578B\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
mps.error.productionAllNonePositive=\u81F3\u5C11\u6709\u4E00\u4E2A\u6708\u4EA7\u80FD\u6570\u636E\u5927\u4E8E0
mps.error.noDataToSend=\u6682\u65E0\u6570\u636E\u53EF\u53D1\u9001
mps.error.emptyBeginMonth=\u5F00\u59CB\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
mps.error.emptyEndMonth=\u7ED3\u675F\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
mps.error.planModuleTypeNotFound={0}\u8BA1\u5212\u7248\u578B\u672A\u627E\u5230
mps.error.noPowerPredict=DP{0}\u7F3A\u5931\u529F\u7387\u9884\u6D4B
mps.error.DPSyncFail=\u6B63\u5728\u540C\u6B65DP,{0}\u7248\u672C\u4FDD\u5B58\u5931\u8D25,\u8BF7{1}\u5206\u949F\u540E\u91CD\u8BD5
mps.error.emptyMaterialProperty=\u6750\u6599\u5C5E\u6027\u4E0D\u53EF\u4E3A\u7A7A
mps.error.emptyAttri=\u5C5E\u6027\u503C\u4E0D\u53EF\u4E3A\u7A7A
mps.error.sameCellProductAndNumberMainGrids=\u7B2C{0}\u884C\u6570\u636E\uFF0C\u5DF2\u5B58\u5728\u76F8\u540C\u7684\u7535\u6C60\u4EA7\u54C1\u53CA\u4E3B\u6805\u6570\uFF0C\u8BF7\u786E\u8BA4
mps.error.cantFindWithLov=Lov:{0},\u67E5\u8BE2\u4E0D\u5230{1}
mps.error.createFileFail=\u521B\u5EFA\u6587\u4EF6\u540D{0}\u6587\u4EF6\u5931\u8D25
mps.error.unmatchedDpSapOrderAndLineId=\u9500\u552E\u8BA2\u5355({0})+\u9500\u552E\u8BA2\u5355\u884C({1}),\u672A\u5339\u914D\u5230DP\u8BA2\u5355\u6570\u636E
mps.error.facotryAndWorkCenterAndPlanVersionAndLineBodyAndWorkYearAndWorkMonthNoUnique=\u5DE5\u5382{0}+\u5DE5\u4F5C\u4E2D\u5FC3{1}+\u7248\u578B{2}+\u7EBF\u4F53{3}+\u5E74\u4EFD{4}+\u6708\u4EFD{5} \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u68C0\u67E5
mps.error.workCenterAndAttendanceModeAndWorkMonthNoUnique=\u5DE5\u4F5C\u4E2D\u5FC3{0}+\u51FA\u52E4\u6A21\u5F0F{1}+\u6708\u4EFD{2} \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u68C0\u67E5
mps.error.multipleData=\u4EE5\u4E0B\u6570\u636E\u5B58\u5728\u91CD\u590D : {0},\u4E0D\u80FD\u91CD\u590D\u5BFC\u5165
mps.error.multipleDataInDatabase=\u6570\u636E\u5E93\u4E2D,\u4EE5\u4E0B\u6570\u636E\u5B58\u5728\u91CD\u590D : {0},\u4E0D\u80FD\u91CD\u590D\u5BFC\u5165
mps.error.cableLengthErr=\u7EBF\u7F06\u957F\u5EA6\u7684\u503C\u9519\u8BEF---{0},\u8BF7\u68C0\u67E5\u6570\u636E\u662F\u5426\u4E0Elov\u4E2D\u7EF4\u62A4\u7684\u540D\u79F0\u5B57\u6BB5\u4E00\u81F4
mps.error.projectPlaceErr=\u9879\u76EE\u5730\u7684\u503C\u9519\u8BEF---{0},\u8BF7\u68C0\u67E5\u6570\u636E\u662F\u5426\u4E0Elov\u4E2D\u7EF4\u62A4\u7684\u540D\u79F0\u5B57\u6BB5\u4E00\u81F4
mps.error.dataVersionErr=\u7248\u672C\u53F7\u7684\u503C\u9519\u8BEF---{0},\u8BF7\u68C0\u67E5\u6570\u636E\u662F\u5426\u4E0Elov\u4E2D\u7EF4\u62A4\u7684\u540D\u79F0\u5B57\u6BB5\u4E00\u81F4
mps.error.installTypeErr1=\u6A2A\u7AD6\u88C5\u7684\u503C\u9519\u8BEF---{0},\u8BF7\u68C0\u67E5\u6570\u636E\u662F\u5426\u4E0Elov\u4E2D\u7EF4\u62A4\u7684\u540D\u79F0\u5B57\u6BB5\u4E00\u81F4
mps.error.productionFamilyErrWithDetail=\u4EA7\u54C1\u65CF\u7684\u503C\u9519\u8BEF---{0},\u8BF7\u68C0\u67E5\u6570\u636E\u662F\u5426\u4E0Elov\u4E2D\u7EF4\u62A4\u7684\u540D\u79F0\u5B57\u6BB5\u4E00\u81F4
mps.error.powerChangeRuleNotFound=\u6839\u636E\u4EA7\u54C1\u65CF\u65E0\u6CD5\u83B7\u53D6\u5230\u529F\u7387\u53D8\u5316\u89C4\u5219,\u65E0\u6CD5\u66F4\u65B0\u8D77\u59CB\u529F\u7387---{0}
mps.error.DOErr=DO\u5B57\u6BB5\u7684\u503C\u9519\u8BEF---{0}
mps.error.powerNormalDistributionNotFound=\u6839\u636E\u7535\u6C60\u7C7B\u578B{0},\u6708\u4EFD{1}\u6CA1\u6709\u83B7\u53D6\u5230\u6B63\u6001\u5206\u5E03\u6570\u636E
mps.error.waitForCalc={0}\u6708\u6B63\u5728\u8BA1\u7B97\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\u6570\u636E\uFF0C\u8BF7\u5148\u7B49\u5F85\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\u6570\u636E\u8BA1\u7B97\u5B8C\u6210\u518D\u8FDB\u884C\u529F\u7387\u9884\u6D4B
mps.error.calcingPower={0}\u6708\u6B63\u5728\u8BA1\u7B97\u529F\u7387\u9884\u6D4B\uFF0C\u8BF7\u4E0D\u8981\u91CD\u590D\u8BA1\u7B97
mps.error.dpDataNotFound=\u6839\u636E\u65E5\u671F{0}\u672A\u83B7\u53D6\u5230dp\u5355\u6570\u636E,\u8BF7\u5148\u540C\u6B65DP\u5355
mps.error.emptyMonthWithDpGroupId=dpGroupId{0},\u6392\u4EA7\u65E5\u671F\u4E0D\u80FD\u4E3A\u7A7A
mps.error.powerResultShortNotFoundWithDpGroupIdAndProductFamilyAndInstallTypeAndCableLengthAndComponentSize=\u6839\u636EdpGroupId{0}\u3001\u4EA7\u54C1\u65CF{1}\u3001\u6A2A\u7AD6\u88C5{2}\u3001\u7EBF\u7F06\u957F\u5EA6{3}\u3001\u7EC4\u4EF6\u5C3A\u5BF8{4},\u672A\u83B7\u53D6\u5230\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\u6570\u636E,\u8BF7\u57FA\u7840\u6863\u4F4D,\u5E76\u751F\u6210\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\u6570\u636E
mps.error.powerResultShortNotFoundWithVersion=\u4F20\u5165\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\u7248\u672C{0}\u83B7\u53D6\u4E0D\u5230\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\u6570\u636E
mps.error.calcNormalDistributioinDataOrCellDataFirst=dpGroupId{0}\u3001\u7535\u6C60\u7C7B\u578B{1}\u672A\u83B7\u53D6\u5230\u6B63\u6001\u5206\u5E03\u6570\u636E,\u8BF7\u5148\u8BA1\u7B97\u6B63\u6001\u5206\u5E03\u6570\u636E\u6216\u7535\u6C60\u6570\u636E
mps.error.cellTypeNotGenerated=dpLineId{0}\u6CA1\u6709\u751F\u6210\u7535\u6C60\u7C7B\u578B
mps.error.predictResultVerNotGenerated={0}\u6708\u4EFD\u6CA1\u6709\u8FD8\u6CA1\u6709\u751F\u6210\u9884\u6D4B\u7ED3\u679C\u7248\u672C
mps.error.monthNotSelected=\u8BF7\u5148\u9009\u62E9\u6708\u4EFD
mps.error.archiveFlagErr=\u7B2C{0}\u884C,\u5F52\u6863\u6807\u8BC6\u4E0D\u6B63\u786E
mps.error.unmatchedCellSeries=\u7B2C{0}\u884C,\u672A\u5339\u914D\u5230\u4EA7\u54C1\u7CFB\u5217
mps.error.unmatchedMaterialCombination=\u7B2C{0}\u884C,\u672A\u5339\u914D\u5230\u6750\u6599\u642D\u914D\u7EC4\u5408
mps.error.unmatchedCellString=\u7B2C{0}\u884C,\u672A\u5339\u914D\u5230\u7247\u4E32
mps.error.emptyMonth=\u8BA1\u7B97\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\uFF0C\u5FC5\u987B\u9009\u62E9\u6708\u4EFD
mps.error.calcingPowerShortConst={0}\u6708\u6B63\u5728\u8BA1\u7B97\u6708\u5EA6\u57FA\u7840\u6863\u4F4D\uFF0C\u8BF7\u4E0D\u8981\u91CD\u590D\u8BA1\u7B97
mps.error.cableLengthFormatErr={0}\u7EBF\u7F06\u957F\u5EA6\u683C\u5F0F\u6709\u8BEF
mps.error.duplicateDataInExcelWithScheduleMonthAndExpiredMonthAndEffectAttributeAndAStandard=excel\u4E2D\u5B58\u5728\u91CD\u590D\u6570\u636E\uFF08\u6392\u4EA7\u65E5\u671F:{0}/\u8FC7\u671F\u65E5\u671F:{1}/\u529F\u7387\u5F71\u54CD\u5C5E\u6027:{2}/\u6807\u51C6:{3}\uFF09
mps.error.noDataInTheVersion=\u5F53\u524D\u7248\u672C\u6CA1\u6709\u6570\u636E
mps.error.addDuplicateDimData=\u4E0D\u5141\u8BB8\u6DFB\u52A0\u91CD\u590D\u7EF4\u5EA6\u6570\u636E
mps.error.updateDuplicateDimData=\u6570\u636E\u5E93\u5DF2\u5B58\u5728\u76F8\u540C\u7EF4\u5EA6\u6570\u636E,\u4E0D\u5141\u8BB8\u4FEE\u6539
mps.error.duplicateCellSeriesDescAndMaterialCombinationDescAndCellString=\u5F53\u524D\u5BFC\u5165\u6570\u636E\u5B58\u5728\u91CD\u590D\u6570\u636E\uFF0C\u4EA7\u54C1\u7CFB\u5217\u63CF\u8FF0\u3001\u6750\u6599\u642D\u914D\u7EC4\u5408\u63CF\u8FF0\u3001\u7247\u4E32
mps.erro.totalSumMismatchErr=\u603B\u8BA1\u4E0E\u5404\u6708\u76F8\u52A0\u603B\u6570\u4E0D\u7B49
mps.error.crystalNoFound=\u5728\u3010MPS.PROCESS_ID\u3011\u4E0B\u672A\u627E\u5230\u3010\u62C9\u6676\u3011\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4
mps.error.waferNoFound=\u5728\u3010MPS.PROCESS_ID\u3011\u4E0B\u672A\u627E\u5230\u3010\u5207\u7247\u3011\u6570\u636E\uFF0C\u8BF7\u786E\u8BA4
mps.error.sourceTypeError=\u6765\u6E90\u7C7B\u578B\u9519\u8BEF
mps.error.factory.data.loss=\u5DE5\u5382\u6570\u636E\u53D1\u5E03\u4E0D\u5168
mps.error.properties.not.empty=\u5C5E\u6027\u540D\u4E0D\u80FD\u4E3A\u7A7A
mps.error.properties.not.found=\u5C5E\u6027\u540D\u4E0D\u5B58\u5728
mps.error.properties.incorrect=\u5C5E\u6027\u540D\u4E0D\u6B63\u786E
mps.error.factory.noWorkCenter=\u6B64\u5DE5\u5382\u4E0B\u6CA1\u6709\u5DE5\u4F5C\u4E2D\u5FC3:
mps.error.workshop.noWorkCenter=\u6B64\u8F66\u95F4\u4E0B\u6CA1\u6709\u5DE5\u4F5C\u4E2D\u5FC3:
mps.error.foldFactor.repeat=\u7248\u672C+\u4EA7\u54C1+\u6676\u68D2\u5206\u7C7B+\u7269\u6599\u7F16\u7801+\u6570\u636E\u5206\u7C7B \u5DF2\u5B58\u5728
mps.error.perUnit.repeat=\u7248\u672C+\u5DE6\u4EA7\u54C1\u578B\u53F7+\u53F3\u4EA7\u54C1\u578B\u53F7+\u6570\u636E\u5206\u7C7B \u5DF2\u5B58\u5728
mps.error.perUnit.allBlank=5\u4E2A\u767E\u5206\u6BD4\u4E0D\u80FD\u90FD\u662F\u7A7A
mps.error.hearth.repeat=\u7248\u672C+\u5DE5\u4F5C\u4E2D\u5FC3+\u4EA7\u54C1+\u65E5\u671F+\u5B9A\u5411/\u975E\u5B9A\u5411+\u6570\u636E\u7C7B\u578B+\u7845\u6599\u4F9B\u5E94\u5546 \u5DF2\u5B58\u5728
mps.error.crystalUsedNum.repeat=\u7248\u672C+\u5DE5\u5382+\u5DE5\u4F5C\u4E2D\u5FC3+N/P\u578B+\u5B9A\u5411/\u975E\u5B9A\u5411 \u5DF2\u5B58\u5728
mps.error.retentionDeduction.repeat=\u4EA7\u54C1+\u662F\u5426\u5916\u4E70+\u8F66\u95F4+\u70ED\u573A+\u7559\u57DA\u7387+\u5E74 \u5DF2\u5B58\u5728
mps.error.retentionDeduction.month=\u6708\u4EFD\u6570\u636E\u81F3\u5C11\u8981\u6709\u4E00\u4E2A\u6708\u4EFD\u4E0D\u7B49\u4E8E0
mps.error.data.exist=\u5DF2\u5B58\u5728\u76F8\u540C\u6570\u636E
mps.error.mustNotEmpty=\u5FC5\u586B\u9879\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u8BF7\u786E\u8BA4
mps.error.equipment.noWorkCenter=\u6B64\u7089\u53F0\u4E0E\u5DE5\u4F5C\u4E2D\u5FC3\u4E0D\u5339\u914D
mps.error.equipment.dataConflict=\u6570\u636E\u51B2\u7A81
mps.error.notFindData=\u672A\u627E\u5230\u6570\u636E!
mps.error.modelTypeNotNull=\u6A21\u578B\u7C7B\u578B\u4E0D\u53EF\u4E3A\u7A7A\uFF01
mps.error.factoryIdNotNull=\u5DE5\u5382\u4E0D\u53EF\u4E3A\u7A7A\uFF01
mps.error.schedulingStartTimeNotNull=\u5F00\u59CB\u65F6\u95F4\u4E0D\u53EF\u4E3A\u7A7A\uFF01
mps.error.schedulingEndTimeNotNull=\u7ED3\u675F\u65F6\u95F4\u4E0D\u53EF\u4E3A\u7A7A\uFF01
mps.error.dateRangeError=\u65F6\u95F4\u8303\u56F4\u9519\u8BEF\uFF01
mps.error.tooManyDays=\u65F6\u95F4\u8303\u56F4\u4E0D\u53EF\u8D85\u8FC7365\u5929\uFF01
mps.error.directionalIdNotNull=\u5B9A\u5411/\u975E\u5B9A\u5411\u4E0D\u53EF\u4E3A\u7A7A\uFF01
mps.error.notNull=\u4E0D\u80FD\u4E3A\u7A7A
mps.error.domestic.oversea=\u6392\u4EA7\u533A\u57DF
mps.error.cell.type=\u7535\u6C60\u7C7B\u578B
mps.error.primary.gateNumber=\u4E3B\u6805\u6570
mps.error.thickness=\u539A\u5EA6
mps.error.vendor.brand=\u4F9B\u5E94\u5546\u54C1\u724C
mps.error.vendor.stall=\u4F9B\u5E94\u5546\u6863\u4F4D
mps.error.delivery.area=\u5230\u8D27\u533A\u57DF
mps.error.packageType=\u5305\u88C5\u65B9\u5F0F
mps.error.data.not.match=\u672A\u5339\u914D\u5230\u76F8\u5173\u6570\u636E
mps.error.send.factory=\u53D1\u8D27\u5DE5\u5382
mps.error.receive.factory=\u6536\u8D27\u5DE5\u5382
mps.error.direction=\u5B9A\u5411/\u975E\u5B9A\u5411
mps.error.efficiency=\u7535\u6C60\u6548\u7387
mps.selfBatteryAllotPlan.export.name=\u81EA\u4EA7\u7535\u6C60\u8C03\u62E8\u8BA1\u5212
mps.error.notNumber=\u4E0D\u662F\u6570\u503C
mps.error.not.contains.percent=\u5FC5\u987B\u5E26%
mps.assignBasePriority.export.name=\u5206\u914D\u57FA\u5730\u4F18\u5148\u7EA7\u522B
mps.modulePlanAmendmentReport.export.name=\u7EC4\u4EF6\u751F\u4EA7\u8BA1\u5212\u6539\u5355\u660E\u7EC6\u62A5\u8868
mps.powerPredictionDemandOccupancy.export.name=\u5404\u5C3A\u5BF8\u5E93\u5B58\u63A8\u79FB
mps.exportCellProductPurchase.export.name=\u81EA\u4EA7\u7535\u6C60\u53D1\u8D27\u9700\u6C42
mps.exportCellOverseaPurchase.export.name=\u7535\u6C60\u5916\u8D2D\u9700\u6C42
mps.powerPredictionCellAllocation.export.name=\u7535\u6C60\u5206\u914D\u7ED3\u679C
mps.product.plan.vendor=\u4F9B\u5E94\u5546
mps.product.plan.vendor.desc=\u63CF\u8FF0
mps.error.vendorBrandAndFromFactoryIdAndAreaId=\u4F9B\u5E94\u5546\u54C1\u724C\u3001\u53D1\u51FA\u5DE5\u5382\u4EE3\u7801\u3001\u63A5\u6536\u6392\u4EA7\u533A\u57DF\u5B58\u5728\u76F8\u540C\u6570\u636E
mps.error.supplyInput.error=\u5F53\u8865\u6295\u65B9\u5F0F\u4E3A\u3010\u975E\u8FDE\u7EED\u751F\u4EA7\u3011\u65F6\uFF0C\u8865\u6295\u6570\u91CF\u8981\u5927\u4E8E0
mps.error.saleOrderLine.error=\u9500\u552E\u8BA2\u5355\u884C\u53F7[{0}]\u4F4D\u6570\u4E0D\u7B49\u4E8E10\u4F4D
mps.stockTransferDirectional.export.name=\u81EA\u4EA7\u7535\u6C60\u8C03\u62E8\u8BA1\u5212\u5B9A\u5411\u975E\u5B9A\u5411\u8F6C\u6362
mps.error.empty.workshop=\u8F66\u95F4\u6570\u636E\u4E3A\u7A7A
mps.powerPredictionSupply.executeSave.efficiencyError.name=\u7EC4\u4EF6\u6392\u4EA7\u8BA1\u5212\u5B58\u5728\u65E0\u6807\u51C6\u7535\u6C60\u6548\u7387\u503C
mps.error.running.task=\u4EFB\u52A1\u6B63\u5728\u6267\u884C\u4E2D,\u8BF7\u52FF\u91CD\u590D\u64CD\u4F5C
mps.error.rgbError=\u9519\u8BEF\u7684RGB\u683C\u5F0F
mps.powerPredictionDemandOccupancy.exportDetail.name=\u5404\u5C3A\u5BF8\u5E93\u5B58\u63A8\u79FB\u9700\u6C42\u660E\u7EC6
mps.stockLocationInfo.saveOrUpdate.exists.error=\u5DE5\u5382\u4EE3\u7801_ERP\u5E93\u5B58\u5730\u70B9_\u7535\u6C60\u4EA7\u54C1\u4E0D\u5141\u8BB8\u91CD\u590D
mps.error.factoryNoExist=\u5DE5\u5382[{0}]\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4
mps.error.factoryRelationAddress=\u5DE5\u5382[{0}]\u5173\u8054\u4E86\u591A\u4E2A\u533A\u57DF\uFF0C\u8BF7\u786E\u8BA4
mps.supplyInput.delete.status.error = \u5DF2\u540C\u610F\u7684\u884C\u4E0D\u5141\u8BB8\u5220\u9664
mps.error.conflict.with.excel=\u7B2C[{0}]\u884C\u4E0E\u7B2C[{1}]\u884C\u6570\u636E\u91CD\u590D
mps.error.value.invalid=\u503C\u65E0\u6548
mps.error.conflict.with.db=\u7B2C[{0}]\u884C\u7684[{1}]\u5217\u7684\u6570\u636E\u4E0E\u6570\u636E\u5E93\u51B2\u7A81
mps.standardPreUnitClimb.export.name=\u56FD\u5185\u8001\u57FA\u5730\u6807\u51C6\u5355\u4EA7\u722C\u5761
mps.error.data.type=\u6570\u636E\u5206\u7C7B
mps.error.workshop=\u8F66\u95F4
mps.error.crucible.provider=\u5769\u57DA\u5382\u5BB6
mps.error.crucible.class=\u5769\u57DA\u7B49\u7EA7
mps.error.product.type=\u4EA7\u54C1\u7C7B\u578B
mps.error.material.vendor=\u7845\u6599\u5382\u5546
mps.error.sales.area=\u9500\u552E\u533A\u57DF
mps.error.crystal.type=\u9AD8\u4F4E\u963B
mps.error.directional=\u662F\u5426\u5B9A\u5411
sys.error.sourceVersion.notBlank=\u57FA\u51C6\u7248\u672C\u53F7\u4E0D\u80FD\u4E3A\u7A7A
sys.error.targetVersion.notBlank=\u76EE\u6807\u7248\u672C\u53F7\u4E0D\u80FD\u4E3A\u7A7A
mps.error.NoExist=\u503C\u4E0D\u5B58\u5728

mps.title.dataType.plan=\u8BA1\u5212
mps.title.dataType.actual=\u5B9E\u9645
mps.title.dataType.diff=\u5DEE\u5F02
mps.title.total=\u5408\u8BA1
mps.title.waferPlanYear=\u5207\u7247\u5E74\u5EA6\u8FBE\u6210\u8868
mps.slice.production.plan.report.export.name=\u5207\u7247\u4EA7\u91CF\u8DDF\u8FDB\u62A5\u8868

mps.import.pre.unit.climb.center.factory=\u6839\u636E\u5DE5\u4F5C\u4E2D\u5FC3\u7F16\u7801\u4E2D\u5FC3\u672A\u627E\u5230\u5DE5\u5382
mps.import.pre.unit.climb.factory=\u6839\u636E\u5DE5\u4F5C\u4E2D\u5FC3\u7F16\u7801\u627E\u5230\u7684\u5DE5\u5382\u503C\u65E0\u6548
mps.import.pre.unit.climb.center.area=\u6839\u636E\u5DE5\u4F5C\u4E2D\u5FC3\u7F16\u7801\u4E2D\u5FC3\u672A\u627E\u5230\u6392\u4EA7\u533A\u57DF
mps.import.pre.unit.climb.area=\u6839\u636E\u5DE5\u4F5C\u4E2D\u5FC3\u7F16\u7801\u627E\u5230\u7684\u6392\u4EA7\u533A\u57DF\u503C\u65E0\u6548
mps.nonModuleOperatingRate.export.name=\u975E\u7EC4\u4EF6\u5F00\u5DE5\u7387\u57FA\u7840\u6570\u636E

mps.volumeProcurement.export.name=\u5546\u52A1\u5916\u53D1\u6392\u7A0B\u91CF
mps.volumeScheduling.export.name=\u7EFC\u5408\u91C7\u8D2D\u62DB\u6807\u91CF
mps.mps-crystal-silicon-supply.export-name=\u62C9\u6676\u7845\u6599\u4F9B\u5E94
mps.sliceChamferSwitchPlan.export.name=\u62C9\u6676\u5012\u89D2\u5207\u6362\u8BA1\u5212
mps.longTermTurnaroundDays.export.name=\u4E2D\u957F\u671F\u5B89\u5168\u5468\u8F6C\u5929\u6570
mps.error.data.already.exist=\u6570\u636E\u5DF2\u7ECF\u5B58\u5728
mps.longTermBatteryLossRatio.export.name=\u4E2D\u957F\u671F\u7535\u6C60\u635F\u8017\u6BD4\u4F8B
mps.longTermBatteryLossRatio.checkRatio=\u635F\u8017\u6BD4\u4F8B\u4E0D\u80FD\u5927\u4E8E1
mps.longTermPlanForDays.export.name=\u4E2D\u957F\u671F\u6BCF\u6708\u89C4\u5212\u5929\u6570
mps.longTermPlanForDays.days.all.empty=\u5929\u6570\u4E0D\u80FD\u5168\u90E8\u4E3A\u7A7A

mps.title.subTotal=\u5C0F\u7ED3
mps.error.row.conflict.with.db=\u7B2C[{0}]\u884C\u7684\u6570\u636E\u4E0E\u6570\u636E\u5E93\u51B2\u7A81

mps.crystalPalnYearCrucibleRate.export.name=\u5E74\u5EA6\u5769\u57DA\u9884\u7B97\u6BD4\u4F8B
mps.crystalProductSwticPlan.export.name=\u5DE5\u5355\u5207\u6362\u8BA1\u5212
mps.crystalProductFineTuning.export.name=\u5355\u4EA7\u5FAE\u8C03

mps.process.limitation.repeat=\u5DE5\u5382\u3001\u5DE5\u4F5C\u4E2D\u5FC3\u3001\u533A\u57DF\u6570\u636E\u5DF2\u7ECF\u5B58\u5728
mps.error.workCenter=\u5DE5\u4F5C\u4E2D\u5FC3


mps.error.factoryCodeAndWorkCenterNameAndAttendanceCodeNoUnique=\u5DE5\u5382{0} \u5DE5\u4F5C\u4E2D\u5FC3{1} \u4EA7\u7EBF\u603B\u6570{2} \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u68C0\u67E5
mps.error.factoryCodeAndWorkCenterNameAndAttendanceCodeAndAttendanceDateNoUnique=\u5DE5\u5382{0} \u5DE5\u4F5C\u4E2D\u5FC3{1} \u4EA7\u7EBF\u603B\u6570{2} \u65E5\u671F{3} \u6570\u636E\u4E0D\u552F\u4E00\uFF0C\u8BF7\u68C0\u67E5
mps.error.modelTypeEmpty=\u6A21\u578B\u5206\u7C7B\u4E0D\u80FD\u4E3A\u7A7A
mps.error.modelTypeNoCenter=\u6839\u636E\u6A21\u578B\u5206\u7C7B\u672A\u627E\u5230\u5DE5\u4F5C\u4E2D\u5FC3
mps.error.modelTypeNoOversea=\u6839\u636E\u6A21\u578B\u5206\u7C7B\u672A\u627E\u5230\u533A\u57DF
mps.error.overseaNoData=\u672A\u627E\u5230\u533A\u57DF{0}\u5BF9\u5E94\u7684\u9700\u6C42\u6570\u636E

mps.title.average.efficiency=\u5E73\u5747\u6548\u7387
mps.error.modelTypeNoDataType=\u6839\u636E\u6A21\u578B\u5206\u7C7B\u672A\u627E\u5230\u5BF9\u5E94\u7684\u6570\u636E\u5206\u7C7B\uFF0C\u8BF7\u914D\u7F6E
mps.error.noHotFieldSwitchingPlan=\u672A\u627E\u5230\u6570\u636E\u7C7B\u578B[{0}]\u70ED\u573A\u5207\u6362\u8BA1\u5212\uFF0C\u8BF7\u786E\u8BA4
mps.error.ruleNumNoEq=\u722C\u5761\u6570\u91CF\u548C\u914D\u65B9\u5207\u6362\u6570\u91CF\u4E0D\u76F8\u7B49
mps.error.ruleSplitNoNumber=\u672A\u627E\u5230\u722C\u5761\u89C4\u5219\u62C6\u5206\u6570\u91CF,\u5DE5\u4F5C\u4E2D\u5FC3:[{0}],\u65E5\u671F:[{1}],\u7248\u672C:[{2}]
mps.error.noRule=\u672A\u627E\u5230\u722C\u5761\u89C4\u5219[{0}]

mps.error.requestIdNotFound=RequestId:{0}\u5728SCP\u7CFB\u7EDF\u4E0D\u5B58\u5728
mps.error.material.purchase.status=\u53EA\u6709\u5F85\u4F20\u9001\u6216\u8005\u5BA1\u6279\u62D2\u7EDD\u72B6\u6001\u7684\u5355\u636E\u624D\u80FD\u91CD\u65B0\u63D0\u4EA4
mps.error.standardLifeNumError=\u6807\u51C6\u5BFF\u547D\u8F6C\u6362\u540E\u4E0D\u80FD\u5C0F\u4E8E0,\u8BF7\u68C0\u67E5\u6570\u636E
mps.error.sapOrderNoAndSapLineIdAnd=\u9500\u552E\u8BA2\u5355\u53F7[{0}]\u3001\u884C\u53F7[{1}]\u3001\u5DE5\u5382\u4EE3\u7801[{2}]\u5728\u9700\u6C42\u7BA1\u7406\u4E2D\u4E0D\u5B58\u5728\uFF0C\u8BF7\u786E\u8BA4

mps.error.unmatchedProductType=\u672A\u5339\u914D\u5230\u4EA7\u54C1
mps.error.sendProductionSuggestionRequest=\u751F\u4EA7\u5EFA\u8BAE\u4E0B\u53D1\u5931\u8D25
mps.error.sendProductionSuggestionRequest.dto=\u5DE5\u4F5C\u4E2D\u5FC3\u548C\u6570\u91CF\u4E0D\u80FD\u4E3A\u7A7A
mps.error.sendProductionSuggestionRequest.overseaQty=\u6570\u91CF\u4E4B\u548C\u4E0D\u80FD\u5927\u4E8E\u8BA1\u5212\u6570\u91CF

mps.error.crystalBar.length=\u65B9\u68D2\u6807\u51C6\u957F\u5EA6\u4E3A0
mps.error.thickness.rate=\u5207\u7247\u539A\u5EA6\u826F\u7387\u6CA1\u6709\u7EF4\u62A4
mps.nonPlan.query.valid=\u672A\u627E\u5230\u6B64\u5DE5\u4F5C\u4E2D\u5FC3\u3010{0}\u3011\u4E0B\u5BF9\u5E94\u7684\u660E\u7EC6\u6570\u636E

mps.productSwitchConfig.export.name=\u4EA7\u54C1\u5207\u6362\u8BA1\u5212\u7EF4\u62A4

mps.cellForecastColorRate.export.name=\u7535\u6C60\u4EA7\u51FA\u9884\u6D4B\u989C\u8272\u5360\u6BD4
mps.error.factory.code=\u5DE5\u5382\u7F16\u7801
mps.error.color=\u989C\u8272

mps.cell.type.pn.compare.report.export.name=PN\u578B\u7535\u6C60\u4EA7\u51FA\u5BF9\u6BD4\u62A5\u8868
mps.cell.forecast.version.compare.report.export.name=\u7535\u6C60\u4EA7\u51FA\u9884\u6D4B\u7248\u672C\u5BF9\u6BD4\u62A5\u8868

mps.error.wafer.preUnit.notExist=\u4EA7\u54C1\u826F\u7387\u4FE1\u606F\u4E0D\u5B58\u5728
mps.error.plan.data.notExist=\u6392\u4EA7\u6570\u636E\u4E0D\u5B58\u5728
mps.error.convertLovFrontGlassThicknessFail=\u8f6c\u6362Lov\u6b63\u73bb\u539a\u5ea6 ATTR_TYPE_005_ATTR_1500\u5931\u8d25\uff01
mps.error.convertLovCableLengthFail=\u8f6c\u6362Lov\u7ebf\u7f06\u957f\u5ea6 MPS.CABLE_LENGTH\u5931\u8d25\uff01
mps.error.convertLovBusBarSpecFail=\u8f6c\u6362Lov\u6c47\u6d41\u6761\u89c4\u683c ATTR_TYPE_009_ATTR_2000\u5931\u8d25\uff01

mrp.slicePlanShiftDTOList.export.name=\u56fd\u5185\u5207\u7247\u6392\u4ea7\u63a8\u79fb