sys.error.fileId.notNull=file id not null
sys.error.bizKey.notBlank=business key not null
sys.error.bizType.notBlank=business type not null
sys.error.fileType.notSupport=file type not support
mps.fullProduction.conventional.export.fileName=Conventional full production
mps.fullProduction.special.export.fileName=Special full production
mps.error.crystalNoFound=Unable to find data for \u3010crystal\u3011 pulling under \u3010MPS.PROCESS_ID\u3011. Please confirm
mps.error.waferNoFound=Unable to find data for \u3010wafer\u3011 pulling under \u3010MPS.PROCESS_ID\u3011. Please confirm
mps.error.sourceTypeError=Source type error
mps.error.factory.data.loss=Version data of factory missing
mps.error.properties.not.empty=Properties name can't be empty
mps.error.properties.not.found=Properties name not found
mps.error.properties.incorrect=Properties name is incorrect
mps.error.importingWait=importing in progress, please wait
mps.error.umatchedProcessStep=the processStep must be either CRYSTAL_GROWTH or WAFER_SLICING
mps.error.dataException=data exception
mps.error.productionFamilyErr=product family value error---{0}
mps.error.installTypeErr=wrong value for installType---{0}
mps.error.isOverseaErr=value error for domestic/overseas---{0}
mps.error.typeErr=wrong value for type---{0}
mps.error.startDateErr=wrong value for start date---
mps.error.completeDateErr=the value of the complete date is incorrect---
mps.error.emptyFieldAttriValueErr=the field attribute value is empty
mps.error.attriErr=attribute error
mps.error.atrriOfFiledErr=there is an error in the attribute value of the {0} field -->{1},
mps.error.sameWorkCenterAndPlanVersionCodeAndMainGridIdAndSupplyUsa=the data for work center, planVersionCode, mainGridIds and year and SupplyUsa is not unique. Please confirm
mps.error.sameWorkCenterAndPlanVersionCodeAndMainGridIdWithDetail=same workCenter {0}+same PlanVersionCode {1}+same MainGridId {2}, please check
mps.error.multiDataInExcelWithDetial=there is duplicate data in Excel (certification category: {0}/certification country: {1}/product family: {2})
mps.error.umatchedCat=certification category input unmatched
mps.error.emptyCat=the certification category cannot be empty
mps.error.unmatchedCertificationCountry=certification country input unmatched
mps.error.emptyCertificationCountry=certification country cannot be empty
mps.error.umatchedProductionFamily=product family input unmatched
mps.error.emptyProductionFamily=product family cannot be empty
mps.error.certificationStartGearErr=certification start gear input error
mps.error.certificationStartGearTooLong=the input length of the certification start gear is too long
mps.error.emptyCertificationStartGear=the certification start gear cannot be empty
mps.error.certificationEndGearErr=certification termination gear input error
mps.error.certificationEndGearTooLong=certification termination gear input length too long
mps.error.emptyCertificationEndGear=the certification termination gear cannot be empty
mps.error.moduleTypeSaveErr=abnormal saving of component layout
mps.error.productModelNoExist=the product model does not exist
mps.error.duplicateInSameWorkshopOrFactory=data error: cannot be duplicated within the same work center workshop or factory
mps.error.multiData=duplicate data already exists
mps.error.workShopInfoNoExist=no corresponding organizational information found for the workshop
mps.error.multiWorkShopInfo=multiple organization information corresponding to the workshop has been queried, please confirm
mps.error.lovForWorkShopIdNotFound=workshop code not found LOV data, please confirm
mps.error.processNoExist=process does not exist, value=
mps.error.matchedItemNumButMapNoExsit=item number matching. mapping relationship does not exist
mps.error.arrtCodeNotMaintained=scheduling attribute ATTR-CODE not maintained
mps.error.matchedItemNumNotSelected=matching item number not selected
mps.error.publishedCantModify=published, modification not allowed
mps.error.umatchedItemNum=there is unmatched item number data and cannot be published
mps.error.emptyFactorySet=factory collection cannot be empty
mps.error.emptySourceType=source type cannot be empty
mps.error.duplicateExcel=excel has duplicates
mps.error.headerSumLessThanOne=the sum of efficiency value headers needs to be greater than 1
mps.error.dataExistAddFail=new addition failed, data already exists
mps.error.dataExistUpdateFail=new update failed, data already exists
mps.error.productionfamilyInputErr==product family input error
mps.error.emptyDpID=dpID is empty
mps.error.emptyPower=power is empty
mps.error.duplicateDataInImportFile=there is duplicate data in the imported file
mps.error.convertLovFacotryFail=conversion of Lov factory failed!
mps.error.convertLovWorkCenterFail=conversion of Lov work center failed!
mps.error.convertLovCellSizeFail=conversion of Lov cell size failed!
mps.error.convertLovMainGridNumFail=conversion of Lov main grid number failed!
mps.error.convertLovCrystalTypeFail=conversion of Lov crystal type failed!
mps.error.convertLovSealTypeFail=conversion of Lov single and double seal failed!
mps.error.convertLovCellProductionTypeFail=conversion of Lov cell production type failed!
mps.error.convertLovWeldingFail=conversion of Lov physical control specified welding failed!
mps.error.convertLovBusBarFail=conversion of Lov physical control specified bus bar failed!
mps.error.convertLovRearCoverTypeFail=convert Lov SYS REAR-COVER-TYPE failed!
mps.error.dataErr=there is an issue with the data
mps.error.invalidDPGroupId=the DPGroupId field is a long string of integers, please check the data format
mps.error.duplicateData=the following data is duplicated: {0}, errList
mps.error.duplicateDataInExcel=there is duplicate data in Excel, please check!
mps.error.invalidMonth=wrong value for month---{0}, date case 202212
mps.error.excelHeaderNoExist=excel header [{0}] does not exist and cannot be imported!
mps.error.invalidDate=unsupported date format:
mps.error.valErr=wrong number:
mps.error.factoryAndWorkCenterAndVersionAndYearNoUnique=factory+work center+version+year is not unique, please confirm
mps.error.dataNull=data is null
mps.error.inventoryOrganizationErr=inventory organization value error---{0}
mps.error.mps.error.erpOfInventoryOrganizationErr=the value of ERP corresponding to the inventory organization does not exist---{0}
mps.error.productTypeErr=the value of the product type is incorrect---{0}
mps.error.basePlaceErr=the value of the base place is incorrect---{0}
mps.error.workShopErr=value error in production workshop---{0}
mps.error.workUnitErr=the value of the production unit is incorrect---{0}
mps.error.cellTypeErr=the value of the cell type is incorrect---{0}
mps.error.supplyTypeErr=the value of supply type is incorrect---{0}
mps.error.supplierErr=the value of supplier type is incorrect---{0}
mps.error.statusErr=the value of status is incorrect---{0}
mps.error.noDataInTheMonth={0} month currently has no data!
mps.error.workshopNameAndIsOverseaNameNoUnique=uniqueness verification workshop: {0} Domestic/overseas: {1} Error
mps.error.workCenterAndMaintenaceAreaAndFromTimeAndToTimeNoUnique=the data for work center+maintenace area+from time+end time is not unique. please confirm
mps.error.businessTypeErr=the value of business type is incorrect---{0}
mps.error.typeToConvertErr=wrong value for conversion type---{0}
mps.error.attriAffectedByPowerErr=The value of the power impact attribute is incorrect---{0}
mps.error.moduleTypeNoExist=the planned module type not exist, please confirm
mps.error.productionAllNonePositive=at least one month's production capacity data is greater than 0
mps.error.noDataToSend=there is currently no data to send
mps.error.emptyBeginMonth=start date cannot be empty
mps.error.emptyEndMonth=end date cannot be empty
mps.error.planModuleTypeNotFound=[{0}]module type not found
mps.error.noPowerPredict=DP{0} missing power prediction
mps.error.DPSyncFail=syncing DP, {0} version save failed. Please try again in {{0} minutes
mps.error.emptyMaterialProperty=material properties cannot be empty
mps.error.emptyAttri=attribute value cannot be empty
mps.error.sameCellProductAndNumberMainGrids=the data in line {0} already contains the same battery product and number of main grids. Please confirm
mps.error.cantFindWithLov=Lov: {0}, unable to find {1}
mps.error.createFileFail=Failed to create file name {0} file
mps.error.unmatchedDpSapOrderAndLineId=DpSapOrder ({0})+DpSapLineId({1}), not matching DP order data
mps.error.facotryAndWorkCenterAndPlanVersionAndLineBodyAndWorkYearAndWorkMonthNoUnique=The data for factory Id {0}+work center Id {1}+plan version code {2}+line body {3}+work year {4}+work month {5} is not unique. Please check
mps.error.workCenterAndAttendanceModeAndWorkMonthNoUnique=work center{0}+attendance mode{1}+work month{2} is not unique. Please check
mps.error.multipleData=The following data has duplicates: {0}, cannot be imported repeatedly
mps.error.multipleDataInDatabase=in the database, the following data has duplicates: {0}, cannot be imported repeatedly
mps.error.cableLengthErr=the value of cable length is incorrect---{0}. please check if the data matches the name field maintained in lov
mps.error.projectPlaceErr=the value of project place is incorrect---{0}. please check if the data matches the name field maintained in lov
mps.error.dataVersionErr=the value of data version is incorrect---{0}. please check if the data matches the name field maintained in lov
mps.error.installTypeErr1=the value of install type is incorrect---{0}. please check if the data matches the name field maintained in lov
mps.error.productionFamilyErrWithDetail=the value of production family is incorrect---{0}. please check if the data matches the name field maintained in lov
mps.error.powerChangeRuleNotFound=according to the product family, the power change rule cannot be obtained, and the begin power cannot be updated -- {0}
mps.error.DOErr=the value of the DO field is incorrect---{0}
mps.error.powerNormalDistributionNotFound=according to battery type {0}, no normal distribution data was obtained for month {1}
mps.error.waitForCalc={0} month is calculating monthly basic gear data. Please wait for the monthly basic gear data calculation to be completed before making power predictions
mps.error.calcingPower=The power forecast for month {0} is currently being calculated, please do not repeat the calculation
mps.error.dpDataNotFound=according to date {0}, no DP lines was obtained. please synchronize the DP lines
mps.error.emptyMonthWithDpGroupId=dpGroupId{0}, month cannot be empty
mps.error.powerResultShortNotFoundWithDpGroupIdAndProductFamilyAndInstallTypeAndCableLengthAndComponentSize=according to dpGroupId {0}, product family {1}, install type {2}, cable length {3}, and component size {4}, monthly basic gear data was not obtained. please base the gear and generate monthly basic gear data
mps.error.powerResultShortNotFoundWithVersion=unable to obtain monthly basic level data by passing in monthly basic level version {0}
mps.error.calcNormalDistributioinDataOrCellDataFirst=dpGroupId {0}, cell type {1} did not obtain normal distribution data. Please calculate normal distribution data or battery data first
mps.error.cellTypeNotGenerated=DpLineId {0} did not generate a battery type
mps.error.predictResultVerNotGenerated=the month {0} has not yet generated a prediction result version
mps.error.monthNotSelected=please select the month first
mps.error.archiveFlagErr=line {0}, incorrect archive flag
mps.error.unmatchedCellSeries=line {0}, cell series not matched
mps.error.unmatchedMaterialCombination=line {0}, no material combination matching found
mps.error.unmatchedCellString=line {0}, no cell string matching found
mps.error.emptyMonth=to calculate the monthly base level, a month must be selected
mps.error.calcingPowerShortConst={0} month is currently calculating the monthly base level, please do not double calculate
mps.error.cableLengthFormatErr=the length format of the {0} cable is incorrect
mps.error.duplicateDataInExcelWithScheduleMonthAndExpiredMonthAndEffectAttributeAndAStandard=there is duplicate data in Excel (schedule month: {0}/expired month: {1}/power impact attribute: {2}/standard: {3})
mps.error.noDataInTheVersion=there is no data in the current version
mps.error.addDuplicateDimData=adding duplicate dimension data is not allowed
mps.error.updateDuplicateDimData=the same dimension data already exists in the database and cannot be modified
mps.error.duplicateCellSeriesDescAndMaterialCombinationDescAndCellString=there is duplicate data in the current imported data, including cell series description, material combination description, and cell string
mps.erro.totalSumMismatchErr=the total is not equal to the total sum of each month
mps.error.factory.noWorkCenter=There is no work center in factory:
mps.error.workshop.noWorkCenter=There is no work center in workshop:
mps.error.foldFactor.repeat=Version+product+crystal rod classification+item code+data classification already exists
mps.error.perUnit.repeat=Version+left product model+right product model+data classification already exists
mps.error.perUnit.allBlank=5 percentages cannot all be empty
mps.error.hearth.repeat=Version+Work Center+Product+Date+Directed/Non Directed+Data Type+SiliconSupplier already exists
mps.error.crystalUsedNum.repeat=Version+factory+work center+N/P type+directional/non directional already exists
mps.error.retentionDeduction.repeat=Product+whether purchased externally+workshop+thermal field+pot retention rate+years of existence
mps.error.retentionDeduction.month=At least one month of monthly data must be equeal 0
mps.error.data.exist=Repeatable Data
mps.error.unmatchedProductType=Product type not matched
mps.error.domesticOverseaName.not.empty=Domestic Oversea can't be empty
mps.error.convertLovDomesticOverseaFail=conversion of Lov Domestic Oversea failed!
mps.error.factory.not.empty=Factory can't be empty
mps.error.workcenter.not.empty=Work center can't be empty
mps.error.workshop.not.empty=Work shop can't be empty
mps.error.linebody.not.empty=Line body can't be empty
mps.error.planVersion.not.empty=Plan version can't be empty
mps.error.workcenterAndProductionDateAndAttendanceAndPriorityAndResourceQuantityAndModelTypeNoUnique=Work center+Production Date+Attendance+Priority+Resource Quantity+Model Type already exists
mps.error.mustNotEmpty=Required fields cannot be empty, please confirm
mps.error.equipment.noWorkCenter=The device does not match the work center
mps.error.equipment.dataConflict=Data conflict
mps.error.notFindData=Data not found!
mps.error.modelTypeNotNull=The model type cannot be empty!
mps.error.factoryIdNotNull=The factory cannot be empty!
mps.error.schedulingStartTimeNotNull=The start time cannot be empty!
mps.error.schedulingEndTimeNotNull=The end time cannot be empty!
mps.error.dateRangeError=Time range error!
mps.error.tooManyDays=The time range cannot exceed 365 days!
mps.error.dataTypeAndFactoryIdAndWorkCenterIdAndYearNoUnique=DataType +FactoryCode+WorkCenter+Year No Unique
mps.error.dataTypeAndFactoryIdAndWorkCenterIdAndPlanVersionAndLineBodyNameNoUnique=DataType +FactoryCode+WorkCenter+PlanVersion+LineBodyName No Unique
mps.error.directionalIdNotNull=Directional/Non directional cannot be empty!
mps.error.workCenterCodeAndDataTypeAndFactoryCodeNoTrue=CenterCode And DataType And FactoryCode No True
mps.error.dataTypeAndFactoryIdAndWorkCenterIdAndYearAndModuleTypeNoUnique=DataType +FactoryCode+WorkCenter+Year+ModuleType No Unique
mps.error.moduleTypeNoFound=ModuleType  Not  Found
mps.error.notNull= Can't be empty
mps.error.domestic.oversea=Domestic Oversea
mps.error.cell.type=Cell Type
mps.error.primary.gateNumber=Primary GateNumber
mps.error.thickness=Thickness
mps.error.vendor.brand=Vendor Brand
mps.error.vendor.stall=Vendor Stall
mps.error.delivery.area=Delivery Area
mps.error.packageType=Package Type
mps.error.data.not.match= not found match data
mps.error.workCenterCodeAndProductTypeNameAndThicknessAndMachineNumAndStartTimeNoUnique=WorkCenterCode + ProductTypeName + Thickness + MachineNum + StartTime No Unique
mps.error.workCenterCodeAndProductTypeNameAndThicknessAndCycleAndCycleLatitudeAndIncreaseNoUnique=WorkCenterCode + ProductTypeName + Thickness + Cycle + CycleLatitude  + Increas No Unique
mps.error.workCenterCodeAndProductTypeNameAndThicknessAndPlanClimbMachineAndStartTimeNoUnique=WorkCenterCode + ProductTypeName + Thickness +PlanClimbMachine +StartTime No Unique
mps.error.send.factory=Send Factory
mps.error.receive.factory=Receive Factory
mps.error.direction=Direction
mps.error.efficiency=Efficiency
mps.error.attendanceModeAlreadyExist={0} AttendanceMode Already Exist
mps.selfBatteryAllotPlan.export.name=Self Battery Allot Plan
mps.error.notNumber= Not Numeric
mps.error.not.contains.percent= must contains %
mps.assignBasePriority.export.name=Assign base priorities
mps.modulePlanAmendmentReport.export.name=Component Production Plan Change Order Detail Report
mps.powerPredictionDemandOccupancy.export.name=Inventory transfer of various sizes
mps.error.workCenterNameAndProductTypeNameAndThicknessAndSizeAndStartTimeAndValidEndTimeAndPreUnit=workCenterName ProductTypeName Thickness Size StartTime ValidEndTime PreUnit AlreadyExist
mps.error.TheBenchmarkYieldIsLowerThanTheA+Yield=The benchmark yield is lower than the A+ yield  
mps.error.productionLineNumNotNull=ProductionLineNum Not Null
mps.error.attendanceCodeFalse= attendanceCode  False
mps.error.productionLineNumNoBigWorkNum=ProductionLineNum Not Big Than WorkNum
mps.error.moduleTypeNotMatch=ModuleType Not Match
mps.error.equipmentCodeAndWorkCenterCodeOptionalMandatory=EEquipmentCode WorkCenterCode 2 Optional 1 Mandatory
mps.exportCellProductPurchase.export.name=Delivery demand for self-produced batteries
mps.exportCellOverseaPurchase.export.name=External purchase demand for batteries
mps.error.checkStatusSame=check Status is Same
mps.powerPredictionCellAllocation.export.name=Battery allocation results
mps.error.WorkNumNotZERO={0}WorkNum Not ZERO
mps.error.CapacityNotZERO={0}Climb capacity is not null
mps.error.CapacityExist=Climb capacity is not null
mps.product.plan.vendor=Supplier
mps.product.plan.vendor.desc=Desc
mps.error.vendorBrandAndFromFactoryIdAndAreaId=Vendor Brand, From Factory And Area Exist
mps.error.supplyInput.error=If supply input is Non-continuous production supply num must be greater than 0
mps.error.saleOrderLine.error=Sale order line [{0}] length not equal to 10
mps.stockTransferDirectional.export.name=
mps.error.empty.workshop=Workshop data is empty
mps.powerPredictionSupply.executeSave.efficiencyError.name=There are component production plans without standard battery efficiency values
mps.error.running.task=Schedule task is running,Please wait!
mps.error.workCenterCodeAndFactoryCodeAndProductionLineNumNoTrue=WorkCenterCode FactoryCode  ProductionLine Not part of the work center \uFF01
mps.error.switchTypeAndWorkCenterAndDataTypeAndProductAlreadyExist=SwitchType WorkCenter DataType Product Already Exist!
mps.error.rgbError=rgb format error
mps.powerPredictionDemandOccupancy.exportDetail.name=Inventory transfer of various sizes demand detail
mps.stockLocationInfo.saveOrUpdate.exists.error=Factory And erpStock And cellProduct exists
mps.error.factoryNoExist=The factory [{0}] does not exist, please confirm
mps.error.factoryRelationAddress=Factory [{0}] is associated with multiple regions, please confirm
mps.error.conflict.with.excel=The [{0}] row is the same as the [{1}] row
mps.error.value.invalid=The value is invalid
mps.error.conflict.with.db=The data in the [{1}] column of the \u7B2C[{0}] row conflicts with the database
mps.standardPreUnitClimb.export.name=Standard Pre Unit Climb
mps.error.data.type=Data Type
mps.error.workshop=Workshop
mps.error.crucible.provider=Crucible Provider
mps.error.crucible.class=Crucible Class
mps.error.product.type=Product Type
mps.error.material.vendor=Material Vendor
mps.error.sales.area=Sales Area
mps.error.crystal.type=Crystal Type
mps.error.directional=Directional
mps.error.NoExist=The value does not exist
mps.title.dataType.plan=Plan
mps.title.dataType.actual=Actual
mps.title.dataType.diff=Diff
mps.longTermTurnaroundDays.export.name=Safe Turnaround Days
mps.error.data.already.exist=The data already exists
mps.longTermBatteryLossRatio.export.name=Proportion Of Battery Loss
mps.longTermBatteryLossRatio.checkRatio=Loss Ratio Cannot Be Greater Than One
mps.longTermPlanForDays.export.name=Number of days per month for planning

mps.title.subTotal=SubTotal
mps.title.total=Total
mps.title.waferPlanYear=WaferPlanYearExcel
mps.slice.production.plan.report.export.name=SlicingProductionPlanReport

mps.import.pre.unit.climb.center.factory=The factory was not found in the center according to the work center code
mps.import.pre.unit.climb.factory=The factory value found based on the work center code is invalid
mps.import.pre.unit.climb.center.area=No scheduled area was found in the center based on the work center code
mps.import.pre.unit.climb.area=The value of the schedule area found based on the work center code is invalid
mps.nonModuleOperatingRate.export.name=Non Module Operating Rate Data

mps.volumeProcurement.export.name=Business outgoing scheduling
mps.volumeScheduling.export.name=Comprehensive procurement bidding volume

mps.mps-crystal-silicon-supply.export-name=Crystal silicon material supply
mps.sliceChamferSwitchPlan.export.name=Chamfer Switch Plan
mps.longTermPlanForDays.days.all.empty=The number of days cannot be all empty

mps.error.row.conflict.with.db=The data in the [{0}] row conflicts with the database

mps.crystalPalnYearCrucibleRate.export.name=Annual crucible budget ratio
mps.crystalProductSwticPlan.export.name=Ticket switching schedule
mps.crystalProductFineTuning.export.name=Yield fine-tuning

mps.process.limitation.repeat=Data Of Factory\u3001WorkCenter\u3001Area Already Exists
mps.error.workCenter=Work Center


mps.error.factoryCodeAndWorkCenterNameAndAttendanceCodeNoUnique=Factory Code{0} Work Center{1} AttendanceCode{2} data NoUnique
mps.error.factoryCodeAndWorkCenterNameAndAttendanceCodeAndAttendanceDateNoUnique=Factory Code{0} Work Center{1} AttendanceCode{2} AttendanceCode{3} data NoUnique
mps.error.modelTypeEmpty=Model classification cannot be empty
mps.error.modelTypeNoCenter=According to the model classification, no work center was found
mps.error.modelTypeNoOversea=Region not found based on model classification
mps.error.overseaNoData=No demand data found for region {0}

mps.title.average.efficiency=Average Efficiency
mps.error.modelTypeNoDataType=According to the model classification, no corresponding data classification was found. Please configure
mps.error.noHotFieldSwitchingPlan=Data type [{0}] thermal field switching plan not found, please confirm
mps.error.ruleNumNoEq=The number of uphill climbs and the number of recipe switches are not equal
mps.error.ruleSplitNoNumber=Number of climbing rule splits not found, Work Center: [{0}], Date: [{1}], Version: [{2}]
mps.error.noRule=Climbing rule not found [{0}]

mps.error.requestIdNotFound=RequestId {0} not found in SCP System
mps.error.material.purchase.status=Status must be Waiting or Rejected
mps.error.standardLifeNumError=After converting the standard lifespan, it cannot be less than 0. Please check the data
mps.error.sapOrderNoAndSapLineIdAnd=sapOrderNo[{0}]\u3001sapLineId[{1}]\u3001factoryCode[{2}] not In demand manage\uFF0Cplease confirm
mps.error.sendProductionSuggestionRequest=Failed to issue production suggestions
mps.error.sendProductionSuggestionRequest.dto=Work center and quantity cannot be empty
mps.error.sendProductionSuggestionRequest.overseaQty=The sum of quantities cannot be greater than the planned quantity

mps.error.crystalBar.length=The Length Of CrystalBar is Zero
mps.error.thickness.rate=The Rate Of Thickness Is Not Config
mps.nonPlan.query.valid=the corresponding detailed data under this work center [{0}] was not found

mps.productSwitchConfig.export.name=Product Switch Plan Config

mps.cellForecastColorRate.export.name=Color Rate Of Cell Forecast
mps.error.factory.code=Factory Code
mps.error.color=Color

mps.cell.type.pn.compare.report.export.name=Cell Type Of PN Report
mps.cell.forecast.version.compare.report.export.name=Version Compare Report Of CellForecast
mps.error.noDataForPlannedVersionNumber=Plan version number {0} has no data

mps.error.wafer.preUnit.notExist=The PreUnit Of Product Is Not Confined
mps.error.plan.data.notExist=The Plan Data Is Not Exist
mps.error.convertLovFrontGlassThicknessFail=Failed to convert Lov positive glass thickness ATTR_TYPE-005_TTR-1500!
mps.error.convertLovCableLengthFail=Convert Lov cable length MPS CABLE_LENGTH failed!
mps.error.convertLovBusBarSpecFail=Failed to convert Lov bus bar specification ATTR_TYPE-009_TTR-2000!
mps.error.machineNumCountOverNumberOfAvailableMachines=The number of machines in the {0} work center cannot exceed the number of available machines
mps.error.electricalPerformanceNull=The electrical performance of the data in line {0} is empty
mps.error.notExsitItemId=The item id does not exist, not publish
mps.error.electricalPerformance=ElectricalPerformance
mps.error.specifySupplier=SpecifySupplier
mps.powerPredictionSupply.executeSave.batchSavePowerPredictionDemand.error=No planned data available for calculation