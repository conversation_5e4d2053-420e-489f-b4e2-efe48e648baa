status = debug
appender.console.type = Console
appender.console.name = STDOUT
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d{yyyy-MM-dd HH:mm:ss.SSS} %traceId [%t] %-5level %logger{36} - %msg%n
######################################################################
appender.file.name = file
appender.file.type = File
appender.file.fileName = ../logs/osp-${application.name}-api.log
appender.file.layout.type = PatternLayout
appender.file.layout.pattern = %traceId %d %p %C{1.} [%t] %m%n
######################################################################
appender.info.type = RollingRandomAccessFile
appender.info.name = info
appender.info.fileName = ../logs/osp-${application.name}-api-info.log
appender.info.filePattern = ../logs/info-%d{yyyy-MM-dd-HH}-%i.log.gz
appender.info.layout.type = PatternLayout
appender.info.layout.pattern = %d{yyyy-MM-dd HH:mm:ss z} %traceId %-5level %class{36}%L %M - %msg%xEx%n
appender.info.policies.type = Policies
appender.info.policies.size.type = SizeBasedTriggeringPolicy
appender.info.policies.size.size = 10MB
appender.info.filter.threshold.type = ThresholdFilter
appender.info.filter.threshold.level = info
appender.error.type = RollingRandomAccessFile
appender.error.name = error
appender.error.fileName = ../logs/osp-${application.name}-api-error.log
appender.error.filePattern = ../logs/error-%d{yyyy-MM-dd-HH}-%i.log.gz
appender.error.layout.type = PatternLayout
appender.error.layout.pattern = %d{yyyy-MM-dd HH:mm:ss z} %traceId %-5level %class{36}%L %M - %msg%xEx%n
appender.error.policies.type = Policies
appender.error.policies.size.type = SizeBasedTriggeringPolicy
appender.error.policies.size.size = 10MB
appender.error.filter.threshold.type = ThresholdFilter
appender.error.filter.threshold.level = error
rootLogger.level = info
rootLogger.appenderRef.stdout.ref = STDOUT
rootLogger.appenderRef.file.ref = file
rootLogger.appenderRef.info.ref = info
rootLogger.appenderRef.error.ref = error
