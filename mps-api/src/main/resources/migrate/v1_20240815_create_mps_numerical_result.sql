DROP TABLE IF EXISTS mps_numerical_result;
CREATE TABLE mps_numerical_result
(
    `id`              BIGINT      NOT NULL COMMENT 'ID',
    `target_id`       BIGINT COMMENT '主表ID',
    `numerical`       DECIMAL(24, 6) COMMENT '数值',
    `numerical_value` DECIMAL(24, 6) COMMENT '数值结果',
    `tenant_id`       VARCHAR(32) NOT NULL DEFAULT 'JINKO' COMMENT '租户号',
    `opt_counter`     INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `is_deleted`      INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    `created_by`      VARCHAR(64) NOT NULL DEFAULT '-1' COMMENT '创建人',
    `created_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`      VARCHAR(64) NOT NULL DEFAULT '-1' COMMENT '更新人',
    `updated_time`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '数值结果表';
