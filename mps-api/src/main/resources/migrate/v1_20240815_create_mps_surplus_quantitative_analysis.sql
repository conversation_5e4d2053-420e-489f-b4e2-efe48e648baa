DROP TABLE IF EXISTS mps_surplus_quantitative_analysis;
CREATE TABLE mps_surplus_quantitative_analysis
(
    `id`               BIGINT      NOT NULL COMMENT 'ID',
    `domestic_oversea` BIGINT COMMENT '排产区域',
    `cell_type_id`     BIGINT COMMENT '电池类型',
    `product_type_id`  BIGINT COMMENT '产品类型',
    `main_grid_id`     BIGINT COMMENT '主栅',
    `supply_us`        BIGINT COMMENT '是否供美',
    `type`             BIGINT COMMENT '类型',
    `year`             INT COMMENT '年份',
    `tenant_id`        VARCHAR(32) NOT NULL DEFAULT 'JINKO' COMMENT '租户号',
    `opt_counter`      INT         NOT NULL DEFAULT 1 COMMENT '乐观锁',
    `is_deleted`       INT         NOT NULL DEFAULT 0 COMMENT '是否删除,0=正常，1=删除',
    `created_by`       VARCHAR(64) NOT NULL DEFAULT '-1' COMMENT '创建人',
    `created_time`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_by`       VARCHAR(64) NOT NULL DEFAULT '-1' COMMENT '更新人',
    `updated_time`     DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (id)
) COMMENT = '剩余可接单量分析';
