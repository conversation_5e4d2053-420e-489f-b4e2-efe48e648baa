package com.jinkosolar.scp.mps.domain.annotate;

import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @ClassName ExcelHead
 * @Description excel表头
 * <AUTHOR>
 * @Date 2023/4/17 15:34
 * @Version 1.0
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelHeader {

    /**
     * 列索引
     *
     * @return
     */
    int index();

    /**
     * 是否百分比
     *
     * @return
     */
    boolean isPercent() default false;

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
