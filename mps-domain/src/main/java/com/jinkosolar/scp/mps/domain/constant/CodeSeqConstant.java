package com.jinkosolar.scp.mps.domain.constant;

/**
 * 序列生成相关常量
 */
public class CodeSeqConstant {
    /**
     * 组件单块瓦数
     */
    public static final String SINGLE_MODULE_WATTAGE = "mps_single_module_wattage";
    /**
     * 组件单片电池
     */
    public static final String MPS_BATTERY_WATTAGE = "mps_battery_wattage";


    /**
     * 组件定线规划
     */
    public static final String COMPONENT_ALIGNMENT_PLAN = "mps_component_alignment_plan_version";

    /**
     * 投产方案对应的材料搭配对照关系相关业务实现,投产方案编码
     */
    public static final String PRODUCT_MATERIAL_COMBINATION_MAPPING = "mps_product_material_combination_mapping";
    /**
     * 生产建议编号
     */
    public static final String   MPS_MODULE_PRODUCTION_SUGGESTION="mps_module_production_suggestion";

    /**
     * 拉晶特殊扣减编号
     */
    public static final String MPS_CRYSTAL_SPECIAL_DEDUCTION = "mps_crystal_special_deduction";

    /**
     * 拉晶特殊扣减编号2
     */
    public static final String MPS_CRYSTAL_SPECIAL_DEDUCTION_TWO = "mps_crystal_special_deduction_two";

    /**
     * 拉晶特殊扣减编号3
     */
    public static final String MPS_CRYSTAL_SPECIAL_DEDUCTION_THREE = "mps_crystal_special_deduction_three";

    /**
     * 坩埚等级扣减编码
     */
    public static final String MPS_CRYSTAL_GRADE_DEDUCTION = "mps_crystal_grade_deduction";


    /**
     * 炉型扣减编码
     */
    public static final String   MPS_WAFER_FURNACE_DEDUCTION="mps_wafer_furnace_deduction";

    /**
     * 坩埚鼓包扣减编码
     */
    public static final String   MPS_CRUCIBLEE_BULGE_DEDUCTION="mps_crucible_bulge_deduction";



    /**
     * 拉晶JKE扣减编码
     */
    public static final String   MPS_CRYSTAL_JKE_DEDUCTION="mps_crystal_jke_deduction";

    /**
     * 配方扣减编码
     */
    public static final String   MPS_CRYSTAL_FORMULA_DEDUCTION="mps_crystal_formula_deduction";


    /**
     * 坩埚高度扣减
     * */
    public static final String   MPS_CRUCIBLE_HEIGHT_DEDUCTION="mps_crucible_height_deduction";



    /**
     * 可分配资源
     */
    public static final String MPS_RELEASABLE_RESOURCE = "mps_releasable_resource";

    /**
     *拉晶工作中心对应关系
     */
    public static final String MPS_WORK_CENTER_CORRESPONDENCE = "mps_work_center_correspondence";
    /**
     *单产提升规则
     */
    public static final String MPS_UNIT_YIELD_INCREASE_RULES = "mps_unit_yield_increase_rules";
    /**
     *标准坩埚寿命
     */
    public static final String MPS_STANDARD_CRUCIBLE_LIFE = "mps_standard_crucible_life";

    /**
     *热场切换计划
     */
    public static final String MPS_HOT_FIELD_SWITCHING_PLAN = "mps_hot_field_switching_plan";

    /**
     *配方切换计划
     */
    public static final String MPS_FORMULA_SWITCHING_PLAN = "mps_formula_switching_plan";

    /**
     *炉型切换计划
     */
    public static final String MPS_FURNACE_SWITCHING_PLAN = "mps_furnace_switching_plan";

    /**
     *jke切换计划
     */
    public static final String MPS_JKE_SWITCHING_PLAN = "mps_jke_switching_plan";

    /**
     * 坩埚等级切换计划
     * */
    public static final String MPS_CRYSTAL_GRADE_SWITCH= "mps_crystal_grade_switch";

    /**
     * 坩埚高度切换计划
     * */
    public static final String MPS_CRYSTAL_HEIGHT_SWITCH= "mps_crystal_height_switch";

    /**
     * 拉晶—实际良率
     * */
    public static final String MPS_CRYSTAL_ACTUAL_YIELD= "mps_crystal_actual_yield";

    /**
     * 产品切换计划表
     */
    public static final String   MPS_CRYSTAL_PRODUCT_WSTIC_PLAN="mps_crystal_product_swtic_plan";


    /**
     * 单产微调
     */
    public static final String  MPS_CRYSTAL_PRODUCT_FINE_TUNING="mps_crystal_product_fine_tuning";

    /**
     * 电池-切换损失数据表
     */
    public static final String MPS_SWITCH_LOSS_DATA = "mps_switch_loss_data";
}
