package com.jinkosolar.scp.mps.domain.constant;

public class CommonConstant {

    /**
     * 年月日日期格式
     */
    public static final String DATE_FORMAT_YYYYMMDD = "yyyy-MM-dd";

    /**
     * 年月日日期格式
     */
    public static final String DATETIME_FORMAT_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 一年的总月数
     */
    public static final int YEAR_MONTH_SIZE = 12;

    /**
     * 月最大天数
     */
    public static final int MONTH_DAY_MAX = 31;

    /**
     * 满产产能
     */
    public static final int PRODUCTION_TYPE_GRADE_0 = 0;

    /**
     * 爬坡产能
     */
    public static final int PRODUCTION_TYPE_GRADE_1 = 1;

    /**
     * 实验产能
     */
    public static final int PRODUCTION_TYPE_GRADE_2 = 2;

    /**
     * 目标利用率类型
     */
    public static final String TARGET_RATE_TYPE_NAME = "目标";

    /**
     * 指令代码
     */
    public static final String DEFAULT_INSTRUCTION_CODE = "M";

    /**
     * 指令种类
     */
    public static final String DEFAULT_INSTRUCTION_TYPE = "使用指令";

    /**
     * 工序代码
     */
    public static final String DEFAULT_PROCEDURE_CODE = "组件";

    /**
     * 工序编号
     */
    public static final String DEFAULT_PROCEDURE_NO = "10";

    /**
     * 通配符，所有
     */
    public final static String ALL = "*";
    /**
     * 期初
     */
    public final static Integer BOH = 0;
    /**
     * 间隔符号
     */
    public final static String INTERVAL = "-";
    /**
     * 汇总
     */
    public final static String SUMMARY = "汇总";
    /**
     * 0点库存
     */
    public final static String ZERO_INVENTORY = "0";
    /**
     * 8:20库存
     */
    public final static String OTHER_INVENTORY = "8.20";

    /**
     * in查询最大数量
     */
    public static final int MAX_IN_SIZE= 1000;

    /**
     * 供美
     */
    public static final String NA = "NA";

    /**
     * 非供美
     */
    public static final String NON_NA = "Non_NA";

    /**
     * 预发布状态
     */
    public final static String PREPUBLISH_STATUS = "P";

    /**
     * 未调整版本（仅供BOM创建）
     */
    public final static String BOMPUBLISH_STATUS = "S";
}
