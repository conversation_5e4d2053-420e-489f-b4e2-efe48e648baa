package com.jinkosolar.scp.mps.domain.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/9/22
 */
public class Constants {
    public static final Integer PAGE_SIZE = 600;

    public static final Integer MONTH_SIZE = 12;

    public static final Integer CAPACITY_YEAR_SIZE = 2;

    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT_2 = "yyyy/MM/dd";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_FORMAT_ = "yyyyMMdd";

    /*type类型周度产能报表
    1：IE各基地周度达成率
     * 2：IE各车间周度达成率
     * 3：IE各产品周度达成率
    */
    public static final String WEEK_BASE_PLACE = "1";

    public static final String WEEK_WORKSHOP = "2";

    public static final String WEEK_PRODUCT = "3";


    public static final String Y = "Y";

    public static final String N = "N";


    public static final String LOV_WORK_SHOP = "work_shop";

    public static final String OEM = "OEM";

    //三个分类维度的redis名
    public static final String APS_WEEK_ACHIEVEMENT_RATE_FLUSH_STATUS_BASE_PLACE = "APS_WeekAchievementRate_Flush_Status_base_place";

    public static final String APS_WEEK_ACHIEVEMENT_RATE_FLUSH_STATUS_WORK_SHOP = "APS_WeekAchievementRate_Flush_Status_work_shop";

    public static final String APS_WEEK_ACHIEVEMENT_RATE_FLUSH_STATUS_PRODUCT_SERIES = "APS_WeekAchievementRate_Flush_Status_product_series";

    public static final String LOCK_NAME = "aps:weekachievementrate:flushabc";

    public static final String LOCK_OFF = "0";

    public static final String LOCK_ON = "1";

    public static final String FROM_DATE_TIME = "fromDateTime";

    public static final String TO_DATE_TIME = "toDateTime";

   /* *//**
     * 利用率月份
     * *//*
    public static final String UR_MONTH1 = "Month1";
    public static final String UR_MONTH2 = "Month2";
    public static final String UR_MONTH3 = "Month3";
    public static final String UR_MONTH4 = "Month4";
    public static final String UR_MONTH5 = "Month5";
    public static final String UR_MONTH6 = "Month6";
    public static final String UR_MONTH7 = "Month7";
    public static final String UR_MONTH8 = "Month8";
    public static final String UR_MONTH9 = "Month9";
    public static final String UR_MONTH10 = "Month10";
    public static final String UR_MONTH11 = "Month11";
    public static final String UR_MONTH12 = "Month12";
    public static final String UR_ERRO = "利用率总和必须等于1";*/


    /**
     * 来源类型
     */
    //拉晶
    public static final String SOURCE_TYPE_CRYSTAL = "CRYSTAL";
    public static final String SOURCE_TYPE_CRYSTAL_NAME = "拉晶";
    //切片
    public static final String SOURCE_TYPE_WAFER = "WAFER";
    public static final String SOURCE_TYPE_WAFER_NAME = "切片";

    //组件
    public static final String SOURCE_TYPE_MODULE = "MODULE";
    public static final String SOURCE_MODULE_NAME = "组件";
    //切片

    public static final String SOURCE_TYPE_CELL_NAME = "电池";
    //组件排产
    public static final String MODULE_TYPE_DEFAULT = "ZJPC";
    //组件
    public static final String BUSINESS_DEPT_ZJ = "ZJ";
    //组件排产asprova表
    public static final String TABLE_ASP_MODULE_PRODUCTION_PLAN = "mrp_module_production_plan";
    //组件排产
    public static final String CAPACITY_UNIT_DEFAULT = "PH";
    //限制类型
    public static final String LIMIT_TYPE_PRODUCTION = "GY";
    //限制类型
    public static final String EMPTY_CAPACITY_NAME = "空产能";
    //限制类型
    public static final String EMPTY_CAPACITY_DESC = "待接单";
    //分隔符
    public static final String SPLIT_LINE_CHAR = "-";
    //外购电池日计划
    public static final String BATTERY_DELIVERY_PLAN_DAY = "BDPD";
    //外购电池月计划
    public static final String BATTERY_DELIVERY_PLAN_MONTH = "BDPM";
    //操作标识-新增
    public static final String OPERATION_ADD = "Add";
    public static final String PRODUCTION_PLAN_PUBLISH_TITLE = "生产计划已发版";
    public static final String PRODUCTION_PLAN_PUBLISH_CONTENT ="生产计划已发版，请主物控查收核对！";

    //未调整版本发布晶彩消息标题
    public static final String PRODUCTION_PLAN_PUBLISH_UNADJUSTED_TITLE = "未调整组件生产计划已发版";
    public static final String PRODUCTION_PLAN_PUBLISH_UNADJUSTED_CONTENT ="未调整组件生产计划已发版！";
    //补投数据-不可连续生产
    public static final String NOT_SUPPLY_INPUT ="1782962284772069376";

    public static final String PROCESS_LIMIT_NOTICE_TITLE = "工艺限制解限提醒";

    public static final String PROCESS_LIMIT_NOTICE_CONTENT = "您有单据号为{0}的工艺限制流程，还有{1}天将到期自动解除限制，请确认是否继续限制！";

    public static final Integer MAX_SEQUENCE_NO= 3;

    public static final Integer MAX_FORMULA_SEQUENCE_NO= 12;
    //外购电池日计划
    public static final String BATTERY_DELIVERY_PLAN_DEFAULT_VERSION = "V0";
    //爬坡拆分数据
    public static final String CLIMB_SLICE_SPLIT_DATA = "JTCF";
    //爬坡拆分数据
    public static final String CLIMB_SWITCH_SPLIT_DATA = "DCCF";
    //需求拆分数据
    public static final String SECTION_SPLIT_DATA = "XQCF";
    /**
     /**
     */
    /**
     * in查询最大数量
     */
    public static final Integer MAX_IN_SIZE = 1000;

    /**
     *  晶棒属性映射
     */
    public static final String BOM_CB_ITEMMAPPING = "BOM.CB_ITEMMAPPING";
    public static final int BATCH_COMMIT_ROWS = 5000;

    public static final String NOTHING = "无";

    public static final String MONTH_CN = "月";

    public static final String DAY_CN = "日";

    @AllArgsConstructor
    @Getter
    public enum RunStatusEnum {
        WAIT("0", "等待中"),
        RUNNING("1", "正在运行"),
        SUCCESS("2", "刷新成功"),
        FAIL("3", "刷新失败");

        String code;

        String remark;
    }

    /**
     * 属性行是电池产品
     */
    public static final String ATTR_TYPE_CELL_PRODUCT = "cell_product";

    public static final String BIZ_TYPE_PROCESS_LIMIT = "productProcessLimit";
    /**
     * 晶棒类型-方棒
     */
    public static final String MPS_CRYSTAL_SQUARE_BAR_TYPE = "10";

    public static final String DEFAULT_ALL = "ALL";

    /**
     * 实验产能
     * */
    public static final String CAPACITY_DYNAMIC_CLIMB = "爬坡产能";
    public static final String CAPACITY_DYNAMIC_TEST = "实验产能";

    public static final String DEFAULT_LIMIT_START_DATE = "2000-01-01";
    public static final String DEFAULT_LIMIT_END_DATE = "2100-01-01";

    /**
     * 单产数据类型
     * */
    public static final String DATA_TYPE_CLIMB = "climb";
    public static final String DATA_TYPE_BASICS = "basics";
    public static final String DATA_TYPE_PRE_UNIT_CLIMB = "PreUnitClimb";
    public static final String MES_SYNC_FAIL = "未同步";
    /**
     * 利用率月份
     * */
    public static final String UR_MONTH1 = "Month1";
    public static final String UR_MONTH2 = "Month2";
    public static final String UR_MONTH3 = "Month3";
    public static final String UR_MONTH4 = "Month4";
    public static final String UR_MONTH5 = "Month5";
    public static final String UR_MONTH6 = "Month6";
    public static final String UR_MONTH7 = "Month7";
    public static final String UR_MONTH8 = "Month8";
    public static final String UR_MONTH9 = "Month9";
    public static final String UR_MONTH10 = "Month10";
    public static final String UR_MONTH11 = "Month11";
    public static final String UR_MONTH12 = "Month12";

    public static final String UR_ERRO = "利用率总和必须等于1";

    public static final String UR_MONTH1_TEXT = "1月份";
    public static final String UR_MONTH2_TEXT = "2月份";
    public static final String UR_MONTH3_TEXT= "3月份";
    public static final String UR_MONTH4_TEXT = "4月份";
    public static final String UR_MONTH5_TEXT = "5月份";
    public static final String UR_MONTH6_TEXT = "6月份";
    public static final String UR_MONTH7_TEXT = "7月份";
    public static final String UR_MONTH8_TEXT = "8月份";
    public static final String UR_MONTH9_TEXT = "9月份";
    public static final String UR_MONTH10_TEXT= "10月份";
    public static final String UR_MONTH11_TEXT = "11月份";
    public static final String UR_MONTH12_TEXT = "12月份";

    public static final String CELL_FROM_OUT ="外购电池片";
    public static final String SOURCE_TYPE_OUT ="外销需求";
    public static final String VERIFICATION_MARK ="可靠性";

    public static final String SURPLUS_QUANTITATIVE_ANALYSIS_NOTICE_TITLE = "剩余可接单量生成通知";

    public static final String SURPLUS_QUANTITATIVE_ANALYSIS_NOTICE_CONTENT = "计划已发布，剩余可接单已计算，请查阅！";


    public static final String MAIN_SUM = "合计";

    public static final String SUB_SUM = "小计";

    public static final String NON_PRODUCTION_PLAN_PUBLISH_CONTENT ="生产计划已发版，请于SCP系统查看！";

    public static final String WORK_CENTER_CODE_QP_DEFAULT = "YNJJ1005";

    public static final String WORK_SHOP_CODE_QP_DEFAULT = "Q178301";

    public static final String PRODUCT_SWITCH_PLAN_UPDATED_TITLE = "产品切换计划已更新,请于SCP系统产品切换维护页面查看";

    public static final String CELL_PLAN_DAY_IMPORT_CONTENT ="IE更新月度日计划已上传，请于SCP系统页面查看！";

    public static final String GNDC_MODEL_TYPE="GNDC";

    public static final String DPM_PRO_TYPE = "DPM.PRO_TYPE";

    public static final String MODEL_TYPE__GNQP="GNQP01";

}
