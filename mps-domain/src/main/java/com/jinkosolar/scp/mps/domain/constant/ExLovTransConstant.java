package com.jinkosolar.scp.mps.domain.constant;

/**
 * Excel 导入lov名称转换常量
 */
public class ExLovTransConstant {

    /**
     * lov名称转 id sql
     */
    public static final String SQL = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code= ";

    /**
     * lov值转 id sql
     */
    public static final String VALUE_SQL = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_value = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code= ";

    /**
     * SYS.PM_CODE 转换
     */
    public static final String PM_CODE_SQL = "select min(p.prod_id) as lov_line_id from bom_product_group p  where p.is_deleted = 0 and p.pm_code = ?1";
}
