package com.jinkosolar.scp.mps.domain.constant;

import java.util.Arrays;
import java.util.List;

public interface MaterialCertificationConst {

    String MANUAL_IMPORT = "手工导入";
    String AUTO_IMPORT = "认证模块";
    String PRODUCT_FAMILY = "产品族";
    String EQUAL = "等于";
    List<String> MATERIAL_TYPE = Arrays.asList(new String[]{"汇流条", "互联条", "接线盒"});
    String BELT_BUS_BAR = "汇流条";
    String THICKNESS = "厚度(mm)";
    String WELD_STRIP = "互联条";
    String DIAMETER = "直径(mm)";
    String JUNCTION_BOX = "接线盒";
    String JUNCTION_BOX_TYPE = "接线盒型号";
    String IEC = "IEC";
    String UL = "UL";

    //手动导入
    int SOURCE_IMPORT = 0;
    //自动生成
    int SOURCE_AUTO = 1;
}
