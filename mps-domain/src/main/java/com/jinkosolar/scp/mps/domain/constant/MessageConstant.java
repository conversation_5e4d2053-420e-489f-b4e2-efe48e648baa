package com.jinkosolar.scp.mps.domain.constant;

/**
 * <AUTHOR>
 * @date 2024/5/31
 * @description
 */
public class MessageConstant {

    //在【MPS.PROCESS_ID】下未找到【拉晶】数据，请确认
    public static final String MPS_ERROR_CRYSTAL_NO_FOUND = "mps.error.crystalNoFound";
    //在【MPS.PROCESS_ID】下未找到【拉晶】数据，请确认
    public static final String MPS_ERROR_WAFER_NO_FOUND = "mps.error.waferNoFound";
    // mps.error.sourceTypeError = 来源类型错误
    public static final String MPS_ERROR_SOURCE_TYPE_ERROR = "mps.error.sourceTypeError";
    // mps.error.factory.data.loss = 工厂数据缺失
    public static final String MPS_ERROR_FACTORY_VERSION_DATA_LOSS = "mps.error.factory.data.loss";

}
