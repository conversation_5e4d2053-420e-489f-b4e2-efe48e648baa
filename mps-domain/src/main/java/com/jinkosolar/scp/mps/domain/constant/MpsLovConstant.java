package com.jinkosolar.scp.mps.domain.constant;

public class MpsLovConstant {

    public final static String WORKSHOP = "MPS.WORK_SHOP";

    public final static String BUSINESS_DEPRTMENT = "MPS.BUSINESS_DEPRTMENT";

    public final static String LOCATION = "SYS.DOMESTIC_OVERSEA";

    public final static String MPS_SESTEM_AREA = "MPS.SESTEM_AREA";

    public final static String ATTR_TYPE_006_ATTR_1000 = "ATTR_TYPE_006_ATTR_1000";

    /**
     * 利用率类型
     */
    public final static String TEXT_TYPE = "MPS.TEXT_TYPE";

    /**
     * 利用率大类
     */
    public final static String PARENT_TEXT_TYPE = "MPS.PARENT_TEXT_TYPE";

    /**
     * 补投方式
     */
    public final static String REMEDY_WAY = "MPS.REMEDY_WAY";

    /**
     * 工作中心
     */
    public final static String WORK_CENTER = "SYS.WORKCENTER";

    public final static String MPS_WAFER_CRYSTAL_INVENTORY_SITE = "MPS.WAFER_CRYSTAL_INVENTORY_SITE";

    /**
     * 工厂
     */
    public final static String FACTORY = "SYS.ORG_ARCHITECTURE";

    /**
     * 线缆长度 MPS.CABLE_LENGTH
     */
    public final static String CABLE_LENGTH = "MPS.CABLE_LENGTH";

    /**
     * 膜袋-是否贴膜 MPS.TIEMO
     */
    public final static String TIEMO = "MPS.TIEMO";

    /**
     * 料号匹配.匹配状态
     */
    public static final String LOV_CODE_BOM_ITEM_MAPPING_STATUS = "BOM.ITEM_MAPPING_STATUS";

    /**
     * 料号匹配.发布状态
     */
    public static final String LOV_CODE_BOM_PUBLISHED_STATUS = "BOM.PUBLISHED_STATUS";

    public static final String LOV_CODE_MPS_BUSINESS_DEPARTMENT = "MPS.BUSINESS_DEPRTMENT";

    public static final String LOV_CODE_MPS_PROCESS_ID = "MPS.PROCESS_ID";

    public static final String LOV_CODE_BOM_MATCH_ITEM_MAPPING = "BOM.MATCH_ITEM_MAPPING";

    /**
     * 规则分类
     */
    public static final String LOV_CODE_SYS_RULE_TYPE = "SYS.RULE_TYPE";

    /**
     * 补投数据状态
     */
    public final static String MPS_BT_STATE = "MPS.BT_state";

    /**
     * 正排/倒排
     */
    public final static String MPS_BT_SORT = "MPS.BT_Sort";

    /**
     * 切换类型：电池产品
     */
    public final static String MPS_BATTERY = "MPS.BATTERY";

    /**
     * 切换类型：片串
     */
    public final static String MPS_SHEET = "MPS.SHEET";

    /**
     * 切换类型：工艺
     */
    public final static String MPS_TECH = "MPS.TECH";

    /**
     * 切换类型：单双玻
     */
    public final static String MPS_GLASS = "MPS.GLASS";

    public static final String VERSION_STATUS = "MRP.VERSION_STATUS";

    /**
     * 空产能推送APS表名
     */
    public static final String EMPTY_CAPACITY_APS_LOV_VALUE = "mps_module_production_empty_capacity_aps";

    /**
     * 已投产未完工推送APS表名
     */
    public static final String MRP_PRODUCTION_UNFINISHED_PLAN = "mrp_production_unfinished_plan";

    /**
     * 实时库存
     */
    public static final String MRP_REALTIME_INVENTORY = "mrp_realtime_inventory";

    /**
     * 采购配额
     */
    public static final String MRP_PURCHASE_QUOTA_DISASSEMBLE = "mrp_purchase_quota_disassemble";

    /**
     * 到货回复
     */
    public static final String MRP_DELIVERY_PROMISE = "mrp_delivery_promise";

    /**
     * 组件排产计划头推送APS表名
     */
    public static final String PRODUCT_PLAN_HEADER_APS_LOV_VALUE = "mps_product_plan_head";

    /**
     * 组件排产计划明细推送APS表名
     */
    public static final String PRODUCT_PLAN_LINE_APS_LOV_VALUE = "mps_product_plan_line";

    /**
     * 硅片_产品
     */
    public static final String ATTR_TYPE_029_ATTR_1400 = "ATTR_TYPE_029_ATTR_1400";


    public static final String MODULE_PRODUCTION_PLAN_PUBLISH_LOCK = "MODULE_PRODUCTION_PLAN_PUBLISH_LOCK";

    public static final String MPS_COMPONENT_OEM_ACTUAL_INVESTMENT = "mps_component_oem_actual_investment";

    /**
     * 目的地区域
     */
    public final static String DP_DESTINATION_REGION = "DP.Destination Region";

    public final static String MPS_INEFFICIENCY_RATIO = "MPS.INEFFICIENCY_RATIO";

    public final static String MPS_LOTNOFACTORY = "MPS.LOTNOFACTORY";

    /**
     * 周报表-库存地址
     */
    public final static String MPS_INV_POS = "MPS.Inv_Pos";

}
