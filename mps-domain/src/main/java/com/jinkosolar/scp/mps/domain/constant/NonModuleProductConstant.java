package com.jinkosolar.scp.mps.domain.constant;

public interface NonModuleProductConstant {

    //定向
    String STRING_ORIENTATION_NAME = "定向";

    //研发
    String STRING_TYPE="YF";

    // R:研发 P:大货
    String STRING_TYPE_R="R";
    String STRING_TYPE_P="P";


    //  是否定向
    String STRING_Directional_Y="Y";
    String STRING_Directional_N="N";

    /**
     * IUD标识 :
     * I:新建
     * U:修改
     * D:删除
     */
    String STRING_ZIUD_I="I";
    String STRING_ZIUD_U="U";
    String STRING_ZIUD_D="D";

    // 常规需求
    public static final String SPECIAL_FLAG_NAME_CG = "常规需求";

    // 特殊需求
    public static final String SPECIAL_FLAG_NAME_TS = "特殊需求";
}
