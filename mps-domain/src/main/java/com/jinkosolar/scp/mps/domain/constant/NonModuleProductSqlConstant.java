package com.jinkosolar.scp.mps.domain.constant;


public class NonModuleProductSqlConstant {

    /**
     * 获取电池库存数据
     */
    public static final String QUERY_INVENTORY = "select sum(t.quantity) quantity,special_flag,product_type from" +
            "(" +
            " select i.quantity,b.segment1 product_type,b.segment8,b.segment18,b.segment6 + 0 segment6,case when (b.segment8 is not null and b.segment8 != '') or (b.segment18 is not null and b.segment18 != '') then 'Y' else 'N' end special_flag from dp_realtime_inventory i,bom_items b where i.item_code = b.item_code" +
            " and i.factory_code = b.factory_code" +
            " and i.inv_site_code in ('F201','F202')" +
            " and i.is_deleted = 0" +
            ") t where t.segment6 >= :attribute21 group by special_flag,product_type";

    /**
     * 获取电池库存数据
     */
    public static final String QUERY_INVENTORY_HISTORY = "select special_flag, product_type, efficiency, quantity from dp_realtime_inventory_history where is_deleted = 0 and inventory_date = :inventoryDate and efficiency >= :efficiency";

    /**
     * 获取组件针对电池排产数据
     */
    public static final String QUERY_PRODUCTION_PLAN = "select IFNULL(m.special_flag,'N') special_flag,m.item_attribute65 product_type,m.batch_directional is_directional,m.aps_plan_date,ROUND(sum(m.plan_qty)*66/10000,4) quantity,m.domestic_oversea from mps_module_production_plan m where m.plan_version" +
            " in (select plan_version from mps_plan_verison_controll where plan_type in (SELECT concat('ZJPC-',t1.lov_value) FROM sys_lov_lines t1,sys_lov_header t2 where t1.lov_header_id=t2.lov_header_id AND t2.is_deleted=0 AND t1.is_deleted=0 AND t2.lov_code='SYS.DOMESTIC_OVERSEA') and status = 'Y' and is_deleted = 0)" +
            " group by m.item_attribute65,m.aps_plan_date,IFNULL(m.special_flag,'N'),m.batch_directional,m.domestic_oversea" +
            " order by m.aps_plan_date";

    public static final String QUERY_QPPC1 = "" +
            "select '青海返棒在途天数'                     as 'dateType',\n" +
            "       a.product_type                         as 'productType',\n" +
            "       a.directional                          as 'directional',\n" +
            "       a.crystal_type1                        as 'crystalType1',\n" +
            "       a.chamfer_id                           as 'chamfer',\n" +
            "       a.formula                              as 'formula',\n" +
            "       a.silicon_supplier                     as 'siliconSupplier',\n" +
            "       a.thickness                            as 'thickness',\n" +
            "       DATE(a.scheduling_start_time)          as 'dayDate',\n" +
            "       (a.scheduling_qty * a.plan_yield_rate) as 'quantityYb'\n" +
            "from mps_non_module_production_plan a\n" +
            "where a.is_deleted = 0\n" +
            "  and a.plan_version in (select plan_version\n" +
            "                         from mps_plan_verison_controll\n" +
            "                         where is_deleted = 0\n" +
            "                           and plan_type = 'GNLJ01'\n" +
            "                           and status = 'Y')\n" +
            "  and process_no = 40";

    public static final String QUERY_QPPC2 = "" +
            "select '山西返棒在途天数' as 'dateType',\n" +
            "       b.product_item_no  as 'productType',\n" +
            "       b.directional      as 'directional',\n" +
            "       b.item_attribute76 as 'crystalType1',\n" +
            "       b.item_attribute45 as 'chamfer',\n" +
            "       b.item_attribute79 as 'formula',\n" +
            "       b.item_attribute23 as 'siliconSupplier',\n" +
            "       b.item_attribute75 as 'thickness',\n" +
            "       a.planned_wip_date as 'dayDate',\n" +
            "       a.plan_qty         as 'quantityFb'\n" +
            "from dp_demand_lines_detail a\n" +
            "         inner join dp_demand_lines b on (a.dp_lines_id = b.dp_lines_id and b.is_deleted = 0)\n" +
            "         inner join dp_demand_header c on (b.header_id = c.id and c.is_deleted = 0)\n" +
            "where a.is_deleted = 0\n" +
            "  and c.id = (select max(t.id)\n" +
            "              from dp_demand_header t\n" +
            "              where t.demand_type = 'pc_independent'\n" +
            "                and t.is_deleted = 0\n" +
            "                and t.status = '1789097780912984064'\n" +
            "                and t.domestic_oversea = '1783118270098968576'\n" +
            "                and t.remark = '山西返棒')";

    public static final String QUERY_QPPC3 = "" +
            "select '新疆返棒在途天数' as 'dateType',\n" +
            "       b.product_item_no  as 'productType',\n" +
            "       b.directional      as 'directional',\n" +
            "       b.item_attribute76 as 'crystalType1',\n" +
            "       b.item_attribute45 as 'chamfer',\n" +
            "       b.item_attribute79 as 'formula',\n" +
            "       b.item_attribute23 as 'siliconSupplier',\n" +
            "       b.item_attribute75 as 'thickness',\n" +
            "       a.planned_wip_date as 'dayDate',\n" +
            "       a.plan_qty         as 'quantityFb'\n" +
            "from dp_demand_lines_detail a\n" +
            "         inner join dp_demand_lines b on (a.dp_lines_id = b.dp_lines_id and b.is_deleted = 0)\n" +
            "         inner join dp_demand_header c on (b.header_id = c.id and c.is_deleted = 0)\n" +
            "where a.is_deleted = 0\n" +
            "  and c.id = (select max(t.id)\n" +
            "              from dp_demand_header t\n" +
            "              where t.demand_type = 'pc_independent'\n" +
            "                and t.is_deleted = 0\n" +
            "                and t.status = '1789097780912984064'\n" +
            "                and t.domestic_oversea = '1783118270098968576'\n" +
            "                and t.remark = '新疆返棒')";

    public static final String QUERY_QPPC4 = "" +
            "select if(from_factory_code = '1581', '青海返棒在途天数',\n" +
            "          if(from_factory_code = '5041', '山西返棒在途天数', '其它')) as 'dateType'\n" +
            "     , t2.product_type                                                as 'productType'\n" +
            "     , t2.directional                                                 as 'directional'\n" +
            "     , t2.crystal_type1                                               as 'crystalType1'\n" +
            "     , t2.chamfer                                                     as 'chamfer'\n" +
            "     , t2.formula                                                     as 'formula'\n" +
            "     , t2.silicon_supplier                                            as 'siliconSupplier'\n" +
            "     , t2.thickness                                                   as 'thickness'\n" +
            "     , DATE(t1.send_time)                                             as 'dayDate'\n" +
            "     , t1.quantity                                                    as 'quantityFb'\n" +
            "from mps_stock_from_transit t1\n" +
            "         left join(select item_code\n" +
            "                        , max(segment10) as product_type\n" +
            "                        , max(segment8)  as directional\n" +
            "                        , max(segment3)  as crystal_type1\n" +
            "                        , max(segment11) as chamfer\n" +
            "                        , max(segment6)  as formula\n" +
            "                        , max(segment9)  as silicon_supplier\n" +
            "                        , max(null)      as thickness\n" +
            "                   from bom_items\n" +
            "                   where is_deleted = 0\n" +
            "                     and category_segment5 = '方棒'\n" +
            "                   group by item_code) t2 on t1.item_code = t2.item_code\n" +
            "where t1.is_deleted = 0\n" +
            "  and t1.to_factory_code in ('1301', '1891');";

    /**
     * 获取终产量数据
     */
    public static final String QUERY_PRODUCTION_PROD = "select b.* from (\n" +
            "select a.*,\n" +
            "DATE_ADD(a.startDate1, INTERVAL 3 DAY)  as startDate \n" +
            "from (\n" +
            "select \n" +
            ":#{#query.factoryId} as factoryId,\n" +
            "mcpcsrt.work_center_id_code as workCenterCode,\n" +
            "mcpcsrt.product_type_id_name as productType,\n" +
            "mcpcsrt.size_id_name as sizeName,\n" +
            "mcpcsrt.directional as directionalName,\n" +
            "mcpcsrt.formula_id_name as formula,\n" +
            "mcpcsrt.caculate_num as machineQty,\n" +
            "mcpcsrt.demand_qty as schedulingQty,\n" +
            "mcpcsrt.per_unit as singleProduction,\n" +
            "mcpcsrt.start_date as startDate1,\n" +
            "mcpcsrt.crystal_type_id_name as crystalType,\n" +
            "mcpcsrt.data_type as modelType,\n" +
            "sll6.lov_name as directionalFlag\n" +
            "from mps_crystal_plan_climb_section_result_temp mcpcsrt \n" +
            "left join sys_lov_lines as sll6 on mcpcsrt.dire_flag = sll6.lov_line_id \n" +
            "where data_type = (\n" +
            "select model_type from mps_non_module_production_plan \n" +
            "where attribute3 = :#{#query.factoryId}\n" +
            "and process_no = '40'\n" +
            "and latest_version_flag = 'Y'\n" +
            "LIMIT 1\n" +
            ")\n" +
            "and mcpcsrt.start_date >= DATE_SUB(:#{#query.schedulingStartTime}, INTERVAL 3 DAY) \n" +
            "and mcpcsrt.start_date <DATE_ADD(:#{#query.schedulingEndTime}, INTERVAL 1 DAY) "+
            "union \n" +
            "select \n" +
            "mnmpp.attribute3 as factoryId ,\n" +
            "mnmpp.work_center_code as workCenterCode,\n" +
            "mnmpp.product_type as productType,\n" +
            "sll1.lov_value as sizeName,\n" +
            "sll2.lov_name as directionalName,\n" +
            "sll3.lov_value as formula,\n" +
            "mnmpp.machine_qty as machineQty,\n" +
            "mnmpp.machine_qty *mnmpp.single_production as schedulingQty,\n" +
            "mnmpp.single_production as singleProduction,\n" +
            "STR_TO_DATE(LEFT(mnmpp.attribute19, 10), '%Y/%m/%d') as startDate1,\n" +
            "sll4.lov_value as crystalType,\n" +
            "mnmpp.model_type as modelType,\n" +
            "mnmpp.attribute14 as directionalFlag\n" +
            "from mps_non_module_production_plan mnmpp \n" +
            "left join sys_lov_lines as sll1 on mnmpp.size = sll1.lov_line_id \n" +
            "left join sys_lov_lines as sll2 on mnmpp.directional = sll2.lov_line_id \n" +
            "left join sys_lov_lines as sll3 on mnmpp.formula = sll3.lov_line_id \n" +
            "left join sys_lov_lines as sll4 on mnmpp.crystal_type1 = sll4.lov_line_id \n" +
            "where mnmpp.latest_version_flag = 'Y'\n" +
            "and mnmpp.process_no = '40'\n" +
            "and STR_TO_DATE(LEFT(mnmpp.attribute19, 10), '%Y/%m/%d') > GREATEST((select c.start_date from mps_crystal_plan_climb_section_result_temp as c order by c.start_date desc LIMIT 1),DATE_SUB(:#{#query.schedulingStartTime}, INTERVAL 4 DAY))\n" +
            "and STR_TO_DATE(LEFT(mnmpp.attribute19, 10), '%Y/%m/%d') < DATE_ADD(:#{#query.schedulingEndTime}, INTERVAL 1 DAY)\n" +
            "and mnmpp.attribute3 = :#{#query.factoryId}\n" +
            ") a \n" +
            "ORDER BY a.workCenterCode,a.startDate1\n" +
            ") b\n" +
            "where b.startDate is not null and b.startDate<DATE_ADD(:#{#query.schedulingEndTime}, INTERVAL 1 DAY)";
}
