package com.jinkosolar.scp.mps.domain.constant;

import java.math.BigDecimal;

public interface PowerCollectGapConst {

    //效率字段起始数字
    int EFFICIENCY_FIELD_START_NUM = 1;
    //效率字段结束数字
    int EFFICIENCY_FIELD_END_NUM = 40;

    interface Type {
        String SUPPLY = "supply";
        String DEMAND = "demand";
        String GAP = "gap";
    }

    //gap预警阈值
    BigDecimal GAP_WARN_THRESHOLD = new BigDecimal("0.03");
    //连续列数量
    int GAP_ADJUST_CONTINUOUS_COL_NUM = 3;
    //连续列和的阈值
    BigDecimal GAP_ADJUST_CONTINUOUS_COL_SUM_THRESHOLD = new BigDecimal("0.05");
    BigDecimal GAP_ADJUST_STEP = new BigDecimal("0.01");

    Integer GAP_ADJUST_COL_THRESHOLD = 200;

    //自动调整gap叠加新版本比例阈值
    int GAP_ADJUST_VERSION_RATE_FACTOR = 100;

    String POWER_GAP_ADJUST_PROFIX = "GAP";

    interface IsGapAdjustData{
        int YES = 1;

        int NO = 0;
    }
}





