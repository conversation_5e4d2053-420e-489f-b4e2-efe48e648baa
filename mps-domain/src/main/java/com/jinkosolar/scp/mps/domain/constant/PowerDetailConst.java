package com.jinkosolar.scp.mps.domain.constant;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public interface PowerDetailConst {

    //效率字段起始数字
    int EFFICIENCY_FIELD_START_NUM = 1;
    //效率字段结束数字
    int EFFICIENCY_FIELD_END_NUM = 40;
    //材料字段起始数字
    int ITEM_ATTRIBUTE_FIELD_START_NUM = 1;
    //材料字段结束数字
    int ITEM_ATTRIBUTE_FIELD_END_NUM = 20;
    //最小功率段数
    int MIN_EFFICIENCY_SEGMENT_IDX = 1;
    //最大功率段数
    int MAX_EFFICIENCY_SEGMENT_IDX = 5;
    //最小版本
    int MIN_VERSION_IDX = 1;
    //最大版本
    int MAX_VERSION_IDX = 5;
    //降档量报警阈值
    BigDecimal DOWNSHIFT_PERCENT_WARN_FACTOR = new BigDecimal("0.2");
    //副产物预警阈值
    BigDecimal BY_PRODUCT_PERCENT_WARN_FACTOR = new BigDecimal("0.02");
    //默认调整正态分布步长
    BigDecimal DEFAULT_NORMAL_DISTRIBUTION_STEP = new BigDecimal("0.01");

    BigDecimal POWER_TO_MPOWER = new BigDecimal("1000000");
    String SUB_TITLE_FIELD_NAME = "subTitle";
    String EFFICIENCY_FIELD_NAME = "efficiency";
    String GAP_ADJUST_CNT_FIELD_NAME = "gapAdjustCnt";
    String OUT_GROWTH_EFFICIENCY_SUM_FIELD_NAME = "outGrowthEfficiencySum";
    String RANGE_FIELD_NAME = "range";
    String VERSION_FIELD_NAME = "Version";
    String NORMAL_DISTR_FIELD_NAME = "powerNormalDistribution";
    String ITEMATTRIBUTE_FIELD_NAME = "itemAttribute";
    String TEMP_NORMAL_DISTR_FIELD_NAME = "tempPowerNormalDistribution";

    String DETAIL_LOCK_PROFIX = "APS:POWER_DETAIL:";

    String DETAIL_SYNC_DP_LOCK_PROFIX = "APS:POWER_RESULT_SYNC_DP:";


    Integer YES = 1;
    Integer NO = 0;

    enum PowerLineType {
        //需求功率行
        DEMAND,
        //最后一行需求功率
        END_DEMAND_LINE,
        //超过需求功率
        OUT_GROWTH,
        //副产物
        PRODUCT_PERCENT,
        //大于最小需求功率小于最大需求功率，但是没有需求数量的行
        SKIP_LINE;
    }

    @AllArgsConstructor
    @Getter
    enum DPLinesType {
        @ApiModelProperty("汇总")
        COLLECT(1, "汇总"),
        @ApiModelProperty("明细")
        DETAIL(2, "明细"),
        @ApiModelProperty("明细")
        INDEPENDENT(3, "独立");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;

    }

    @Getter
    @AllArgsConstructor
    enum Flag {
        MANUAL("1", "手动"),
        AUTO("0", "自动");

        String code;
        String remark;

        public static Flag match(String code) {
            Flag[] values = Flag.values();
            for (Flag value : values) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            return Flag.AUTO;
        }
    }

    interface Type {
        //电池正态比例
        String CELL_NORMAL_RATE = "电池正态比例";
        //电池还原比例
        String CELL_RESTORE_RATE = "电池还原比例";
    }

    interface DemandFlag {
        String YES = "是";
        String NO = "否";
    }

    interface DpSpecial {
        String POWER_RESERVED = "功率预留";
        String PIECE_REQUIRE = "指定标片";
        String POWER_RESERVED_PATTERN = "([0-9]*)";
        String PIECE_REQUIRE_PATTERN = "(-?[0-9]*)";
    }

    interface PowerDetailImportServiceName {
        String PERCENT = "percent";
        String MATERIAL = "material";
        String EFFIENCY = "effiency";
    }

    int POWER_DETAIL_IMPORT_ATTR_NUM = 20;

    @AllArgsConstructor
    @Getter
    enum QuarterMapping {

        Q1("q1","Q1", new Integer[]{1, 2, 3}),
        Q2("q2","Q2", new Integer[]{4, 5, 6}),
        Q3("q3","Q3", new Integer[]{7, 8, 9}),
        Q4("q4","Q4", new Integer[]{10, 11, 12});

        private String code;
        private String code2;
        private Integer[] value;

        public static Map<String, List<Integer>> loadQuarterMap() {
            Map<String, List<Integer>> quarterMap = Maps.newHashMap();
            for (QuarterMapping quarterMapping : values()) {
                quarterMap.put(quarterMapping.code, Arrays.asList(quarterMapping.value));
            }
            return quarterMap;
        }

        public static String getCode2ByVal(Integer val) {
            for (QuarterMapping quarterMapping : values()) {
                for (Integer month : quarterMapping.value) {
                    if(month.equals(val)){
                        return quarterMapping.code2;
                    }
                }
            }
            return null;
        }

        public static List<String> loadQuarterCode() {
            List<String> codes = Lists.newArrayList();
            for (QuarterMapping quarterMapping : values()) {
                codes.add(quarterMapping.code);
            }
            return codes;
        }

    }

    @AllArgsConstructor
    @Getter
    enum SheetName {
        POWER_DETAIL_PERCENT("组件功率符合率","power-prediction-email-1"),
        POWER_DETAIL_MATERIAL("辅料材料要求","power-prediction-email-2"),
        POWER_DETAIL_EFFIENCY_LINE("电池分布","power-prediction-email-3");
        String sheetName;
        String lovName;

        public static String getLov(String sheetName) {
            SheetName[] values = SheetName.values();
            for (SheetName value : values) {
                if (value.sheetName.equals(sheetName)) {
                    return value.getLovName();
                }
            }
            return null;
        }

    }

    String EMAIL_FILE_LOCAL_DIR_NAME = "./email_file";
}
