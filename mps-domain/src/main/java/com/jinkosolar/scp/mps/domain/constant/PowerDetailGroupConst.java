package com.jinkosolar.scp.mps.domain.constant;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface PowerDetailGroupConst {

    String POWER_DETAIL_GROUP_PROFIX = "YC";

    @AllArgsConstructor
    @Getter
    enum Status {
        @ApiModelProperty("预测中")
        PREDICT_WAIT(1, "预测执行中"),
        @ApiModelProperty("预测结束")
        PREDICT_OVER(2, "预测结束"),
        @ApiModelProperty("gap自动调整成功")
        GAP_SUCCESS(3, "gap自动调整结束"),
        @ApiModelProperty("gap自动调整失败")
        GAP_FAIL(4, "gap自动调整失败");

        /**
         * 编码
         */
        private Integer code;
        /**
         * 描述
         */
        private String desc;

        public static Status match(Integer code) {
            Status [] values = Status.values();
            for (Status value : values) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            return Status.PREDICT_WAIT;
        }
    }

}
