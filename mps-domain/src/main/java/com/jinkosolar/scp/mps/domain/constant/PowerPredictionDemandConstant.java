package com.jinkosolar.scp.mps.domain.constant;

/**
 * <AUTHOR>
 *
 * 功率预测试常量类
 */
public class PowerPredictionDemandConstant {

    public static final String COMMON_COLUMN_SQL = "select t.*,min(plan_date_tmp) aps_plan_date," +
            "min(t.schedule_start) schedule_start_date,max(t.schedule_end) schedule_end_date,DATE_FORMAT(plan_date_tmp,'%Y-%m') aps_month,sum(t.plan_quantity) plan_qty,sum(t.plan_quantity)*cell_string cell_demand_qty,group_concat(DISTINCT t.sap_order_line) sap_line_id,CEIL(sum(t.plan_quantity)*cell_string * t.high_grade) high_demand_qty,FLOOR(sum(t.plan_quantity)*cell_string * t.low_grade) low_demand_qty," +
            "max(t.specifySupplier) specify_supplier,max(t.specify_supplier_name1) " +
            "specify_supplier_name,max(t.odd_even_id) single_glass_identification_id,max(t.odd_even) single_glass_identification,null low_cell_efficiency,null high_cell_efficiency,null power_derating_version,null efficiency_value,null power_value,null reset_power from";

    public static final String EFFICIENCY_COLUMN_SQL = "select t.*,min(plan_date_tmp) aps_plan_date,min(t.schedule_start) schedule_start_date,max(t.schedule_end) schedule_end_date,DATE_FORMAT(plan_date_tmp,'%Y-%m') aps_month,sum(t.plan_quantity) plan_qty,sum(t.plan_quantity)*cell_string cell_demand_qty,group_concat(DISTINCT t.sap_order_line) sap_line_id,CEIL(sum(t.plan_quantity)*cell_string * t.high_grade) high_demand_qty,FLOOR(sum(t.plan_quantity)*cell_string * t.low_grade) low_demand_qty,null low_cell_efficiency,null high_cell_efficiency,rel.power_derating_version,rel.efficiency + 0 efficiency_value,rel.power power_value,t.gifts_power - rel.power reset_power from ";

    public static final String JOIN_COMMON_SQL = "left join mps_power_prediction_release rel on t.apply_power_version = rel.version and t.apply_cell_series_desc = rel.cell_series_desc and t.apply_material_combination_desc = rel.material_combination_desc and t.cell_string = rel.cell_string ";

    public static final String ORDER_BY_SQL = "order by t.demand_type,t.special_type,t.schedule_start_date";

    public static final String DEMAND_SQL =
            "(" +
            " SELECT m.id,m.tenant_id,m.opt_counter,m.is_deleted,m.created_by,m.created_time,m." +
                    "updated_by,m.updated_time,m.item_attribute60 aps_order_code,m.factory factory_id,m.factory_code,m.factory_name factory_desc,m.workshop_id,m.workshops_code,m.workshops_desc,m.work_center_id,m.work_center_code,m.work_center_desc,m.domestic_oversea area_id,lov.lov_name area_desc,lov.attribute3 group_area_id,null group_area_desc,lov2.area_id delivery_area_id,lov2.area_name delivery_area_desc,m.sap_order_no,m.sap_line_id sap_order_line,m.item_attribute65 cell_product_code,null cell_product_id,m.item_attribute40 grid_num,m.schedule_start_date schedule_start,m.schedule_end_date schedule_end,case when DATE_ADD(DATE_FORMAT(m.aps_plan_date,'%Y-%m-%d'),INTERVAL :demandDay DAY) < DATE_FORMAT(SYSDATE(),'%Y-%m-%d') then DATE_FORMAT(SYSDATE(),'%Y-%m-%d') else DATE_ADD(DATE_FORMAT(m.aps_plan_date,'%Y-%m-%d'),INTERVAL :demandDay DAY) end plan_date_tmp,m.plan_qty plan_quantity,m.five_w_binned_flag,m.five_w_binned,m.item_attribute2 gifts_power,m.item_attribute63 high_wattage,ROUND(m.item_attribute53 / 100,4) high_grade,m.item_attribute64 low_wattage,ROUND(m.item_attribute52 / 100,4) low_grade,m.item_attribute59 module_color,m.chip_string_id cell_string,m.power_forecast_version apply_power_version,m.item_attribute61 apply_cell_series_desc,m.accessory_result_desc apply_material_combination_desc,m.standard_cell_efficiency + 0 cell_efficiency,m.batch_directional is_directional,m.supervision_flag,m.customer_inspection_flag,m.specify_factory," +
                    "m.specify_supplier specifySupplier,m.specify_supplier_name specify_supplier_name1,m.item_attribute54 " +
                    "french_carbon_label,m.item_attribute55 special_french_carbon_label,m.item_attribute56 zero_carbon_label,m.item_attribute57 special_zero_carbon_label,m.item_attribute62 galvanic_cell_factory," +
                    "mt.odd_even_id,mt.odd_even" +
                    ",s.max_efficiency + 0 max_efficiency,s.min_efficiency + 0 min_efficiency," +
            " case " +
            "  when m.item_attribute62 is not null and m.item_attribute62 != '' " +
            "  then 10" +
            "  when m.specify_supplier_name is not null and m.specify_supplier_name != '' " +
            "  then 19" +
            "  when m.specify_factory is not null and m.specify_factory != '' " +
            "  then 20" +

            "  when m.item_attribute59 = 'B' or m.item_attribute59 = 'BDB' or m.item_attribute59 = 'DB'  then 21" +
            "  when mt.odd_even = '单玻' " +
            "  then 22" +
            " else" +
            "  30" +
            " end demand_type," +
            " case " +
            "  when m.item_attribute57 = 'Y'" +
            "  then 10" +
            "  when m.item_attribute56 = 'Y'" +
            "  then 20" +
            "  when m.item_attribute55 = 'Y'" +
            "  then 30" +
            "  when m.item_attribute54 = 'Y' and m.batch_directional = 'Y'" +
            "  then 40" +
            "  when m.batch_directional = 'Y'" +
            "  then 50" +
            "  when m.item_attribute54 = 'Y'" +
            "  then 60" +
//            "  when m.standard_cell_efficiency = s.max_efficiency" +
//            "  then 70" +
            "  when m.standard_cell_efficiency = s.min_efficiency" +
            "  then 80" +
            "  when m.five_w_binned_flag != 'Y'" +
            "  then 90" +
            "  when m.five_w_binned_flag = 'Y'" +
            "  then 100" +
            " else" +
            "  999" +
            " end special_type" +
            " FROM (select * from mps_module_production_plan where is_deleted = 0  and plan_version in (:planVersionList)) m" +
            " LEFT JOIN mps_module_type mt on m.plan_layout = mt.full_name" +
            " LEFT JOIN sys_lov_lines lov on m.domestic_oversea = lov.lov_line_id" +
            " LEFT JOIN (SELECT t1.lov_line_id,t1.lov_name,t1.lov_value,t1.attribute5 area_id,t3.lov_name area_name FROM sys_lov_lines t1,sys_lov_header t2,sys_lov_lines t3 where t1.lov_header_id=t2.lov_header_id AND t2.is_deleted=0 AND t1.is_deleted=0 AND t2.lov_code='SYS.ORG_ARCHITECTURE' AND t1.attribute5 = t3.lov_line_id) lov2 ON m.factory_code = lov2.lov_value" +
            " LEFT JOIN (SELECT domestic_oversea,DATE_FORMAT(aps_plan_date,'%Y-%m') aps_month,max(standard_cell_efficiency) max_efficiency,min(standard_cell_efficiency) min_efficiency FROM mps_module_production_plan WHERE 1=1 AND plan_version IN (:planVersionList) GROUP BY domestic_oversea,DATE_FORMAT(aps_plan_date,'%Y-%m')) s on m.domestic_oversea = s.domestic_oversea and DATE_FORMAT(m.aps_plan_date,'%Y-%m') = s.aps_month" +
            " WHERE 1=1 AND lov.attribute3 =:groupAreaId AND m.item_attribute65 in (:productCode)" +
            " AND plan_version in (:planVersionList) AND m.aps_plan_date >= DATE_FORMAT(SYSDATE(),'%Y-%m-%d')" +
            " ) t group by t.aps_order_code,t.factory_code,t.work_center_code,DATE_FORMAT(t.plan_date_tmp,'%Y-%m') ";

    public static final String DEMAND_SQL_DAY = "SELECT m.*,min(m.schedule_start) schedule_start_date,max(m.schedule_end) schedule_end_date,DATE_FORMAT(m.aps_plan_date,'%Y-%m') aps_month,sum(m.plan_quantity) plan_qty,sum(m.plan_quantity)*cell_string cell_demand_qty,group_concat(DISTINCT m.sap_order_line) sap_line_id, " +
            "max(m.specify_supplier1) specify_supplier,max(m.specify_supplier_name1) " +
            "specify_supplier_name,max(m.odd_even_id) single_glass_identification_id,max(m.odd_even) single_glass_identification from" +
            " (SELECT m.id,m.tenant_id,m.opt_counter,m.is_deleted,m.created_by,m.created_time,m.updated_by,m.updated_time,m.item_attribute60 aps_order_code,m.factory factory_id,m.factory_code,m.factory_name factory_desc,m.workshop_id,m.workshops_code,m.workshops_desc,m.work_center_id,m.work_center_code,m.work_center_desc,m.domestic_oversea area_id," +
            "mt.odd_even_id,mt.odd_even,lov.attribute3 group_area_id,null group_area_desc,lov.lov_name area_desc,lov2.area_id delivery_area_id,lov2.area_name delivery_area_desc,m.sap_order_no,m.sap_line_id sap_order_line,m.item_attribute65 cell_product_code,null cell_product_id,m.item_attribute40 grid_num,m.schedule_start_date schedule_start,m.schedule_end_date schedule_end,case when DATE_ADD(DATE_FORMAT(m.aps_plan_date,'%Y-%m-%d'),INTERVAL :demandDay DAY) < DATE_FORMAT(SYSDATE(),'%Y-%m-%d') then DATE_FORMAT(SYSDATE(),'%Y-%m-%d') else DATE_ADD(DATE_FORMAT(m.aps_plan_date,'%Y-%m-%d'),INTERVAL :demandDay DAY) end aps_plan_date,m.plan_qty plan_quantity,m.five_w_binned_flag,m.five_w_binned,m.item_attribute2 gifts_power,m.item_attribute63 high_wattage,ROUND(m.item_attribute53 / 100,4) high_grade,m.item_attribute64 low_wattage,ROUND(m.item_attribute52 / 100,4) low_grade,m.item_attribute59 module_color,m.chip_string_id cell_string,m.power_forecast_version apply_power_version,m.item_attribute61 apply_cell_series_desc,m.accessory_result_desc apply_material_combination_desc,m.standard_cell_efficiency + 0 cell_efficiency,m.batch_directional is_directional,m.supervision_flag,m.customer_inspection_flag,m.specify_factory," +
            "m.specify_supplier specify_supplier1,m.specify_supplier_name specify_supplier_name1,m.item_attribute54 french_carbon_label,m.item_attribute55 special_french_carbon_label,m.item_attribute56 zero_carbon_label,m.item_attribute57 special_zero_carbon_label,m.item_attribute62 galvanic_cell_factory," +
                    " case " +
                    "  when m.item_attribute62 is not null and m.item_attribute62 != '' " +
                    "  then 10" +
                    "  when m.specify_supplier_name is not null and m.specify_supplier_name != '' " +
                    "  then 19" +
                    "  when m.specify_factory is not null and m.specify_factory != '' " +
                    "  then 20" +
                    "  when m.item_attribute59 = 'B' or m.item_attribute59 = 'BDB'  or m.item_attribute59 = 'DB' then 21" +
                    "  when mt.odd_even = '单玻' " +
                    "  then 22" +
                    " else" +
                    "  30" +
                    " end demand_type," +
                    " null special_type,null high_demand_qty,null low_demand_qty,null low_cell_efficiency,null high_cell_efficiency,null power_derating_version,null efficiency_value,null power_value,null reset_power" +
                    " FROM mps_module_production_plan m" +
                    " LEFT JOIN mps_module_type mt on m.plan_layout = mt.full_name" +
                    " LEFT JOIN (SELECT t1.lov_line_id,t1.lov_name,t1.lov_value,t1.attribute5 area_id,t3.lov_name area_name FROM sys_lov_lines t1,sys_lov_header t2,sys_lov_lines t3 where t1.lov_header_id=t2.lov_header_id AND t2.is_deleted=0 AND t1.is_deleted=0 AND t2.lov_code='SYS.ORG_ARCHITECTURE' AND t1.attribute5 = t3.lov_line_id) lov2 ON m.factory_code = lov2.lov_value" +
                    " LEFT JOIN sys_lov_lines lov on m.domestic_oversea = lov.lov_line_id" +
                    " WHERE 1=1 AND lov.attribute3 =:groupAreaId AND m.item_attribute65 in (:productCode)" +
                    " AND plan_version in (select plan_version from mps_plan_verison_controll where plan_type in (SELECT concat('ZJPC-',t1.lov_value) FROM sys_lov_lines t1,sys_lov_lines t3,sys_lov_header t2 where t1.lov_header_id=t2.lov_header_id AND t2.is_deleted=0 AND t1.is_deleted=0 AND t1.attribute3 = t3.lov_line_id AND t2.lov_code='SYS.DOMESTIC_OVERSEA' and t3.lov_line_id =:groupAreaId) and status = 'Y' and is_deleted = 0) AND m.aps_plan_date >= DATE_FORMAT(SYSDATE(),'%Y-%m-%d')) m" +
                    " group by m.aps_order_code,m.factory_code,m.work_center_code,m.aps_plan_date";

    // select t.*,
    //       min(plan_date_tmp)                                      aps_plan_date,
    //       min(t.schedule_start)                                   schedule_start_date,
    //       max(t.schedule_end)                                     schedule_end_date,
    //       DATE_FORMAT(plan_date_tmp, '%Y-%m')                     aps_month,
    //       sum(t.plan_quantity)                                    plan_qty,
    //       sum(t.plan_quantity) * cell_string                      cell_demand_qty,
    //       group_concat(DISTINCT t.sap_order_line)                 sap_line_id,
    //       CEIL(sum(t.plan_quantity) * cell_string * t.high_grade) high_demand_qty,
    //       FLOOR(sum(t.plan_quantity) * cell_string * t.low_grade) low_demand_qty,
    //       max(t.specifySupplier)                                  specify_supplier,
    //       max(t.specify_supplier_name1)                           specify_supplier_name,
    //       max(t.odd_even_id)                                      single_glass_identification_id,
    //       max(t.odd_even)                                         single_glass_identification,
    //       null                                                    low_cell_efficiency,
    //       null                                                    high_cell_efficiency,
    //       null                                                    power_derating_version,
    //       null                                                    efficiency_value,
    //       null                                                    power_value,
    //       null                                                    reset_power
    //from (SELECT m.id,
    //             m.tenant_id,
    //             m.opt_counter,
    //             m.is_deleted,
    //             m.created_by,
    //             m.created_time,
    //             m.updated_by,
    //             m.updated_time,
    //             m.item_attribute60                                                                       aps_order_code,
    //             m.factory                                                                                factory_id,
    //             m.factory_code,
    //             m.factory_name                                                                           factory_desc,
    //             m.workshop_id,
    //             m.workshops_code,
    //             m.workshops_desc,
    //             m.work_center_id,
    //             m.work_center_code,
    //             m.work_center_desc,
    //             m.domestic_oversea                                                                       area_id,
    //             lov.lov_name                                                                             area_desc,
    //             lov.attribute3                                                                           group_area_id,
    //             null                                                                                     group_area_desc,
    //             lov2.area_id                                                                             delivery_area_id,
    //             lov2.area_name                                                                           delivery_area_desc,
    //             m.sap_order_no,
    //             m.sap_line_id                                                                            sap_order_line,
    //             m.item_attribute65                                                                       cell_product_code,
    //             null                                                                                     cell_product_id,
    //             m.item_attribute40                                                                       grid_num,
    //             m.schedule_start_date                                                                    schedule_start,
    //             m.schedule_end_date                                                                      schedule_end,
    //             case
    //                 when DATE_ADD(DATE_FORMAT(m.aps_plan_date, '%Y-%m-%d'), INTERVAL :demandDay DAY) <
    //                      DATE_FORMAT(SYSDATE(), '%Y-%m-%d') then DATE_FORMAT(SYSDATE(), '%Y-%m-%d')
    //                 else DATE_ADD(DATE_FORMAT(m.aps_plan_date, '%Y-%m-%d'), INTERVAL :demandDay DAY) end plan_date_tmp,
    //             m.plan_qty                                                                               plan_quantity,
    //             m.five_w_binned_flag,
    //             m.five_w_binned,
    //             m.item_attribute2                                                                        gifts_power,
    //             m.item_attribute63                                                                       high_wattage,
    //             ROUND(m.item_attribute53 / 100, 4)                                                       high_grade,
    //             m.item_attribute64                                                                       low_wattage,
    //             ROUND(m.item_attribute52 / 100, 4)                                                       low_grade,
    //             m.item_attribute59                                                                       module_color,
    //             m.chip_string_id                                                                         cell_string,
    //             m.power_forecast_version                                                                 apply_power_version,
    //             m.item_attribute61                                                                       apply_cell_series_desc,
    //             m.accessory_result_desc                                                                  apply_material_combination_desc,
    //             m.standard_cell_efficiency + 0                                                           cell_efficiency,
    //             m.batch_directional                                                                      is_directional,
    //             m.supervision_flag,
    //             m.customer_inspection_flag,
    //             m.specify_factory,
    //             m.specify_supplier                                                                       specifySupplier,
    //             m.specify_supplier_name                                                                  specify_supplier_name1,
    //             m.item_attribute54                                                                       french_carbon_label,
    //             m.item_attribute55                                                                       special_french_carbon_label,
    //             m.item_attribute56                                                                       zero_carbon_label,
    //             m.item_attribute57                                                                       special_zero_carbon_label,
    //             m.item_attribute62                                                                       galvanic_cell_factory,
    //             mt.odd_even_id,
    //             mt.odd_even,
    //             s.max_efficiency + 0                                                                     max_efficiency,
    //             s.min_efficiency + 0                                                                     min_efficiency,
    //             case
    //                 when m.item_attribute62 is not null and m.item_attribute62 != '' then 10
    //                 when m.specify_supplier_name is not null and m.specify_supplier_name != '' then 19
    //                 when m.specify_factory is not null and m.specify_factory != '' then 20
    //                 when m.item_attribute59 = 'B' or m.item_attribute59 = 'BDB' or m.item_attribute59 = 'DB' then 21
    //                 when mt.odd_even = '单玻' then 22
    //                 else 30 end                                                                          demand_type,
    //             case
    //                 when m.item_attribute57 = 'Y' then 10
    //                 when m.item_attribute56 = 'Y' then 20
    //                 when m.item_attribute55 = 'Y' then 30
    //                 when m.item_attribute54 = 'Y' and m.batch_directional = 'Y' then 40
    //                 when m.batch_directional = 'Y' then 50
    //                 when m.item_attribute54 = 'Y' then 60
    //                 when m.standard_cell_efficiency = s.min_efficiency then 80
    //                 when m.five_w_binned_flag != 'Y' then 90
    //                 when m.five_w_binned_flag = 'Y' then 100
    //                 else 999 end                                                                         special_type
    //      FROM (select * from mps_module_production_plan a where a.is_deleted = 0 and a.id in (:planIdList)) m
    //               LEFT JOIN mps_module_type mt on m.plan_layout = mt.full_name
    //               LEFT JOIN sys_lov_lines lov on m.domestic_oversea = lov.lov_line_id
    //               LEFT JOIN (SELECT t1.lov_line_id, t1.lov_name, t1.lov_value, t1.attribute5 area_id, t3.lov_name area_name
    //                          FROM sys_lov_lines t1,
    //                               sys_lov_header t2,
    //                               sys_lov_lines t3
    //                          where t1.lov_header_id = t2.lov_header_id
    //                            AND t2.is_deleted = 0
    //                            AND t1.is_deleted = 0
    //                            AND t2.lov_code = 'SYS.ORG_ARCHITECTURE'
    //                            AND t1.attribute5 = t3.lov_line_id) lov2 ON m.factory_code = lov2.lov_value
    //               LEFT JOIN (SELECT domestic_oversea,
    //                                 DATE_FORMAT(aps_plan_date, '%Y-%m') aps_month,
    //                                 max(standard_cell_efficiency)       max_efficiency,
    //                                 min(standard_cell_efficiency)       min_efficiency
    //                          FROM mps_module_production_plan b
    //                          WHERE 1 = 1
    //                            AND b.id IN (:planIdList)
    //                          GROUP BY domestic_oversea, DATE_FORMAT(aps_plan_date, '%Y-%m')) s
    //                         on m.domestic_oversea = s.domestic_oversea and
    //                            DATE_FORMAT(m.aps_plan_date, '%Y-%m') = s.aps_month
    //      WHERE 1 = 1
    //        AND m.id in (:planIdList)
    //        AND m.aps_plan_date >= DATE_FORMAT(SYSDATE(), '%Y-%m-%d')) t
    //group by t.aps_order_code, t.factory_code, t.work_center_code, DATE_FORMAT(t.plan_date_tmp, '%Y-%m');

    public static final String DEMAND_SQL_BY_ID_LIST = "select t.*,\n" +
            "       min(plan_date_tmp)                                      aps_plan_date,\n" +
            "       min(t.schedule_start)                                   schedule_start_date,\n" +
            "       max(t.schedule_end)                                     schedule_end_date,\n" +
            "       DATE_FORMAT(plan_date_tmp, '%Y-%m')                     aps_month,\n" +
            "       sum(t.plan_quantity)                                    plan_qty,\n" +
            "       sum(t.plan_quantity) * cell_string                      cell_demand_qty,\n" +
            "       group_concat(DISTINCT t.sap_order_line)                 sap_line_id,\n" +
            "       CEIL(sum(t.plan_quantity) * cell_string * t.high_grade) high_demand_qty,\n" +
            "       FLOOR(sum(t.plan_quantity) * cell_string * t.low_grade) low_demand_qty,\n" +
            "       max(t.specifySupplier)                                  specify_supplier,\n" +
            "       max(t.specify_supplier_name1)                           specify_supplier_name,\n" +
            "       max(t.odd_even_id)                                      single_glass_identification_id,\n" +
            "       max(t.odd_even)                                         single_glass_identification,\n" +
            "       null                                                    low_cell_efficiency,\n" +
            "       null                                                    high_cell_efficiency,\n" +
            "       null                                                    power_derating_version,\n" +
            "       null                                                    efficiency_value,\n" +
            "       null                                                    power_value,\n" +
            "       null                                                    reset_power\n" +
            "from (SELECT m.id,\n" +
            "             m.tenant_id,\n" +
            "             m.opt_counter,\n" +
            "             m.is_deleted,\n" +
            "             m.created_by,\n" +
            "             m.created_time,\n" +
            "             m.updated_by,\n" +
            "             m.updated_time,\n" +
            "             m.item_attribute60                                                                       aps_order_code,\n" +
            "             m.factory                                                                                factory_id,\n" +
            "             m.factory_code,\n" +
            "             m.factory_name                                                                           factory_desc,\n" +
            "             m.workshop_id,\n" +
            "             m.workshops_code,\n" +
            "             m.workshops_desc,\n" +
            "             m.work_center_id,\n" +
            "             m.work_center_code,\n" +
            "             m.work_center_desc,\n" +
            "             m.domestic_oversea                                                                       area_id,\n" +
            "             lov.lov_name                                                                             area_desc,\n" +
            "             lov.attribute3                                                                           group_area_id,\n" +
            "             null                                                                                     group_area_desc,\n" +
            "             lov2.area_id                                                                             delivery_area_id,\n" +
            "             lov2.area_name                                                                           delivery_area_desc,\n" +
            "             m.sap_order_no,\n" +
            "             m.sap_line_id                                                                            sap_order_line,\n" +
            "             m.item_attribute65                                                                       cell_product_code,\n" +
            "             null                                                                                     cell_product_id,\n" +
            "             m.item_attribute40                                                                       grid_num,\n" +
            "             m.schedule_start_date                                                                    schedule_start,\n" +
            "             m.schedule_end_date                                                                      schedule_end,\n" +
            "             case\n" +
            "                 when DATE_ADD(DATE_FORMAT(m.aps_plan_date, '%Y-%m-%d'), INTERVAL :demandDay DAY) <\n" +
            "                      DATE_FORMAT(SYSDATE(), '%Y-%m-%d') then DATE_FORMAT(SYSDATE(), '%Y-%m-%d')\n" +
            "                 else DATE_ADD(DATE_FORMAT(m.aps_plan_date, '%Y-%m-%d'), INTERVAL :demandDay DAY) end plan_date_tmp,\n" +
            "             m.plan_qty                                                                               plan_quantity,\n" +
            "             m.five_w_binned_flag,\n" +
            "             m.five_w_binned,\n" +
            "             m.item_attribute2                                                                        gifts_power,\n" +
            "             m.item_attribute63                                                                       high_wattage,\n" +
            "             ROUND(m.item_attribute53 / 100, 4)                                                       high_grade,\n" +
            "             m.item_attribute64                                                                       low_wattage,\n" +
            "             ROUND(m.item_attribute52 / 100, 4)                                                       low_grade,\n" +
            "             m.item_attribute59                                                                       module_color,\n" +
            "             m.chip_string_id                                                                         cell_string,\n" +
            "             m.power_forecast_version                                                                 apply_power_version,\n" +
            "             m.item_attribute61                                                                       apply_cell_series_desc,\n" +
            "             m.accessory_result_desc                                                                  apply_material_combination_desc,\n" +
            "             m.standard_cell_efficiency + 0                                                           cell_efficiency,\n" +
            "             m.batch_directional                                                                      is_directional,\n" +
            "             m.supervision_flag,\n" +
            "             m.customer_inspection_flag,\n" +
            "             m.specify_factory,\n" +
            "             m.specify_supplier                                                                       specifySupplier,\n" +
            "             m.specify_supplier_name                                                                  specify_supplier_name1,\n" +
            "             m.item_attribute54                                                                       french_carbon_label,\n" +
            "             m.item_attribute55                                                                       special_french_carbon_label,\n" +
            "             m.item_attribute56                                                                       zero_carbon_label,\n" +
            "             m.item_attribute57                                                                       special_zero_carbon_label,\n" +
            "             m.item_attribute62                                                                       galvanic_cell_factory,\n" +
            "             mt.odd_even_id,\n" +
            "             mt.odd_even,\n" +
            "             s.max_efficiency + 0                                                                     max_efficiency,\n" +
            "             s.min_efficiency + 0                                                                     min_efficiency,\n" +
            "             case\n" +
            "                 when m.item_attribute62 is not null and m.item_attribute62 != '' then 10\n" +
            "                 when m.specify_supplier_name is not null and m.specify_supplier_name != '' then 19\n" +
            "                 when m.specify_factory is not null and m.specify_factory != '' then 20\n" +
            "                 when m.item_attribute59 = 'B' or m.item_attribute59 = 'BDB' or m.item_attribute59 = 'DB' then 21\n" +
            "                 when mt.odd_even = '单玻' then 22\n" +
            "                 else 30 end                                                                          demand_type,\n" +
            "             case\n" +
            "                 when m.item_attribute57 = 'Y' then 10\n" +
            "                 when m.item_attribute56 = 'Y' then 20\n" +
            "                 when m.item_attribute55 = 'Y' then 30\n" +
            "                 when m.item_attribute54 = 'Y' and m.batch_directional = 'Y' then 40\n" +
            "                 when m.batch_directional = 'Y' then 50\n" +
            "                 when m.item_attribute54 = 'Y' then 60\n" +
            "                 when m.standard_cell_efficiency = s.min_efficiency then 80\n" +
            "                 when m.five_w_binned_flag != 'Y' then 90\n" +
            "                 when m.five_w_binned_flag = 'Y' then 100\n" +
            "                 else 999 end                                                                         special_type\n" +
            "      FROM (select * from mps_module_production_plan a where a.is_deleted = 0 and a.id in (:planIdList)) m\n" +
            "               LEFT JOIN mps_module_type mt on m.plan_layout = mt.full_name\n" +
            "               LEFT JOIN sys_lov_lines lov on m.domestic_oversea = lov.lov_line_id\n" +
            "               LEFT JOIN (SELECT t1.lov_line_id, t1.lov_name, t1.lov_value, t1.attribute5 area_id, t3.lov_name area_name\n" +
            "                          FROM sys_lov_lines t1,\n" +
            "                               sys_lov_header t2,\n" +
            "                               sys_lov_lines t3\n" +
            "                          where t1.lov_header_id = t2.lov_header_id\n" +
            "                            AND t2.is_deleted = 0\n" +
            "                            AND t1.is_deleted = 0\n" +
            "                            AND t2.lov_code = 'SYS.ORG_ARCHITECTURE'\n" +
            "                            AND t1.attribute5 = t3.lov_line_id) lov2 ON m.factory_code = lov2.lov_value\n" +
            "               LEFT JOIN (SELECT domestic_oversea,\n" +
            "                                 DATE_FORMAT(aps_plan_date, '%Y-%m') aps_month,\n" +
            "                                 max(standard_cell_efficiency)       max_efficiency,\n" +
            "                                 min(standard_cell_efficiency)       min_efficiency\n" +
            "                          FROM mps_module_production_plan b\n" +
            "                          WHERE 1 = 1\n" +
            "                            AND b.id IN (:planIdList)\n" +
            "                          GROUP BY domestic_oversea, DATE_FORMAT(aps_plan_date, '%Y-%m')) s\n" +
            "                         on m.domestic_oversea = s.domestic_oversea and\n" +
            "                            DATE_FORMAT(m.aps_plan_date, '%Y-%m') = s.aps_month\n" +
            "      WHERE 1 = 1\n" +
            "        AND m.id in (:planIdList)\n" +
            "        ) t\n" +
            "group by t.id, t.aps_order_code, t.factory_code, t.work_center_code, DATE_FORMAT(t.plan_date_tmp, '%Y-%m')";
}
