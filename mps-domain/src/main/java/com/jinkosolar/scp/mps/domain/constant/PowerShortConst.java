package com.jinkosolar.scp.mps.domain.constant;

import java.math.BigDecimal;

public interface PowerShortConst {

    //起始版本
    String INIT_DATA_VERSION = "1";
    //最大功率段数
    int MAX_EFFICIENCY_SEGMENT_IDX = 5;
    //最小功率段数
    int MIN_EFFICIENCY_SEGMENT_IDX = 1;
    //默认功率行划分步长
    BigDecimal DEFAULT_POWER_LINE_RANGE = new BigDecimal("5");
    //扩展字段起始数字
    int EFFICIENCY_FIELD_START_NUM = 1;
    //扩展字段结束数字
    int EFFICIENCY_FIELD_END_NUM = 40;
    //扩展字段起始数字
    int ITEM_ATTRIBUTE_FIELD_START_NUM = 1;
    //扩展字段结束数字
    int ITEM_ATTRIBUTE_FIELD_END_NUM = 20;
    //是功率行
    int IS_POWER_LINE = 1;
    //动态字段线缆长度的编码
    String ATTR_CABLE_LENGTH = "cable_length";
    String SUB_TITLE_FIELD_NAME = "subTitle";
    String EFFICIENCY_FIELD_NAME = "efficiency";

    String RESULT_LOCK_PROFIX = "APS_POWER_RESULT:";
    /**
     * 是否重新计算结果数据
     */
    interface RecalcResultData {
        int YES = 1;
        int NO = 0;
    }

    interface PowerChangeRuleType {
        String STATE_CHANGE = "状态转换";
        String MATERIAL_CHANGE = "材料转换";
        String STEP_CHANGE = "步长转换";
    }
}
