package com.jinkosolar.scp.mps.domain.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface RealityMaterialRuleConst {

    @Getter
    @AllArgsConstructor
    enum Type {
        DP_ID("dp_id", "DP-ID"),
        PRODUCT_FAMILY("product_family", "产品族");

        String code;
        String name;

        public static Type match(String code){
            for (Type value : values()) {
                if(value.getCode().equals(code) || value.getName().equals(code)){
                    return value;
                }
            }
            return null;
        }
    }

    @Getter
    @AllArgsConstructor
    enum Y_N {
        Y("是","Y"),
        N("否","N");

        String name;
        String code;


        public static Y_N match(String name){
            for (Y_N value : values()) {
                if(value.getCode().equals(name) || value.getName().equals(name)){
                    return value;
                }
            }
            return null;
        }
    }

}
