package com.jinkosolar.scp.mps.domain.constant;

/**
 * <AUTHOR>
 *
 * 销售预测需求匹配常量类
 */
public class SalesForecastPlanConstant {

    // 国内产能 Domestic
    public static String DOMESTIC_DOMESTIC = "Domestic";

    // 海外产能 Oversea
    public static String DOMESTIC_OVERSEA = "Oversea";

    // 中长期统计区域 国内+山西
    public static String AREA_DOMESTIC_A_NAME = "国内+山西";

    // 中长期统计区域 海外
    public static String AREA_DOMESTIC_B_NAME = "海外";

    // 中长期统计区域 整体
    public static String AREA_DOMESTIC_ALL_NAME = "整体";

    // 中长期统计区域 国内+山西
    public static String AREA_DOMESTIC_A_CODE = "国内+山西";

    // 中长期统计区域 海外
    public static String AREA_DOMESTIC_B_CODE = "海外";

    // 排序编码 A
    public static String AREA_ORDER_C_CODE = "C";

    // 排序编码 B
    public static String AREA_ORDER_A_CODE = "A";


    // 排序编码 B
    public static String AREA_ORDER_B_CODE = "B";

    public static String TYPE_NAME_ONE = "销售需求预测";

    public static String TYPE_NAME_TWO = "组件产量规划";

    public static String TYPE_NAME_THREE = "匹配差异";

    // Q1
    public static String YEAR_Q1 = "Q1";
    // Q2
    public static String YEAR_Q2 = "Q2";
    // Q3
    public static String YEAR_Q3 = "Q3";
    // Q4
    public static String YEAR_Q4 = "Q4";

    public static String SUM_PRODUCT_TYPE = "小计";

    public static String PLACE_ORDER_FLAG_INVENTORY = "Inventory(existing)";


}
