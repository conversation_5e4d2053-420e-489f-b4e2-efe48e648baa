package com.jinkosolar.scp.mps.domain.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface ScheduleLinesConst {

    String SCHEDULE_LINES_BUILD_EMAIL_FILE_LOCK = "APS:SCHEDULE_LINES_FILE_LOCK:";

    String CAPACITY_UTILIZATION_LOCK = "APS:CAPACITY_UTILIZATION_LOCK:";
    String COMPONENT_PLAN_BACKUP = "APS:COMPONENT_PLAN_BACKUP:";

    String SCHEDULE_EMAIL_TASK_CODE_PROFIX = "ZJJH_EMAIL_";

    String DEFAULT_SEPARATOR = "\\*";

    String ALL_SCOPE = "-1";

    String EMAIL_FILE_LOCAL_DIR_NAME = "./email_file";
    //邮件发送阈值
    Integer EMAIL_SEND_THRESHOLD = 3;

    String EMAIL_TEMPLATE = "schedule_lines_email.ftl";

    interface ScheduleEmailTaskStatus {
        //构建文件
//        String BUILD_FILE = "00";
        //构建文件失败
        String BUILD_FILE_ERR = "01";
        //构建文件成功
        String BUILD_FILE_SUCCESS = "02";
    }

    interface ScheduleEmailTaskLineStatus {
        //未发送文件
        String WAIT_SEND_FILE = "10";
        //发送文件失败
        String SEND_FILE_ERR = "11";
        //发送文件成功
        String SEND_FILE_SUCCESS = "12";
    }

    @AllArgsConstructor
    @Getter
    enum ScheduleEmailFileName{
        IN_INVENTORY(ScheduleEmailTaskDataType.IN_INVENTORY, "入库计划"),
        COMPONENT_PLAN(ScheduleEmailTaskDataType.COMPONENT_PLAN, "投产计划");

        String code;
        String name;

        public static ScheduleEmailFileName match(String code) {
            ScheduleEmailFileName [] values = ScheduleEmailFileName.values();
            for (ScheduleEmailFileName value : values) {
                if (value.code.equals(code)) {
                    return value;
                }
            }
            return null;
        }
    }

    interface ScheduleEmailTaskDataType {
        //组件入库计划
        String IN_INVENTORY = "inInventory";
        //组件排产计划
        String COMPONENT_PLAN = "componentPlan";
    }

}
