package com.jinkosolar.scp.mps.domain.constant;

/**
 * <AUTHOR>
 *
 * 补减投报表常量类
 */
public class SupplementReduceInvestmentConstant {


    public static final String REPORT_SQL_DAY = "select ROW_NUMBER() over(ORDER BY a.sap_order_no) id,null tenant_id,null opt_counter,null is_deleted,null created_by,null created_time,null updated_by,null updated_time,a.sap_order_no,a.sap_line_id,a.factory_code,a.demand_qty,a.plan_type,a.convert_inventory_rate,a.planned_complete_date,a.actual_quantity,IFNULL(t1.fi_quantity,0) ok_quantity,IFNULL(t2.fi_quantity,0) ng_quantity,IFNULL(t3.fi_quantity,0) jk_quantity,IFNULL(t4.fi_quantity,0) bf_quantity,null workshop_yield_rate,null out_put_quantity,null wip_quantity,null order_yield_rate,null demand_diff_qty,null bjt_quantity from" +
            " (" +
            " select DISTINCT a.sap_order_no,a.sap_line_id,a.factory_code,a.demand_qty,a.plan_type,a.item_attribute44 convert_inventory_rate,a.planned_complete_date,b.actual_quantity from" +
            " (" +
            " select sap_order_no,sap_line_id,specify_base factory_code,demand_qty,plan_type,item_attribute44,planned_complete_date from dp_demand_lines where demand_type = 'dp' and " +
             "version_num in  " +
            " (select max(version_num) from dp_demand_lines where (specify_base is not null and specify_base != '') and is_deleted = 0 and demand_type = 'dp' group by sap_order_no,sap_line_id,specify_base)" +
            " and (specify_base is not null and specify_base != '') and (plan_type is not null and plan_type != '') and is_deleted = 0" +
            " ) a," +
            " (" +
            " select sales_order_no sap_order_no,sales_order_line_no sap_line_id,factory_code,sum(actual_production_quantity - actual_cancelled_quantity) actual_quantity from mps_module_actual_production_quantity where is_deleted = 0 group by sales_order_no,sales_order_line_no,factory_code" +
            " ) b" +
            " where a.sap_order_no = b.sap_order_no and a.sap_line_id = b.sap_line_id and a.factory_code = b.factory_code" +
            " ) a " +
            " left join (select sales_order_no sap_order_no,sales_order_line_no sap_line_id,factory_code,sum(fi_quantity) fi_quantity from mps_fi_posting_detail where fi_judgement_result = 'OK' and is_deleted = 0 group by sales_order_no,sales_order_line_no,factory_code,fi_judgement_result) t1 on a.sap_order_no = t1.sap_order_no and a.sap_line_id = t1.sap_line_id and a.factory_code = t1.factory_code" +
            " left join (select sales_order_no sap_order_no,sales_order_line_no sap_line_id,factory_code,sum(fi_quantity) fi_quantity from mps_fi_posting_detail where fi_judgement_result = 'NG' and is_deleted = 0 group by sales_order_no,sales_order_line_no,factory_code,fi_judgement_result) t2 on a.sap_order_no = t2.sap_order_no and a.sap_line_id = t2.sap_line_id and a.factory_code = t2.factory_code" +
            " left join (select sales_order_no sap_order_no,sales_order_line_no sap_line_id,factory_code,sum(fi_quantity) fi_quantity from mps_fi_posting_detail where fi_judgement_result = 'JK' and is_deleted = 0 group by sales_order_no,sales_order_line_no,factory_code,fi_judgement_result) t3 on a.sap_order_no = t3.sap_order_no and a.sap_line_id = t3.sap_line_id and a.factory_code = t3.factory_code" +
            " left join (select sales_order_no sap_order_no,sales_order_line_no sap_line_id,factory_code,sum(fi_quantity) fi_quantity from mps_fi_posting_detail where fi_judgement_result = '报废' and is_deleted = 0 group by sales_order_no,sales_order_line_no,factory_code,fi_judgement_result) t4 on a.sap_order_no = t4.sap_order_no and a.sap_line_id = t4.sap_line_id and a.factory_code = t4.factory_code";

    public static final String WORKSHOP_YIELD_SQL = "select ifnull(plan_to_inventory_rate,0) plan_to_inventory_rate,ifnull(plan_to_reduce_rate,0) plan_to_reduce_rate,plan_version,product_factory_code,m.full_name plan_type from mps_plan_good_rate r,mps_module_type m where r.plan_version = m.id and r.is_deleted = 0 and r.is_deleted = 0 group by ifnull(plan_to_inventory_rate,0),ifnull(plan_to_reduce_rate,0),plan_version,product_factory_code";
}
