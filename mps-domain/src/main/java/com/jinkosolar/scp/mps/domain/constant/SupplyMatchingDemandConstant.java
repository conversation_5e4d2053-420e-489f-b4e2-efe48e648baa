package com.jinkosolar.scp.mps.domain.constant;

/**
 * <AUTHOR>
 *
 * 功率预测供需匹配常量类
 */
public class SupplyMatchingDemandConstant {

    // 组件颜色B
    public static final String MODULE_COLOR_B = "B";

    // 组件颜色BDB
    public static final String MODULE_COLOR_BDB = "BDB";


    // 组件颜色DB
    public static final String MODULE_COLOR_DB = "DB";

    // 是否定向 是
    public static final String IS_DIRECTIONAL_YES = "Y";

    public static final String DIRECTIONAL_CODE = "directional";

    public static final String NON_DIRECTIONAL_CODE = "non directional";

    public static final String DIRECTIONAL_NAME = "定向";

    public static final String NON_DIRECTIONAL_NAME = "非定向";

    // 是否定向 否
    public static final String IS_DIRECTIONAL_NO = "N";

    // 是否监造 是
    public static final String SUPERVISION_FLAG_YES = "Y";

    // 是否验货 是
    public static final String CUSTOMER_INSPECTION_YES = "Y";

    // 法碳订单 是
    public static final String FRENCH_CARBON_YES = "Y";

    // 法碳订单 是
    public static final String FRENCH_CARBON_NO = "N";

    // 特殊法碳订单 是
    public static final String SPECIAL_FRENCH_CARBON_YES = "Y";

    // 零碳订单 是
    public static final String ZERO_CARBON_YES = "Y";

    // 特殊零碳订单 是
    public static final String SPECIAL_ZERO_CARBON_YES = "Y";

    // 晶科包装_01
    public static final String JK_PACKAGE_VALUE = "JK01";

    // 晶科包装_02
    public static final String JK_PACKAGE_OTHER = "JK02";

    // 电池颜色深蓝C
    public static final String CELL_COLOR_SL_C = "深蓝C";

    // 电池颜色深蓝B
    public static final String CELL_COLOR_SL_B = "深蓝B";

    // 组件颜色 全黑
    public static final String MODULE_COLOR_QH = "全黑";

    // 组件颜色 常规
    public static final String MODULE_COLOR_CG = "常规";

    // 需求
    public static final String DEMAND_DATA_TYPE_NAME_A = "需求";

    // 供应
    public static final String DEMAND_DATA_TYPE_NAME_B = "供应";

    // 结存
    public static final String DEMAND_DATA_TYPE_NAME_C = "结存";

    // A
    public static final String DEMAND_DATA_TYPE_A = "A";

    // B
    public static final String DEMAND_DATA_TYPE_B = "B";

    // C
    public static final String DEMAND_DATA_TYPE_C = "C";

    // D
    public static final String DEMAND_DATA_TYPE_D = "D";

    // E
    public static final String DEMAND_DATA_TYPE_E = "E";

    // F
    public static final String DEMAND_DATA_TYPE_F = "F";


    // 需求合计
    public static final String DEMAND_DATA_SUMMARY_NAME_A = "需求合计";

    // 结存合计
    public static final String DEMAND_DATA_SUMMARY_NAME_B = "结存合计";

    // 周转天数
    public static final String DEMAND_DATA_SUMMARY_NAME_C = "周转天数";

    public static final String CELL_ALLOCATION_NOTICE_TITLE = "电池分配结果通知";

    public static final String CELL_ALLOCATION_NOTICE_CONTENT = "电池分配已计算完成，请查阅！";
}
