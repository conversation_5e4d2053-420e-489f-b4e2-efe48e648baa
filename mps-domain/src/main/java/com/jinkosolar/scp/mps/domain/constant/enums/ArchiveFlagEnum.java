package com.jinkosolar.scp.mps.domain.constant.enums;

import com.ibm.scp.common.api.annotation.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 归档标识
 */
@Getter
@AllArgsConstructor
public enum ArchiveFlagEnum implements BaseEnum {
    /**
     * Y 冻结
     */
    FREEZE("Y", "冻结"),
    /**
     * N 正常
     */
    NORMAL("N", "正常");
    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据描述获取爬坡产能类型编码
     *
     * @param desc 编码
     * @return 描述
     */
    public static ArchiveFlagEnum getByDesc(String desc) {
        return Stream.of(ArchiveFlagEnum.values())
                .filter(p -> p.desc.equals(desc))
                .findAny()
                .orElse(null);
    }

    /**
     * 根据编码获取爬坡产能类型描述
     *
     * @param code 编码
     * @return 描述
     */
    public static ArchiveFlagEnum getByCode(String code) {
        for (ArchiveFlagEnum anEnum : ArchiveFlagEnum.values()) {
            if (Objects.equals(anEnum.getCode(), code)) {
                return anEnum;
            }
        }
        return null;
    }

    @Override
    public String getRemark() {
        return this.desc;
    }
}
