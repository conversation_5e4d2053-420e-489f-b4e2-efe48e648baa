package com.jinkosolar.scp.mps.domain.constant.enums;

import cn.hutool.core.util.EnumUtil;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 电池每日结存类型
 *
 * <AUTHOR>
 * @date 2022年7月26日17:03:46
 */
@AllArgsConstructor
@Getter
public enum CellLeftTypeEnum {
    /**
     * 需求
     */
    @ApiModelProperty("需求")
    DEMAND(1, "DEMAND", "需求"),
    /**
     * 供应
     */
    @ApiModelProperty("供应")
    SUPPLY(2, "SUPPLY", "供应"),
    /**
     * 结余
     */
    @ApiModelProperty("结余")
    SURPLUS(3, "SURPLUS", "结余"),
    /**
     * 结余
     */
    @ApiModelProperty("结余汇总")
    SURPLUS_SUMMARY(4, "SURPLUS_SUMMARY", "结余汇总"),
    /**
     * 周转天数
     */
    @ApiModelProperty("周转天数")
    TURNOVER_DAY(5, "TURNOVER_DAY", "周转天数"),
    /**
     * 周转数量
     */
    @ApiModelProperty("周转数量")
    TURNOVER(6, "TURNOVER", "周转数量"),
    /**
     * 外购需求
     */
    @ApiModelProperty("外购需求")
    PURCHASE_DEMAND(7, "PURCHASE_DEMAND", "外购需求"),
    /**
     * 累计外购需求
     */
    @ApiModelProperty("累计外购需求")
    SUMMARY_PURCHASE_DEMAND(8, "SUMMARY_PURCHASE_DEMAND", "累计外购需求");

    /**
     * 排序
     */
    private Integer sort;
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    /**
     * 根据编码获取描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDesc(String code) {
        return Optional.ofNullable(code).map(c -> EnumUtil.likeValueOf(CellLeftTypeEnum.class, c))
                .map(CellLeftTypeEnum::getDesc).orElse(null);
    }

    /**
     * 根据编码获取排序
     *
     * @param code 编码
     * @return 排序
     */
    public static Integer sort(String code) {
        return Optional.ofNullable(code).map(c -> EnumUtil.likeValueOf(CellLeftTypeEnum.class, c))
                .map(CellLeftTypeEnum::getSort).orElse(Integer.MAX_VALUE);
    }

    /**
     * 根据描述获取编码
     *
     * @param desc 描述
     * @return 编码
     */
    public static String getCode(String desc) {
        return Arrays.stream(values()).filter(e -> e.getDesc().equals(desc)).findFirst()
                .map(CellLeftTypeEnum::getCode).orElse(null);
    }

    /**
     * 统计使用
     * @return
     */
    public static List<String> statistics(){
        return Lists.newArrayList(DEMAND.getCode(), SURPLUS.getCode(), SUPPLY.getCode());
    }
}
