package com.jinkosolar.scp.mps.domain.constant.enums;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jinkosolar.scp.mps.domain.util.MathUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: ItemType
 * @date 2023/12/17 15:39
 */
@Getter
@AllArgsConstructor
public enum ItemType {

    /**
     * 电池片产量
     */
    AOP_PRODUCTION("aopProduction", "电池片产量"),

    /**
     * 推A-比例
     */
    PUSH_EFFICIENCY_PERCENT_NOT_A("pushEfficiencyPercentNotA", "推A-比例"),

    /**
     * 推A-效率
     */
    PUSH_EFFICIENCY_NOT_A("pushEfficiencyNotA", "推A-效率"),

    /**
     * 推A-功率
     */
    PUSH_POWER_NOT_A("pushPowerNotA", "推A-功率"),

    /**
     * 电池片产量
     */
    AOP_PRODUCTION_NOT_A("aopProductionNotA", "电池片产量A-"),

    /**
     * 正常A比例
     */
    EFFICIENCY_PERCENT_RELEASE("efficiencyPercentRelease", "正常A比例"),

    /**
     * 正常A效率
     */
    EFFICIENCY_RELEASE("efficiencyRelease", "正常A效率"),

    /**
     * 正常A功率
     */
    POWER_RELEASE("powerRelease", "正常A功率"),

    /**
     * 正常A-比例
     */
    EFFICIENCY_PERCENT_NOT_A("efficiencyPercentNotA", "正常A-比例"),

    /**
     * 正常A-效率
     */
    EFFICIENCY_NOT_A("efficiencyNotA", "正常A-效率"),

    /**
     * 正常A-功率
     */
    POWER_NOT_A("powerNotA", "正常A-功率"),

    /**
     * 推A比例
     */
    PUSH_EFFICIENCY_PERCENT_A("pushEfficiencyPercentA", "推A比例"),

    /**
     * 推A效率
     */
    PUSH_EFFICIENCY_A("pushEfficiencyA", "推A效率"),

    /**
     * 推A功率
     */
    PUSH_POWER_A("pushPowerA", "推A功率"),

    /**
     * total效率
     */
    TOTAL_EFFICIENCY("totalEfficiency", "total效率"),

    /**
     * total功率
     */
    TOTAL_POWER("totalPower", "total功率");

    String code;
    String remark;

    public static ItemType match(String code) {
        ItemType[] values = ItemType.values();
        for (ItemType value : values) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static LinkedHashMap sortMap(Map map) {
        LinkedHashMap result = Maps.newLinkedHashMap();
        ItemType[] values = ItemType.values();
        for (ItemType value : values) {
            if (map.containsKey(value.code)) {
                BigDecimal numerical = (BigDecimal) map.get(value.code);
                result.put(value.code, value.verifyPercent() ? MathUtils.num2PerStr(numerical) : MathUtils.num2str(numerical, 2));
            }
        }
        return result;
    }


    /**
     * 判断当前是否是百分比数值
     *
     * @return
     */
    public boolean verifyPercent() {
        return isPercentItem().contains(this);
    }

    /**
     * 是百分比的数值
     *
     * @return
     */
    private List<ItemType> isPercentItem() {
        return Lists.newArrayList(ItemType.PUSH_EFFICIENCY_PERCENT_A, ItemType.EFFICIENCY_PERCENT_NOT_A, ItemType.PUSH_EFFICIENCY_PERCENT_NOT_A, ItemType.PUSH_EFFICIENCY_A, ItemType.EFFICIENCY_NOT_A, ItemType.PUSH_EFFICIENCY_NOT_A, ItemType.EFFICIENCY_RELEASE, ItemType.EFFICIENCY_NOT_A, ItemType.PUSH_EFFICIENCY_NOT_A, ItemType.EFFICIENCY_PERCENT_RELEASE, ItemType.EFFICIENCY_PERCENT_NOT_A, ItemType.PUSH_EFFICIENCY_PERCENT_NOT_A, ItemType.TOTAL_EFFICIENCY);
    }
}
