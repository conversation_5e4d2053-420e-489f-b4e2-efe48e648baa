package com.jinkosolar.scp.mps.domain.constant.enums;

import com.ibm.scp.common.api.annotation.BaseEnum;
import com.ibm.scp.common.api.util.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ProcessStepEnum implements BaseEnum {

    /**
     * 1-拉晶
     */
    CRYSTAL_GROWTH(1, "拉晶"),
    /**
     * 2-切片
     */
    WAFER_SLICING(2, "切片");


    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据描述获取爬坡产能类型编码
     *
     * @param desc 编码
     * @return 描述
     */
    public static ProcessStepEnum getByDesc(String desc) {
        return Stream.of(ProcessStepEnum.values())
                .filter(p -> p.desc.equals(desc))
                .findAny()
                .orElseThrow(()->new BizException("mps.error.umatchedProcessStep"));
    }

    /**
     * 根据编码获取爬坡产能类型描述
     *
     * @param code 编码
     * @return 描述
     */
    public static ProcessStepEnum getByCode(Integer code) {
        for (ProcessStepEnum anEnum : ProcessStepEnum.values()) {
            if (Objects.equals(anEnum.getCode(), code)) {
                return anEnum;
            }
        }
        return null;
    }


    @Override
    public String getRemark() {
        return this.desc;
    }
}
