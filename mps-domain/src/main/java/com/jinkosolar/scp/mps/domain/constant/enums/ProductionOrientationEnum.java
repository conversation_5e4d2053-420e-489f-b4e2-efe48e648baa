package com.jinkosolar.scp.mps.domain.constant.enums;

import com.ibm.scp.common.api.annotation.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ProductionOrientationEnum implements BaseEnum {

    /**
     * 1-定向
     */
    ORIENTED(1, "定向"),
    /**
     * 2-非定向
     */
    UN_ORIENTED(2, "非定向");


    /**
     * 编码
     */
    private final Integer code;

    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据描述获取爬坡产能类型编码
     *
     * @param desc 编码
     * @return 描述
     */
    public static ProductionOrientationEnum getByDesc(String desc) {
        return Stream.of(ProductionOrientationEnum.values())
                .filter(p -> p.desc.equals(desc))
                .findAny()
                .orElse(null);
    }

    /**
     * 根据编码获取爬坡产能类型描述
     *
     * @param code 编码
     * @return 描述
     */
    public static ProductionOrientationEnum getByCode(Integer code) {
        for (ProductionOrientationEnum anEnum : ProductionOrientationEnum.values()) {
            if (Objects.equals(anEnum.getCode(), code)) {
                return anEnum;
            }
        }
        return null;
    }

    @Override
    public String getRemark() {
        return this.desc;
    }
}
