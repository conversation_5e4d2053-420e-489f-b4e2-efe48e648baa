package com.jinkosolar.scp.mps.domain.constant.enums;

import com.ibm.scp.common.api.util.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Getter
@AllArgsConstructor
public enum SlicePlanShiftDataTitleTypeEnum {
    /**
     * 合计
     */
    JBDB(1, "晶棒到棒/T"),
    JBPC(2, "晶棒排产/万片"),
    KJS(3, "开机数/台"),
    QPPC(4, "切片排产/万片"),
    GPXQ(5, "硅片需求/万片"),
    JBJC(6, "晶棒结存/T"),
    GPJC(7, "硅片结存/T"),
    JBGPZJC(8, "晶棒硅片总结存/T");


    /**
     * 编码
     */
    private final Integer order;

    /**
     * 描述
     */
    private final String desc;

    public static Integer getOrderByDesc(String desc) {
        return Arrays.stream(SlicePlanShiftDataTitleTypeEnum.values()).filter(i ->
                i.getDesc().equals(desc)).findFirst().orElseThrow(() -> new BizException("类型不存在")).getOrder();
    }

    public static SlicePlanShiftDataTitleTypeEnum getByDesc(String desc) {
        return Arrays.stream(SlicePlanShiftDataTitleTypeEnum.values()).filter(i ->
                i.getDesc().equals(desc)).findFirst().orElseThrow(() -> new BizException("类型不存在"));
    }
}
