package com.jinkosolar.scp.mps.domain.constant.enums;

import com.ibm.scp.common.api.util.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/2/27
 */
@Getter
@AllArgsConstructor
public enum SlicePlanShiftDataTypeEnum {
    /**
     * 合计
     */
    TOTAL_SUM(1, "类别合计"),
    PRODUCT_SUM(2, "产品合计"),
    DETAIL(3, "细分合计");


    /**
     * 编码
     */
    private final Integer order;

    /**
     * 描述
     */
    private final String desc;


    public static Integer getOrderByDesc(String desc) {
        return Arrays.stream(SlicePlanShiftDataTypeEnum.values()).filter(i ->
                i.getDesc().equals(desc)).findFirst().orElseThrow(() -> new BizException("类型不存在")).getOrder();
    }

    public static SlicePlanShiftDataTypeEnum getByDesc(String desc) {
        return Arrays.stream(SlicePlanShiftDataTypeEnum.values()).filter(i ->
                i.getDesc().equals(desc)).findFirst().orElseThrow(() -> new BizException("类型不存在"));
    }
}
