package com.jinkosolar.scp.mps.domain.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum TaskEnum {



    SYNC_DATA("Sync_Data", "同步数据"),

    POWER_EFFICIENCY_CALCULATION("Power_Efficiency_Calculation", "功率效率计算"),

    CELL_ALLOCATION("Cell_Allocation","电池分配计算");

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;



    public static TaskEnum getByCode(String code) {
        for (TaskEnum anEnum : TaskEnum.values()) {
            if (Objects.equals(anEnum.getCode(), code)) {
                return anEnum;
            }
        }
        return null;
    }
}
