package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.mrp.AccessoryResultQueryDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlan;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AccessoryResultConvert {
    AccessoryResultConvert INSTANCE = Mappers.getMapper(AccessoryResultConvert.class);

    AccessoryResultQueryDTO toQueryDTO(ModuleProductionPlan moduleProductionPlan);
}