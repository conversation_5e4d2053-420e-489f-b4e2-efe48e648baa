package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.AlignmentPlanDynamicDTO;
import com.jinkosolar.scp.mps.domain.entity.AlignmentPlanDynamic;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AlignmentPlanDynamicDEConvert extends BaseDEConvert<AlignmentPlanDynamicDTO, AlignmentPlanDynamic> {
    AlignmentPlanDynamicDEConvert INSTANCE = Mappers.getMapper(AlignmentPlanDynamicDEConvert.class);

    void resetAlignmentPlanDynamic(AlignmentPlanDynamicDTO alignmentPlanDynamicDTO, @MappingTarget AlignmentPlanDynamic alignmentPlanDynamic);
}