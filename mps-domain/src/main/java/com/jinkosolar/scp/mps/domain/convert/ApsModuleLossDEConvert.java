package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.ApsModuleLossDTO;
import com.jinkosolar.scp.mps.domain.entity.ApsModuleLoss;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import com.ibm.scp.common.api.convert.BaseDEConvert;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ApsModuleLossDEConvert extends BaseDEConvert<ApsModuleLossDTO,ApsModuleLoss> {
    ApsModuleLossDEConvert INSTANCE = Mappers.getMapper(ApsModuleLossDEConvert.class);
}