package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ApsPlanLossDTO;
import com.jinkosolar.scp.mps.domain.entity.ApsPlanLoss;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ApsPlanLossDEConvert extends BaseDEConvert<ApsPlanLossDTO, ApsPlanLoss> {
    ApsPlanLossDEConvert INSTANCE = Mappers.getMapper(ApsPlanLossDEConvert.class);
}