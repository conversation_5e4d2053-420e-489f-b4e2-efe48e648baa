package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.entity.ApsProductionCalendar;
import com.jinkosolar.scp.mps.domain.dto.ApsProductionCalendarDTO;
import com.jinkosolar.scp.mps.domain.excel.ApsProductionCalendarExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ApsProductionCalendarSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]APS生产日历数据表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ApsProductionCalendarDEConvert extends BaseDEConvert<ApsProductionCalendarDTO, ApsProductionCalendar> {

    ApsProductionCalendarDEConvert INSTANCE = Mappers.getMapper(ApsProductionCalendarDEConvert.class);

    List<ApsProductionCalendarExcelDTO> toExcelDTO(List<ApsProductionCalendarDTO> dtos);

    ApsProductionCalendarExcelDTO toExcelDTO(ApsProductionCalendarDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    ApsProductionCalendar saveDTOtoEntity(ApsProductionCalendarSaveDTO saveDTO, @MappingTarget ApsProductionCalendar entity);
}
