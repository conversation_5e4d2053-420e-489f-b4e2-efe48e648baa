package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.AssignBasePriorityDTO;
import com.jinkosolar.scp.mps.domain.entity.AssignBasePriority;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AssignBasePriorityDEConvert extends BaseDEConvert<AssignBasePriorityDTO, AssignBasePriority> {
    AssignBasePriorityDEConvert INSTANCE = Mappers.getMapper(AssignBasePriorityDEConvert.class);

    void resetAssignBasePriority(AssignBasePriorityDTO assignBasePriorityDTO, @MappingTarget AssignBasePriority assignBasePriority);
}