package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.AttendanceDTO;
import com.jinkosolar.scp.mps.domain.entity.Attendance;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AttendanceDEConvert extends BaseDEConvert<AttendanceDTO, Attendance> {
    AttendanceDEConvert INSTANCE = Mappers.getMapper(AttendanceDEConvert.class);

    void resetAttendance(AttendanceDTO attendanceDTO, @MappingTarget Attendance attendance);
}