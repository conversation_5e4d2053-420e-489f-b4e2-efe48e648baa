package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.LovWorkCenterVO;
import com.jinkosolar.scp.mps.domain.entity.AvailableProductionLinesDetail;
import com.jinkosolar.scp.mps.domain.dto.AvailableProductionLinesDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.WorkCenterLineAdjustSummary;
import com.jinkosolar.scp.mps.domain.excel.AvailableProductionLinesDetailExcelDTO;
import com.jinkosolar.scp.mps.domain.save.AvailableProductionLinesDetailSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]组件可用产线与历史开线明细表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-13
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AvailableProductionLinesDetailDEConvert extends BaseDEConvert<AvailableProductionLinesDetailDTO, AvailableProductionLinesDetail> {

    AvailableProductionLinesDetailDEConvert INSTANCE = Mappers.getMapper(AvailableProductionLinesDetailDEConvert.class);

    List<AvailableProductionLinesDetailExcelDTO> toExcelDTO(List<AvailableProductionLinesDetailDTO> dtos);

    AvailableProductionLinesDetailExcelDTO toExcelDTO(AvailableProductionLinesDetailDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    AvailableProductionLinesDetail lovWorkCenterVOToEntity(LovWorkCenterVO lovWorkCenterVO, @MappingTarget AvailableProductionLinesDetail entity);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    AvailableProductionLinesDetail saveDTOtoEntity(AvailableProductionLinesDetailSaveDTO saveDTO, @MappingTarget AvailableProductionLinesDetail entity);
}
