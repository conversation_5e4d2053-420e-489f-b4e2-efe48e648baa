package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.entity.BaseCellIePlan;
import com.jinkosolar.scp.mps.domain.dto.BaseCellIePlanDTO;
import com.jinkosolar.scp.mps.domain.excel.BaseCellIePlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.BaseCellIePlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]IE更新月度日计划表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-12-11
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BaseCellIePlanDEConvert extends BaseDEConvert<BaseCellIePlanDTO, BaseCellIePlan> {

    BaseCellIePlanDEConvert INSTANCE = Mappers.getMapper(BaseCellIePlanDEConvert.class);

    List<BaseCellIePlanExcelDTO> toExcelDTO(List<BaseCellIePlanDTO> dtos);

    BaseCellIePlanExcelDTO toExcelDTO(BaseCellIePlanDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    BaseCellIePlan saveDTOtoEntity(BaseCellIePlanSaveDTO saveDTO, @MappingTarget BaseCellIePlan entity);
}
