package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BaseCellOutputMonthDTO;
import com.jinkosolar.scp.mps.domain.entity.BaseCellOutputMonth;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BaseCellOutputMonthDEConvert extends BaseDEConvert<BaseCellOutputMonthDTO, BaseCellOutputMonth> {
    BaseCellOutputMonthDEConvert INSTANCE = Mappers.getMapper(BaseCellOutputMonthDEConvert.class);

    void resetBaseCellOutputMonth(BaseCellOutputMonthDTO baseCellOutputMonthDTO, @MappingTarget BaseCellOutputMonth baseCellOutputMonth);
}