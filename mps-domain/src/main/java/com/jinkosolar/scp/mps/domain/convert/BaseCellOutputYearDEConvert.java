package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BaseCellOutputYearDTO;
import com.jinkosolar.scp.mps.domain.entity.BaseCellOutputYear;
import com.jinkosolar.scp.mps.domain.entity.BaseCellOutputYear;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BaseCellOutputYearDEConvert extends BaseDEConvert<BaseCellOutputYearDTO, BaseCellOutputYear> {
    BaseCellOutputYearDEConvert INSTANCE = Mappers.getMapper(BaseCellOutputYearDEConvert.class);

    void resetBaseCellOutputYear(BaseCellOutputYearDTO baseCellOutputYearDTO, @MappingTarget BaseCellOutputYear baseCellOutputYear);
}