package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BaseGradeCapacityDTO;
import com.jinkosolar.scp.mps.domain.entity.BaseGradeCapacity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BaseGradeCapacityDEConvert extends BaseDEConvert<BaseGradeCapacityDTO, BaseGradeCapacity> {
    BaseGradeCapacityDEConvert INSTANCE = Mappers.getMapper(BaseGradeCapacityDEConvert.class);

    void resetBaseGradeCapacity(BaseGradeCapacityDTO baseGradeCapacityDTO, @MappingTarget BaseGradeCapacity baseGradeCapacity);
}