package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryAgingConversionDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryAgingConversion;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 新老水位转换规则转换器
 *
 * @author: gencode 2024-12-19
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryAgingConversionDEConvert extends BaseDEConvert<BatteryAgingConversionDTO, BatteryAgingConversion> {
    BatteryAgingConversionDEConvert INSTANCE = Mappers.getMapper(BatteryAgingConversionDEConvert.class);

    void resetBatteryAgingConversion(BatteryAgingConversionDTO batteryAgingConversionDTO, @MappingTarget BatteryAgingConversion batteryAgingConversion);
} 