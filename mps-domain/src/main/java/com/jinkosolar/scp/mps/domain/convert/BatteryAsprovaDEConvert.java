package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryAsprovaDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryAsprova;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryAsprovaDEConvert extends BaseDEConvert<BatteryAsprovaDTO, BatteryAsprova> {
    BatteryAsprovaDEConvert INSTANCE = Mappers.getMapper(BatteryAsprovaDEConvert.class);

    void resetBatteryAsprova(BatteryAsprovaDTO BatteryAsprovaDTO, @MappingTarget BatteryAsprova BatteryAsprova);
}
