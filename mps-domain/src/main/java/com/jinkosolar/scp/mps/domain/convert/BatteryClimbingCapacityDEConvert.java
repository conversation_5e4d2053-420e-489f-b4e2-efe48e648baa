package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryClimbingCapacityDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryClimbingCapacity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryClimbingCapacityDEConvert extends BaseDEConvert<BatteryClimbingCapacityDTO, BatteryClimbingCapacity> {
    BatteryClimbingCapacityDEConvert INSTANCE = Mappers.getMapper(BatteryClimbingCapacityDEConvert.class);

    void resetBatteryClimbingCapacity(BatteryClimbingCapacityDTO BatteryClimbingCapacityDTO, @MappingTarget BatteryClimbingCapacity BatteryClimbingCapacity);
}
