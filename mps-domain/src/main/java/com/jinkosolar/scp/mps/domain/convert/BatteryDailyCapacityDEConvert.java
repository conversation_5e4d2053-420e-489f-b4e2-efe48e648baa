package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryDailyCapacityDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryDailyCapacity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryDailyCapacityDEConvert extends BaseDEConvert<BatteryDailyCapacityDTO, BatteryDailyCapacity> {
    BatteryDailyCapacityDEConvert INSTANCE = Mappers.getMapper(BatteryDailyCapacityDEConvert.class);

    void resetBatteryDailyCapacity(BatteryDailyCapacityDTO BatteryDailyCapacityDTO, @MappingTarget BatteryDailyCapacity BatteryDailyCapacity);
}
