package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryDeliveryPlanDayDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryDeliveryPlanDay;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryDeliveryPlanDayDEConvert extends BaseDEConvert<BatteryDeliveryPlanDayDTO, BatteryDeliveryPlanDay> {
    BatteryDeliveryPlanDayDEConvert INSTANCE = Mappers.getMapper(BatteryDeliveryPlanDayDEConvert.class);

    void resetBatteryDeliveryPlanDay(BatteryDeliveryPlanDayDTO batteryDeliveryPlanDayDTO, @MappingTarget BatteryDeliveryPlanDay batteryDeliveryPlanDay);
}