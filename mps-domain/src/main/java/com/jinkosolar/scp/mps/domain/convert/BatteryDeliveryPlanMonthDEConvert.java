package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryDeliveryPlanMonthDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryDeliveryPlanMonth;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryDeliveryPlanMonthDEConvert extends BaseDEConvert<BatteryDeliveryPlanMonthDTO, BatteryDeliveryPlanMonth> {
    BatteryDeliveryPlanMonthDEConvert INSTANCE = Mappers.getMapper(BatteryDeliveryPlanMonthDEConvert.class);

    void resetBatteryDeliveryPlanMonth(BatteryDeliveryPlanMonthDTO batteryDeliveryPlanMonthDTO, @MappingTarget BatteryDeliveryPlanMonth batteryDeliveryPlanMonth);
}