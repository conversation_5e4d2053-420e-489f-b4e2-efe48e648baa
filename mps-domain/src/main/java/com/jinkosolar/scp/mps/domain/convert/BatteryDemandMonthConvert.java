package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.BatteryDemandMonthDTO;
import com.jinkosolar.scp.mps.domain.dto.BatteryDemandMonthDiffDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryDemandMonthConvert{

    BatteryDemandMonthConvert INSTANCE = Mappers.getMapper(BatteryDemandMonthConvert.class);





    @Mappings({


    @Mapping(target = "category", constant = "IE"),
    @Mapping(target = "craftList", source = "ieCraftList"),
    @Mapping(target = "purchaseMethodList", source = "iePurchaseMethodList"),
    @Mapping(target = "mainGridList", source = "ieMainGridList"),
    @Mapping(target = "thicknessList", source = "ieThicknessList"),
    @Mapping(target = "directList", source = "ieDirectList"),
    @Mapping(target = "cellSizeLengthList", source = "ieCellSizeLengthList"),
    @Mapping(target = "cellSizeWidthList", source = "ieCellSizeWidthList"),
    @Mapping(target = "electricalPerformanceList", source = "ieElectricalPerformanceList"),
    @Mapping(target = "qty", source = "ieQty")
    })
    BatteryDemandMonthDTO toIeDto(BatteryDemandMonthDiffDTO diffDTO);

    @Mappings({


            @Mapping(target = "category", constant = "经管"),
            @Mapping(target = "craftList", source = "dpCraftList"),
            @Mapping(target = "purchaseMethodList", source = "dpPurchaseMethodList"),
            @Mapping(target = "mainGridList", source = "dpMainGridList"),
            @Mapping(target = "thicknessList", source = "dpThicknessList"),
            @Mapping(target = "directList", source = "dpDirectList"),
            @Mapping(target = "cellSizeLengthList", source = "dpCellSizeLengthList"),
            @Mapping(target = "cellSizeWidthList", source = "dpCellSizeWidthList"),
            @Mapping(target = "electricalPerformanceList", source = "dpElectricalPerformanceList"),
            @Mapping(target = "qty", source = "dpQty")
    })
    BatteryDemandMonthDTO toDpDto(BatteryDemandMonthDiffDTO diffDTO);


    @Mappings({
            @Mapping(target = "category", constant = "差异"),
            @Mapping(target = "craftList", expression = "java(mapBooleanToYesNo(diffDTO.getCraftDiff()))"),
            @Mapping(target = "purchaseMethodList", expression = "java(mapBooleanToYesNo(diffDTO.getPurchaseMethodDiff()))"),
            @Mapping(target = "mainGridList", expression = "java(mapBooleanToYesNo(diffDTO.getMainGridDiff()))"),
            @Mapping(target = "thicknessList", expression = "java(mapBooleanToYesNo(diffDTO.getThicknessDiff()))"),
            @Mapping(target = "directList", expression = "java(mapBooleanToYesNo(diffDTO.getDirectDiff()))"),
            @Mapping(target = "cellSizeLengthList", expression = "java(mapBooleanToYesNo(diffDTO.getCellSizeLengthDiff()))"),
            @Mapping(target = "cellSizeWidthList", expression = "java(mapBooleanToYesNo(diffDTO.getCellSizeWidthDiff()))"),
            @Mapping(target = "electricalPerformanceList", expression = "java(mapBooleanToYesNo(diffDTO.getElectricalPerformanceDiff()))"),
            @Mapping(target = "qty", source = "qtyDiff")
    })
    BatteryDemandMonthDTO toDiffDto(BatteryDemandMonthDiffDTO diffDTO);


    default String mapBooleanToYesNo(boolean value) {
        return value ? "Y" : "N";
    }
}
