package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryDemandMonthDiffDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryDemandMonthDiff;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;



@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryDemandMonthDiffDEConvert extends BaseDEConvert<BatteryDemandMonthDiffDTO, BatteryDemandMonthDiff> {

    BatteryDemandMonthDiffDEConvert INSTANCE = Mappers.getMapper(BatteryDemandMonthDiffDEConvert.class);


    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    BatteryDemandMonthDiff saveDTOtoEntity(BatteryDemandMonthDiffDTO saveDTO, @MappingTarget BatteryDemandMonthDiff entity);
}