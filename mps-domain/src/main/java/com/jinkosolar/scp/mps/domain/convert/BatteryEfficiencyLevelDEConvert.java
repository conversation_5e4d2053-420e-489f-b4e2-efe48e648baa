package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.entity.BatteryEfficiencyLevel;
import com.jinkosolar.scp.mps.domain.dto.BatteryEfficiencyLevelDTO;
import com.jinkosolar.scp.mps.domain.excel.BatteryEfficiencyLevelExcelDTO;
import com.jinkosolar.scp.mps.domain.save.BatteryEfficiencyLevelSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池启用效率档位 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-02 10:22:51
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryEfficiencyLevelDEConvert extends BaseDEConvert<BatteryEfficiencyLevelDTO, BatteryEfficiencyLevel> {

    BatteryEfficiencyLevelDEConvert INSTANCE = Mappers.getMapper(BatteryEfficiencyLevelDEConvert.class);

    List<BatteryEfficiencyLevelExcelDTO> toExcelDTO(List<BatteryEfficiencyLevelDTO> dtos);

    BatteryEfficiencyLevelExcelDTO toExcelDTO(BatteryEfficiencyLevelDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    BatteryEfficiencyLevel saveDTOtoEntity(BatteryEfficiencyLevelSaveDTO saveDTO, @MappingTarget BatteryEfficiencyLevel entity);
}
