package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryInStockRecordDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryInStockRecord;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryInStockRecordDEConvert extends BaseDEConvert<BatteryInStockRecordDTO, BatteryInStockRecord> {
    BatteryInStockRecordDEConvert INSTANCE = Mappers.getMapper(BatteryInStockRecordDEConvert.class);

    void resetBatteryInStockRecord(BatteryInStockRecordDTO batteryInStockRecordDTO, @MappingTarget BatteryInStockRecord batteryInStockRecord);
}