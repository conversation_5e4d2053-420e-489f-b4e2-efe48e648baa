package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryWattageDTO;
import com.jinkosolar.scp.mps.domain.entity.BatteryWattage;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatteryWattageDEConvert extends BaseDEConvert<BatteryWattageDTO, BatteryWattage> {
    BatteryWattageDEConvert INSTANCE = Mappers.getMapper(BatteryWattageDEConvert.class);

    void resetBatteryWattage(BatteryWattageDTO batteryWattageDTO, @MappingTarget BatteryWattage batteryWattage);
}