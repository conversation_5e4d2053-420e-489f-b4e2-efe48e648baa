package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CapacityPlanCompareDTO;
import com.jinkosolar.scp.mps.domain.entity.CapacityPlanCompare;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 组件产能与规划对比汇总转换类
 *
 * <AUTHOR> chenc
 * @date : 2024-11-11
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CapacityPlanCompareDEConvert extends BaseDEConvert<CapacityPlanCompareDTO, CapacityPlanCompare> {

    CapacityPlanCompareDEConvert INSTANCE = Mappers.getMapper(CapacityPlanCompareDEConvert.class);

    void resetCapacityPlanCompare(CapacityPlanCompareDTO capacityPlanCompareDTO, @MappingTarget CapacityPlanCompare capacityPlanCompare);

    CapacityPlanCompareDTO.MonthDTO copyCapacityPlanCompareMonthDTO(CapacityPlanCompareDTO.MonthDTO from);
    List<CapacityPlanCompareDTO.MonthDTO> copyCapacityPlanCompareMonthDTOs(List<CapacityPlanCompareDTO.MonthDTO> froms);

}