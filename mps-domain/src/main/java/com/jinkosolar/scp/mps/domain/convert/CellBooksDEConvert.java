package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellBooksDTO;
import com.jinkosolar.scp.mps.domain.entity.CellBooks;
import com.jinkosolar.scp.mps.domain.excel.CellBooksExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellBooksSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 帐套信息 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:35
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellBooksDEConvert extends BaseDEConvert<CellBooksDTO, CellBooks> {

    CellBooksDEConvert INSTANCE = Mappers.getMapper(CellBooksDEConvert.class);

    List<CellBooksExcelDTO> toExcelDTO(List<CellBooksDTO> dtos);

    CellBooksExcelDTO toExcelDTO(CellBooksDTO dto);

    List<CellBooksSaveDTO> toSaveDTO(List<CellBooksDTO> excelData);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({@Mapping(target = "id", ignore = true)})
    CellBooks saveDTOtoEntity(CellBooksSaveDTO saveDTO, @MappingTarget CellBooks newObj);
}
