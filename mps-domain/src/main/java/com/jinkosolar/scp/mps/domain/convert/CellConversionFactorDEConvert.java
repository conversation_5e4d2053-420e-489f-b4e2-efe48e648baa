package com.jinkosolar.scp.mps.domain.convert;
import com.jinkosolar.scp.mps.domain.dto.CellConversionFactorDTO;
import com.jinkosolar.scp.mps.domain.entity.CellConversionFactor;
import com.jinkosolar.scp.mps.domain.excel.CellConversionFactorExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellConversionFactorSaveDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MapStrutUtil;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.ibm.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 万片与兆瓦折算系数 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-31 10:33:29
 */
@Mapper(componentModel = "spring", imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellConversionFactorDEConvert extends BaseDEConvert<CellConversionFactorDTO, CellConversionFactor> {

    CellConversionFactorDEConvert INSTANCE = Mappers.getMapper(CellConversionFactorDEConvert.class);

    @Mappings(
            {
                    @Mapping(target = "cellsType", expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(dto.getCellsTypeId()))"),
            }
    )
    CellConversionFactorExcelDTO toExcelDTO(CellConversionFactorDTO dto);

    List<CellConversionFactorExcelDTO> toExcelDTO(List<CellConversionFactorDTO> dtos);


    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "cellsTypeId", expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()).getLovLineId())")
    })
    CellConversionFactor saveDTOtoEntity(CellConversionFactorSaveDTO dto, @MappingTarget CellConversionFactor entity);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true),
            @Mapping(target = "cellsTypeId", expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellsType()).getLovLineId())")
    })
    CellConversionFactorSaveDTO excelDtoToSaveDto(CellConversionFactorExcelDTO dto);

    List<CellConversionFactorSaveDTO> excelDtoToSaveDto(List<CellConversionFactorExcelDTO> excelDTO);

    @Override
    CellConversionFactorDTO toDto(CellConversionFactor dto);
}
