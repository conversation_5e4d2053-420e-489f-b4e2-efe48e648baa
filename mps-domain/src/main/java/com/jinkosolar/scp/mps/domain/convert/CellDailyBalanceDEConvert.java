package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.ibm.scp.common.api.util.LovUtils;
import com.jinkosolar.scp.mps.domain.dto.CellDailyBalanceDTO;
import com.jinkosolar.scp.mps.domain.dto.CellDailyBalanceExcelDTO;
import com.jinkosolar.scp.mps.domain.entity.CellDailyBalance;
import com.jinkosolar.scp.mps.domain.query.CellDailyBalanceQuery;
import com.jinkosolar.scp.mps.domain.save.CellDailyBalanceSaveDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MapStrutUtil;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 每日结存报表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-12 06:19:24
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellDailyBalanceDEConvert extends BaseDEConvert<CellDailyBalanceDTO, CellDailyBalance> {

    CellDailyBalanceDEConvert INSTANCE = Mappers.getMapper(CellDailyBalanceDEConvert.class);

    List<CellDailyBalanceExcelDTO> toExcelDTO(List<CellDailyBalanceDTO> dtos);

    CellDailyBalanceExcelDTO toExcelDTO(CellDailyBalanceDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellDailyBalance saveDTOtoEntity(CellDailyBalanceSaveDTO saveDTO, @MappingTarget CellDailyBalance entity);

    @Override
    @Mappings({
            @Mapping(target = "cellTypeId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellType())!=null?com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, dto.getCellType()).getLovLineId():0)"),
            @Mapping(target = "isOverseaId" ,expression = "java( com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()).getLovLineId())")
    })
    CellDailyBalance toEntity(CellDailyBalanceDTO dto);

    @Mappings({
            @Mapping(target = "cellsType" ,expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getCNNameByNameLang(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType(),lang))"),
            @Mapping(target = "isOversea" ,expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getCNNameByNameLang(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),lang))")
    })
    CellDailyBalanceQuery toCellDailyBalanceQuery(CellDailyBalanceQuery query, String lang);
    default  void test(CellDailyBalanceQuery query, String lang){
         MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.DOMESTIC_OVERSEA,query.getIsOversea(),lang);
         MapStrutUtil.getCNNameByNameLang(LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType(),lang);
    }
}
