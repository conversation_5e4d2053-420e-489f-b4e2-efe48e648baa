package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellFineMidDTO;
import com.jinkosolar.scp.mps.domain.dto.ConfigCellGoodDTO;
import com.jinkosolar.scp.mps.domain.entity.CellFineMid;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池良率中间表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellFineMidDEConvert extends BaseDEConvert<CellFineMidDTO, CellFineMid> {

    CellFineMidDEConvert INSTANCE = Mappers.getMapper(CellFineMidDEConvert.class);

    @Mappings({
            @Mapping(target = "id", source = "id",ignore = true),
            @Mapping(target = "isOversea", source = "isOverseaName"),
            @Mapping(target = "parameterType", source = "parameterTypeName"),
            @Mapping(target = "productType", source = "productTypeName"),
            @Mapping(target = "productCategory", source = "productCategoryName"),
            @Mapping(target = "mainGrid", source = "mainGridName"),
            @Mapping(target = "crystalType", source = "crystalTypeName"),
            @Mapping(target = "crystalSpec", source = "crystalSpecName"),
            @Mapping(target = "workshop", source = "workshopName"),
            @Mapping(target = "m1",source = "quantityM1"),
            @Mapping(target = "m2", source = "quantityM2" ),
            @Mapping(target = "m3",source = "quantityM3" ),
            @Mapping(target = "m4", source = "quantityM4" ),
            @Mapping(target = "m5", source = "quantityM5" ),
            @Mapping(target = "m6", source = "quantityM6" ),
            @Mapping(target = "m7", source = "quantityM7" ),
            @Mapping(target = "m8", source = "quantityM8" ),
            @Mapping(target = "m9", source = "quantityM9" ),
            @Mapping(target = "m10", source = "quantityM10" ),
            @Mapping(target = "m11", source = "quantityM11" ),
            @Mapping(target = "m12", source = "quantityM12" ),
            @Mapping(target = "cellsTypeLeft", expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getCellsTypeLeftByConfigCellGoodDTO(dto))")



    })
    CellFineMid toCellFineMid(ConfigCellGoodDTO dto);

    List<CellFineMid> toCellFineMid(List<ConfigCellGoodDTO> dtos);
}
