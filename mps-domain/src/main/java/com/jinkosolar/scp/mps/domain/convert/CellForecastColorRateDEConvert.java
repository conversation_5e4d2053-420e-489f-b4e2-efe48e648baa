package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellForecastColorRateDTO;
import com.jinkosolar.scp.mps.domain.entity.CellForecastColorRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellForecastColorRateDEConvert extends BaseDEConvert<CellForecastColorRateDTO, CellForecastColorRate> {
    CellForecastColorRateDEConvert INSTANCE = Mappers.getMapper(CellForecastColorRateDEConvert.class);

    void resetCellForecastColorRate(CellForecastColorRateDTO cellForecastColorRateDTO, @MappingTarget CellForecastColorRate cellForecastColorRate);
}