package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellForecastDTO;
import com.jinkosolar.scp.mps.domain.entity.CellForecast;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellForecastDEConvert extends BaseDEConvert<CellForecastDTO, CellForecast> {
    CellForecastDEConvert INSTANCE = Mappers.getMapper(CellForecastDEConvert.class);

    void resetCellForecast(CellForecastDTO cellForecastDTO, @MappingTarget CellForecast cellForecast);
}