package com.jinkosolar.scp.mps.domain.convert;


import com.jinkosolar.scp.mps.domain.dto.CellGradeRuleMateDto;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.CellInstockPlan;
import com.jinkosolar.scp.mps.domain.excel.CellInstockPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanQuery;
import com.jinkosolar.scp.mps.domain.save.CellInstockPlanSaveDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MapStrutUtil;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.ibm.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-23 10:13:25
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInstockPlanDEConvert extends BaseDEConvert<CellInstockPlanDTO, CellInstockPlan> {

    CellInstockPlanDEConvert INSTANCE = Mappers.getMapper(CellInstockPlanDEConvert.class);

    List<CellInstockPlanExcelDTO> toExcelDTO(List<CellInstockPlanDTO> dtos);

    CellInstockPlanExcelDTO toExcelDTO(CellInstockPlanDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellInstockPlan saveDTOtoEntity(CellInstockPlanSaveDTO saveDTO, @MappingTarget CellInstockPlan entity);


    CellInstockPlanQuery toCellInstockPlanByName(CellInstockPlanQuery dto);

    CellInstockPlanQuery toCellInstockPlanQueryFromCellGradeRuleMateDto(CellGradeRuleMateDto dto);


}
