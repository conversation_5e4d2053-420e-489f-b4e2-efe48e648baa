package com.jinkosolar.scp.mps.domain.convert;


import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.ibm.scp.common.api.util.LovUtils;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanTotalDTO;
import com.jinkosolar.scp.mps.domain.save.CellInstockPlanTotalSaveDTO;
import com.jinkosolar.scp.mps.domain.entity.CellInstockPlanTotal;
import com.jinkosolar.scp.mps.domain.excel.CellInstockPlanTotalExcelDTO;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanQuery;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanTotalQuery;
import com.jinkosolar.scp.mps.domain.query.CellPlanLineTotalQuery;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MapStrutUtil;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划汇总表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-02-26 11:20:51
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovUtils.class, LovHeaderCodeConstant.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInstockPlanTotalDEConvert extends BaseDEConvert<CellInstockPlanTotalDTO, CellInstockPlanTotal> {

    CellInstockPlanTotalDEConvert INSTANCE = Mappers.getMapper(CellInstockPlanTotalDEConvert.class);

    List<CellInstockPlanTotalExcelDTO> toExcelDTO(List<CellInstockPlanTotalDTO> dtos);

    CellInstockPlanTotalExcelDTO toExcelDTO(CellInstockPlanTotalDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellInstockPlanTotal saveDTOtoEntity(CellInstockPlanTotalSaveDTO saveDTO, @MappingTarget CellInstockPlanTotal entity);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellInstockPlanQuery toCellInstockPlan(CellInstockPlanTotalQuery query);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellPlanLineTotalQuery toCellPlanLineTotalQuery(CellPlanLineTotalQuery query);
    default  void test(CellInstockPlanTotalQuery query){
        MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BATTERY_TYPE,query.getCellsType());
    }
    CellInstockPlanQuery toCellInstockPlanQuery(CellInstockPlanTotalQuery query);

    CellInstockPlanTotalDTO toCellInstockPlanTotalDTO(CellInstockPlanDTO dto);

}
