package com.jinkosolar.scp.mps.domain.convert;


import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanVersionDTO;
import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanVersionQueryDto;
import com.jinkosolar.scp.mps.domain.entity.CellInstockPlanVersion;
import com.jinkosolar.scp.mps.domain.excel.CellInstockPlanVersionExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellInstockPlanVersionSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划版本管理表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-18 05:30:42
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInstockPlanVersionDEConvert extends BaseDEConvert<CellInstockPlanVersionDTO, CellInstockPlanVersion> {

    CellInstockPlanVersionDEConvert INSTANCE = Mappers.getMapper(CellInstockPlanVersionDEConvert.class);

    List<CellInstockPlanVersionExcelDTO> toExcelDTO(List<CellInstockPlanVersionDTO> dtos);

    CellInstockPlanVersionExcelDTO toExcelDTO(CellInstockPlanVersionDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellInstockPlanVersion saveDTOtoEntity(CellInstockPlanVersionSaveDTO saveDTO, @MappingTarget CellInstockPlanVersion entity);

    @Mappings({
            @Mapping(target = "isOverseaId", expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA, dto.getIsOversea()).getLovLineId())")
    })
    @Override
    CellInstockPlanVersion toEntity(CellInstockPlanVersionDTO dto);

    @Mappings(
            {
                    @Mapping(target = "isOversea", expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getNameByValue(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
            }
    )
    CellInstockPlanVersionQueryDto toCellInstockPlanVersionQueryDtoByName(CellInstockPlanVersionQueryDto query);
}
