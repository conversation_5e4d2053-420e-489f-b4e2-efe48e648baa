package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellInventoryDTO;
import com.jinkosolar.scp.mps.domain.dto.tj.CellInventoryApiData;
import com.jinkosolar.scp.mps.domain.entity.CellInventory;
import com.jinkosolar.scp.mps.domain.save.CellInventorySaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池库存 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-16 14:33:44
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellInventoryDEConvert extends BaseDEConvert<CellInventoryDTO, CellInventory> {

    CellInventoryDEConvert INSTANCE = Mappers.getMapper(CellInventoryDEConvert.class);

    CellInventory toCellInventory(CellInventory cellInventory);

    List<CellInventory> toCellInventory(List<CellInventory> cellInventories);

    List<CellInventorySaveDTO> toSaveDTO(List<CellInventoryApiData> apiDataList);
}
