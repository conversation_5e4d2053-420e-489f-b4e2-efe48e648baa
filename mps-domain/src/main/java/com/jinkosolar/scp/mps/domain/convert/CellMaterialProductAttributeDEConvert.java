package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellMaterialProductAttributeDTO;
import com.jinkosolar.scp.mps.domain.entity.CellMaterialProductAttribute;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellMaterialProductAttributeDEConvert extends BaseDEConvert<CellMaterialProductAttributeDTO, CellMaterialProductAttribute> {
    CellMaterialProductAttributeDEConvert INSTANCE = Mappers.getMapper(CellMaterialProductAttributeDEConvert.class);

    void resetCellMaterialProductAttribute(CellMaterialProductAttributeDTO cellMaterialProductAttributeDTO, @MappingTarget CellMaterialProductAttribute cellMaterialProductAttribute);
}