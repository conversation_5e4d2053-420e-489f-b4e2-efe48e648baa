package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbMachineDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanClimbMachine;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanClimbMachineDEConvert extends BaseDEConvert<CellPlanClimbMachineDTO, CellPlanClimbMachine> {
    CellPlanClimbMachineDEConvert INSTANCE = Mappers.getMapper(CellPlanClimbMachineDEConvert.class);

    void resetCellPlanClimbMachine(CellPlanClimbMachineDTO cellPlanClimbMachineDTO, @MappingTarget CellPlanClimbMachine cellPlanClimbMachine);
}