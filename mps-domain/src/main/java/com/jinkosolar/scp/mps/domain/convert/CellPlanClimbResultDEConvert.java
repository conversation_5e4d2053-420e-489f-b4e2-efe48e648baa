package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbResultDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanClimbResult;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanClimbResultDEConvert extends BaseDEConvert<CellPlanClimbResultDTO, CellPlanClimbResult> {
    CellPlanClimbResultDEConvert INSTANCE = Mappers.getMapper(CellPlanClimbResultDEConvert.class);

    void resetCellPlanClimbResult(CellPlanClimbResultDTO cellPlanClimbResultDTO, @MappingTarget CellPlanClimbResult cellPlanClimbResult);
}