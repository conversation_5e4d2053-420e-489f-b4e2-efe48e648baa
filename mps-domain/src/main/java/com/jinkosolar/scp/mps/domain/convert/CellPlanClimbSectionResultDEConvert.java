package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbResultDTO;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSectionResultDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanClimbSectionResult;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanClimbSectionResultDEConvert extends BaseDEConvert<CellPlanClimbSectionResultDTO, CellPlanClimbSectionResult> {
    CellPlanClimbSectionResultDEConvert INSTANCE = Mappers.getMapper(CellPlanClimbSectionResultDEConvert.class);

    void resetCellPlanClimbSectionResult(CellPlanClimbSectionResultDTO cellPlanClimbSectionResultDTO, @MappingTarget CellPlanClimbSectionResult cellPlanClimbSectionResult);

    @Mapping(source = "directionalName", target = "directional")
    @Mapping(source = "directional", target = "directionalId")
    CellPlanClimbSectionResultDTO toDTO(CellPlanClimbResultDTO dto);

    @Mapping(source = "directional", target = "directionalName")
    CellPlanClimbResultDTO toDTO(CellPlanClimbSectionResultDTO dto);


    CellPlanClimbSectionResultDTO copyDto(CellPlanClimbSectionResultDTO dto);
}