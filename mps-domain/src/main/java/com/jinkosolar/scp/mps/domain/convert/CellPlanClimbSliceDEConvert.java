package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSliceDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanClimbSlice;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanClimbSliceDEConvert extends BaseDEConvert<CellPlanClimbSliceDTO, CellPlanClimbSlice> {
    CellPlanClimbSliceDEConvert INSTANCE = Mappers.getMapper(CellPlanClimbSliceDEConvert.class);

    void resetCellPlanClimbSlice(CellPlanClimbSliceDTO cellPlanClimbSliceDTO, @MappingTarget CellPlanClimbSlice cellPlanClimbSlice);
}