package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSwitchDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanClimbSwitch;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanClimbSwitchDEConvert extends BaseDEConvert<CellPlanClimbSwitchDTO, CellPlanClimbSwitch> {
    CellPlanClimbSwitchDEConvert INSTANCE = Mappers.getMapper(CellPlanClimbSwitchDEConvert.class);

    void resetCellPlanClimbSwitch(CellPlanClimbSwitchDTO cellPlanClimbSwitchDTO, @MappingTarget CellPlanClimbSwitch cellPlanClimbSwitch);
}