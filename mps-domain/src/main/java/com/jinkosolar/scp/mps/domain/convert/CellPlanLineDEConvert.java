package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.ibm.scp.common.api.util.LovUtils;
import com.jinkosolar.scp.mps.domain.dto.CellPlanLineDTO;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanSummaryDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanLine;
import com.jinkosolar.scp.mps.domain.excel.CellPlanLineExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellPlanLineSaveDTO;
import com.jinkosolar.scp.mps.domain.util.MapStrutUtil;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class , LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineDEConvert extends BaseDEConvert<CellPlanLineDTO, CellPlanLine> {

    CellPlanLineDEConvert INSTANCE = Mappers.getMapper(CellPlanLineDEConvert.class);

    List<CellPlanLineExcelDTO> toExcelDTO(List<CellPlanLineDTO> dtos);

    CellPlanLineExcelDTO toExcelDTO(CellPlanLineDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellPlanLine saveDTOtoEntity(CellPlanLineSaveDTO saveDTO, @MappingTarget CellPlanLine entity);

    @Override
    CellPlanLineDTO toDto(CellPlanLine entity);

    CellPlanLineSaveDTO toSaveDto(CellPlanLine entity);
    CellPlanLine  toEntityFromSaveDto(CellPlanLineSaveDTO dto);

    @Override
    CellPlanLine toEntity(CellPlanLineDTO entity);

    CellProductionPlanSummaryDTO detailsToSummary(CellPlanLineDTO cellPlanLineDTO);
    CellPlanLine toCopyCellPlanLine(CellPlanLine cellPlanLine);

}
