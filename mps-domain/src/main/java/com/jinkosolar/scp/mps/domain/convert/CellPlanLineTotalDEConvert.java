package com.jinkosolar.scp.mps.domain.convert;


import com.jinkosolar.scp.mps.domain.dto.CellPlanLineTotalDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanLineTotal;
import com.jinkosolar.scp.mps.domain.excel.CellPlanLineTotalExcelDTO;
import com.jinkosolar.scp.mps.domain.query.CellPlanLineTotalQuery;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MapStrutUtil;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.ibm.scp.common.api.util.LovUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库计划汇总表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",imports = {MapStrutUtil.class, LovHeaderCodeConstant.class, LovUtils.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPlanLineTotalDEConvert extends BaseDEConvert<CellPlanLineTotalDTO, CellPlanLineTotal> {

    CellPlanLineTotalDEConvert INSTANCE = Mappers.getMapper(CellPlanLineTotalDEConvert.class);

    List<CellPlanLineTotalExcelDTO> toExcelDTO(List<CellPlanLineTotalDTO> dtos);

    CellPlanLineTotalExcelDTO toExcelDTO(CellPlanLineTotalDTO dto);
    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(MapStrutUtil.getCNNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellPlanLineTotalQuery toCellPlanLineTotalQuery(CellPlanLineTotalQuery query);
    default  void test (CellPlanLineTotalQuery query ){
        MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType());
        MapStrutUtil.getNameByValue(LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea());
        MapStrutUtil.getNameByValue(LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace());
        MapStrutUtil.getNameByValue(LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop());

    }

}
