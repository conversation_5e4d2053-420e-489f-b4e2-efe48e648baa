package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.CellPlanLineDTO;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanSummaryDTO;
import com.jinkosolar.scp.mps.domain.entity.CellProductionPlan;
import com.jinkosolar.scp.mps.domain.excel.CellProductionPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellProductionPlanSaveDTO;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产计划 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellProductionPlanDEConvert extends BaseDEConvert<CellProductionPlanDTO, CellProductionPlan> {

    CellProductionPlanDEConvert INSTANCE = Mappers.getMapper(CellProductionPlanDEConvert.class);

    List<CellProductionPlanExcelDTO> toExcelDTO(List<CellProductionPlanDTO> dtos);

    CellProductionPlanExcelDTO toExcelDTO(CellProductionPlanDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })

    CellProductionPlan saveDTOtoEntity(CellProductionPlanSaveDTO saveDTO, @MappingTarget CellProductionPlan entity);
    @Override
    @Mappings({
            @Mapping(target = "HTrace", source = "HTrace", defaultValue = ""),
            @Mapping(target = "aesthetics", source = "aesthetics", defaultValue = ""),
            @Mapping(target = "transparentDoubleGlass", source = "transparentDoubleGlass", defaultValue = ""),
            @Mapping(target = "cellSource", source = "cellSource", defaultValue = "")
    }
    )
    CellProductionPlanDTO toDto(CellProductionPlan entity);
    @Override
    List<CellProductionPlanDTO>  toDto(List<CellProductionPlan> entities);

    @Mappings({
            @Mapping(target = "cellsTypeId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, entity.getCellsType()).getLovLineId())"),
            @Mapping(target = "isOverseaId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()).getLovLineId())"),
            //@Mapping(target = "factoryId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BASE_PLACE, entity.getBasePlace()).getLovLineId())"),
            @Mapping(target = "workshopId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.WORK_SHOP, entity.getWorkshop()).getLovLineId())"),
            @Mapping(target = "workunitId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.WORK_UNIT, entity.getWorkunit()).getLovLineId())")
    })
    CellProductionPlanSaveDTO toSaveDto(CellProductionPlan entity);
    List<CellProductionPlanSaveDTO> toSaveDto(List<CellProductionPlan> entity);
    CellProductionPlan  toEntityFromSaveDto(CellProductionPlanSaveDTO dto);
    List<CellProductionPlan>  toEntityFromSaveDto(List<CellProductionPlanSaveDTO> dtos);
    CellProductionPlanSummaryDTO detailsToSummary(CellPlanLineDTO cellPlanLineDTO);
    List<CellProductionPlan> SavetoPlanDto(List<CellProductionPlanDTO>productionPlanDTOList);

}
