package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.CellProductionPlanTotalDTO;
import com.jinkosolar.scp.mps.domain.entity.CellProductionPlanTotal;
import com.jinkosolar.scp.mps.domain.excel.CellProductionPlanTotalExcelDTO;
import com.jinkosolar.scp.mps.domain.query.CellProductionPlanQuery;
import com.jinkosolar.scp.mps.domain.query.CellProductionPlanTotalQuery;
import com.jinkosolar.scp.mps.domain.save.CellProductionPlanTotalSaveDTO;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 投产计划汇总表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellProductionPlanTotalDEConvert extends BaseDEConvert<CellProductionPlanTotalDTO, CellProductionPlanTotal> {

    CellProductionPlanTotalDEConvert INSTANCE = Mappers.getMapper(CellProductionPlanTotalDEConvert.class);

    List<CellProductionPlanTotalExcelDTO> toExcelDTO(List<CellProductionPlanTotalDTO> dtos);

    CellProductionPlanTotalExcelDTO toExcelDTO(CellProductionPlanTotalDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellProductionPlanTotal saveDTOtoEntity(CellProductionPlanTotalSaveDTO saveDTO, @MappingTarget CellProductionPlanTotal entity);
    CellProductionPlanQuery toCellProductionPlanQuery(CellProductionPlanTotalQuery query);
    @Mappings({
            @Mapping(target = "cellsTypeId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, entity.getCellsType()).getLovLineId())"),
            @Mapping(target = "isOverseaId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA, entity.getIsOversea()).getLovLineId())"),
            @Mapping(target = "basePlaceId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BASE_PLACE, entity.getBasePlace()).getLovLineId())"),
            @Mapping(target = "workshopId" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getByName(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.WORK_SHOP, entity.getWorkshop()).getLovLineId())")
    })
    CellProductionPlanTotalSaveDTO toSaveDTOFromEntity(CellProductionPlanTotal entity);
    List<CellProductionPlanTotalSaveDTO> toSaveDTOFromEntity(List<CellProductionPlanTotal> dtos);
    CellProductionPlanTotal toEntityFromSaveDto(  CellProductionPlanTotalSaveDTO dto);
    List<CellProductionPlanTotal> toEntityFromSaveDto(List<CellProductionPlanTotalSaveDTO> dtos);

    @Mappings(
            {
                    @Mapping(target = "cellsType" ,expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getNameByValue(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BATTERY_TYPE, query.getCellsType()))"),
                    @Mapping(target = "isOversea" ,expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getNameByValue(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.DOMESTIC_OVERSEA, query.getIsOversea()))"),
                    @Mapping(target = "basePlace" ,expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getNameByValue(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.BASE_PLACE, query.getBasePlace()))"),
                    @Mapping(target = "workshop" ,expression = "java(com.jinkosolar.scp.mps.domain.util.MapStrutUtil.getNameByValue(com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant.WORK_SHOP, query.getWorkshop()))")
            }
    )
    CellProductionPlanTotalQuery toCellProductionPlanTotalQueryByName(CellProductionPlanTotalQuery query);
    CellProductionPlanTotalDTO toCellProductionPlanTotal(CellProductionPlanDTO cellProductionPlan);
}
