package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.*;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellProductionPlanVersionConvert  {

    CellProductionPlanVersionConvert INSTANCE = Mappers.getMapper(CellProductionPlanVersionConvert.class);


//    @ApiModelProperty(value = "工艺差异标识")
//    private Boolean craftDiff=false;
//
//    @ApiModelProperty(value = "当前工艺列表")
//    private String currCraftList;
//
//    @ApiModelProperty(value = "历史工艺列表")
//    private String preCraftList;
//
//    @ApiModelProperty(value = "采购方式差异标识")
//    private  Boolean  purchaseMethodDiff=false;
//
//    @ApiModelProperty(value = "当前采购方式列表")
//    private String currPurchaseMethodList;
//
//    @ApiModelProperty(value = "历史采购方式列表")
//    private String prePurchaseMethodList;
//
//
//    @ApiModelProperty(value = "栅线数差异标识")
//    private Boolean mainGridDiff=false;
//
//
//    @ApiModelProperty(value = "当前栅线数列表")
//    private String currMainGridList;
//
//
//    @ApiModelProperty(value = "历史栅数列表")
//    private String preMainGridList;
//
//
//    @ApiModelProperty(value = "厚度差异标识")
//    private Boolean thicknessDiff=false;
//
//    @ApiModelProperty(value = "当前厚度列表")
//    private String currThicknessList;
//
//    @ApiModelProperty(value = "历史厚度列表")
//    private String preThicknessList;
//
//
//    @ApiModelProperty(value = "是否定向差异标识")
//    private Boolean  directDiff=false;
//
//
//    @ApiModelProperty(value = "当前定向列表")
//    private String currDirectList;
//
//    @ApiModelProperty(value = "历史定向列表")
//    private String preDirectList;
//
//
//    @ApiModelProperty(value = "电性能差异标识")
//    private Boolean electricalPerformanceDiff=false;
//
//    @ApiModelProperty(value = "历史电性能列表")
//    private String preElectricalPerformanceList;
//
//    @ApiModelProperty(value = "当前电性能列表")
//    private String currElectricalPerformanceList;


    @Mappings({
    @Mapping(target = "craftList", source = "currCraftList"),
    @Mapping(target = "purchaseMethodList", source = "currPurchaseMethodList"),
    @Mapping(target = "mainGridList", source = "currMainGridList"),
    @Mapping(target = "thicknessList", source = "currThicknessList"),
    @Mapping(target = "directList", source = "currDirectList"),
    @Mapping(target = "electricalPerformanceList", source = "currElectricalPerformanceList"),
    @Mapping(target = "dynamicColumnMap", source = "currDynamicColumnMap")
    })
    CellProductionPlanVersionDTO toCurrVersionDto(CellProductionPlanVersionDiffDTO diffDTO);

    @Mappings({
            @Mapping(target = "apsPlanVersion", source = "compareToApsPlanVersion"),
            @Mapping(target = "craftList", source = "preCraftList"),
            @Mapping(target = "purchaseMethodList", source = "prePurchaseMethodList"),
            @Mapping(target = "mainGridList", source = "preMainGridList"),
            @Mapping(target = "thicknessList", source = "preThicknessList"),
            @Mapping(target = "directList", source = "preDirectList"),
            @Mapping(target = "electricalPerformanceList", source = "preElectricalPerformanceList"),
            @Mapping(target = "dynamicColumnMap", source = "preDynamicColumnMap")
    })
    CellProductionPlanVersionDTO toCompareVersionDto(CellProductionPlanVersionDiffDTO diffDTO);


    @Mappings({
            @Mapping(target = "apsPlanVersion", constant = "差异"),
            @Mapping(target = "craftList", expression = "java(mapBooleanToYesNo(diffDTO.getCraftDiff()))"),
            @Mapping(target = "purchaseMethodList", expression = "java(mapBooleanToYesNo(diffDTO.getPurchaseMethodDiff()))"),
            @Mapping(target = "mainGridList", expression = "java(mapBooleanToYesNo(diffDTO.getMainGridDiff()))"),
            @Mapping(target = "thicknessList", expression = "java(mapBooleanToYesNo(diffDTO.getThicknessDiff()))"),
            @Mapping(target = "directList", expression = "java(mapBooleanToYesNo(diffDTO.getDirectDiff()))"),
            @Mapping(target = "electricalPerformanceList", expression = "java(mapBooleanToYesNo(diffDTO.getElectricalPerformanceDiff()))"),
            @Mapping(target = "dynamicColumnMap", source = "dynamicColumnMap")
    })
    CellProductionPlanVersionDTO toDiffDto(CellProductionPlanVersionDiffDTO diffDTO);


    default String mapBooleanToYesNo(boolean value) {
        return value ? "Y" : "N";
    }
}
