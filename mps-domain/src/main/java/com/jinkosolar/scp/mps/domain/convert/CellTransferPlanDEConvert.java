package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellTransferPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.CellTransferPlan;
import com.jinkosolar.scp.mps.domain.save.CellTransferPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池转运计划 DTO与实体转换器
 *
 * @author: gencode 2024-12-17 10:00:00
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellTransferPlanDEConvert extends BaseDEConvert<CellTransferPlanDTO, CellTransferPlan> {

    CellTransferPlanDEConvert INSTANCE = Mappers.getMapper(CellTransferPlanDEConvert.class);


} 