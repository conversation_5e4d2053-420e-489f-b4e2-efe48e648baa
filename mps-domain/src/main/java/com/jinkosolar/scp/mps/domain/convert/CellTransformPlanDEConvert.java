package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellTransformPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.CellTransformPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellTransformPlanDEConvert extends BaseDEConvert<CellTransformPlanDTO, CellTransformPlan> {
    CellTransformPlanDEConvert INSTANCE = Mappers.getMapper(CellTransformPlanDEConvert.class);

    void resetCellTransformPlan(CellTransformPlanDTO cellTransformPlanDTO, @MappingTarget CellTransformPlan cellTransformPlan);
}