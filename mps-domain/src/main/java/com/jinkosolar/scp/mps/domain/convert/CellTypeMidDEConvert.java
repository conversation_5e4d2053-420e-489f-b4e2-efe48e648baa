package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.BatteryTypeMainDTO;
import com.jinkosolar.scp.mps.domain.dto.CellTypeMidDTO;
import com.jinkosolar.scp.mps.domain.entity.CellTypeMid;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池类型转换表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-20 02:44:29
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellTypeMidDEConvert extends BaseDEConvert<CellTypeMidDTO, CellTypeMid> {

    CellTypeMidDEConvert INSTANCE = Mappers.getMapper(CellTypeMidDEConvert.class);

    List<CellTypeMidDTO> toExcelDTO(List<CellTypeMidDTO> dtos);

    CellTypeMidDTO toExcelDTO(CellTypeMidDTO dto);
    CellTypeMid toCellTypeMid(BatteryTypeMainDTO dto);
    List<CellTypeMid> toCellTypeMid(List<BatteryTypeMainDTO> dtos);
}
