package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.CellInstockPlanTotalDTO;
import com.jinkosolar.scp.mps.domain.dto.CellPlanLineTotalDTO;
import com.jinkosolar.scp.mps.domain.dto.CellVersionPlanHistoryDTO;
import com.jinkosolar.scp.mps.domain.entity.CellVersionPlanHistory;
import com.jinkosolar.scp.mps.domain.excel.CellVersionPlanHistoryExcelDTO;
import com.jinkosolar.scp.mps.domain.query.CellInstockPlanTotalQuery;
import com.jinkosolar.scp.mps.domain.query.CellPlanLineTotalQuery;
import com.jinkosolar.scp.mps.domain.query.CellProductionPlanTotalQuery;
import com.jinkosolar.scp.mps.domain.query.CellVersionPlanHistoryQuery;
import com.jinkosolar.scp.mps.domain.save.CellVersionPlanHistorySaveDTO;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 计划与上一版本计划对比 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-04 07:54:09
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellVersionPlanHistoryDEConvert extends BaseDEConvert<CellVersionPlanHistoryDTO, CellVersionPlanHistory> {

    CellVersionPlanHistoryDEConvert INSTANCE = Mappers.getMapper(CellVersionPlanHistoryDEConvert.class);

    List<CellVersionPlanHistoryExcelDTO> toExcelDTO(List<CellVersionPlanHistoryDTO> dtos);

    CellVersionPlanHistoryExcelDTO toExcelDTO(CellVersionPlanHistoryDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellVersionPlanHistory saveDTOtoEntity(CellVersionPlanHistorySaveDTO saveDTO, @MappingTarget CellVersionPlanHistory entity);
    @Mappings(
            {
               @Mapping(source = "isOverseaName", target = "isOversea"),
               @Mapping(source = "workshopName", target = "workshop"),
               @Mapping(source = "cellTypeName", target = "cellsType")
          }
    )
    CellProductionPlanTotalQuery toCellProductionPlanTotalQuery(CellVersionPlanHistoryQuery query);
    @Mappings(
            {
                    @Mapping(source = "isOverseaName", target = "isOversea"),
                    @Mapping(source = "workshopName", target = "workshop"),
                    @Mapping(source = "cellTypeName", target = "cellsType"),
                    @Mapping(source = "cellTypeId", target = "cellsTypeId")
            }
    )
    CellPlanLineTotalQuery toCellPlanPlanTotalQuery(CellVersionPlanHistoryQuery query);

    @Mappings(
            {
                    @Mapping(source = "isOverseaName", target = "isOversea"),
                    @Mapping(source = "workshopName", target = "workshop"),
                    @Mapping(source = "cellTypeName", target = "cellsType"),
                    @Mapping(source = "cellTypeId", target = "cellsTypeId")
            }
    )
    CellInstockPlanTotalQuery toCellInstockPlanTotalQuery(CellVersionPlanHistoryQuery query);

    @Override
    CellVersionPlanHistory toEntity(CellVersionPlanHistoryDTO dto);
    @Mappings(
            {
                    @Mapping(target = "isOverseaName", source = "isOversea"),
                    @Mapping(target = "workshopName", source = "workshop"),
                    @Mapping(target = "cellTypeName", source = "cellsType"),
                    @Mapping(target = "cellTypeId", source = "cellsTypeId")
            }
    )
    CellVersionPlanHistoryDTO toCellVersionPlanHistoryFromCellPlanLineTotalDTO(CellPlanLineTotalDTO cellPlanLineTotalDTO);

    @Mappings(
            {
                    @Mapping(target = "isOverseaName", source = "isOversea"),
                    @Mapping(target = "workshopName", source = "workshop"),
                    @Mapping(target = "cellTypeName", source = "cellsType"),
                    @Mapping(target = "cellTypeId", source = "cellsTypeId")
            }
    )
    CellVersionPlanHistoryDTO toCellVersionPlanHistoryFromCellInstockPlanTotalDTO(CellInstockPlanTotalDTO cellInstockPlanTotalDTO);

    List<CellVersionPlanHistoryExcelDTO> toExcelDTOFromEntity(List<CellVersionPlanHistory> cellVersionPlanHistoryCollect);
    CellVersionPlanHistoryExcelDTO toExcelDTOFromEntity(CellVersionPlanHistory cellVersionPlanHistory );
}
