package com.jinkosolar.scp.mps.domain.convert;



import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellWipDTO;
import com.jinkosolar.scp.mps.domain.entity.CellWip;
import com.jinkosolar.scp.mps.domain.excel.CellWipExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellWipSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工单生成预览 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-15 08:28:30
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellWipDEConvert extends BaseDEConvert<CellWipDTO, CellWip> {

    CellWipDEConvert INSTANCE = Mappers.getMapper(CellWipDEConvert.class);

    List<CellWipExcelDTO> toExcelDTO(List<CellWipDTO> dtos);

    CellWipExcelDTO toExcelDTO(CellWipDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CellWip saveDTOtoEntity(CellWipSaveDTO saveDTO, @MappingTarget CellWip entity);

}
