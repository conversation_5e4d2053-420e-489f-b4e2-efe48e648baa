package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellWorkshopPriorityTargetDTO;
import com.jinkosolar.scp.mps.domain.entity.CellWorkshopPriorityTarget;
import com.jinkosolar.scp.mps.domain.excel.CellWorkshopPriorityTargetExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CellWorkshopPriorityTargetSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 车间优先度效率目标值 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-28 02:59:28
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellWorkshopPriorityTargetDEConvert extends BaseDEConvert<CellWorkshopPriorityTargetDTO, CellWorkshopPriorityTarget> {

    CellWorkshopPriorityTargetDEConvert INSTANCE = Mappers.getMapper(CellWorkshopPriorityTargetDEConvert.class);
    @Mappings(
            @Mapping(target = "workshop" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(dto.getWorkshopId()))")
    )
    CellWorkshopPriorityTargetExcelDTO toExcelDTO(CellWorkshopPriorityTargetDTO dto);
    List<CellWorkshopPriorityTargetExcelDTO> toExcelDTO(List<CellWorkshopPriorityTargetDTO> dtos);
    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CellWorkshopPriorityTarget saveDTOtoEntity(CellWorkshopPriorityTargetSaveDTO saveDTO, @MappingTarget CellWorkshopPriorityTarget entity);

    @Override
    @Mappings(
            @Mapping(target = "workshop" ,expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(dto.getWorkshopId()))")
    )
    CellWorkshopPriorityTargetDTO toDto(CellWorkshopPriorityTarget dto);

    @Override
    List<CellWorkshopPriorityTargetDTO> toDto(List<CellWorkshopPriorityTarget> entities);
}
