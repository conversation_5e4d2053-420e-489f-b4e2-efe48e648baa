package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanCompDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.ComponentAlignmentPlanComp;
import com.jinkosolar.scp.mps.domain.excel.ComponentAlignmentPlanCompExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ComponentAlignmentPlanCompSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 组件定线规划 版本对比表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-28 11:29:31
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentAlignmentPlanCompDEConvert extends BaseDEConvert<ComponentAlignmentPlanCompDTO, ComponentAlignmentPlanComp> {

    ComponentAlignmentPlanCompDEConvert INSTANCE = Mappers.getMapper(ComponentAlignmentPlanCompDEConvert.class);

    List<ComponentAlignmentPlanCompExcelDTO> toExcelDTO(List<ComponentAlignmentPlanCompDTO> dtos);

    ComponentAlignmentPlanCompExcelDTO toExcelDTO(ComponentAlignmentPlanCompDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    ComponentAlignmentPlanComp saveDTOtoEntity(ComponentAlignmentPlanCompSaveDTO saveDTO, @MappingTarget ComponentAlignmentPlanComp entity);

    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    ComponentAlignmentPlanComp planDTOtoEntity(ComponentAlignmentPlanDTO sourceDatum);

    List<ComponentAlignmentPlanComp> planDTOtoEntity(List<ComponentAlignmentPlanDTO> sourceDatum);

    @Override
    @Mappings({
            @Mapping(target = "workCenterName",
                    expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(entity.getWorkCenterId()))"),
            @Mapping(target = "accessoryName",
                    expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(entity.getAccessoryId()))"),
            @Mapping(target = "mainGridName",
                    expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(entity.getMainGridId()))"),
            @Mapping(target = "supplyUsaName",
                    expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(entity.getSupplyUsaId()))"),
            @Mapping(target = "domesticOrOverseasName",
                    expression = "java(com.ibm.scp.common.api.util.LovUtils.getName(entity.getDomesticOrOverseasId()))"),
    })
    ComponentAlignmentPlanCompDTO toDto(ComponentAlignmentPlanComp entity);
}
