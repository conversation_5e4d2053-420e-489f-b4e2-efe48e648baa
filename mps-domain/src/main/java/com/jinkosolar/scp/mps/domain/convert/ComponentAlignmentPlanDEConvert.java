package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanExcelImpDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanRepDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentCapacityDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.ComponentAlignmentPlan;
import com.jinkosolar.scp.mps.domain.excel.ComponentCapacityDetailExcelDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentAlignmentPlanDEConvert extends BaseDEConvert<ComponentAlignmentPlanDTO, ComponentAlignmentPlan> {
    ComponentAlignmentPlanDEConvert INSTANCE = Mappers.getMapper(ComponentAlignmentPlanDEConvert.class);

    void resetComponentAlignmentPlan(ComponentAlignmentPlanDTO componentAlignmentPlanDTO, @MappingTarget ComponentAlignmentPlan componentAlignmentPlan);

    void resetComponentAlignmentPlanDtos(List<ComponentAlignmentPlan> list, @MappingTarget List<ComponentAlignmentPlanRepDTO> componentAlignmentPlanRepDTOS);
//    void resetComponentAlignmentPlans(List<ComponentAlignmentPlan> list, @MappingTarget List<ComponentAlignmentPlan> componentAlignmentPlans);
    void resetComponentAlignmentPlan(ComponentAlignmentPlan componentAlignmentPlan, @MappingTarget ComponentAlignmentPlan componentAlignmentPlanNew);
//    void resetComponentAlignmentPlanRep(ComponentAlignmentPlanRepDTO componentAlignmentPlanDTO, @MappingTarget ComponentAlignmentPlanRepDTO componentAlignmentPlan);

    ComponentAlignmentPlanDTO toDTOFromImp(ComponentAlignmentPlanExcelImpDTO impDTO);

}