package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.LovWorkCenterVO;
import com.jinkosolar.scp.mps.domain.entity.ComponentCapacityDetail;
import com.jinkosolar.scp.mps.domain.dto.ComponentCapacityDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductPlanLineDetail;
import com.jinkosolar.scp.mps.domain.entity.WorkCenterLineAdjustSummary;
import com.jinkosolar.scp.mps.domain.excel.ComponentCapacityDetailExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ComponentCapacityDetailSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]组件产能明细表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-13
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentCapacityDetailDEConvert extends BaseDEConvert<ComponentCapacityDetailDTO, ComponentCapacityDetail> {

    ComponentCapacityDetailDEConvert INSTANCE = Mappers.getMapper(ComponentCapacityDetailDEConvert.class);

    List<ComponentCapacityDetailExcelDTO> toExcelDTO(List<ComponentCapacityDetailDTO> dtos);

    ComponentCapacityDetailExcelDTO toExcelDTO(ComponentCapacityDetailDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "id", ignore = true)
    ComponentCapacityDetail productPlanLineDetailToEntity(ProductPlanLineDetail productPlanLineDetail, @MappingTarget ComponentCapacityDetail entity);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    ComponentCapacityDetail lovWorkCenterVOToEntity(LovWorkCenterVO lovWorkCenterVO, @MappingTarget ComponentCapacityDetail entity);


    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    ComponentCapacityDetail saveDTOtoEntity(ComponentCapacityDetailSaveDTO saveDTO, @MappingTarget ComponentCapacityDetail entity);
}
