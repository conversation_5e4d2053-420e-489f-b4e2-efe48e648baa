package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ComponentLinePlanDetailDTO;
import com.jinkosolar.scp.mps.domain.dto.LovWorkCenterVO;
import com.jinkosolar.scp.mps.domain.entity.ComponentLinePlanDetail;
import com.jinkosolar.scp.mps.domain.excel.ComponentLinePlanDetailExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ComponentLinePlanDetailSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]定线规划明细表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-13
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentLinePlanDetailDEConvert extends BaseDEConvert<ComponentLinePlanDetailDTO, ComponentLinePlanDetail> {

    ComponentLinePlanDetailDEConvert INSTANCE = Mappers.getMapper(ComponentLinePlanDetailDEConvert.class);

    List<ComponentLinePlanDetailExcelDTO> toExcelDTO(List<ComponentLinePlanDetailDTO> dtos);

    ComponentLinePlanDetailExcelDTO toExcelDTO(ComponentLinePlanDetailDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    ComponentLinePlanDetail lovWorkCenterVOToEntity(LovWorkCenterVO lovWorkCenterVO, @MappingTarget ComponentLinePlanDetail entity);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    ComponentLinePlanDetail saveDTOtoEntity(ComponentLinePlanDetailSaveDTO saveDTO, @MappingTarget ComponentLinePlanDetail entity);
}
