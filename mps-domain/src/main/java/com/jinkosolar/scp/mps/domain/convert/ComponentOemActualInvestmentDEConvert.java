package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ComponentOemActualInvestmentDTO;
import com.jinkosolar.scp.mps.domain.entity.ComponentOemActualInvestment;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentOemActualInvestmentDEConvert extends BaseDEConvert<ComponentOemActualInvestmentDTO, ComponentOemActualInvestment> {
    ComponentOemActualInvestmentDEConvert INSTANCE = Mappers.getMapper(ComponentOemActualInvestmentDEConvert.class);

    void resetComponentOemActualInvestment(ComponentOemActualInvestmentDTO componentOemActualInvestmentDTO, @MappingTarget ComponentOemActualInvestment componentOemActualInvestment);
}