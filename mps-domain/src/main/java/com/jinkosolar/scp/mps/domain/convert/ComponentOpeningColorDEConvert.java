package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ComponentOpeningColorDTO;
import com.jinkosolar.scp.mps.domain.entity.ComponentOpeningColor;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentOpeningColorDEConvert extends BaseDEConvert<ComponentOpeningColorDTO, ComponentOpeningColor> {
    ComponentOpeningColorDEConvert INSTANCE = Mappers.getMapper(ComponentOpeningColorDEConvert.class);

    void resetComponentOpeningColor(ComponentOpeningColorDTO componentOpeningColorDTO, @MappingTarget ComponentOpeningColor componentOpeningColor);
}