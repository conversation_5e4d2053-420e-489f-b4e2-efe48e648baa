package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.ComponentSwitchLossDTO;
import com.jinkosolar.scp.mps.domain.entity.ComponentSwitchLoss;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 组件-切换损失数据转换器
 *
 * <AUTHOR>
 * @date 2024-04-09 16:42:30
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentSwitchLossDEConvert extends BaseDEConvert<ComponentSwitchLossDTO, ComponentSwitchLoss> {

    ComponentSwitchLossDEConvert INSTANCE = Mappers.getMapper(ComponentSwitchLossDEConvert.class);

}
