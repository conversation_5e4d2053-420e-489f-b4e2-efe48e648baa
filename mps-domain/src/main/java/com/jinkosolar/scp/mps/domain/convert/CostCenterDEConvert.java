package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.CostCenterDTO;
import com.jinkosolar.scp.mps.domain.entity.CostCenterDO;
import com.jinkosolar.scp.mps.domain.excel.CostCenterExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CostCenterSaveDTO;
import lombok.*;
import java.util.*;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 成本中心维护Convert
*
* <AUTHOR>
*/
@Mapper(componentModel = "spring",
unmappedTargetPolicy = ReportingPolicy.IGNORE,
nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CostCenterDEConvert extends BaseDEConvert<CostCenterDTO, CostCenterDO>{

    CostCenterDEConvert INSTANCE = Mappers.getMapper(CostCenterDEConvert.class);

    List<CostCenterDTO> toExcelDTO(List<CostCenterDTO> dtos);

    CostCenterExcelDTO toExcelDTO(CostCenterDTO dto);

    @BeanMapping(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
    @Mapping(target = "id", ignore = true)
    })
    CostCenterDO saveDTOtoEntity(CostCenterSaveDTO saveDTO, @MappingTarget CostCenterDO entity);
}