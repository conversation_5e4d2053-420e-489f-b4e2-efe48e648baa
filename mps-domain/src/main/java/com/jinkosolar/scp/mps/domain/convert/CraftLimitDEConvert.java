package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.CraftLimitDTO;
import com.jinkosolar.scp.mps.domain.entity.CraftLimitDO;
import com.jinkosolar.scp.mps.domain.excel.CraftLimitExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CraftLimitSaveDTO;
import lombok.*;
import java.util.*;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 工艺限制Convert
*
* <AUTHOR>
*/
@Mapper(componentModel = "spring",
unmappedTargetPolicy = ReportingPolicy.IGNORE,
nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CraftLimitDEConvert extends Base<PERSON>Convert<CraftLimitDTO, CraftLimitDO>{

    CraftLimitDEConvert INSTANCE = Mappers.getMapper(CraftLimitDEConvert.class);

    List<CraftLimitDTO> toExcelDTO(List<CraftLimitDTO> dtos);

    CraftLimitExcelDTO toExcelDTO(CraftLimitDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    CraftLimitDTO saveDTOtoDto(CraftLimitSaveDTO saveDTO, @MappingTarget CraftLimitDTO dto);

    @BeanMapping(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
    @Mapping(target = "id", ignore = true)
    })
    CraftLimitDO saveDTOtoEntity(CraftLimitSaveDTO saveDTO, @MappingTarget CraftLimitDO entity);
}