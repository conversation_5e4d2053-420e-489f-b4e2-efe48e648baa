package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrucibleBulgeDeductionDTO;
import com.jinkosolar.scp.mps.domain.entity.CrucibleBulgeDeduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrucibleBulgeDeductionDEConvert extends BaseDEConvert<CrucibleBulgeDeductionDTO, CrucibleBulgeDeduction> {
    CrucibleBulgeDeductionDEConvert INSTANCE = Mappers.getMapper(CrucibleBulgeDeductionDEConvert.class);

    void resetCrucibleBulgeDeduction(CrucibleBulgeDeductionDTO crucibleBulgeDeductionDTO, @MappingTarget CrucibleBulgeDeduction crucibleBulgeDeduction);
}