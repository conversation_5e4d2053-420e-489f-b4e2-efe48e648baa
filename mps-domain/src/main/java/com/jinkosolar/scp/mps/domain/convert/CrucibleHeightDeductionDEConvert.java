package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrucibleHeightDeductionDTO;
import com.jinkosolar.scp.mps.domain.entity.CrucibleHeightDeduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrucibleHeightDeductionDEConvert extends BaseDEConvert<CrucibleHeightDeductionDTO, CrucibleHeightDeduction> {
    CrucibleHeightDeductionDEConvert INSTANCE = Mappers.getMapper(CrucibleHeightDeductionDEConvert.class);

    void resetCrucibleHeightDeduction(CrucibleHeightDeductionDTO crucibleHeightDeductionDTO, @MappingTarget CrucibleHeightDeduction crucibleHeightDeduction);
}