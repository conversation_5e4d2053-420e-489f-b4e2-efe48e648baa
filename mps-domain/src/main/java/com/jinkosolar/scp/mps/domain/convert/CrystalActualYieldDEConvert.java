package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalActualYieldDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalActualYield;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalActualYieldDEConvert extends BaseDEConvert<CrystalActualYieldDTO, CrystalActualYield> {
    CrystalActualYieldDEConvert INSTANCE = Mappers.getMapper(CrystalActualYieldDEConvert.class);

    void resetCrystalActualYield(CrystalActualYieldDTO crystalActualYieldDTO, @MappingTarget CrystalActualYield crystalActualYield);
}