package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalActualYieldMesInfoDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalActualYieldMesInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalActualYieldMesInfoDEConvert extends BaseDEConvert<CrystalActualYieldMesInfoDTO, CrystalActualYieldMesInfo> {
    CrystalActualYieldMesInfoDEConvert INSTANCE = Mappers.getMapper(CrystalActualYieldMesInfoDEConvert.class);

    void resetCrystalActualYieldMesInfo(CrystalActualYieldMesInfoDTO crystalActualYieldMesInfoDTO, @MappingTarget CrystalActualYieldMesInfo crystalActualYieldMesInfo);
}