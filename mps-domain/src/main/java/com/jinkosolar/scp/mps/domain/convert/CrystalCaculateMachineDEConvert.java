package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalCaculateMachineDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalCaculateMachine;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalCaculateMachineDEConvert extends BaseDEConvert<CrystalCaculateMachineDTO, CrystalCaculateMachine> {
    CrystalCaculateMachineDEConvert INSTANCE = Mappers.getMapper(CrystalCaculateMachineDEConvert.class);

    void resetCrystalCaculateMachine(CrystalCaculateMachineDTO crystalCaculateMachineDTO, @MappingTarget CrystalCaculateMachine crystalCaculateMachine);
}