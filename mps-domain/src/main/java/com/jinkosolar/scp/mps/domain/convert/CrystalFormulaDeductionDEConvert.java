package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalFormulaDeductionDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalFormulaDeduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalFormulaDeductionDEConvert extends BaseDEConvert<CrystalFormulaDeductionDTO, CrystalFormulaDeduction> {
    CrystalFormulaDeductionDEConvert INSTANCE = Mappers.getMapper(CrystalFormulaDeductionDEConvert.class);

    void resetCrystalFormulaDeduction(CrystalFormulaDeductionDTO crystalFormulaDeductionDTO, @MappingTarget CrystalFormulaDeduction crystalFormulaDeduction);
}