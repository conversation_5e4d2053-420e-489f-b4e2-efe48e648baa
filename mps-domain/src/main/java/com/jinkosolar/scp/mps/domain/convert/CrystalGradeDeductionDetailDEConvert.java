package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeDeductionDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalGradeDeductionDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalGradeDeductionDetailDEConvert extends BaseDEConvert<CrystalGradeDeductionDetailDTO, CrystalGradeDeductionDetail> {
    CrystalGradeDeductionDetailDEConvert INSTANCE = Mappers.getMapper(CrystalGradeDeductionDetailDEConvert.class);

    void resetCrystalGradeDeductionDetail(CrystalGradeDeductionDetailDTO crystalGradeDeductionDetailDTO, @MappingTarget CrystalGradeDeductionDetail crystalGradeDeductionDetail);
}