package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeDeductionMainDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalGradeDeductionMain;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalGradeDeductionMainDEConvert extends BaseDEConvert<CrystalGradeDeductionMainDTO, CrystalGradeDeductionMain> {
    CrystalGradeDeductionMainDEConvert INSTANCE = Mappers.getMapper(CrystalGradeDeductionMainDEConvert.class);

    void resetCrystalGradeDeductionMain(CrystalGradeDeductionMainDTO crystalGradeDeductionMainDTO, @MappingTarget CrystalGradeDeductionMain crystalGradeDeductionMain);
}