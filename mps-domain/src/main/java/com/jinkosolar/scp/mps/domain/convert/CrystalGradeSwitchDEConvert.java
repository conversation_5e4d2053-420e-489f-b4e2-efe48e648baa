package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalGradeSwitchDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalGradeSwitch;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalGradeSwitchDEConvert extends BaseDEConvert<CrystalGradeSwitchDTO, CrystalGradeSwitch> {
    CrystalGradeSwitchDEConvert INSTANCE = Mappers.getMapper(CrystalGradeSwitchDEConvert.class);

    void resetCrystalGradeSwitch(CrystalGradeSwitchDTO crystalGradeSwitchDTO, @MappingTarget CrystalGradeSwitch crystalGradeSwitch);
}