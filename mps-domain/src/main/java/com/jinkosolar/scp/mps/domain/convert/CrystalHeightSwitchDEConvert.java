package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalHeightSwitchDTO;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalHeightSwitch;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalHeightSwitchDEConvert extends BaseDEConvert<CrystalHeightSwitchDTO, CrystalHeightSwitch> {
    CrystalHeightSwitchDEConvert INSTANCE = Mappers.getMapper(CrystalHeightSwitchDEConvert.class);

    void resetCrystalHeightSwitch(CrystalHeightSwitchDTO crystalHeightSwitchDTO, @MappingTarget CrystalHeightSwitch crystalHeightSwitch);

    @Mappings({
            @Mapping(source = "factoryId", target = "attribute3"),
            @Mapping(source = "source.startTime", target = "schedulingStartTime"),
            @Mapping(source = "source.workCenterId", target = "workCenterId"),
            @Mapping(source = "source.furnaceSizeId", target = "thermalFieldSize"),
            @Mapping(source = "source.heightId", target = "crucibleSpecification"),
            @Mapping(source = "source.crystalHearthQuantity", target = "machineQty"),
            @Mapping(source = "source.workShopId", target = "attribute4"),
            @Mapping(source = "source.versionNumber", target = "planVersion"),
            @Mapping(source = "source.supplierId", target = "attribute15"),
            @Mapping(source = "source.startTime", target = "startTime"),
            @Mapping(source = "source.endTime", target = "endTime"),
            @Mapping(source = "productType", target = "productType"),
    })
    NonModuleProductionPlanDTO toNonModuleProductionPlanDTO(CrystalHeightSwitch source,
                                                            String factoryId,
                                                            String productType);
}