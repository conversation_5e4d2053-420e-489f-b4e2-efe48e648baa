package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalJkeDeductionDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalJkeDeduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalJkeDeductionDEConvert extends BaseDEConvert<CrystalJkeDeductionDTO, CrystalJkeDeduction> {
    CrystalJkeDeductionDEConvert INSTANCE = Mappers.getMapper(CrystalJkeDeductionDEConvert.class);

    void resetCrystalJkeDeduction(CrystalJkeDeductionDTO crystalJkeDeductionDTO, @MappingTarget CrystalJkeDeduction crystalJkeDeduction);
}