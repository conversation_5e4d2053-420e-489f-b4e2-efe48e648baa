package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalPlanClimbSectionResultDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalPlanClimbSectionResult;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalPlanClimbSectionResultDEConvert extends BaseDEConvert<CrystalPlanClimbSectionResultDTO, CrystalPlanClimbSectionResult> {
    CrystalPlanClimbSectionResultDEConvert INSTANCE = Mappers.getMapper(CrystalPlanClimbSectionResultDEConvert.class);

    void resetCrystalPlanClimbSectionResult(CrystalPlanClimbSectionResultDTO crystalPlanClimbSectionResultDTO, @MappingTarget CrystalPlanClimbSectionResult crystalPlanClimbSectionResult);
}