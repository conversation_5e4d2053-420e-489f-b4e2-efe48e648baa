package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalPlanYearCrucibleRateDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalPlanYearCrucibleRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalPlanYearCrucibleRateDEConvert extends BaseDEConvert<CrystalPlanYearCrucibleRateDTO, CrystalPlanYearCrucibleRate> {
    CrystalPlanYearCrucibleRateDEConvert INSTANCE = Mappers.getMapper(CrystalPlanYearCrucibleRateDEConvert.class);

    void resetCrystalPalnYearCrucibleRate(CrystalPlanYearCrucibleRateDTO crystalPlanYearCrucibleRateDTO, @MappingTarget CrystalPlanYearCrucibleRate crystalPlanYearCrucibleRate);
}