package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalProductFineTuningDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalProductFineTuning;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalProductFineTuningDEConvert extends BaseDEConvert<CrystalProductFineTuningDTO, CrystalProductFineTuning> {
    CrystalProductFineTuningDEConvert INSTANCE = Mappers.getMapper(CrystalProductFineTuningDEConvert.class);

    void resetCrystalProductFineTuning(CrystalProductFineTuningDTO crystalProductFineTuningDTO, @MappingTarget CrystalProductFineTuning crystalProductFineTuning);
}