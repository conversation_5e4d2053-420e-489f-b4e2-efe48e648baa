package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalProductSwticPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalProductSwticPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalProductSwticPlanDEConvert extends BaseDEConvert<CrystalProductSwticPlanDTO, CrystalProductSwticPlan> {
    CrystalProductSwticPlanDEConvert INSTANCE = Mappers.getMapper(CrystalProductSwticPlanDEConvert.class);

    void resetCrystalProductSwticPlan(CrystalProductSwticPlanDTO crystalProductSwticPlanDTO, @MappingTarget CrystalProductSwticPlan crystalProductSwticPlan);
}