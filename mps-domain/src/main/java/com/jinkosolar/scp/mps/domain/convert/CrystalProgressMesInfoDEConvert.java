package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.mes.CrystalProgressMesInfoDataDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalProgressMesInfo;
import com.jinkosolar.scp.mps.domain.dto.CrystalProgressMesInfoDTO;
import com.jinkosolar.scp.mps.domain.excel.CrystalProgressMesInfoExcelDTO;
import com.jinkosolar.scp.mps.domain.save.CrystalProgressMesInfoSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]籽晶酸洗切方进度（mes接口返回） DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-11
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalProgressMesInfoDEConvert extends BaseDEConvert<CrystalProgressMesInfoDTO, CrystalProgressMesInfo> {

    CrystalProgressMesInfoDEConvert INSTANCE = Mappers.getMapper(CrystalProgressMesInfoDEConvert.class);

    List<CrystalProgressMesInfoExcelDTO> toExcelDTO(List<CrystalProgressMesInfoDTO> dtos);

    CrystalProgressMesInfoExcelDTO toExcelDTO(CrystalProgressMesInfoDTO dto);

    CrystalProgressMesInfo mesInfoToEntity(CrystalProgressMesInfoDataDTO crystalProgressMesInfoDataDTO);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    CrystalProgressMesInfo mesInfoToEntity(CrystalProgressMesInfoDataDTO crystalProgressMesInfoDataDTO, @MappingTarget CrystalProgressMesInfo entity);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    CrystalProgressMesInfo saveDTOtoEntity(CrystalProgressMesInfoSaveDTO saveDTO, @MappingTarget CrystalProgressMesInfo entity);
}
