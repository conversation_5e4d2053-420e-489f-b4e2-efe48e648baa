package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalPullingCutSquaresYieldDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalPullingCutSquaresYield;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalPullingCutSquaresYieldDEConvert extends BaseDEConvert<CrystalPullingCutSquaresYieldDTO, CrystalPullingCutSquaresYield> {
    CrystalPullingCutSquaresYieldDEConvert INSTANCE = Mappers.getMapper(CrystalPullingCutSquaresYieldDEConvert.class);

    void resetCrystalPullingCutSquaresYield(CrystalPullingCutSquaresYieldDTO crystalPullingCutSquaresYieldDTO, @MappingTarget CrystalPullingCutSquaresYield crystalPullingCutSquaresYield);
}