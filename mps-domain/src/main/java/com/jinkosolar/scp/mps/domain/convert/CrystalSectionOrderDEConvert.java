package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalSectionOrderDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalSectionOrder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalSectionOrderDEConvert extends BaseDEConvert<CrystalSectionOrderDTO, CrystalSectionOrder> {
    CrystalSectionOrderDEConvert INSTANCE = Mappers.getMapper(CrystalSectionOrderDEConvert.class);

    void resetCrystalSectionOrder(CrystalSectionOrderDTO crystalSectionOrderDTO, @MappingTarget CrystalSectionOrder crystalSectionOrder);
}