package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalSiliconSupplyDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalSiliconSupply;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 拉晶硅料供应转换类
 *
 * <AUTHOR> chenc
 * @date : 2024-11-6
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalSiliconSupplyDEConvert extends BaseDEConvert<CrystalSiliconSupplyDTO, CrystalSiliconSupply> {

    CrystalSiliconSupplyDEConvert INSTANCE = Mappers.getMapper(CrystalSiliconSupplyDEConvert.class);

    void resetCrystalSiliconSupply(CrystalSiliconSupplyDTO crystalSiliconSupplyDTO, @MappingTarget CrystalSiliconSupply crystalSiliconSupply);
}