package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalSiliconSupplyDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalSiliconSupplyDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 拉晶硅料供应明细转换类
 *
 * <AUTHOR> chenc
 * @date : 2024-11-6
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalSiliconSupplyDetailDEConvert extends BaseDEConvert<CrystalSiliconSupplyDetailDTO, CrystalSiliconSupplyDetail> {

    CrystalSiliconSupplyDetailDEConvert INSTANCE = Mappers.getMapper(CrystalSiliconSupplyDetailDEConvert.class);

    void resetCrystalSiliconSupplyDetail(CrystalSiliconSupplyDetailDTO crystalSiliconSupplyDetailDTO, @MappingTarget CrystalSiliconSupplyDetail crystalSiliconSupplyDetail);
}