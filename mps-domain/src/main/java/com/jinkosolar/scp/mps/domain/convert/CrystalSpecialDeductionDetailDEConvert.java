package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalSpecialDeductionDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalSpecialDeductionDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalSpecialDeductionDetailDEConvert extends BaseDEConvert<CrystalSpecialDeductionDetailDTO, CrystalSpecialDeductionDetail> {
    CrystalSpecialDeductionDetailDEConvert INSTANCE = Mappers.getMapper(CrystalSpecialDeductionDetailDEConvert.class);

    void resetCrystalSpecialDeductionDetail(CrystalSpecialDeductionDetailDTO crystalSpecialDeductionDetailDTO, @MappingTarget CrystalSpecialDeductionDetail crystalSpecialDeductionDetail);
}