package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalSpecialDeductionMainDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalSpecialDeductionMain;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalSpecialDeductionMainDEConvert extends BaseDEConvert<CrystalSpecialDeductionMainDTO, CrystalSpecialDeductionMain> {
    CrystalSpecialDeductionMainDEConvert INSTANCE = Mappers.getMapper(CrystalSpecialDeductionMainDEConvert.class);

    void resetCrystalSpecialDeductionMain(CrystalSpecialDeductionMainDTO crystalSpecialDeductionMainDTO, @MappingTarget CrystalSpecialDeductionMain crystalSpecialDeductionMain);
}