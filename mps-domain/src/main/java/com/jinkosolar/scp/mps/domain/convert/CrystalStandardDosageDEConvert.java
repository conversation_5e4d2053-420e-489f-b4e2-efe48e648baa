package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalStandardDosageDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalStandardDosage;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalStandardDosageDEConvert extends BaseDEConvert<CrystalStandardDosageDTO, CrystalStandardDosage> {
    CrystalStandardDosageDEConvert INSTANCE = Mappers.getMapper(CrystalStandardDosageDEConvert.class);

    void resetCrystalStandardDosage(CrystalStandardDosageDTO crystalStandardDosageDTO, @MappingTarget CrystalStandardDosage crystalStandardDosage);
}