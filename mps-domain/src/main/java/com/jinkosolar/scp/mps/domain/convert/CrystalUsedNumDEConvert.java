package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CrystalUsedNumDTO;
import com.jinkosolar.scp.mps.domain.entity.CrystalUsedNum;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CrystalUsedNumDEConvert extends BaseDEConvert<CrystalUsedNumDTO, CrystalUsedNum> {
    CrystalUsedNumDEConvert INSTANCE = Mappers.getMapper(CrystalUsedNumDEConvert.class);

    void resetCrystalUsedNum(CrystalUsedNumDTO crystalUsedNumDTO, @MappingTarget CrystalUsedNum crystalUsedNum);
}