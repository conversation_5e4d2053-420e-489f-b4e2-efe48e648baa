package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CurrentWorkshopStatusDTO;
import com.jinkosolar.scp.mps.domain.entity.CurrentWorkshopStatus;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CurrentWorkshopStatusDEConvert extends BaseDEConvert<CurrentWorkshopStatusDTO, CurrentWorkshopStatus> {
    CurrentWorkshopStatusDEConvert INSTANCE = Mappers.getMapper(CurrentWorkshopStatusDEConvert.class);

    void resetCurrentWorkshopStatus(CurrentWorkshopStatusDTO currentWorkshopStatusDTO, @MappingTarget CurrentWorkshopStatus currentWorkshopStatus);
}