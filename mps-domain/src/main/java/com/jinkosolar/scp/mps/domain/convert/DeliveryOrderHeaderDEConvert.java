package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.DeliveryOrderHeaderDTO;
import com.jinkosolar.scp.mps.domain.entity.DeliveryOrderHeader;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DeliveryOrderHeaderDEConvert extends BaseDEConvert<DeliveryOrderHeaderDTO, DeliveryOrderHeader> {
    DeliveryOrderHeaderDEConvert INSTANCE = Mappers.getMapper(DeliveryOrderHeaderDEConvert.class);

    void resetDeliveryOrderHeader(DeliveryOrderHeaderDTO deliveryOrderHeaderDTO, @MappingTarget DeliveryOrderHeader deliveryOrderHeader);
}