package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.DeliveryOrderLineDTO;
import com.jinkosolar.scp.mps.domain.entity.DeliveryOrderLine;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DeliveryOrderLineDEConvert extends BaseDEConvert<DeliveryOrderLineDTO, DeliveryOrderLine> {
    DeliveryOrderLineDEConvert INSTANCE = Mappers.getMapper(DeliveryOrderLineDEConvert.class);

    void resetDeliveryOrderLine(DeliveryOrderLineDTO deliveryOrderLineDTO, @MappingTarget DeliveryOrderLine deliveryOrderLine);
}