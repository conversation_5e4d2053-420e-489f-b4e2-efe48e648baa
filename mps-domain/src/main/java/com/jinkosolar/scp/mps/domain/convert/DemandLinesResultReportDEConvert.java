package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.DemandLinesResultReportDTO;
import com.jinkosolar.scp.mps.domain.entity.DemandLinesResultReport;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface DemandLinesResultReportDEConvert extends BaseDEConvert<DemandLinesResultReportDTO, DemandLinesResultReport> {
    DemandLinesResultReportDEConvert INSTANCE = Mappers.getMapper(DemandLinesResultReportDEConvert.class);

    void resetDemandLinesResultReport(DemandLinesResultReportDTO demandLinesResultReportDTO, @MappingTarget DemandLinesResultReport demandLinesResultReport);
}