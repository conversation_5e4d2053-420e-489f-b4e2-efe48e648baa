package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.EquipmentInventoryLifespanDTO;
import com.jinkosolar.scp.mps.domain.entity.EquipmentInventoryLifespan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface EquipmentInventoryLifespanDEConvert extends BaseDEConvert<EquipmentInventoryLifespanDTO, EquipmentInventoryLifespan> {
    EquipmentInventoryLifespanDEConvert INSTANCE = Mappers.getMapper(EquipmentInventoryLifespanDEConvert.class);

    void resetEquipmentInventoryLifespan(EquipmentInventoryLifespanDTO equipmentInventoryLifespanDTO, @MappingTarget EquipmentInventoryLifespan equipmentInventoryLifespan);
}