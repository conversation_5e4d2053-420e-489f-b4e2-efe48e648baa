package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.EquipmentInventoryStatusDTO;
import com.jinkosolar.scp.mps.domain.entity.EquipmentInventoryStatus;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface EquipmentInventoryStatusDEConvert extends BaseDEConvert<EquipmentInventoryStatusDTO, EquipmentInventoryStatus> {
    EquipmentInventoryStatusDEConvert INSTANCE = Mappers.getMapper(EquipmentInventoryStatusDEConvert.class);

    void resetEquipmentInventoryStatus(EquipmentInventoryStatusDTO equipmentInventoryStatusDTO, @MappingTarget EquipmentInventoryStatus equipmentInventoryStatus);
}