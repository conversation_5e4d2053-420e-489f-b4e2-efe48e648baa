package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.ErpApprovalSupplierDTO;
import com.jinkosolar.scp.mps.domain.entity.ErpApprovalSupplier;
import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 批准供应商 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:05:46
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ErpApprovalSupplierDEConvert extends BaseDEConvert<ErpApprovalSupplierDTO, ErpApprovalSupplier> {

    ErpApprovalSupplierDEConvert INSTANCE = Mappers.getMapper(ErpApprovalSupplierDEConvert.class);

}
