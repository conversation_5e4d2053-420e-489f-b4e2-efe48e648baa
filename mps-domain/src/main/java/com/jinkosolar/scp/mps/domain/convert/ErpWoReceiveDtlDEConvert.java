package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ErpWoReceiveDtlDTO;
import com.jinkosolar.scp.mps.domain.entity.ErpWoReceiveDtl;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ErpWoReceiveDtlDEConvert extends BaseDEConvert<ErpWoReceiveDtlDTO, ErpWoReceiveDtl> {
    ErpWoReceiveDtlDEConvert INSTANCE = Mappers.getMapper(ErpWoReceiveDtlDEConvert.class);

    void resetErpWoReceiveDtl(ErpWoReceiveDtlDTO erpWoReceiveDtlDTO, @MappingTarget ErpWoReceiveDtl erpWoReceiveDtl);
}