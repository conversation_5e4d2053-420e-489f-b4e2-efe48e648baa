package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.jip.api.dto.factory.FactoryResponseDto;
import com.jinkosolar.scp.mps.domain.dto.FactoryDTO;
import com.jinkosolar.scp.mps.domain.entity.Factory;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FactoryDEConvert extends BaseDEConvert<FactoryDTO, Factory> {
    FactoryDEConvert INSTANCE = Mappers.getMapper(FactoryDEConvert.class);

    void resetFactory(FactoryDTO factoryDTO, @MappingTarget Factory factory);

    Factory toEntity(FactoryResponseDto.FactoryResponseInfo factoryDTO);

    void resetFactory(FactoryResponseDto.FactoryResponseInfo info,@MappingTarget Factory factory);
}
