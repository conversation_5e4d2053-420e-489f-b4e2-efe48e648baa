package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.FiPostingDetailDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.FiPostingDetailSyncDTO;
import com.jinkosolar.scp.mps.domain.entity.FiPostingDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FiPostingDetailDEConvert extends BaseDEConvert<FiPostingDetailDTO, FiPostingDetail> {
    FiPostingDetailDEConvert INSTANCE = Mappers.getMapper(FiPostingDetailDEConvert.class);

    void resetFiPostingDetail(FiPostingDetailDTO fiPostingDetailDTO, @MappingTarget FiPostingDetail fiPostingDetail);

    void syncDtoToEntity(FiPostingDetailSyncDTO fiPostingDetailSyncDTO, @MappingTarget FiPostingDetail fiPostingDetail);
}