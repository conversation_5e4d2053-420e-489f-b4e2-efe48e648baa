package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.FoldFactorDTO;
import com.jinkosolar.scp.mps.domain.entity.FoldFactor;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FoldFactorDEConvert extends BaseDEConvert<FoldFactorDTO, FoldFactor> {
    FoldFactorDEConvert INSTANCE = Mappers.getMapper(FoldFactorDEConvert.class);

    void resetFoldFactor(FoldFactorDTO foldFactorDTO, @MappingTarget FoldFactor foldFactor);
}
