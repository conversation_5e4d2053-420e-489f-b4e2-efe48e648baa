package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.FormulaSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.FormulaSwitchingPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FormulaSwitchingPlanDEConvert extends BaseDEConvert<FormulaSwitchingPlanDTO, FormulaSwitchingPlan> {
    FormulaSwitchingPlanDEConvert INSTANCE = Mappers.getMapper(FormulaSwitchingPlanDEConvert.class);

    void resetFormulaSwitchingPlan(FormulaSwitchingPlanDTO formulaSwitchingPlanDTO, @MappingTarget FormulaSwitchingPlan formulaSwitchingPlan);
}