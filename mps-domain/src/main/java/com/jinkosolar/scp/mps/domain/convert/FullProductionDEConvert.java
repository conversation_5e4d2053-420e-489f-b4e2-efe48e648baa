package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.FullProductionDTO;
import com.jinkosolar.scp.mps.domain.entity.FullProduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FullProductionDEConvert extends BaseDEConvert<FullProductionDTO, FullProduction> {
    FullProductionDEConvert INSTANCE = Mappers.getMapper(FullProductionDEConvert.class);

    void resetFullProduction(FullProductionDTO fullProductionDTO, @MappingTarget FullProduction fullProduction);
}