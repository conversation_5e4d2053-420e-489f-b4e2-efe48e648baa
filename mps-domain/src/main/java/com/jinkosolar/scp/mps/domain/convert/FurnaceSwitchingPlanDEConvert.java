package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.FurnaceSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.FurnaceSwitchingPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface FurnaceSwitchingPlanDEConvert extends BaseDEConvert<FurnaceSwitchingPlanDTO, FurnaceSwitchingPlan> {
    FurnaceSwitchingPlanDEConvert INSTANCE = Mappers.getMapper(FurnaceSwitchingPlanDEConvert.class);

    void resetFurnaceSwitchingPlan(FurnaceSwitchingPlanDTO furnaceSwitchingPlanDTO, @MappingTarget FurnaceSwitchingPlan furnaceSwitchingPlan);
}