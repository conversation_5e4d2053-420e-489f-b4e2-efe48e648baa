package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.HearthClimbRuleDTO;
import com.jinkosolar.scp.mps.domain.entity.HearthClimbRule;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HearthClimbRuleDEConvert extends BaseDEConvert<HearthClimbRuleDTO, HearthClimbRule> {
    HearthClimbRuleDEConvert INSTANCE = Mappers.getMapper(HearthClimbRuleDEConvert.class);

    void resetHearthClimbRule(HearthClimbRuleDTO hearthClimbRuleDTO, @MappingTarget HearthClimbRule hearthClimbRule);
}