package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.HearthClimbRuleSplitDateDTO;
import com.jinkosolar.scp.mps.domain.entity.HearthClimbRuleSplitDate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HearthClimbRuleSplitDateDEConvert extends BaseDEConvert<HearthClimbRuleSplitDateDTO, HearthClimbRuleSplitDate> {
    HearthClimbRuleSplitDateDEConvert INSTANCE = Mappers.getMapper(HearthClimbRuleSplitDateDEConvert.class);

    void resetHearthClimbRuleSplitDate(HearthClimbRuleSplitDateDTO hearthClimbRuleSplitDateDTO, @MappingTarget HearthClimbRuleSplitDate hearthClimbRuleSplitDate);

    HearthClimbRuleSplitDateDTO copy(HearthClimbRuleSplitDateDTO hearthClimbRuleSplitDateDTO);
}