package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.HotFieldSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.HotFieldSwitchingPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HotFieldSwitchingPlanDEConvert extends BaseDEConvert<HotFieldSwitchingPlanDTO, HotFieldSwitchingPlan> {
    HotFieldSwitchingPlanDEConvert INSTANCE = Mappers.getMapper(HotFieldSwitchingPlanDEConvert.class);

    void resetHotFieldSwitchingPlan(HotFieldSwitchingPlanDTO hotFieldSwitchingPlanDTO, @MappingTarget HotFieldSwitchingPlan hotFieldSwitchingPlan);
}