package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.InStockEfficiencyDataDTO;
import com.jinkosolar.scp.mps.domain.dto.ProductInStockRecordDTO;
import com.jinkosolar.scp.mps.domain.entity.InStockEfficiencyData;
import com.jinkosolar.scp.mps.domain.excel.InStockEfficiencyDataExcelDTO;
import com.jinkosolar.scp.mps.domain.save.InStockEfficiencyDataSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 入库效率数据 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-01-16 11:09:43
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface InStockEfficiencyDataDEConvert extends BaseDEConvert<InStockEfficiencyDataDTO, InStockEfficiencyData> {

    InStockEfficiencyDataDEConvert INSTANCE = Mappers.getMapper(InStockEfficiencyDataDEConvert.class);

    List<InStockEfficiencyDataExcelDTO> toExcelDTO(List<InStockEfficiencyDataDTO> dtos);

    InStockEfficiencyDataExcelDTO toExcelDTO(InStockEfficiencyDataDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    InStockEfficiencyData saveDTOtoEntity(InStockEfficiencyDataSaveDTO saveDTO, @MappingTarget InStockEfficiencyData entity);

    @Mappings({
            @Mapping(target = "inventoryDate",
                    expression = "java(inStockEfficiencyData.getInventoryDate().atTime(0,0))")
    })
    ProductInStockRecordDTO toStockRecordDto(InStockEfficiencyData inStockEfficiencyData);

    List<ProductInStockRecordDTO> toStockRecordDto(List<InStockEfficiencyData> inStockEfficiencyData);
}
