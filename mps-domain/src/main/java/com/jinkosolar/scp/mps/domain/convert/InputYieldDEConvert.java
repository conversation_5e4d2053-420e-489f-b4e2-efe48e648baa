package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.InputYieldDTO;
import com.jinkosolar.scp.mps.domain.entity.InputYield;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface InputYieldDEConvert extends BaseDEConvert<InputYieldDTO, InputYield> {
    InputYieldDEConvert INSTANCE = Mappers.getMapper(InputYieldDEConvert.class);

    void resetInputYield(InputYieldDTO inputYieldDTO, @MappingTarget InputYield inputYield);
}