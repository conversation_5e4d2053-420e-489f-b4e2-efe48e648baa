package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.entity.IntegratedOperatingRateReport;
import com.jinkosolar.scp.mps.domain.dto.IntegratedOperatingRateReportDTO;
import com.jinkosolar.scp.mps.domain.excel.IntegratedOperatingRateReportExcelDTO;
import com.jinkosolar.scp.mps.domain.save.IntegratedOperatingRateReportSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]一体化开工率报表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface IntegratedOperatingRateReportDEConvert extends BaseDEConvert<IntegratedOperatingRateReportDTO, IntegratedOperatingRateReport> {

    IntegratedOperatingRateReportDEConvert INSTANCE = Mappers.getMapper(IntegratedOperatingRateReportDEConvert.class);

    List<IntegratedOperatingRateReportExcelDTO> toExcelDTO(List<IntegratedOperatingRateReportDTO> dtos);

    IntegratedOperatingRateReportExcelDTO toExcelDTO(IntegratedOperatingRateReportDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    IntegratedOperatingRateReport saveDTOtoEntity(IntegratedOperatingRateReportSaveDTO saveDTO, @MappingTarget IntegratedOperatingRateReport entity);
}
