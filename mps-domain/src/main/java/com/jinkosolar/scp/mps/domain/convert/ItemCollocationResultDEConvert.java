package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ItemCollocationResultDTO;
import com.jinkosolar.scp.mps.domain.entity.ItemCollocationResult;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ItemCollocationResultDEConvert extends BaseDEConvert<ItemCollocationResultDTO, ItemCollocationResult> {
    ItemCollocationResultDEConvert INSTANCE = Mappers.getMapper(ItemCollocationResultDEConvert.class);

    void resetItemCollocationResult(ItemCollocationResultDTO itemCollocationResultDTO, @MappingTarget ItemCollocationResult itemCollocationResult);
}