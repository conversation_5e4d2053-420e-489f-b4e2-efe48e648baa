package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.JkeSwitchingPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.JkeSwitchingPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface JkeSwitchingPlanDEConvert extends BaseDEConvert<JkeSwitchingPlanDTO, JkeSwitchingPlan> {
    JkeSwitchingPlanDEConvert INSTANCE = Mappers.getMapper(JkeSwitchingPlanDEConvert.class);

    void resetJkeSwitchingPlan(JkeSwitchingPlanDTO jkeSwitchingPlanDTO, @MappingTarget JkeSwitchingPlan jkeSwitchingPlan);
}