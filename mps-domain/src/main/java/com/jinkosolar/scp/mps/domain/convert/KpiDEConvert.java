package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.KpiDTO;
import com.jinkosolar.scp.mps.domain.entity.Kpi;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface KpiDEConvert extends BaseDEConvert<KpiDTO, Kpi> {
    KpiDEConvert INSTANCE = Mappers.getMapper(KpiDEConvert.class);

    void resetKpi(KpiDTO kpiDTO, @MappingTarget Kpi kpi);
}