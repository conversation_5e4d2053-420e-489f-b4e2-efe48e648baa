package com.jinkosolar.scp.mps.domain.convert;


import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.KpiDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.KpiDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface KpiDetailDEConvert extends BaseDEConvert<KpiDetailDTO, KpiDetail> {
    KpiDetailDEConvert INSTANCE = Mappers.getMapper(KpiDetailDEConvert.class);

    void resetKpi(KpiDetailDTO kpiDetailDTO, @MappingTarget KpiDetail kpiDetail);
}