package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.LongTermBatteryLossRatioDTO;
import com.jinkosolar.scp.mps.domain.entity.LongTermBatteryLossRatio;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LongTermBatteryLossRatioDEConvert extends BaseDEConvert<LongTermBatteryLossRatioDTO, LongTermBatteryLossRatio> {
    LongTermBatteryLossRatioDEConvert INSTANCE = Mappers.getMapper(LongTermBatteryLossRatioDEConvert.class);

    void resetLongTermBatteryLossRatio(LongTermBatteryLossRatioDTO longTermBatteryLossRatioDTO, @MappingTarget LongTermBatteryLossRatio longTermBatteryLossRatio);
}