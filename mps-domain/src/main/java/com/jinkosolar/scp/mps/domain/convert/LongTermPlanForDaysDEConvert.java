package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.LongTermPlanForDaysDTO;
import com.jinkosolar.scp.mps.domain.entity.LongTermPlanForDays;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LongTermPlanForDaysDEConvert extends BaseDEConvert<LongTermPlanForDaysDTO, LongTermPlanForDays> {
    LongTermPlanForDaysDEConvert INSTANCE = Mappers.getMapper(LongTermPlanForDaysDEConvert.class);

    void resetLongTermPlanForDays(LongTermPlanForDaysDTO longTermPlanForDaysDTO, @MappingTarget LongTermPlanForDays longTermPlanForDays);
}