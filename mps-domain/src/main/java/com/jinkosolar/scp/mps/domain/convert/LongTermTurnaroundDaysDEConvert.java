package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.LongTermTurnaroundDaysDTO;
import com.jinkosolar.scp.mps.domain.entity.LongTermTurnaroundDays;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface LongTermTurnaroundDaysDEConvert extends BaseDEConvert<LongTermTurnaroundDaysDTO, LongTermTurnaroundDays> {
    LongTermTurnaroundDaysDEConvert INSTANCE = Mappers.getMapper(LongTermTurnaroundDaysDEConvert.class);

    void resetLongTermTurnaroundDays(LongTermTurnaroundDaysDTO longTermTurnaroundDaysDTO, @MappingTarget LongTermTurnaroundDays longTermTurnaroundDays);
}