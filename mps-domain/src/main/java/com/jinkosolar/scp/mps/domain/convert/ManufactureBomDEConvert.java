package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ManufactureBomDTO;
import com.jinkosolar.scp.mps.domain.entity.ManufactureBom;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ManufactureBomDEConvert extends BaseDEConvert<ManufactureBomDTO, ManufactureBom> {
    ManufactureBomDEConvert INSTANCE = Mappers.getMapper(ManufactureBomDEConvert.class);

    ManufactureBomDTO copyDto(ManufactureBomDTO temp);

    void resetManufactureBom(ManufactureBomDTO manufactureBomDTO, @MappingTarget ManufactureBom manufactureBom);
}