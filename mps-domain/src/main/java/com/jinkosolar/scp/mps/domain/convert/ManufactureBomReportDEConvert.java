package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ManufactureBomDTO;
import com.jinkosolar.scp.mps.domain.entity.ManufactureBomReport;
import com.jinkosolar.scp.mps.domain.dto.ManufactureBomReportDTO;
import com.jinkosolar.scp.mps.domain.excel.ManufactureBomReportExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ManufactureBomReportSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]制造BOM报表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-26
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ManufactureBomReportDEConvert extends BaseDEConvert<ManufactureBomReportDTO, ManufactureBomReport> {

    ManufactureBomReportDEConvert INSTANCE = Mappers.getMapper(ManufactureBomReportDEConvert.class);

    List<ManufactureBomReportExcelDTO> toExcelDTO(List<ManufactureBomReportDTO> dtos);

    ManufactureBomReportExcelDTO toExcelDTO(ManufactureBomReportDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    ManufactureBomReport saveDTOtoEntity(ManufactureBomReportSaveDTO saveDTO, @MappingTarget ManufactureBomReport entity);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "productTypeCode", target = "productIdCode")
    ManufactureBomReport toReportEntity(ManufactureBomDTO dto);

    @Mapping(target = "id", ignore = true)
    List<ManufactureBomReport> toReportEntity(List<ManufactureBomDTO> dto);
}
