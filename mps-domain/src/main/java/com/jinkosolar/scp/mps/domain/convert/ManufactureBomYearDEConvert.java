package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ManufactureBomYearDTO;
import com.jinkosolar.scp.mps.domain.entity.ManufactureBomYear;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ManufactureBomYearDEConvert extends BaseDEConvert<ManufactureBomYearDTO, ManufactureBomYear> {
    ManufactureBomYearDEConvert INSTANCE = Mappers.getMapper(ManufactureBomYearDEConvert.class);

    void resetManufactureBomYear(ManufactureBomYearDTO manufactureBomYearDTO, @MappingTarget ManufactureBomYear manufactureBomYear);
}