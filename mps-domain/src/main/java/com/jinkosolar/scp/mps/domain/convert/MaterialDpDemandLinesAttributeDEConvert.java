package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MaterialDpDemandLinesAttributeDTO;
import com.jinkosolar.scp.mps.domain.entity.MaterialDpDemandLinesAttribute;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MaterialDpDemandLinesAttributeDEConvert extends BaseDEConvert<MaterialDpDemandLinesAttributeDTO, MaterialDpDemandLinesAttribute> {
    MaterialDpDemandLinesAttributeDEConvert INSTANCE = Mappers.getMapper(MaterialDpDemandLinesAttributeDEConvert.class);

    void resetMaterialDpDemandLinesAttribute(MaterialDpDemandLinesAttributeDTO materialDpDemandLinesAttributeDTO, @MappingTarget MaterialDpDemandLinesAttribute materialDpDemandLinesAttribute);
}