package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MdaFactoryItemDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.MdaFactoryItemDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MdaFactoryItemDetailDEConvert extends BaseDEConvert<MdaFactoryItemDetailDTO, MdaFactoryItemDetail> {
    MdaFactoryItemDetailDEConvert INSTANCE = Mappers.getMapper(MdaFactoryItemDetailDEConvert.class);

    void resetMdaFactoryItemDetail(MdaFactoryItemDetailDTO mdaFactoryItemDetailDTO, @MappingTarget MdaFactoryItemDetail mdaFactoryItemDetail);
}