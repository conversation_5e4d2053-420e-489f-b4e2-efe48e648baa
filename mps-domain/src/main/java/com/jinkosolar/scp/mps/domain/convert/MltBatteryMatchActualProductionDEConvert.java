package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchActualProductionDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchActualProduction;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchActualProductionExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchActualProductionSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-历史实投 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchActualProductionDEConvert extends BaseDEConvert<MltBatteryMatchActualProductionDTO, MltBatteryMatchActualProduction> {

    MltBatteryMatchActualProductionDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchActualProductionDEConvert.class);

    List<MltBatteryMatchActualProductionExcelDTO> toExcelDTO(List<MltBatteryMatchActualProductionDTO> dtos);

    MltBatteryMatchActualProductionExcelDTO toExcelDTO(MltBatteryMatchActualProductionDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchActualProduction saveDTOtoEntity(MltBatteryMatchActualProductionSaveDTO saveDTO, @MappingTarget MltBatteryMatchActualProduction entity);
}
