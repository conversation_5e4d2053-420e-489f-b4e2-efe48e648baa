package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchAlignmentPlan;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchAlignmentPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchAlignmentPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-定线规划 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchAlignmentPlanDEConvert extends BaseDEConvert<MltBatteryMatchAlignmentPlanDTO, MltBatteryMatchAlignmentPlan> {

    MltBatteryMatchAlignmentPlanDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchAlignmentPlanDEConvert.class);

    List<MltBatteryMatchAlignmentPlanExcelDTO> toExcelDTO(List<MltBatteryMatchAlignmentPlanDTO> dtos);

    MltBatteryMatchAlignmentPlanExcelDTO toExcelDTO(MltBatteryMatchAlignmentPlanDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchAlignmentPlan saveDTOtoEntity(MltBatteryMatchAlignmentPlanSaveDTO saveDTO, @MappingTarget MltBatteryMatchAlignmentPlan entity);

    MltBatteryMatchAlignmentPlan deepCopy(MltBatteryMatchAlignmentPlan entity);

}
