package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchBatteryInStockDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchBatteryInStock;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchBatteryInStockExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchBatteryInStockSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-ERP采购入库 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchBatteryInStockDEConvert extends BaseDEConvert<MltBatteryMatchBatteryInStockDTO, MltBatteryMatchBatteryInStock> {

    MltBatteryMatchBatteryInStockDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchBatteryInStockDEConvert.class);

    List<MltBatteryMatchBatteryInStockExcelDTO> toExcelDTO(List<MltBatteryMatchBatteryInStockDTO> dtos);

    MltBatteryMatchBatteryInStockExcelDTO toExcelDTO(MltBatteryMatchBatteryInStockDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchBatteryInStock saveDTOtoEntity(MltBatteryMatchBatteryInStockSaveDTO saveDTO, @MappingTarget MltBatteryMatchBatteryInStock entity);
}
