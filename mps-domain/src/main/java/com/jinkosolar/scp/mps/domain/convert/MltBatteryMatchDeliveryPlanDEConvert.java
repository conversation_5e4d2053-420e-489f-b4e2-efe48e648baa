package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchDeliveryPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchDeliveryPlan;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchDeliveryPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchDeliveryPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-月到货计划 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchDeliveryPlanDEConvert extends BaseDEConvert<MltBatteryMatchDeliveryPlanDTO, MltBatteryMatchDeliveryPlan> {

    MltBatteryMatchDeliveryPlanDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchDeliveryPlanDEConvert.class);

    List<MltBatteryMatchDeliveryPlanExcelDTO> toExcelDTO(List<MltBatteryMatchDeliveryPlanDTO> dtos);

    MltBatteryMatchDeliveryPlanExcelDTO toExcelDTO(MltBatteryMatchDeliveryPlanDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchDeliveryPlan saveDTOtoEntity(MltBatteryMatchDeliveryPlanSaveDTO saveDTO, @MappingTarget MltBatteryMatchDeliveryPlan entity);
}
