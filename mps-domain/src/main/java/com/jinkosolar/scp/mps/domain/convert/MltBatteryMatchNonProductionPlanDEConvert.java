package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchNonProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchNonProductionPlan;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchNonProductionPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchNonProductionPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-电池主计划排产 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchNonProductionPlanDEConvert extends BaseDEConvert<MltBatteryMatchNonProductionPlanDTO, MltBatteryMatchNonProductionPlan> {

    MltBatteryMatchNonProductionPlanDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchNonProductionPlanDEConvert.class);

    List<MltBatteryMatchNonProductionPlanExcelDTO> toExcelDTO(List<MltBatteryMatchNonProductionPlanDTO> dtos);

    MltBatteryMatchNonProductionPlanExcelDTO toExcelDTO(MltBatteryMatchNonProductionPlanDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchNonProductionPlan saveDTOtoEntity(MltBatteryMatchNonProductionPlanSaveDTO saveDTO, @MappingTarget MltBatteryMatchNonProductionPlan entity);
}
