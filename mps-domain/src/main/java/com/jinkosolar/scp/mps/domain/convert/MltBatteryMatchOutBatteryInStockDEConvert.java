package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchOutBatteryInStockDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchOutBatteryInStock;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchOutBatteryInStockExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchOutBatteryInStockSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-外购电池入库 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:45
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchOutBatteryInStockDEConvert extends BaseDEConvert<MltBatteryMatchOutBatteryInStockDTO, MltBatteryMatchOutBatteryInStock> {

    MltBatteryMatchOutBatteryInStockDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchOutBatteryInStockDEConvert.class);

    List<MltBatteryMatchOutBatteryInStockExcelDTO> toExcelDTO(List<MltBatteryMatchOutBatteryInStockDTO> dtos);

    MltBatteryMatchOutBatteryInStockExcelDTO toExcelDTO(MltBatteryMatchOutBatteryInStockDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchOutBatteryInStock saveDTOtoEntity(MltBatteryMatchOutBatteryInStockSaveDTO saveDTO, @MappingTarget MltBatteryMatchOutBatteryInStock entity);
}
