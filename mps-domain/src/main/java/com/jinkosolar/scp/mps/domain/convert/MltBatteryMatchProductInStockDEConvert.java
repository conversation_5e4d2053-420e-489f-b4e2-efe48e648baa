package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchProductInStockDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchProductInStock;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchProductInStockExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchProductInStockSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-ERP电池生产入库 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:45
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchProductInStockDEConvert extends BaseDEConvert<MltBatteryMatchProductInStockDTO, MltBatteryMatchProductInStock> {

    MltBatteryMatchProductInStockDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchProductInStockDEConvert.class);

    List<MltBatteryMatchProductInStockExcelDTO> toExcelDTO(List<MltBatteryMatchProductInStockDTO> dtos);

    MltBatteryMatchProductInStockExcelDTO toExcelDTO(MltBatteryMatchProductInStockDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchProductInStock saveDTOtoEntity(MltBatteryMatchProductInStockSaveDTO saveDTO, @MappingTarget MltBatteryMatchProductInStock entity);
}
