package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchProductionPlan;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchProductionPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchProductionPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-组件排产结果 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:45
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchProductionPlanDEConvert extends BaseDEConvert<MltBatteryMatchProductionPlanDTO, MltBatteryMatchProductionPlan> {

    MltBatteryMatchProductionPlanDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchProductionPlanDEConvert.class);

    List<MltBatteryMatchProductionPlanExcelDTO> toExcelDTO(List<MltBatteryMatchProductionPlanDTO> dtos);

    MltBatteryMatchProductionPlanExcelDTO toExcelDTO(MltBatteryMatchProductionPlanDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchProductionPlan saveDTOtoEntity(MltBatteryMatchProductionPlanSaveDTO saveDTO, @MappingTarget MltBatteryMatchProductionPlan entity);
}
