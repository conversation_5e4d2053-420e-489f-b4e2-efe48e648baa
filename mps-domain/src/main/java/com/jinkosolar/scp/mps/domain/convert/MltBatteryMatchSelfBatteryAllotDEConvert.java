package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltBatteryMatchSelfBatteryAllotDTO;
import com.jinkosolar.scp.mps.domain.entity.MltBatteryMatchSelfBatteryAllot;
import com.jinkosolar.scp.mps.domain.excel.MltBatteryMatchSelfBatteryAllotExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltBatteryMatchSelfBatteryAllotSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期电池匹配-自产电池调拨 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:45
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltBatteryMatchSelfBatteryAllotDEConvert extends BaseDEConvert<MltBatteryMatchSelfBatteryAllotDTO, MltBatteryMatchSelfBatteryAllot> {

    MltBatteryMatchSelfBatteryAllotDEConvert INSTANCE = Mappers.getMapper(MltBatteryMatchSelfBatteryAllotDEConvert.class);

    List<MltBatteryMatchSelfBatteryAllotExcelDTO> toExcelDTO(List<MltBatteryMatchSelfBatteryAllotDTO> dtos);

    MltBatteryMatchSelfBatteryAllotExcelDTO toExcelDTO(MltBatteryMatchSelfBatteryAllotDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltBatteryMatchSelfBatteryAllot saveDTOtoEntity(MltBatteryMatchSelfBatteryAllotSaveDTO saveDTO, @MappingTarget MltBatteryMatchSelfBatteryAllot entity);
}
