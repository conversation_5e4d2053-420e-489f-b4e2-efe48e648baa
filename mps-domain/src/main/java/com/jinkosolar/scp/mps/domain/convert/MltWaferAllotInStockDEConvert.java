package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltWaferAllotInStockDTO;
import com.jinkosolar.scp.mps.domain.entity.MltWaferAllotInStock;
import com.jinkosolar.scp.mps.domain.excel.MltWaferAllotInStockExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltWaferAllotInStockSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期硅片匹配-硅片调拨入库11 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-10 10:07:14
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltWaferAllotInStockDEConvert extends BaseDEConvert<MltWaferAllotInStockDTO, MltWaferAllotInStock> {

    MltWaferAllotInStockDEConvert INSTANCE = Mappers.getMapper(MltWaferAllotInStockDEConvert.class);

    List<MltWaferAllotInStockExcelDTO> toExcelDTO(List<MltWaferAllotInStockDTO> dtos);

    MltWaferAllotInStockExcelDTO toExcelDTO(MltWaferAllotInStockDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltWaferAllotInStock saveDTOtoEntity(MltWaferAllotInStockSaveDTO saveDTO, @MappingTarget MltWaferAllotInStock entity);
}
