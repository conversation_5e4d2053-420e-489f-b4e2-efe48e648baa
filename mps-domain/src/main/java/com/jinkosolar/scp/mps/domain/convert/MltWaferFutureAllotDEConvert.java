package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltWaferFutureAllotDTO;
import com.jinkosolar.scp.mps.domain.entity.MltWaferFutureAllot;
import com.jinkosolar.scp.mps.domain.excel.MltWaferFutureAllotExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltWaferFutureAllotSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期硅片匹配-硅片未来调拨计划-GNHW-11.1 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-10 10:07:14
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltWaferFutureAllotDEConvert extends BaseDEConvert<MltWaferFutureAllotDTO, MltWaferFutureAllot> {

    MltWaferFutureAllotDEConvert INSTANCE = Mappers.getMapper(MltWaferFutureAllotDEConvert.class);

    List<MltWaferFutureAllotExcelDTO> toExcelDTO(List<MltWaferFutureAllotDTO> dtos);

    MltWaferFutureAllotExcelDTO toExcelDTO(MltWaferFutureAllotDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltWaferFutureAllot saveDTOtoEntity(MltWaferFutureAllotSaveDTO saveDTO, @MappingTarget MltWaferFutureAllot entity);
}
