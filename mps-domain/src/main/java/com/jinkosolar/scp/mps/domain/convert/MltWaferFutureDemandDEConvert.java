package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltWaferFutureDemandDTO;
import com.jinkosolar.scp.mps.domain.entity.MltWaferFutureDemand;
import com.jinkosolar.scp.mps.domain.excel.MltWaferFutureDemandExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltWaferFutureDemandSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期硅片匹配-电池未来硅片需求-12.1 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-10 10:07:14
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltWaferFutureDemandDEConvert extends BaseDEConvert<MltWaferFutureDemandDTO, MltWaferFutureDemand> {

    MltWaferFutureDemandDEConvert INSTANCE = Mappers.getMapper(MltWaferFutureDemandDEConvert.class);

    List<MltWaferFutureDemandExcelDTO> toExcelDTO(List<MltWaferFutureDemandDTO> dtos);

    MltWaferFutureDemandExcelDTO toExcelDTO(MltWaferFutureDemandDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltWaferFutureDemand saveDTOtoEntity(MltWaferFutureDemandSaveDTO saveDTO, @MappingTarget MltWaferFutureDemand entity);
}
