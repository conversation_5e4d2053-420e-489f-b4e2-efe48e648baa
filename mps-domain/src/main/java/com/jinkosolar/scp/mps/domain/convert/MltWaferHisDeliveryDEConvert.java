package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltWaferHisDeliveryDTO;
import com.jinkosolar.scp.mps.domain.entity.MltWaferHisDelivery;
import com.jinkosolar.scp.mps.domain.excel.MltWaferHisDeliveryExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltWaferHisDeliverySaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期硅片匹配-硅片历史发货-12 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 14:43:08
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltWaferHisDeliveryDEConvert extends BaseDEConvert<MltWaferHisDeliveryDTO, MltWaferHisDelivery> {

    MltWaferHisDeliveryDEConvert INSTANCE = Mappers.getMapper(MltWaferHisDeliveryDEConvert.class);

    List<MltWaferHisDeliveryExcelDTO> toExcelDTO(List<MltWaferHisDeliveryDTO> dtos);

    MltWaferHisDeliveryExcelDTO toExcelDTO(MltWaferHisDeliveryDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltWaferHisDelivery saveDTOtoEntity(MltWaferHisDeliverySaveDTO saveDTO, @MappingTarget MltWaferHisDelivery entity);
}
