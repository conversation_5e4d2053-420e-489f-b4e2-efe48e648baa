package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltWaferMatchNonProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.MltWaferMatchNonProductionPlan;
import com.jinkosolar.scp.mps.domain.excel.MltWaferMatchNonProductionPlanExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltWaferMatchNonProductionPlanSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期硅片匹配-硅片未来产出 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-27 15:50:45
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltWaferMatchNonProductionPlanDEConvert extends BaseDEConvert<MltWaferMatchNonProductionPlanDTO, MltWaferMatchNonProductionPlan> {

    MltWaferMatchNonProductionPlanDEConvert INSTANCE = Mappers.getMapper(MltWaferMatchNonProductionPlanDEConvert.class);

    List<MltWaferMatchNonProductionPlanExcelDTO> toExcelDTO(List<MltWaferMatchNonProductionPlanDTO> dtos);

    MltWaferMatchNonProductionPlanExcelDTO toExcelDTO(MltWaferMatchNonProductionPlanDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltWaferMatchNonProductionPlan saveDTOtoEntity(MltWaferMatchNonProductionPlanSaveDTO saveDTO, @MappingTarget MltWaferMatchNonProductionPlan entity);
}
