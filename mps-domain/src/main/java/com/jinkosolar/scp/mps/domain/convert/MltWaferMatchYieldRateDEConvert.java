package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.MltWaferMatchYieldRateDTO;
import com.jinkosolar.scp.mps.domain.entity.MltWaferMatchYieldRate;
import com.jinkosolar.scp.mps.domain.excel.MltWaferMatchYieldRateExcelDTO;
import com.jinkosolar.scp.mps.domain.save.MltWaferMatchYieldRateSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 中长期硅片匹配-历史实际投产 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-27 15:50:45
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MltWaferMatchYieldRateDEConvert extends BaseDEConvert<MltWaferMatchYieldRateDTO, MltWaferMatchYieldRate> {

    MltWaferMatchYieldRateDEConvert INSTANCE = Mappers.getMapper(MltWaferMatchYieldRateDEConvert.class);

    List<MltWaferMatchYieldRateExcelDTO> toExcelDTO(List<MltWaferMatchYieldRateDTO> dtos);

    MltWaferMatchYieldRateExcelDTO toExcelDTO(MltWaferMatchYieldRateDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    MltWaferMatchYieldRate saveDTOtoEntity(MltWaferMatchYieldRateSaveDTO saveDTO, @MappingTarget MltWaferMatchYieldRate entity);
}
