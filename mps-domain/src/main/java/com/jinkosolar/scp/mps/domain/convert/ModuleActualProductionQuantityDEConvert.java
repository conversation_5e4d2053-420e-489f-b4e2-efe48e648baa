package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleActualProductionQuantityDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.ModuleActualProductionQuantitySyncDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleActualProductionQuantity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleActualProductionQuantityDEConvert extends BaseDEConvert<ModuleActualProductionQuantityDTO, ModuleActualProductionQuantity> {
    ModuleActualProductionQuantityDEConvert INSTANCE = Mappers.getMapper(ModuleActualProductionQuantityDEConvert.class);

    void resetModuleActualProductionQuantity(ModuleActualProductionQuantityDTO moduleActualProductionQuantityDTO, @MappingTarget ModuleActualProductionQuantity moduleActualProductionQuantity);

    void syncDtoToEntity(ModuleActualProductionQuantitySyncDTO moduleActualProductionQuantitySyncDTO, @MappingTarget ModuleActualProductionQuantity moduleActualProductionQuantity);

}