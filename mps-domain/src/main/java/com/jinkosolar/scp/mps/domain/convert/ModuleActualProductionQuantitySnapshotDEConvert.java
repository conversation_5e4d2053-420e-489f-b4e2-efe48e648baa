package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleActualProductionQuantitySnapshotDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleActualProductionQuantitySnapshot;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleActualProductionQuantitySnapshotDEConvert extends BaseDEConvert<ModuleActualProductionQuantitySnapshotDTO, ModuleActualProductionQuantitySnapshot> {
    ModuleActualProductionQuantitySnapshotDEConvert INSTANCE = Mappers.getMapper(ModuleActualProductionQuantitySnapshotDEConvert.class);

    void resetModuleActualProductionQuantitySnapshot(ModuleActualProductionQuantitySnapshotDTO moduleActualProductionQuantitySnapshotDTO, @MappingTarget ModuleActualProductionQuantitySnapshot moduleActualProductionQuantitySnapshot);
}