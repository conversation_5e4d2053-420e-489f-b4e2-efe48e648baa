package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleActualYieldRateDTO;
import com.jinkosolar.scp.mps.domain.dto.sync.ModuleActualYieldRateSyncDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleActualYieldRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleActualYieldRateDEConvert extends BaseDEConvert<ModuleActualYieldRateDTO, ModuleActualYieldRate> {
    ModuleActualYieldRateDEConvert INSTANCE = Mappers.getMapper(ModuleActualYieldRateDEConvert.class);

    void resetModuleActualYieldRate(ModuleActualYieldRateDTO moduleActualYieldRateDTO, @MappingTarget ModuleActualYieldRate moduleActualYieldRate);

    void resetModuleActualYieldRate(ModuleActualYieldRateSyncDTO moduleActualYieldRateDTO, @MappingTarget ModuleActualYieldRate moduleActualYieldRate);

    void syncDtoToEntity(ModuleActualYieldRateSyncDTO moduleActualYieldRateDTO, @MappingTarget ModuleActualYieldRate moduleActualYieldRate);
}