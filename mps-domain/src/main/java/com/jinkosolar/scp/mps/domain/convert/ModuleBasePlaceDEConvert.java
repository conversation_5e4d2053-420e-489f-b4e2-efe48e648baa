package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleBasePlaceDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleBasePlace;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 基础产地 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-11 09:12:21
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleBasePlaceDEConvert extends BaseDEConvert<ModuleBasePlaceDTO, ModuleBasePlace> {

    ModuleBasePlaceDEConvert INSTANCE = Mappers.getMapper(ModuleBasePlaceDEConvert.class);

}
