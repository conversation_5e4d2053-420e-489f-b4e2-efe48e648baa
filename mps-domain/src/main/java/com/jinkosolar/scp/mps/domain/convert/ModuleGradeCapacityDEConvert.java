package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleGradeCapacityDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleGradeCapacity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleGradeCapacityDEConvert extends BaseDEConvert<ModuleGradeCapacityDTO, ModuleGradeCapacity> {
    ModuleGradeCapacityDEConvert INSTANCE = Mappers.getMapper(ModuleGradeCapacityDEConvert.class);

    void resetModuleGradeCapacity(ModuleGradeCapacityDTO moduleGradeCapacityDTO, @MappingTarget ModuleGradeCapacity moduleGradeCapacity);
}