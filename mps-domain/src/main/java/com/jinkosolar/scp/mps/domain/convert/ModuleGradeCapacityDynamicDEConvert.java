package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleGradeCapacityDynamicDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleGradeCapacityDynamic;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleGradeCapacityDynamicDEConvert extends BaseDEConvert<ModuleGradeCapacityDynamicDTO, ModuleGradeCapacityDynamic> {
    ModuleGradeCapacityDynamicDEConvert INSTANCE = Mappers.getMapper(ModuleGradeCapacityDynamicDEConvert.class);

    void resetModuleGradeCapacityDynamic(ModuleGradeCapacityDynamicDTO moduleGradeCapacityDynamicDTO, @MappingTarget ModuleGradeCapacityDynamic moduleGradeCapacityDynamic);
}