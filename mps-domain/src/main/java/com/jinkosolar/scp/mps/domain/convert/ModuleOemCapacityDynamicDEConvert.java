package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleOemCapacityDynamicDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleOemCapacityDynamic;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleOemCapacityDynamicDEConvert extends BaseDEConvert<ModuleOemCapacityDynamicDTO, ModuleOemCapacityDynamic> {
    ModuleOemCapacityDynamicDEConvert INSTANCE = Mappers.getMapper(ModuleOemCapacityDynamicDEConvert.class);

    void resetModuleOemCapacityDynamic(ModuleOemCapacityDynamicDTO moduleOemCapacityDynamicDTO, @MappingTarget ModuleOemCapacityDynamic moduleOemCapacityDynamic);
}