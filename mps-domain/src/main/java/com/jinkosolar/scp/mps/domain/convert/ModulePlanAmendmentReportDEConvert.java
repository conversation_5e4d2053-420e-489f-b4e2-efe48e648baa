package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModulePlanAmendmentReportDTO;
import com.jinkosolar.scp.mps.domain.entity.ModulePlanAmendmentReport;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModulePlanAmendmentReportDEConvert extends BaseDEConvert<ModulePlanAmendmentReportDTO, ModulePlanAmendmentReport> {
    ModulePlanAmendmentReportDEConvert INSTANCE = Mappers.getMapper(ModulePlanAmendmentReportDEConvert.class);

    void resetModulePlanAmendmentReport(ModulePlanAmendmentReportDTO modulePlanAmendmentReportDTO, @MappingTarget ModulePlanAmendmentReport modulePlanAmendmentReport);
}