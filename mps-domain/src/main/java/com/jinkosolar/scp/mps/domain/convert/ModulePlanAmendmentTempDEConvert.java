package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModulePlanAmendmentTempDTO;
import com.jinkosolar.scp.mps.domain.entity.ModulePlanAmendmentTemp;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModulePlanAmendmentTempDEConvert extends BaseDEConvert<ModulePlanAmendmentTempDTO, ModulePlanAmendmentTemp> {
    ModulePlanAmendmentTempDEConvert INSTANCE = Mappers.getMapper(ModulePlanAmendmentTempDEConvert.class);

    void resetModulePlanAmendmentTemp(ModulePlanAmendmentTempDTO modulePlanAmendmentTempDTO, @MappingTarget ModulePlanAmendmentTemp modulePlanAmendmentTemp);
}