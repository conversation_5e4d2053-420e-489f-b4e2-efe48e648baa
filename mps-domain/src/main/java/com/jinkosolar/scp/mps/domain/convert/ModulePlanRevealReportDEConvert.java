package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModulePlanRevealReportDTO;
import com.jinkosolar.scp.mps.domain.entity.ModulePlanRevealReport;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModulePlanRevealReportDEConvert extends BaseDEConvert<ModulePlanRevealReportDTO, ModulePlanRevealReport> {
    ModulePlanRevealReportDEConvert INSTANCE = Mappers.getMapper(ModulePlanRevealReportDEConvert.class);

    void resetModulePlanRevealReport(ModulePlanRevealReportDTO modulePlanRevealReportDTO, @MappingTarget ModulePlanRevealReport modulePlanRevealReport);
}