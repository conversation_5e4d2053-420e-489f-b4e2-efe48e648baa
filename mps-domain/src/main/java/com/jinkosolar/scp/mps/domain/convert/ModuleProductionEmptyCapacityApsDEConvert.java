package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityApsDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionEmptyCapacityAps;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionEmptyCapacityApsDEConvert extends BaseDEConvert<ModuleProductionEmptyCapacityApsDTO, ModuleProductionEmptyCapacityAps> {
    ModuleProductionEmptyCapacityApsDEConvert INSTANCE = Mappers.getMapper(ModuleProductionEmptyCapacityApsDEConvert.class);

    void resetModuleProductionEmptyCapacityAps(ModuleProductionEmptyCapacityApsDTO moduleProductionEmptyCapacityApsDTO, @MappingTarget ModuleProductionEmptyCapacityAps moduleProductionEmptyCapacityAps);
}