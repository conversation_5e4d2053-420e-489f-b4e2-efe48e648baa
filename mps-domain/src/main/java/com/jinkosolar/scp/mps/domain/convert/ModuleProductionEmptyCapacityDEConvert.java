package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionEmptyCapacity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionEmptyCapacityDEConvert extends BaseDEConvert<ModuleProductionEmptyCapacityDTO, ModuleProductionEmptyCapacity> {
    ModuleProductionEmptyCapacityDEConvert INSTANCE = Mappers.getMapper(ModuleProductionEmptyCapacityDEConvert.class);

    void resetModuleProductionEmptyCapacity(ModuleProductionEmptyCapacityDTO moduleProductionEmptyCapacityDTO, @MappingTarget ModuleProductionEmptyCapacity moduleProductionEmptyCapacity);
}