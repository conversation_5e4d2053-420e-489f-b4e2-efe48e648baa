package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionEmptyCapacityTempDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionEmptyCapacityTemp;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionEmptyCapacityTempDEConvert extends BaseDEConvert<ModuleProductionEmptyCapacityTempDTO, ModuleProductionEmptyCapacityTemp> {
    ModuleProductionEmptyCapacityTempDEConvert INSTANCE = Mappers.getMapper(ModuleProductionEmptyCapacityTempDEConvert.class);

    void resetModuleProductionEmptyCapacity(ModuleProductionEmptyCapacityTempDTO moduleProductionEmptyCapacityDTO, @MappingTarget ModuleProductionEmptyCapacityTemp moduleProductionEmptyCapacity);
}