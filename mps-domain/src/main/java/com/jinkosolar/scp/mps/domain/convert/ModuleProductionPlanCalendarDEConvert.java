package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanCalendarDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlanCalendar;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionPlanCalendarDEConvert extends BaseDEConvert<ModuleProductionPlanCalendarDTO, ModuleProductionPlanCalendar> {
    ModuleProductionPlanCalendarDEConvert INSTANCE = Mappers.getMapper(ModuleProductionPlanCalendarDEConvert.class);

    void resetModuleProductionPlanCalendar(ModuleProductionPlanCalendarDTO moduleProductionPlanCalendarDTO, @MappingTarget ModuleProductionPlanCalendar moduleProductionPlanCalendar);
}