package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanCalendarTempDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlanCalendarTemp;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionPlanCalendarTempDEConvert extends BaseDEConvert<ModuleProductionPlanCalendarTempDTO, ModuleProductionPlanCalendarTemp> {
    ModuleProductionPlanCalendarTempDEConvert INSTANCE = Mappers.getMapper(ModuleProductionPlanCalendarTempDEConvert.class);

    void resetModuleProductionPlanCalendarTemp(ModuleProductionPlanCalendarTempDTO moduleProductionPlanCalendarTempDTO, @MappingTarget ModuleProductionPlanCalendarTemp moduleProductionPlanCalendarTemp);
}