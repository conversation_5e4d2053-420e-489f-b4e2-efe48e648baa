package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionPlanDEConvert extends BaseDEConvert<ModuleProductionPlanDTO, ModuleProductionPlan> {
    ModuleProductionPlanDEConvert INSTANCE = Mappers.getMapper(ModuleProductionPlanDEConvert.class);

    void resetModuleProductionPlan(ModuleProductionPlanDTO ModuleProductionPlanDTO, @MappingTarget ModuleProductionPlan ModuleProductionPlan);

}
