package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanReportDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlan;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlanReport;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionPlanReportDEConvert extends BaseDEConvert<ModuleProductionPlanReportDTO, ModuleProductionPlanReport> {
    ModuleProductionPlanReportDEConvert INSTANCE = Mappers.getMapper(ModuleProductionPlanReportDEConvert.class);

    void resetModuleProductionPlanReport(ModuleProductionPlanReportDTO moduleProductionPlanReportDTO, @MappingTarget ModuleProductionPlanReport moduleProductionPlanReport);

    List<ModuleProductionPlanDTO> toModuleProductionPlanList(List<ModuleProductionPlanReport> dtoList);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    ModuleProductionPlanReportDTO copy(ModuleProductionPlan productionPlan);
}