package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionPlanTempDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionPlanTemp;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionPlanTempDEConvert extends BaseDEConvert<ModuleProductionPlanTempDTO, ModuleProductionPlanTemp> {
    ModuleProductionPlanTempDEConvert INSTANCE = Mappers.getMapper(ModuleProductionPlanTempDEConvert.class);

    void resetModuleProductionPlanTemp(ModuleProductionPlanTempDTO moduleProductionPlanTempDTO, @MappingTarget ModuleProductionPlanTemp moduleProductionPlanTemp);
}