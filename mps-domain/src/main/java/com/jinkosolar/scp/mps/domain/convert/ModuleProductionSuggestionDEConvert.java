package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionSuggestionDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionSuggestion;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionSuggestionDEConvert extends BaseDEConvert<ModuleProductionSuggestionDTO, ModuleProductionSuggestion> {
    ModuleProductionSuggestionDEConvert INSTANCE = Mappers.getMapper(ModuleProductionSuggestionDEConvert.class);

    void resetModuleProductionSuggestion(ModuleProductionSuggestionDTO moduleProductionSuggestionDTO, @MappingTarget ModuleProductionSuggestion moduleProductionSuggestion);
}