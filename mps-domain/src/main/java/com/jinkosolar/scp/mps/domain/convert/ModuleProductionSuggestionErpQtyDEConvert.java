package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleProductionSuggestionErpQtyDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleProductionSuggestionErpQty;
import com.jinkosolar.scp.mps.domain.excel.ModuleProductionSuggestionErpQtyExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ModuleProductionSuggestionErpQtySaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 生产建议Erp下发数量 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-19 15:36:03
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleProductionSuggestionErpQtyDEConvert extends BaseDEConvert<ModuleProductionSuggestionErpQtyDTO, ModuleProductionSuggestionErpQty> {

    ModuleProductionSuggestionErpQtyDEConvert INSTANCE = Mappers.getMapper(ModuleProductionSuggestionErpQtyDEConvert.class);

    List<ModuleProductionSuggestionErpQtyExcelDTO> toExcelDTO(List<ModuleProductionSuggestionErpQtyDTO> dtos);

    ModuleProductionSuggestionErpQtyExcelDTO toExcelDTO(ModuleProductionSuggestionErpQtyDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    ModuleProductionSuggestionErpQty saveDTOtoEntity(ModuleProductionSuggestionErpQtySaveDTO saveDTO, @MappingTarget ModuleProductionSuggestionErpQty entity);
}
