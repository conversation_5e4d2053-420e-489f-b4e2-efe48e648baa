package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ModuleTypeDTO;
import com.jinkosolar.scp.mps.domain.entity.ModuleType;
import com.jinkosolar.scp.mps.domain.save.ModuleTypeSaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ModuleTypeDEConvert extends BaseDEConvert<ModuleTypeDTO, ModuleType> {
    ModuleTypeDEConvert INSTANCE = Mappers.getMapper(ModuleTypeDEConvert.class);

    void resetModuleType(ModuleTypeDTO moduleTypeDTO, @MappingTarget ModuleType moduleType);

    void resetModuleType(ModuleTypeSaveDTO moduleTypeDTO, @MappingTarget ModuleType moduleType);

}
