package com.jinkosolar.scp.mps.domain.convert;
import com.jinkosolar.scp.mps.domain.dto.MpsWorkCenterDTO;
import com.jinkosolar.scp.mps.domain.entity.MpsWorkCenter;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import com.ibm.scp.common.api.convert.BaseDEConvert;
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MpsWorkCenterDEConvert extends BaseDEConvert<MpsWorkCenterDTO, MpsWorkCenter> {
    MpsWorkCenterDEConvert INSTANCE = Mappers.getMapper(MpsWorkCenterDEConvert.class);
}