package com.jinkosolar.scp.mps.domain.convert;
import com.jinkosolar.scp.mps.domain.dto.MpsWorkCenterDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.MpsWorkCenterDetail;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import com.ibm.scp.common.api.convert.BaseDEConvert;
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface MpsWorkCenterDetailDEConvert extends BaseDEConvert<MpsWorkCenterDetailDTO, MpsWorkCenterDetail> {
    MpsWorkCenterDetailDEConvert INSTANCE = Mappers.getMapper(MpsWorkCenterDetailDEConvert.class);
}