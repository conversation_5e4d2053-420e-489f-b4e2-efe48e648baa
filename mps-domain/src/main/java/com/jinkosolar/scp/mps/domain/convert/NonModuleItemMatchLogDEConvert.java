package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.entity.NonModuleItemMatchLog;
import com.jinkosolar.scp.mps.domain.dto.NonModuleItemMatchLogDTO;
import com.jinkosolar.scp.mps.domain.excel.NonModuleItemMatchLogExcelDTO;
import com.jinkosolar.scp.mps.domain.save.NonModuleItemMatchLogSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]料号匹配历史记录表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-07-30
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface NonModuleItemMatchLogDEConvert extends BaseDEConvert<NonModuleItemMatchLogDTO, NonModuleItemMatchLog> {

    NonModuleItemMatchLogDEConvert INSTANCE = Mappers.getMapper(NonModuleItemMatchLogDEConvert.class);

    List<NonModuleItemMatchLogExcelDTO> toExcelDTO(List<NonModuleItemMatchLogDTO> dtos);

    NonModuleItemMatchLogExcelDTO toExcelDTO(NonModuleItemMatchLogDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    NonModuleItemMatchLog saveDTOtoEntity(NonModuleItemMatchLogSaveDTO saveDTO, @MappingTarget NonModuleItemMatchLog entity);
}
