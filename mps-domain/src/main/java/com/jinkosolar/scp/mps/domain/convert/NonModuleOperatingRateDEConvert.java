package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.NonModuleOperatingRateDTO;
import com.jinkosolar.scp.mps.domain.entity.NonModuleOperatingRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface NonModuleOperatingRateDEConvert extends BaseDEConvert<NonModuleOperatingRateDTO, NonModuleOperatingRate> {
    NonModuleOperatingRateDEConvert INSTANCE = Mappers.getMapper(NonModuleOperatingRateDEConvert.class);

    void resetNonModuleOperatingRate(NonModuleOperatingRateDTO nonModuleOperatingRateDTO, @MappingTarget NonModuleOperatingRate nonModuleOperatingRate);
}