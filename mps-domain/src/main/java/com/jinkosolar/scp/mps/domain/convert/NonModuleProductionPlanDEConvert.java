package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.entity.NonModuleProductionPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface NonModuleProductionPlanDEConvert extends BaseDEConvert<NonModuleProductionPlanDTO, NonModuleProductionPlan> {
    NonModuleProductionPlanDEConvert INSTANCE = Mappers.getMapper(NonModuleProductionPlanDEConvert.class);

    void resetNonModuleProductionPlan(NonModuleProductionPlanDTO nonModuleProductionPlanDTO, @MappingTarget NonModuleProductionPlan nonModuleProductionPlan);

    NonModuleProductionPlanDTO toDto(NonModuleProductionPlanTempDTO nonModuleProductionPlanTempDTO);

    NonModuleProductionPlanDTO toDto(NonModuleProductionPlanDTO dto);

    SlicePlanShiftHeadDTO copySlicePlanShiftDTO(SlicePlanShiftDTO slicePlanShiftDTO);

    SlicePlanShiftHeadDTO copySlicePlanShiftHeadDTO(SlicePlanShiftHeadDTO headDTO);

    FinalProductionReportDTO copyFinalProductionReportDTO(FinalProductionReportDTO headDTO);
}
