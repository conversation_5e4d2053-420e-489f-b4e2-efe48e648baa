package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanTempDTO;
import com.jinkosolar.scp.mps.domain.entity.NonModuleProductionPlanTemp;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface NonModuleProductionPlanTempDEConvert extends BaseDEConvert<NonModuleProductionPlanTempDTO, NonModuleProductionPlanTemp> {
    NonModuleProductionPlanTempDEConvert INSTANCE = Mappers.getMapper(NonModuleProductionPlanTempDEConvert.class);

    void resetNonModuleProductionPlanTemp(NonModuleProductionPlanTempDTO nonModuleProductionPlanTempDTO, @MappingTarget NonModuleProductionPlanTemp nonModuleProductionPlanTemp);
}