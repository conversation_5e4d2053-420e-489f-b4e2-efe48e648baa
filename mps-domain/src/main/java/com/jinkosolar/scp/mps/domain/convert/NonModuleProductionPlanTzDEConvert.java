package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.*;
import com.jinkosolar.scp.mps.domain.entity.NonModuleProductionPlan;
import com.jinkosolar.scp.mps.domain.entity.NonModuleProductionPlanTz;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface NonModuleProductionPlanTzDEConvert extends BaseDEConvert<NonModuleProductionPlanTzDTO, NonModuleProductionPlanTz> {
    NonModuleProductionPlanTzDEConvert INSTANCE = Mappers.getMapper(NonModuleProductionPlanTzDEConvert.class);

    List<NonModuleProductionPlanTz> toEntitysFromPlanDTOs(List<NonModuleProductionPlanDTO> nonModuleProductionPlanDTOs);

}
