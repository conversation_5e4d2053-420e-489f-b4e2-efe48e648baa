package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.NumericalResultDTO;
import com.jinkosolar.scp.mps.domain.entity.NumericalResult;
import com.jinkosolar.scp.mps.domain.save.NumericalResultSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 数值结果表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 08:02:10
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface NumericalResultDEConvert extends BaseDEConvert<NumericalResultDTO, NumericalResult> {

    NumericalResultDEConvert INSTANCE = Mappers.getMapper(NumericalResultDEConvert.class);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    NumericalResult saveDTOtoEntity(NumericalResultSaveDTO saveDTO, @MappingTarget NumericalResult entity);


    List<NumericalResult> toEntityList(List<NumericalResultSaveDTO> saveList);
}
