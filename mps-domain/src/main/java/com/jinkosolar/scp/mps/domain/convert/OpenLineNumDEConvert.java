package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.OpenLineNumDTO;
import com.jinkosolar.scp.mps.domain.entity.OpenLineNum;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OpenLineNumDEConvert extends BaseDEConvert<OpenLineNumDTO, OpenLineNum> {
    OpenLineNumDEConvert INSTANCE = Mappers.getMapper(OpenLineNumDEConvert.class);

    void resetOpenLineNum(OpenLineNumDTO openLineNumDTO, @MappingTarget OpenLineNum openLineNum);
}