package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.OpenLineNumDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.OpenLineNumDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OpenLineNumDetailDEConvert extends BaseDEConvert<OpenLineNumDetailDTO, OpenLineNumDetail> {
    OpenLineNumDetailDEConvert INSTANCE = Mappers.getMapper(OpenLineNumDetailDEConvert.class);

    void resetOpenLineNumDetail(OpenLineNumDetailDTO openLineNumDetailDTO, @MappingTarget OpenLineNumDetail openLineNumDetail);
}