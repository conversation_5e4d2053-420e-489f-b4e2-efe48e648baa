package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.OpeningInventoryDTO;
import com.jinkosolar.scp.mps.domain.entity.OpeningInventory;
import com.jinkosolar.scp.mps.domain.excel.OpeningInventoryExcelDTO;
import com.jinkosolar.scp.mps.domain.save.OpeningInventorySaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 期初库存表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OpeningInventoryDEConvert extends BaseDEConvert<OpeningInventoryDTO, OpeningInventory> {

    OpeningInventoryDEConvert INSTANCE = Mappers.getMapper(OpeningInventoryDEConvert.class);

    List<OpeningInventoryExcelDTO> toExcelDTO(List<OpeningInventoryDTO> dtos);

    OpeningInventoryExcelDTO toExcelDTO(OpeningInventoryDTO dto);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true)
    })
    OpeningInventory saveDTOtoEntity(OpeningInventorySaveDTO saveDTO, @MappingTarget OpeningInventory entity);
}
