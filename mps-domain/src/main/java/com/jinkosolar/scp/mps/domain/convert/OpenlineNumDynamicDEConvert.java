package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.OpenlineNumDynamicDTO;
import com.jinkosolar.scp.mps.domain.entity.OpenlineNumDynamic;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OpenlineNumDynamicDEConvert extends BaseDEConvert<OpenlineNumDynamicDTO, OpenlineNumDynamic> {
    OpenlineNumDynamicDEConvert INSTANCE = Mappers.getMapper(OpenlineNumDynamicDEConvert.class);

    void resetOpenlineNumDynamic(OpenlineNumDynamicDTO openlineNumDynamicDTO, @MappingTarget OpenlineNumDynamic openlineNumDynamic);
}