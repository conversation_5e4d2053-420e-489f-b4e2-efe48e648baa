package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PerUnitClimbRuleDTO;
import com.jinkosolar.scp.mps.domain.entity.PerUnitClimbRule;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PerUnitClimbRuleDEConvert extends BaseDEConvert<PerUnitClimbRuleDTO, PerUnitClimbRule> {
    PerUnitClimbRuleDEConvert INSTANCE = Mappers.getMapper(PerUnitClimbRuleDEConvert.class);

    void resetPerUnitClimbRule(PerUnitClimbRuleDTO perUnitClimbRuleDTO, @MappingTarget PerUnitClimbRule perUnitClimbRule);
}