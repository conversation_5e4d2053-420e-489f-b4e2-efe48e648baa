package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PlanBomsDTO;
import com.jinkosolar.scp.mps.domain.entity.PlanBoms;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlanBomsDEConvert extends BaseDEConvert<PlanBomsDTO, PlanBoms> {
    PlanBomsDEConvert INSTANCE = Mappers.getMapper(PlanBomsDEConvert.class);

    void resetPlanBoms(PlanBomsDTO planBomsDTO, @MappingTarget PlanBoms planBoms);
}