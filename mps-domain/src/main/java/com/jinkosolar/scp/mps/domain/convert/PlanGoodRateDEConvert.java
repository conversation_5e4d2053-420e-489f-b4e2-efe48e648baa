package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PlanGoodRateDTO;
import com.jinkosolar.scp.mps.domain.entity.PlanGoodRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlanGoodRateDEConvert extends BaseDEConvert<PlanGoodRateDTO, PlanGoodRate> {
    PlanGoodRateDEConvert INSTANCE = Mappers.getMapper(PlanGoodRateDEConvert.class);

    void resetPlanGoodRate(PlanGoodRateDTO planGoodRateDTO, @MappingTarget PlanGoodRate planGoodRate);
}