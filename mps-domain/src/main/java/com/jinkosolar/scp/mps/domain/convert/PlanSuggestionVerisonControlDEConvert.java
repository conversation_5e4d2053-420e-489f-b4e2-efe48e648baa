package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PlanSuggestionVerisonControlDTO;
import com.jinkosolar.scp.mps.domain.entity.PlanSuggestionVerisonControl;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlanSuggestionVerisonControlDEConvert extends BaseDEConvert<PlanSuggestionVerisonControlDTO, PlanSuggestionVerisonControl> {
    PlanSuggestionVerisonControlDEConvert INSTANCE = Mappers.getMapper(PlanSuggestionVerisonControlDEConvert.class);

    void resetPlanSuggestionVerisonControl(PlanSuggestionVerisonControlDTO planSuggestionVerisonControlDTO, @MappingTarget PlanSuggestionVerisonControl planSuggestionVerisonControl);
}