package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PlanVerisonControllDTO;
import com.jinkosolar.scp.mps.domain.entity.PlanVerisonControll;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PlanVerisonControllDEConvert extends BaseDEConvert<PlanVerisonControllDTO, PlanVerisonControll> {
    PlanVerisonControllDEConvert INSTANCE = Mappers.getMapper(PlanVerisonControllDEConvert.class);

    void resetPlanVerisonControll(PlanVerisonControllDTO planVerisonControllDTO, @MappingTarget PlanVerisonControll planVerisonControll);
}