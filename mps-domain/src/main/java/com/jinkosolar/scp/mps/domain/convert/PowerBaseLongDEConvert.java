package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerBaseLongDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerBaseLong;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * @USER: MWZ
 * @DATE: 2022/6/16
 * 长期功率维护 转化
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerBaseLongDEConvert extends BaseDEConvert<PowerBaseLongDTO, PowerBaseLong> {
}
