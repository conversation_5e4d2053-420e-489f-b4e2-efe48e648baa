package com.jinkosolar.scp.mps.domain.convert;


import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerBaseShortDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerBaseShort;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * @USER: MWZ
 * @DATE: 2022/6/16
 * 预测基准档位功率 转化
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerBaseShortDEConvert extends BaseDEConvert<PowerBaseShortDTO, PowerBaseShort> {

}
