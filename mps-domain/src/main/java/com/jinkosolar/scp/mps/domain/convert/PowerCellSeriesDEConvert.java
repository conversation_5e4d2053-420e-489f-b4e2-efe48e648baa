package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerCellSeriesDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerCellSeries;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerCellSeriesDEConvert extends BaseDEConvert<PowerCellSeriesDTO, PowerCellSeries> {
    PowerCellSeriesDEConvert INSTANCE = Mappers.getMapper(PowerCellSeriesDEConvert.class);

    void resetPowerCellSeries(PowerCellSeriesDTO PowerCellSeriesDTO, @MappingTarget PowerCellSeries PowerCellSeries);
}
