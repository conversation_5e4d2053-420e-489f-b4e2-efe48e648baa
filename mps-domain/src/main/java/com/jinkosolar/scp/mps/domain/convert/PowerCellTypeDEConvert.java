package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerCellTypeDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerCellType;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 产品系列对应电池类型 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-28 15:55:47
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerCellTypeDEConvert extends BaseDEConvert<PowerCellTypeDTO, PowerCellType> {

    PowerCellTypeDEConvert INSTANCE = Mappers.getMapper(PowerCellTypeDEConvert.class);

}
