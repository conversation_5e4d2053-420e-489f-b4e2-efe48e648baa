package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerDeratingDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerDerating;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerDeratingDEConvert extends BaseDEConvert<PowerDeratingDTO, PowerDerating> {
    PowerDeratingDEConvert INSTANCE = Mappers.getMapper(PowerDeratingDEConvert.class);

    void resetPowerDerating(PowerDeratingDTO PowerDeratingDTO, @MappingTarget PowerDerating PowerDerating);
}
