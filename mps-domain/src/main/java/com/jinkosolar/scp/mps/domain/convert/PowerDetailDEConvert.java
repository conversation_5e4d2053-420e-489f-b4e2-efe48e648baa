package com.jinkosolar.scp.mps.domain.convert;


import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerDetail;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 版本对比标 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-29 09:40:51
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerDetailDEConvert extends BaseDEConvert<PowerDetailDTO, PowerDetail> {

}
