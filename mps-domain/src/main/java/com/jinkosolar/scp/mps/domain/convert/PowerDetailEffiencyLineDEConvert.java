package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerDetailEffiencyLineDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerDetailEffiencyLine;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 *
 *
 * <AUTHOR>
 * @date 2022-12-13
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerDetailEffiencyLineDEConvert extends BaseDEConvert<PowerDetailEffiencyLineDTO, PowerDetailEffiencyLine> {
}
