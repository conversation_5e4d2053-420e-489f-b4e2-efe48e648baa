package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerEfficiencyMainRelationDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerEfficiencyMainRelation;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 全年效率值
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerEfficiencyMainRelationDEConvert extends BaseDEConvert<PowerEfficiencyMainRelationDTO, PowerEfficiencyMainRelation> {
}
