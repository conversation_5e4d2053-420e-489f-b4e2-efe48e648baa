package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerFinanceDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerFinance;
import com.jinkosolar.scp.mps.domain.save.PowerFinanceSaveDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

/**
 * @USER: MWZ
 * @DATE: 2022/6/16
 * 材料组合
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerFinanceDEConvert extends BaseDEConvert<PowerFinanceDTO, PowerFinance> {
    List<PowerFinanceSaveDTO> toSaveDTO(List<PowerFinanceDTO> dtoList);
}
