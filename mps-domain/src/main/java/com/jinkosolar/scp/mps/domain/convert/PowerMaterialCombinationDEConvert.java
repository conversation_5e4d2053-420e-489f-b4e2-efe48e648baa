package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerMaterialCombinationDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerMaterialCombination;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerMaterialCombinationDEConvert extends BaseDEConvert<PowerMaterialCombinationDTO, PowerMaterialCombination> {
    PowerMaterialCombinationDEConvert INSTANCE = Mappers.getMapper(PowerMaterialCombinationDEConvert.class);

    void resetPowerMaterialCombination(PowerMaterialCombinationDTO PowerMaterialCombinationDTO, @MappingTarget PowerMaterialCombination PowerMaterialCombination);
}
