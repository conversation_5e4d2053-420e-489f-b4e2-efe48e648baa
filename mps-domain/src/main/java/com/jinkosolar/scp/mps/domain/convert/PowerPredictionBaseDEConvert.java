package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionBaseDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionBase;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionBaseDEConvert extends BaseDEConvert<PowerPredictionBaseDTO, PowerPredictionBase> {
    PowerPredictionBaseDEConvert INSTANCE = Mappers.getMapper(PowerPredictionBaseDEConvert.class);

    void resetPowerPredictionBase(PowerPredictionBaseDTO PowerPredictionBaseDTO, @MappingTarget PowerPredictionBase PowerPredictionBase);
}
