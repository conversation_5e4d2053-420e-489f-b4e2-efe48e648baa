package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionBaseDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionBaseDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionBaseDetailDEConvert extends BaseDEConvert<PowerPredictionBaseDetailDTO, PowerPredictionBaseDetail> {
    PowerPredictionBaseDetailDEConvert INSTANCE = Mappers.getMapper(PowerPredictionBaseDetailDEConvert.class);

    void resetPowerPredictionBaseDetail(PowerPredictionBaseDetailDTO PowerPredictionBaseDetailDTO, @MappingTarget PowerPredictionBaseDetail PowerPredictionBaseDetail);
}
