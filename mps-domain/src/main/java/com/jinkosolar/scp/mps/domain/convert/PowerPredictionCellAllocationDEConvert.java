package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionCellAllocationDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionCellAllocation;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionCellAllocationDEConvert extends BaseDEConvert<PowerPredictionCellAllocationDTO, PowerPredictionCellAllocation> {
    PowerPredictionCellAllocationDEConvert INSTANCE = Mappers.getMapper(PowerPredictionCellAllocationDEConvert.class);

    void resetPowerPredictionCellAllocation(PowerPredictionCellAllocationDTO powerPredictionCellAllocationDTO, @MappingTarget PowerPredictionCellAllocation powerPredictionCellAllocation);
}