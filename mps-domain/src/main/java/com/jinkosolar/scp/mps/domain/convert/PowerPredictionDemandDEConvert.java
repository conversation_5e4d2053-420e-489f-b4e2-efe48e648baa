package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandDTO;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandMiniDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemand;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionDemandDEConvert extends BaseDEConvert<PowerPredictionDemandDTO, PowerPredictionDemand> {
    PowerPredictionDemandDEConvert INSTANCE = Mappers.getMapper(PowerPredictionDemandDEConvert.class);

    PowerPredictionDemandMiniDTO toMiniDTO(PowerPredictionDemand powerPredictionDemand);

    List<PowerPredictionDemandMiniDTO> toMiniDTOList(List<PowerPredictionDemand> powerPredictionDemandList);

    void resetPowerPredictionDemand(PowerPredictionDemandDTO powerPredictionDemandDTO, @MappingTarget PowerPredictionDemand powerPredictionDemand);
}