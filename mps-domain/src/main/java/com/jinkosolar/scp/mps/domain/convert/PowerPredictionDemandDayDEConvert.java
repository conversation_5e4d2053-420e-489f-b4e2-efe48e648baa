package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandDayDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemandDay;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionDemandDayDEConvert extends BaseDEConvert<PowerPredictionDemandDayDTO, PowerPredictionDemandDay> {
    PowerPredictionDemandDayDEConvert INSTANCE = Mappers.getMapper(PowerPredictionDemandDayDEConvert.class);

    void resetPowerPredictionDemandDay(PowerPredictionDemandDayDTO powerPredictionDemandDayDTO, @MappingTarget PowerPredictionDemandDay powerPredictionDemandDay);
}