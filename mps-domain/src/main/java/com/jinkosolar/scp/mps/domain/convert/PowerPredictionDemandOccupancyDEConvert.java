package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionDemandOccupancyDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemandOccupancy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionDemandOccupancyDEConvert extends BaseDEConvert<PowerPredictionDemandOccupancyDTO, PowerPredictionDemandOccupancy> {
    PowerPredictionDemandOccupancyDEConvert INSTANCE = Mappers.getMapper(PowerPredictionDemandOccupancyDEConvert.class);

    void resetPowerPredictionDemandOccupancy(PowerPredictionDemandOccupancyDTO powerPredictionDemandOccupancyDTO, @MappingTarget PowerPredictionDemandOccupancy powerPredictionDemandOccupancy);
}