package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionReleaseDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionRelease;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionReleaseDEConvert extends BaseDEConvert<PowerPredictionReleaseDTO, PowerPredictionRelease> {
    PowerPredictionReleaseDEConvert INSTANCE = Mappers.getMapper(PowerPredictionReleaseDEConvert.class);

    void resetPowerPredictionRelease(PowerPredictionReleaseDTO PowerPredictionReleaseDTO, @MappingTarget PowerPredictionRelease PowerPredictionRelease);
}
