package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PowerPredictionSupplyDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionSupply;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PowerPredictionSupplyDEConvert extends BaseDEConvert<PowerPredictionSupplyDTO, PowerPredictionSupply> {
    PowerPredictionSupplyDEConvert INSTANCE = Mappers.getMapper(PowerPredictionSupplyDEConvert.class);

    void resetPowerPredictionSupply(PowerPredictionSupplyDTO powerPredictionSupplyDTO, @MappingTarget PowerPredictionSupply powerPredictionSupply);

    @BeanMapping(
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
            @Mapping(target = "id", ignore = true),
    })
    PowerPredictionSupply copy(PowerPredictionSupply supply);

    List<PowerPredictionSupply> copy(List<PowerPredictionSupply> supplyList);
}