package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitDTO;
import com.jinkosolar.scp.mps.domain.entity.ProcessLimit;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProcessLimitDEConvert extends BaseDEConvert<ProcessLimitDTO, ProcessLimit> {
    ProcessLimitDEConvert INSTANCE = Mappers.getMapper(ProcessLimitDEConvert.class);

    void resetProcessLimit(ProcessLimitDTO processLimitDTO, @MappingTarget ProcessLimit processLimit);
}