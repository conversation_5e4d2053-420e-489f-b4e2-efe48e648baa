package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitHeaderItemDTO;
import com.jinkosolar.scp.mps.domain.entity.ProcessLimitHeaderItem;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProcessLimitHeaderItemDEConvert extends BaseDEConvert<ProcessLimitHeaderItemDTO, ProcessLimitHeaderItem> {
    ProcessLimitHeaderItemDEConvert INSTANCE = Mappers.getMapper(ProcessLimitHeaderItemDEConvert.class);

    void resetProcessLimitHeaderItem(ProcessLimitHeaderItemDTO processLimitHeaderItemDTO, @MappingTarget ProcessLimitHeaderItem processLimitHeaderItem);
}