package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitItemDTO;
import com.jinkosolar.scp.mps.domain.entity.ProcessLimitItem;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProcessLimitItemDEConvert extends BaseDEConvert<ProcessLimitItemDTO, ProcessLimitItem> {
    ProcessLimitItemDEConvert INSTANCE = Mappers.getMapper(ProcessLimitItemDEConvert.class);

    void resetProcessLimitItem(ProcessLimitItemDTO processLimitItemDTO, @MappingTarget ProcessLimitItem processLimitItem);
}