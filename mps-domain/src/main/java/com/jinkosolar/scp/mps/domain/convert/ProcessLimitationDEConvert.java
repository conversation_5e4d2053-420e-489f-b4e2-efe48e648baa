package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProcessLimitationDTO;
import com.jinkosolar.scp.mps.domain.entity.ProcessLimitation;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProcessLimitationDEConvert extends BaseDEConvert<ProcessLimitationDTO, ProcessLimitation> {
    ProcessLimitationDEConvert INSTANCE = Mappers.getMapper(ProcessLimitationDEConvert.class);

    void resetProcessLimitation(ProcessLimitationDTO processLimitationDTO, @MappingTarget ProcessLimitation processLimitation);
}