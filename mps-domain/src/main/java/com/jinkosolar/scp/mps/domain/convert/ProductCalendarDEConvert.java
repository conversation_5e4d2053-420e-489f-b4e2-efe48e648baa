package com.jinkosolar.scp.mps.domain.convert;

import com.jinkosolar.scp.mps.domain.dto.ProductCalendarDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductCalendarDO;
import com.jinkosolar.scp.mps.domain.excel.ProductCalendarExcelDTO;
import com.jinkosolar.scp.mps.domain.save.ProductCalendarSaveDTO;
import lombok.*;
import java.util.*;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 生产日历Convert
*
* <AUTHOR>
*/
@Mapper(componentModel = "spring",
unmappedTargetPolicy = ReportingPolicy.IGNORE,
nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductCalendarDEConvert extends BaseDEConvert<ProductCalendarDTO, ProductCalendarDO>{

    ProductCalendarDEConvert INSTANCE = Mappers.getMapper(ProductCalendarDEConvert.class);

    List<ProductCalendarDTO> toExcelDTO(List<ProductCalendarDTO> dtos);

    ProductCalendarExcelDTO toExcelDTO(ProductCalendarDTO dto);

    @BeanMapping(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
    @Mapping(target = "id", ignore = true)
    })
    ProductCalendarDO saveDTOtoEntity(ProductCalendarSaveDTO saveDTO, @MappingTarget ProductCalendarDO entity);
}