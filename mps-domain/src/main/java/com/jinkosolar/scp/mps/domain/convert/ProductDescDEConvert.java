package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductDescDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductDescMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductDescDEConvert extends BaseDEConvert<ProductDescDTO, ProductDescMapping> {

    ProductDescDEConvert INSTANCE = Mappers.getMapper(ProductDescDEConvert.class);
    void resetProductDescDEC(ProductDescDTO productDescDto, @MappingTarget ProductDescMapping productDesc);
}
