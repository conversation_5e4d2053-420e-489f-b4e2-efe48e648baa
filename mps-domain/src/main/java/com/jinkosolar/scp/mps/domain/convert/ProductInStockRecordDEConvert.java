package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductInStockRecordDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductInStockRecord;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductInStockRecordDEConvert extends BaseDEConvert<ProductInStockRecordDTO, ProductInStockRecord> {
    ProductInStockRecordDEConvert INSTANCE = Mappers.getMapper(ProductInStockRecordDEConvert.class);

    void resetProductInStockRecord(ProductInStockRecordDTO productInStockRecordDTO, @MappingTarget ProductInStockRecord productInStockRecord);
}