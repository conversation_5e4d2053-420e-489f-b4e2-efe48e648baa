package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductMaterialCombinationMappingDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductMaterialCombinationMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductMaterialCombinationMappingDEConvert extends BaseDEConvert<ProductMaterialCombinationMappingDTO, ProductMaterialCombinationMapping> {
    ProductMaterialCombinationMappingDEConvert INSTANCE = Mappers.getMapper(ProductMaterialCombinationMappingDEConvert.class);

    void resetProductMaterialCombinationMapping(ProductMaterialCombinationMappingDTO productMaterialCombinationMappingDTO, @MappingTarget ProductMaterialCombinationMapping productMaterialCombinationMapping);
}