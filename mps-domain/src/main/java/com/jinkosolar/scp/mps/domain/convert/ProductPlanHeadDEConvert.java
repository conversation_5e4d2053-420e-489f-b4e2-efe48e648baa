package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductPlanHeadDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductPlanHead;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductPlanHeadDEConvert extends BaseDEConvert<ProductPlanHeadDTO, ProductPlanHead> {
    ProductPlanHeadDEConvert INSTANCE = Mappers.getMapper(ProductPlanHeadDEConvert.class);

    void resetProductPlanHead(ProductPlanHeadDTO productPlanHeadDTO, @MappingTarget ProductPlanHead productPlanHead);
}