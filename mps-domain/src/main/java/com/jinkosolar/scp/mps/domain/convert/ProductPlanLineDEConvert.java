package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductPlanLineDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductPlanLine;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductPlanLineDEConvert extends BaseDEConvert<ProductPlanLineDTO, ProductPlanLine> {
    ProductPlanLineDEConvert INSTANCE = Mappers.getMapper(ProductPlanLineDEConvert.class);

    void resetProductPlanLine(ProductPlanLineDTO productPlanLineDTO, @MappingTarget ProductPlanLine productPlanLine);
}