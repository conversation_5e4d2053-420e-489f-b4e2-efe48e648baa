package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductPlanLineDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductPlanLineDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductPlanLineDetailDEConvert extends BaseDEConvert<ProductPlanLineDetailDTO, ProductPlanLineDetail> {
    ProductPlanLineDetailDEConvert INSTANCE = Mappers.getMapper(ProductPlanLineDetailDEConvert.class);

    void resetProductPlanLineDetail(ProductPlanLineDetailDTO productPlanLineDetailDTO, @MappingTarget ProductPlanLineDetail productPlanLineDetail);
}