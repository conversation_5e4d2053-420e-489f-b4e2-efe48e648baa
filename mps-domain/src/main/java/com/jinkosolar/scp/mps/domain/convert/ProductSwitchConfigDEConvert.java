package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductSwitchConfigDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductSwitchConfig;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductSwitchConfigDEConvert extends BaseDEConvert<ProductSwitchConfigDTO, ProductSwitchConfig> {
    ProductSwitchConfigDEConvert INSTANCE = Mappers.getMapper(ProductSwitchConfigDEConvert.class);

    void resetProductSwitchConfig(ProductSwitchConfigDTO productSwitchConfigDTO, @MappingTarget ProductSwitchConfig productSwitchConfig);
}