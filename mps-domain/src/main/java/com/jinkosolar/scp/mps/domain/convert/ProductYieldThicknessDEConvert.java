package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductYieldThicknessDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductYieldThickness;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductYieldThicknessDEConvert extends BaseDEConvert<ProductYieldThicknessDTO, ProductYieldThickness> {
    ProductYieldThicknessDEConvert INSTANCE = Mappers.getMapper(ProductYieldThicknessDEConvert.class);

    void resetProductYieldThickness(ProductYieldThicknessDTO productYieldThicknessDTO, @MappingTarget ProductYieldThickness productYieldThickness);
}