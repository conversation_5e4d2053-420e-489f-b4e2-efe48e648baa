package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductionCalendarDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductionCalendar;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductionCalendarDEConvert extends BaseDEConvert<ProductionCalendarDTO, ProductionCalendar> {
    ProductionCalendarDEConvert INSTANCE = Mappers.getMapper(ProductionCalendarDEConvert.class);

    void resetProductionCalendar(ProductionCalendarDTO productionCalendarDTO, @MappingTarget ProductionCalendar productionCalendar);
}