package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ProductionSuggestionSendLogDTO;
import com.jinkosolar.scp.mps.domain.entity.ProductionSuggestionSendLog;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 生产建议推送SAP日志转换类
 *
 * <AUTHOR> chenc
 * @date : 2024-12-27
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductionSuggestionSendLogDEConvert extends BaseDEConvert<ProductionSuggestionSendLogDTO, ProductionSuggestionSendLog> {

    ProductionSuggestionSendLogDEConvert INSTANCE = Mappers.getMapper(ProductionSuggestionSendLogDEConvert.class);

    void resetMpsProductionSuggestionSendLog(ProductionSuggestionSendLogDTO productionSuggestionSendLogDTO, @MappingTarget ProductionSuggestionSendLog productionSuggestionSendLog);
}