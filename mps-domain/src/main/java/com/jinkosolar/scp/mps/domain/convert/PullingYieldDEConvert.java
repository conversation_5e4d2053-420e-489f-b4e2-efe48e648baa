package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.PullingYieldDTO;
import com.jinkosolar.scp.mps.domain.entity.PullingYield;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PullingYieldDEConvert extends BaseDEConvert<PullingYieldDTO, PullingYield> {
    PullingYieldDEConvert INSTANCE = Mappers.getMapper(PullingYieldDEConvert.class);

    void resetPullingYield(PullingYieldDTO pullingYieldDTO, @MappingTarget PullingYield pullingYield);
}