package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ReReleaseVersionDTO;
import com.jinkosolar.scp.mps.domain.entity.ReReleaseVersion;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ReReleaseVersionDEConvert extends BaseDEConvert<ReReleaseVersionDTO, ReReleaseVersion> {
    ReReleaseVersionDEConvert INSTANCE = Mappers.getMapper(ReReleaseVersionDEConvert.class);
}
