package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.RealityMaterialRuleDTO;
import com.jinkosolar.scp.mps.domain.entity.RealityMaterialRule;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 预测结果实际用料规则
 *
 * <AUTHOR>
 * @date 2022-9-30
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RealityMaterialRuleDEConvert extends BaseDEConvert<RealityMaterialRuleDTO, RealityMaterialRule> {
}
