package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.jip.api.dto.mrp.RealtimeInventoryResponse;
import com.jinkosolar.scp.mps.domain.dto.BaseBatteryTakeoutDTO;
import com.jinkosolar.scp.mps.domain.dto.RealtimeInventoryDTO;
import com.jinkosolar.scp.mps.domain.entity.RealtimeInventory;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RealtimeInventoryDEConvert extends BaseDEConvert<RealtimeInventoryDTO, RealtimeInventory> {
    RealtimeInventoryDEConvert INSTANCE = Mappers.getMapper(RealtimeInventoryDEConvert.class);

    void resetRealtimeInventory(RealtimeInventoryDTO realtimeInventoryDTO, @MappingTarget RealtimeInventory realtimeInventory);

    List<RealtimeInventory> toEntityList(List<RealtimeInventoryResponse.RealtimeInventoryResponseInfo> dtoList);

    BaseBatteryTakeoutDTO toBaseBatteryTakeoutDTO(RealtimeInventory realtimeInventory);
}
