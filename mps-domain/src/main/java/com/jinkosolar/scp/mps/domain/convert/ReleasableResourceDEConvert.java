package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ReleasableResourceDTO;
import com.jinkosolar.scp.mps.domain.entity.ReleasableResource;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ReleasableResourceDEConvert extends BaseDEConvert<ReleasableResourceDTO, ReleasableResource> {
    ReleasableResourceDEConvert INSTANCE = Mappers.getMapper(ReleasableResourceDEConvert.class);

    void resetReleasableResource(ReleasableResourceDTO releasableResourceDTO, @MappingTarget ReleasableResource releasableResource);
}