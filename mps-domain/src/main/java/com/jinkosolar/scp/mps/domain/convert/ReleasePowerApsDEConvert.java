package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ReleasePowerApsDTO;
import com.jinkosolar.scp.mps.domain.dto.StandardCellEfficApsDTO;
import com.jinkosolar.scp.mps.domain.entity.ReleasePowerAps;
import com.jinkosolar.scp.mps.domain.entity.StandardCellEfficAps;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ReleasePowerApsDEConvert extends BaseDEConvert<ReleasePowerApsDTO, ReleasePowerAps> {
    ReleasePowerApsDEConvert INSTANCE = Mappers.getMapper(ReleasePowerApsDEConvert.class);

    void resetReleasePowerAps(ReleasePowerApsDTO releasePowerApsDTO, @MappingTarget ReleasePowerAps releasePowerAps);
}