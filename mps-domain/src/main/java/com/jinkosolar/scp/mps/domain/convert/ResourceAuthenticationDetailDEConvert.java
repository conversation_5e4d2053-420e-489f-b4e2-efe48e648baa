package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ResourceAuthenticationDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.ResourceAuthenticationDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ResourceAuthenticationDetailDEConvert extends BaseDEConvert<ResourceAuthenticationDetailDTO, ResourceAuthenticationDetail> {
    ResourceAuthenticationDetailDEConvert INSTANCE = Mappers.getMapper(ResourceAuthenticationDetailDEConvert.class);

    void resetResourceAuthenticationDetail(ResourceAuthenticationDetailDTO resourceAuthenticationDetailDTO, @MappingTarget ResourceAuthenticationDetail resourceAuthenticationDetail);
}