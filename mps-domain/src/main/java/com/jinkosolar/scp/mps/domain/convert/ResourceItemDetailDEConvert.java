package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ResourceItemDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.ResourceItemDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ResourceItemDetailDEConvert extends BaseDEConvert<ResourceItemDetailDTO, ResourceItemDetail> {
    ResourceItemDetailDEConvert INSTANCE = Mappers.getMapper(ResourceItemDetailDEConvert.class);

    void resetResourceItemDetail(ResourceItemDetailDTO resourceItemDetailDTO, @MappingTarget ResourceItemDetail resourceItemDetail);
}