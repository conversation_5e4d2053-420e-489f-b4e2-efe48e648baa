package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.RetentionDeductionDTO;
import com.jinkosolar.scp.mps.domain.entity.RetentionDeduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RetentionDeductionDEConvert extends BaseDEConvert<RetentionDeductionDTO, RetentionDeduction> {
    RetentionDeductionDEConvert INSTANCE = Mappers.getMapper(RetentionDeductionDEConvert.class);

    void resetRetentionDeduction(RetentionDeductionDTO retentionDeductionDTO, @MappingTarget RetentionDeduction retentionDeduction);

    RetentionDeductionDTO copyDto(RetentionDeductionDTO retentionDeductionDTO);
}
