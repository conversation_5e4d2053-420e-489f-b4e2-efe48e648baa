package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.RetentionDeductionDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.RetentionDeductionDetail;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface RetentionDeductionDetailDEConvert extends BaseDEConvert<RetentionDeductionDetailDTO, RetentionDeductionDetail> {
    RetentionDeductionDetailDEConvert INSTANCE = Mappers.getMapper(RetentionDeductionDetailDEConvert.class);

    void resetRetentionDeductionDetail(RetentionDeductionDetailDTO retentionDeductionDetailDTO, @MappingTarget RetentionDeductionDetail retentionDeductionDetail);
}
