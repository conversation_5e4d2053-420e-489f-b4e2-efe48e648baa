package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SalesForecastReportDTO;
import com.jinkosolar.scp.mps.domain.entity.SalesForecastReport;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SalesForecastReportDEConvert extends BaseDEConvert<SalesForecastReportDTO, SalesForecastReport> {
    SalesForecastReportDEConvert INSTANCE = Mappers.getMapper(SalesForecastReportDEConvert.class);

    void resetSalesForecastReport(SalesForecastReportDTO salesForecastReportDTO, @MappingTarget SalesForecastReport salesForecastReport);
}