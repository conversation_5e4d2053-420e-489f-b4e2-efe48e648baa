package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SalesForecastReportPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.SalesForecastReportPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SalesForecastReportPlanDEConvert extends BaseDEConvert<SalesForecastReportPlanDTO, SalesForecastReportPlan> {
    SalesForecastReportPlanDEConvert INSTANCE = Mappers.getMapper(SalesForecastReportPlanDEConvert.class);

    void resetSalesForecastReportPlan(SalesForecastReportPlanDTO salesForecastReportPlanDTO, @MappingTarget SalesForecastReportPlan salesForecastReportPlan);
}