package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SalesForecastReportSummaryDTO;
import com.jinkosolar.scp.mps.domain.entity.SalesForecastReportSummary;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SalesForecastReportSummaryDEConvert extends BaseDEConvert<SalesForecastReportSummaryDTO, SalesForecastReportSummary> {
    SalesForecastReportSummaryDEConvert INSTANCE = Mappers.getMapper(SalesForecastReportSummaryDEConvert.class);

    void resetSalesForecastReportSummary(SalesForecastReportSummaryDTO salesForecastReportSummaryDTO, @MappingTarget SalesForecastReportSummary salesForecastReportSummary);
}