package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.ScheduledTaskLinesDTO;
import com.jinkosolar.scp.mps.domain.entity.ScheduledTaskLines;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ScheduledTaskLinesDEConverter extends BaseDEConvert<ScheduledTaskLinesDTO, ScheduledTaskLines> {
    ScheduledTaskLinesDEConverter INSTANCE = Mappers.getMapper(ScheduledTaskLinesDEConverter.class);

    @Override
    ScheduledTaskLines toEntity(ScheduledTaskLinesDTO dto);

    @Override
    List<ScheduledTaskLines> toEntity(List<ScheduledTaskLinesDTO> dtos);



}
