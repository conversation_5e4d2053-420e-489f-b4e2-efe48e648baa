package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SelfBatteryAllotPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.SelfBatteryAllotPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SelfBatteryAllotPlanDEConvert extends BaseDEConvert<SelfBatteryAllotPlanDTO, SelfBatteryAllotPlan> {
    SelfBatteryAllotPlanDEConvert INSTANCE = Mappers.getMapper(SelfBatteryAllotPlanDEConvert.class);

    void resetSelfBatteryAllotPlan(SelfBatteryAllotPlanDTO selfBatteryAllotPlanDTO, @MappingTarget SelfBatteryAllotPlan selfBatteryAllotPlan);
}