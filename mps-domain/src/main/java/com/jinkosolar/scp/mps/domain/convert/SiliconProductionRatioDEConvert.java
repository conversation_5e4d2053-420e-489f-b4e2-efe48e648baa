package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SiliconProductionRatioDTO;
import com.jinkosolar.scp.mps.domain.entity.SiliconProductionRatio;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SiliconProductionRatioDEConvert extends BaseDEConvert<SiliconProductionRatioDTO, SiliconProductionRatio> {
    SiliconProductionRatioDEConvert INSTANCE = Mappers.getMapper(SiliconProductionRatioDEConvert.class);

    void resetSiliconProductionRatio(SiliconProductionRatioDTO siliconProductionRatioDTO, @MappingTarget SiliconProductionRatio siliconProductionRatio);
}