package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbResultDTO;
import com.jinkosolar.scp.mps.domain.dto.CellPlanClimbSectionResultDTO;
import com.jinkosolar.scp.mps.domain.dto.ComponentAlignmentPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.SiliconeColorContinuityDTO;
import com.jinkosolar.scp.mps.domain.entity.CellPlanClimbSectionResult;
import com.jinkosolar.scp.mps.domain.entity.ComponentAlignmentPlan;
import com.jinkosolar.scp.mps.domain.entity.SiliconeColorContinuity;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SiliconeColorContinuityDEConvert extends BaseDEConvert<SiliconeColorContinuityDTO, SiliconeColorContinuity> {
    SiliconeColorContinuityDEConvert INSTANCE = Mappers.getMapper(SiliconeColorContinuityDEConvert.class);
    void resetSiliconeColorContinuity(SiliconeColorContinuityDTO componentAlignmentPlanDTO, @MappingTarget SiliconeColorContinuity siliconeColorContinuity);
}