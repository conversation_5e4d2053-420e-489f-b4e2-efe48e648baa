package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SingleModuleWattageDTO;
import com.jinkosolar.scp.mps.domain.entity.SingleModuleWattage;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SingleModuleWattageDEConvert extends BaseDEConvert<SingleModuleWattageDTO, SingleModuleWattage> {
    SingleModuleWattageDEConvert INSTANCE = Mappers.getMapper(SingleModuleWattageDEConvert.class);

    void resetSingleModuleWattage(SingleModuleWattageDTO singleModuleWattageDTO, @MappingTarget SingleModuleWattage singleModuleWattage);
}