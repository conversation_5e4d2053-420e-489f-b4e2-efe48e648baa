package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SliceChamferSwitchPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.SliceChamferSwitchPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SliceChamferSwitchPlanDEConvert extends BaseDEConvert<SliceChamferSwitchPlanDTO, SliceChamferSwitchPlan> {
    SliceChamferSwitchPlanDEConvert INSTANCE = Mappers.getMapper(SliceChamferSwitchPlanDEConvert.class);

    void resetSliceChamferSwitchPlan(SliceChamferSwitchPlanDTO sliceChamferSwitchPlanDTO, @MappingTarget SliceChamferSwitchPlan sliceChamferSwitchPlan);
}