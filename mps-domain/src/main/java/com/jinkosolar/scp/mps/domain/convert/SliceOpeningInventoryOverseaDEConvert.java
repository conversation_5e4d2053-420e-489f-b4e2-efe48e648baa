package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SliceOpeningInventoryOverseaDTO;
import com.jinkosolar.scp.mps.domain.entity.SliceOpeningInventoryOversea;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SliceOpeningInventoryOverseaDEConvert extends BaseDEConvert<SliceOpeningInventoryOverseaDTO, SliceOpeningInventoryOversea> {
    SliceOpeningInventoryOverseaDEConvert INSTANCE = Mappers.getMapper(SliceOpeningInventoryOverseaDEConvert.class);

    void resetSliceOpeningInventoryOversea(SliceOpeningInventoryOverseaDTO sliceOpeningInventoryOverseaDTO, @MappingTarget SliceOpeningInventoryOversea sliceOpeningInventoryOversea);
}