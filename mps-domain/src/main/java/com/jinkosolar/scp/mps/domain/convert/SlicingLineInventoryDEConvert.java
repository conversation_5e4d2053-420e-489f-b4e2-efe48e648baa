package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SlicingLineInventoryDTO;
import com.jinkosolar.scp.mps.domain.entity.SlicingLineInventory;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SlicingLineInventoryDEConvert extends BaseDEConvert<SlicingLineInventoryDTO, SlicingLineInventory> {
    SlicingLineInventoryDEConvert INSTANCE = Mappers.getMapper(SlicingLineInventoryDEConvert.class);

    void resetSlicingLineInventory(SlicingLineInventoryDTO slicingLineInventoryDTO, @MappingTarget SlicingLineInventory slicingLineInventory);
}