package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SlicingYieldRateDTO;
import com.jinkosolar.scp.mps.domain.entity.SlicingYieldRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SlicingYieldRateDEConvert extends BaseDEConvert<SlicingYieldRateDTO, SlicingYieldRate> {
    SlicingYieldRateDEConvert INSTANCE = Mappers.getMapper(SlicingYieldRateDEConvert.class);

    void resetSlicingYieldRate(SlicingYieldRateDTO slicingYieldRateDTO, @MappingTarget SlicingYieldRate slicingYieldRate);
}