package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StandardCapacityDTO;
import com.jinkosolar.scp.mps.domain.entity.StandardCapacity;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StandardCapacityDEConvert extends BaseDEConvert<StandardCapacityDTO, StandardCapacity> {
    StandardCapacityDEConvert INSTANCE = Mappers.getMapper(StandardCapacityDEConvert.class);

    void resetStandardCapacity(StandardCapacityDTO standardCapacityDTO, @MappingTarget StandardCapacity standardCapacity);
}