package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StandardCrucibleLifeDTO;
import com.jinkosolar.scp.mps.domain.entity.StandardCrucibleLife;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StandardCrucibleLifeDEConvert extends BaseDEConvert<StandardCrucibleLifeDTO, StandardCrucibleLife> {
    StandardCrucibleLifeDEConvert INSTANCE = Mappers.getMapper(StandardCrucibleLifeDEConvert.class);

    void resetStandardCrucibleLife(StandardCrucibleLifeDTO standardCrucibleLifeDTO, @MappingTarget StandardCrucibleLife standardCrucibleLife);
}