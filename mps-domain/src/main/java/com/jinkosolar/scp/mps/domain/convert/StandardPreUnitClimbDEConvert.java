package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StandardPreUnitClimbDTO;
import com.jinkosolar.scp.mps.domain.entity.StandardPreUnitClimb;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StandardPreUnitClimbDEConvert extends BaseDEConvert<StandardPreUnitClimbDTO, StandardPreUnitClimb> {
    StandardPreUnitClimbDEConvert INSTANCE = Mappers.getMapper(StandardPreUnitClimbDEConvert.class);

    void resetStandardPreUnitClimb(StandardPreUnitClimbDTO standardPreUnitClimbDTO, @MappingTarget StandardPreUnitClimb standardPreUnitClimb);
}