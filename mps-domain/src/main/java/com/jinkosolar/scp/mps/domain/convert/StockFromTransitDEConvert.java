package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StockFromTransitDTO;
import com.jinkosolar.scp.mps.domain.entity.StockFromTransit;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StockFromTransitDEConvert extends BaseDEConvert<StockFromTransitDTO, StockFromTransit> {
    StockFromTransitDEConvert INSTANCE = Mappers.getMapper(StockFromTransitDEConvert.class);

    void resetStockFromTransit(StockFromTransitDTO stockFromTransitDTO, @MappingTarget StockFromTransit stockFromTransit);
}