package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StockInTransitDTO;
import com.jinkosolar.scp.mps.domain.entity.StockInTransit;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StockInTransitDEConvert extends BaseDEConvert<StockInTransitDTO, StockInTransit> {
    StockInTransitDEConvert INSTANCE = Mappers.getMapper(StockInTransitDEConvert.class);

    void resetStockInTransit(StockInTransitDTO stockInTransitDTO, @MappingTarget StockInTransit stockInTransit);
}