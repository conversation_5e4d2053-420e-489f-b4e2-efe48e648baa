package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StockLocationInfoDTO;
import com.jinkosolar.scp.mps.domain.entity.StockLocationInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StockLocationInfoDEConvert extends BaseDEConvert<StockLocationInfoDTO, StockLocationInfo> {
    StockLocationInfoDEConvert INSTANCE = Mappers.getMapper(StockLocationInfoDEConvert.class);

    void resetStockLocationInfo(StockLocationInfoDTO stockLocationInfoDTO, @MappingTarget StockLocationInfo stockLocationInfo);
}