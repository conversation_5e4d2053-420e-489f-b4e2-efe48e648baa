package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.StockTransferDirectionalDTO;
import com.jinkosolar.scp.mps.domain.entity.StockTransferDirectional;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface StockTransferDirectionalDEConvert extends BaseDEConvert<StockTransferDirectionalDTO, StockTransferDirectional> {
    StockTransferDirectionalDEConvert INSTANCE = Mappers.getMapper(StockTransferDirectionalDEConvert.class);

    void resetStockTransferDirectional(StockTransferDirectionalDTO stockTransferDirectionalDTO, @MappingTarget StockTransferDirectional stockTransferDirectional);
}