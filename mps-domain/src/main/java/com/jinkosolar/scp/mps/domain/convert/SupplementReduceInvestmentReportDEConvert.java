package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SupplementReduceInvestmentReportDTO;
import com.jinkosolar.scp.mps.domain.entity.SupplementReduceInvestmentReport;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SupplementReduceInvestmentReportDEConvert extends BaseDEConvert<SupplementReduceInvestmentReportDTO, SupplementReduceInvestmentReport> {
    SupplementReduceInvestmentReportDEConvert INSTANCE = Mappers.getMapper(SupplementReduceInvestmentReportDEConvert.class);

    void resetSupplementReduceInvestmentReport(SupplementReduceInvestmentReportDTO supplementReduceInvestmentReportDTO, @MappingTarget SupplementReduceInvestmentReport supplementReduceInvestmentReport);
}