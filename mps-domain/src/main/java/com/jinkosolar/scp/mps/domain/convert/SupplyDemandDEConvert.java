package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SupplyDemandDTO;
import com.jinkosolar.scp.mps.domain.entity.SupplyDemand;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 供应需求 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-01 18:24:27
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SupplyDemandDEConvert extends BaseDEConvert<SupplyDemandDTO, SupplyDemand> {

    SupplyDemandDEConvert INSTANCE = Mappers.getMapper(SupplyDemandDEConvert.class);

}
