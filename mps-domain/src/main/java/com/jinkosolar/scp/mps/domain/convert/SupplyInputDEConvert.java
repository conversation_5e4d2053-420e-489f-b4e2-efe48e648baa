package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SupplyInputDTO;
import com.jinkosolar.scp.mps.domain.entity.SupplyInput;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SupplyInputDEConvert extends BaseDEConvert<SupplyInputDTO, SupplyInput> {
    SupplyInputDEConvert INSTANCE = Mappers.getMapper(SupplyInputDEConvert.class);

    void resetSupplyInput(SupplyInputDTO supplyInputDTO, @MappingTarget SupplyInput supplyInput);
}