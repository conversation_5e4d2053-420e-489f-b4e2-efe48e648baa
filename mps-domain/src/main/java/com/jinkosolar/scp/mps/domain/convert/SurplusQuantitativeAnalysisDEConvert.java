package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SurplusQuantitativeAnalysisDTO;
import com.jinkosolar.scp.mps.domain.entity.SurplusQuantitativeAnalysis;
import com.jinkosolar.scp.mps.domain.excel.SurplusQuantitativeAnalysisExcelDTO;
import com.jinkosolar.scp.mps.domain.save.SurplusQuantitativeAnalysisSaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 剩余可接单量分析 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 08:02:10
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SurplusQuantitativeAnalysisDEConvert extends BaseDEConvert<SurplusQuantitativeAnalysisDTO, SurplusQuantitativeAnalysis> {

    SurplusQuantitativeAnalysisDEConvert INSTANCE = Mappers.getMapper(SurplusQuantitativeAnalysisDEConvert.class);

    List<SurplusQuantitativeAnalysisExcelDTO> toExcelDTO(List<SurplusQuantitativeAnalysisDTO> dtos);

    SurplusQuantitativeAnalysisExcelDTO toExcelDTO(SurplusQuantitativeAnalysisDTO dto);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    SurplusQuantitativeAnalysis saveDTOtoEntity(SurplusQuantitativeAnalysisSaveDTO saveDTO, @MappingTarget SurplusQuantitativeAnalysis entity);
}
