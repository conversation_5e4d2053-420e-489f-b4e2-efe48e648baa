package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SwInstockRecordDTO;
import com.jinkosolar.scp.mps.domain.entity.SwInstockRecord;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SwInstockRecordDEConvert extends BaseDEConvert<SwInstockRecordDTO, SwInstockRecord> {
    SwInstockRecordDEConvert INSTANCE = Mappers.getMapper(SwInstockRecordDEConvert.class);

    void resetSwInstockRecord(SwInstockRecordDTO swInstockRecordDTO, @MappingTarget SwInstockRecord swInstockRecord);
}