package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SwitchLossDataDTO;
import com.jinkosolar.scp.mps.domain.entity.SwitchLossData;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SwitchLossDataDEConvert extends BaseDEConvert<SwitchLossDataDTO, SwitchLossData> {
    SwitchLossDataDEConvert INSTANCE = Mappers.getMapper(SwitchLossDataDEConvert.class);

    void resetSwitchLossData(SwitchLossDataDTO switchLossDataDTO, @MappingTarget SwitchLossData switchLossData);
}