package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.SwitchLossDataDetailDTO;
import com.jinkosolar.scp.mps.domain.entity.SwitchLossData;
import com.jinkosolar.scp.mps.domain.entity.SwitchLossDataDetail;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface SwitchLossDataDetailDEConvert extends BaseDEConvert<SwitchLossDataDetailDTO, SwitchLossDataDetail> {
    SwitchLossDataDetailDEConvert INSTANCE = Mappers.getMapper(SwitchLossDataDetailDEConvert.class);

    void resetSwitchLossDataDetail(SwitchLossDataDetailDTO switchLossDataDetailDTO, @MappingTarget SwitchLossDataDetail switchLossDataDetail);

    @Mapping(source = "id", target = "switchLossDataId")
    SwitchLossDataDetail toEntity(SwitchLossData switchLossData);

    SwitchLossDataDetail toEntity(SwitchLossDataDetail detail);
}
