package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.UnitYieldIncreaseRulesDTO;
import com.jinkosolar.scp.mps.domain.entity.UnitYieldIncreaseRules;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UnitYieldIncreaseRulesDEConvert extends BaseDEConvert<UnitYieldIncreaseRulesDTO, UnitYieldIncreaseRules> {
    UnitYieldIncreaseRulesDEConvert INSTANCE = Mappers.getMapper(UnitYieldIncreaseRulesDEConvert.class);

    void resetUnitYieldIncreaseRules(UnitYieldIncreaseRulesDTO unitYieldIncreaseRulesDTO, @MappingTarget UnitYieldIncreaseRules unitYieldIncreaseRules);
}