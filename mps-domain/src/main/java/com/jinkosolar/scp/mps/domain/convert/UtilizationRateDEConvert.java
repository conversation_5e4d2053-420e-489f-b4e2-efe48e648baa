package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.UtilizationRateDTO;
import com.jinkosolar.scp.mps.domain.entity.UtilizationRate;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface UtilizationRateDEConvert extends BaseDEConvert<UtilizationRateDTO, UtilizationRate> {
    UtilizationRateDEConvert INSTANCE = Mappers.getMapper(UtilizationRateDEConvert.class);

    void resetUtilizationRate(UtilizationRateDTO utilizationRateDTO, @MappingTarget UtilizationRate utilizationRate);
}