package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.VendorEfficiencyDTO;
import com.jinkosolar.scp.mps.domain.entity.VendorEfficiency;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface VendorEfficiencyDEConvert extends BaseDEConvert<VendorEfficiencyDTO, VendorEfficiency> {
    VendorEfficiencyDEConvert INSTANCE = Mappers.getMapper(VendorEfficiencyDEConvert.class);

    void resetVendorEfficiency(VendorEfficiencyDTO vendorEfficiencyDTO, @MappingTarget VendorEfficiency vendorEfficiency);
}