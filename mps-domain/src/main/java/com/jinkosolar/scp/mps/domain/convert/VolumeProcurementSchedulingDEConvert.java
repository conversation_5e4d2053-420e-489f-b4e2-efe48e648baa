package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.VolumeProcurementSchedulingDTO;
import com.jinkosolar.scp.mps.domain.entity.VolumeProcurementScheduling;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface VolumeProcurementSchedulingDEConvert extends BaseDEConvert<VolumeProcurementSchedulingDTO, VolumeProcurementScheduling> {
    VolumeProcurementSchedulingDEConvert INSTANCE = Mappers.getMapper(VolumeProcurementSchedulingDEConvert.class);

    void resetVolumeProcurementScheduling(VolumeProcurementSchedulingDTO volumeProcurementSchedulingDTO, @MappingTarget VolumeProcurementScheduling volumeProcurementScheduling);
}