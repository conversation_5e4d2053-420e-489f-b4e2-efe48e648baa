package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WaferCrystalProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.entity.WaferCrystalProductionPlan;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WaferCrystalProductionPlanDEConvert extends BaseDEConvert<WaferCrystalProductionPlanDTO, WaferCrystalProductionPlan> {
    WaferCrystalProductionPlanDEConvert INSTANCE = Mappers.getMapper(WaferCrystalProductionPlanDEConvert.class);

    void resetBigBasePlanUpload(WaferCrystalProductionPlanDTO WaferCrystalProductionPlanDTO, @MappingTarget WaferCrystalProductionPlan WaferCrystalProductionPlan);
}
