package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WaferFurnaceDeductionDTO;
import com.jinkosolar.scp.mps.domain.entity.WaferFurnaceDeduction;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WaferFurnaceDeductionDEConvert extends BaseDEConvert<WaferFurnaceDeductionDTO, WaferFurnaceDeduction> {
    WaferFurnaceDeductionDEConvert INSTANCE = Mappers.getMapper(WaferFurnaceDeductionDEConvert.class);

    void resetWaferFurnaceDeduction(WaferFurnaceDeductionDTO waferFurnaceDeductionDTO, @MappingTarget WaferFurnaceDeduction waferFurnaceDeduction);
}