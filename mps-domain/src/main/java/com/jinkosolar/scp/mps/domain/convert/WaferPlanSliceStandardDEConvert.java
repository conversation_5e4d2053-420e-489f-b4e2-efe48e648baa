package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WaferPlanSliceStandardDTO;
import com.jinkosolar.scp.mps.domain.entity.WaferPlanSliceStandard;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WaferPlanSliceStandardDEConvert extends BaseDEConvert<WaferPlanSliceStandardDTO, WaferPlanSliceStandard> {
    WaferPlanSliceStandardDEConvert INSTANCE = Mappers.getMapper(WaferPlanSliceStandardDEConvert.class);

    void resetWaferPlanSliceStandard(WaferPlanSliceStandardDTO waferPlanSliceStandardDTO, @MappingTarget WaferPlanSliceStandard waferPlanSliceStandard);
}