package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WipDTO;
import com.jinkosolar.scp.mps.domain.dto.WipRecordDTO;
import com.jinkosolar.scp.mps.domain.dto.erp.WipWorkOrderDTO;
import com.jinkosolar.scp.mps.domain.entity.WipRecord;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * 工单发放记录 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-29 14:06:13
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WipRecordDEConvert extends BaseDEConvert<WipRecordDTO, WipRecord> {

    WipRecordDEConvert INSTANCE = Mappers.getMapper(WipRecordDEConvert.class);

    @Mappings({
            @Mapping(target = "workshop", source = "dto.workshop"),
            @Mapping(target = "projectNo", source = "wipWorkOrderDTO.attribute8"),
            @Mapping(target = "classCode", source = "wipWorkOrderDTO.classCode"),
            @Mapping(target = "dpId", source = "dto.dpId"),
            @Mapping(target = "isOversea", source = "dto.isOversea"),
            @Mapping(target = "basePlace", source = "dto.productPlace"),
            @Mapping(target = "month", source = "dto.month"),
            @Mapping(target = "country", source = "dto.country"),
            @Mapping(target = "customerId", source = "dto.customerId"),
            @Mapping(target = "customerName", source = "dto.customerName"),
            @Mapping(target = "productFamily", source = "dto.productFamily"),
            @Mapping(target = "scpNo", source = "wipWorkOrderDTO.wipEntityName"),
            @Mapping(target = "organizationId", source = "dto.organizationId"),
            @Mapping(target = "id", ignore = true),
    })
    WipRecord toWipRecord(WipWorkOrderDTO wipWorkOrderDTO, WipDTO dto);
}
