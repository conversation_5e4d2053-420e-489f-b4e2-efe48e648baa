package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WorkCenterCorrespondenceDTO;
import com.jinkosolar.scp.mps.domain.entity.WorkCenterCorrespondence;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WorkCenterCorrespondenceDEConvert extends BaseDEConvert<WorkCenterCorrespondenceDTO, WorkCenterCorrespondence> {
    WorkCenterCorrespondenceDEConvert INSTANCE = Mappers.getMapper(WorkCenterCorrespondenceDEConvert.class);

    void resetWorkCenterCorrespondence(WorkCenterCorrespondenceDTO workCenterCorrespondenceDTO, @MappingTarget WorkCenterCorrespondence workCenterCorrespondence);
}