package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.LovWorkCenterVO;
import com.jinkosolar.scp.mps.domain.entity.WorkCenterLineAdjustSummary;
import com.jinkosolar.scp.mps.domain.dto.WorkCenterLineAdjustSummaryDTO;
import com.jinkosolar.scp.mps.domain.excel.WorkCenterLineAdjustSummaryExcelDTO;
import com.jinkosolar.scp.mps.domain.save.WorkCenterLineAdjustSummarySaveDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * [说明]工作中心调整开线汇总表 DTO与实体转换器
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WorkCenterLineAdjustSummaryDEConvert extends BaseDEConvert<WorkCenterLineAdjustSummaryDTO, WorkCenterLineAdjustSummary> {

    WorkCenterLineAdjustSummaryDEConvert INSTANCE = Mappers.getMapper(WorkCenterLineAdjustSummaryDEConvert.class);

    List<WorkCenterLineAdjustSummaryExcelDTO> toExcelDTO(List<WorkCenterLineAdjustSummaryDTO> dtos);

    WorkCenterLineAdjustSummaryExcelDTO toExcelDTO(WorkCenterLineAdjustSummaryDTO dto);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    WorkCenterLineAdjustSummary lovWorkCenterVOToEntity(LovWorkCenterVO lovWorkCenterVO, @MappingTarget WorkCenterLineAdjustSummary entity);

    @BeanMapping(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mappings({
        @Mapping(target = "id", ignore = true)
    })
    WorkCenterLineAdjustSummary saveDTOtoEntity(WorkCenterLineAdjustSummarySaveDTO saveDTO, @MappingTarget WorkCenterLineAdjustSummary entity);
}
