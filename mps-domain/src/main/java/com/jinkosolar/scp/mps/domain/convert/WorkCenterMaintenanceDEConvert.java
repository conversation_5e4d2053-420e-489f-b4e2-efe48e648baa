package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WorkCenterMaintenanceDTO;
import com.jinkosolar.scp.mps.domain.entity.WorkCenterMaintenance;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WorkCenterMaintenanceDEConvert extends BaseDEConvert<WorkCenterMaintenanceDTO, WorkCenterMaintenance> {
    WorkCenterMaintenanceDEConvert INSTANCE = Mappers.getMapper(WorkCenterMaintenanceDEConvert.class);

    void resetWorkCenterMaintenance(WorkCenterMaintenanceDTO workCenterMaintenanceDTO, @MappingTarget WorkCenterMaintenance workCenterMaintenance);
}