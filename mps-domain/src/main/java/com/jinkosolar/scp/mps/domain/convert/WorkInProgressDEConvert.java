package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WorkInProgressDTO;
import com.jinkosolar.scp.mps.domain.entity.WorkInProgress;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WorkInProgressDEConvert extends BaseDEConvert<WorkInProgressDTO, WorkInProgress> {
    WorkInProgressDEConvert INSTANCE = Mappers.getMapper(WorkInProgressDEConvert.class);

    void resetWorkInProgress(WorkInProgressDTO workInProgressDTO, @MappingTarget WorkInProgress workInProgress);
}