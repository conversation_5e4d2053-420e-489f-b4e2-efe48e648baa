package com.jinkosolar.scp.mps.domain.convert;

import com.ibm.scp.common.api.convert.BaseDEConvert;
import com.jinkosolar.scp.mps.domain.dto.WorkOrderStatusDTO;
import com.jinkosolar.scp.mps.domain.entity.WorkOrderStatus;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WorkOrderStatusDEConvert extends BaseDEConvert<WorkOrderStatusDTO, WorkOrderStatus> {
    WorkOrderStatusDEConvert INSTANCE = Mappers.getMapper(WorkOrderStatusDEConvert.class);

    void resetWorkOrderStatus(WorkOrderStatusDTO workOrderStatusDTO, @MappingTarget WorkOrderStatus workOrderStatus);
}