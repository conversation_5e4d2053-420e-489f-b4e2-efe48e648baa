package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.math.BigDecimal;  


@ApiModel("入库效率档位数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InStockEfficiencyLevelDTO extends BaseDTO implements Serializable {
    /**
     * ID自增列
     */
    @ApiModelProperty("ID自增列")
    @ExcelProperty(value = "ID自增列")  
    private Long id;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")  
    private String factoryCode;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")  
    private String itemCode;
    /**
     * 月平均目标值
     */
    @ApiModelProperty("月平均目标值")
    @ExcelProperty(value = "月平均目标值")  
    private BigDecimal averageTargetVal;
    /**
     * 档位值
     */
    @ApiModelProperty("档位值")
    @ExcelProperty(value = "档位值")  
    private BigDecimal levelVal;
    /**
     * 档位界限操作符
     */
    @ApiModelProperty("档位界限操作符")
    @ExcelProperty(value = "档位界限操作符")  
    private String operator;
}