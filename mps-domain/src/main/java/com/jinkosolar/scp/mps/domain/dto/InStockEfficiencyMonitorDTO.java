package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("入库效率监控报表")
public class InStockEfficiencyMonitorDTO implements Serializable {
    /**
     * 动态列名列表
     */
    @ExcelProperty(value = "动态列名列表")
    private List<String> dynamicColumnList;

    @ApiModelProperty("入庫效率統計列表")
    private List<InStockEfficiencyDTO> inStockEfficiencyDTO;


}
