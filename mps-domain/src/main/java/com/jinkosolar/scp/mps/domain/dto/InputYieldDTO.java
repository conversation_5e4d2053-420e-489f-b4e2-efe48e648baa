package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("拉晶实际单产表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InputYieldDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID，自增
     */
    @ApiModelProperty("主键ID，自增")
    @ExcelProperty(value = "主键ID，自增")  
    private Long id;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String site;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshop;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")  
    private String area;
    /**
     * 炉台设备号
     */
    @ApiModelProperty("炉台设备号")
    @ExcelProperty(value = "炉台设备号")  
    private String equipmentNo;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")  
    private String productionNo;
    /**
     * 热场尺寸
     */
    @ApiModelProperty("热场尺寸")
    @ExcelProperty(value = "热场尺寸")  
    private String furnaceSize;
    /**
     * 产量
     */
    @ApiModelProperty("产量")
    @ExcelProperty(value = "产量")  
    private BigDecimal output;
    /**
     * 耗时
     */
    @ApiModelProperty("耗时")
    @ExcelProperty(value = "耗时")  
    private BigDecimal useday;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "日期")  
    private LocalDateTime date;


    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "item_code")
    private String itemCode;
}