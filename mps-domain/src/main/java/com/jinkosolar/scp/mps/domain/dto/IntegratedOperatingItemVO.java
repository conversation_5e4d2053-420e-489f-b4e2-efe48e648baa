package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * [说明]一体化开工率报表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "一体化开工率报表DTO对象", description = "DTO对象")
public class IntegratedOperatingItemVO {


    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterCode;
    
    /**
     * 处理后工作中心
     */
    @ApiModelProperty(value = "处理后工作中心")
    private String workCenterNewCode;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDateTime dayTime;

    /**
     * 产线总数
     */
    @ApiModelProperty(value = "产线总数")
    private Integer productionLineNum;

    /**
     * 开线数
     */
    @ApiModelProperty(value = "开线数")
    private BigDecimal attendanceNum;

    /**
     * 出勤模式
     */
    @ApiModelProperty(value = "出勤模式")
    private String attendanceType;
    

}
