package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;


/**
 * [说明]一体化开工率报表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "一体化开工率报表DTO对象", description = "DTO对象")
public class IntegratedOperatingRateReportDTO extends BaseDTO {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private String year;
    
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    
    /**
     * 工段
     */
    @ApiModelProperty(value = "工段")
    private Long workNumId;
    
    /**
     * 工段名称
     */
    @ApiModelProperty(value = "工段名称")
    private String workNumIdName;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterId;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterCode;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterName;
    
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeCode;
    
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    
    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id")
    private Long areaId;
    
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaIdName;
    
    /**
     * 车间Id
     */
    @ApiModelProperty(value = "车间Id")
    private Long workShopId;
    
    /**
     * 车间代码
     */
    @ApiModelProperty(value = "车间代码")
    private String workShopCode;
    
    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String workShopName;
    
    /**
     * 合计
     */
    @ApiModelProperty(value = "合计")
    private BigDecimal quantity;

    @Data
    public static class Result {
        @ApiModelProperty("年")
        private String year;

        @ApiModelProperty("工段")
        private List<IntegratedOperatingRateReportDTO.WorkNum> workNums;
    }

    @Data
    public static class WorkNum {
        @ApiModelProperty("工段")
        private String workNumIdName;

        @ApiModelProperty("排序")
        private Integer sort;

        @ApiModelProperty("区域")
        private List<IntegratedOperatingRateReportDTO.Type> types;
    }

    @Data
    public static class Type {
        @ApiModelProperty("类型")
        private String typeName;

        @ApiModelProperty("区域")
        private List<IntegratedOperatingRateReportDTO.Area> areas;
    }

    @Data
    public static class Area {
        @ApiModelProperty("区域")
        private Long areaId;
        private String areaIdName;
        private Integer sort;
        @ApiModelProperty("数据")
        private List<IntegratedOperatingRateReportDTO.MonthDTO> months;
    }

    @Data
    public static class MonthDTO {

        @ApiModelProperty("车间编号")
        private String workShopCode;

        @ApiModelProperty("车间")
        private String workShopName;
        /**
         * 一月
         */
        @ApiModelProperty("一月")
        private BigDecimal m1 = BigDecimal.ZERO;
        private String m1Str;
        /**
         * 二月
         */
        @ApiModelProperty("二月")
        private BigDecimal m2 = BigDecimal.ZERO;
        private String m2Str;
        /**
         * 三月
         */
        @ApiModelProperty("三月")
        private BigDecimal m3 = BigDecimal.ZERO;
        private String m3Str;
        /**
         * 四月
         */
        @ApiModelProperty("四月")
        private BigDecimal m4 = BigDecimal.ZERO;
        private String m4Str;
        /**
         * 五月
         */
        @ApiModelProperty("五月")
        private BigDecimal m5 = BigDecimal.ZERO;
        private String m5Str;
        /**
         * 六月
         */
        @ApiModelProperty("六月")
        private BigDecimal m6 = BigDecimal.ZERO;
        private String m6Str;
        /**
         * 七月
         */
        @ApiModelProperty("七月")
        private BigDecimal m7 = BigDecimal.ZERO;
        private String m7Str;
        /**
         * 八月
         */
        @ApiModelProperty("八月")
        private BigDecimal m8 = BigDecimal.ZERO;
        private String m8Str;
        /**
         * 九月
         */
        @ApiModelProperty("九月")
        private BigDecimal m9 = BigDecimal.ZERO;
        private String m9Str;
        /**
         * 十月
         */
        @ApiModelProperty("十月")
        private BigDecimal m10 = BigDecimal.ZERO;
        private String m10Str;
        /**
         * 十一月
         */
        @ApiModelProperty("十一月")
        private BigDecimal m11 = BigDecimal.ZERO;
        private String m11Str;
        /**
         * 十二月
         */
        @ApiModelProperty("十二月")
        private BigDecimal m12 = BigDecimal.ZERO;
        private String m12Str;

        @ApiModelProperty("1季度")
        private BigDecimal q1;
        private String q1Str;

        @ApiModelProperty("2季度")
        private BigDecimal q2;
        private String q2Str;

        @ApiModelProperty("3季度")
        private BigDecimal q3;
        private String q3Str;

        @ApiModelProperty("4季度")
        private BigDecimal q4;
        private String q4Str;

        @ApiModelProperty("年度汇总")
        private BigDecimal yearTotal;
        private String yearTotalStr;

        @ApiModelProperty("备注")
        private String remark;

    }

}
