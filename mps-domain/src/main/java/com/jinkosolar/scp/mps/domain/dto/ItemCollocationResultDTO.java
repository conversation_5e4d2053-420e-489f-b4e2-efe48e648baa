package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.time.LocalDate;


@ApiModel("MRP物料搭配结果表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ItemCollocationResultDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")  
    private String sapOrderNum;
    /**
     * SAP行号
     */
    @ApiModelProperty("SAP行号")
    @ExcelProperty(value = "SAP行号")  
    private Long sapLineNum;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")  
    private String factoryCode;
    /**
     * 车间编码
     */
    @ApiModelProperty("车间编码")
    @ExcelProperty(value = "车间编码")  
    private String workShopCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private String workCenterCode;
    /**
     * 组件编码
     */
    @ApiModelProperty("组件编码")
    @ExcelProperty(value = "组件编码")  
    private String componentCode;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")  
    private String itemCode;
    /**
     * 物料产品结构名称
     */
    @ApiModelProperty("物料产品结构名称")
    @ExcelProperty(value = "物料产品结构名称")  
    private String structureName;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String materialDesc;
    /**
     * 供应厂商
     */
    @ApiModelProperty("供应厂商")
    @ExcelProperty(value = "供应厂商")  
    private String supplierBrand;
    /**
     * 玻璃属性
     */
    @ApiModelProperty("玻璃属性")
    @ExcelProperty(value = "玻璃属性")  
    private String glassProperty;
    /**
     * 线盒属性
     */
    @ApiModelProperty("线盒属性")
    @ExcelProperty(value = "线盒属性")  
    private String cableBoxAttribute;
}