package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("jke切换计划数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JkeSwitchingPlanDTO extends BaseDTO implements SwitchPlanInterface,Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String versionNumber;
    /**
     * 炉台
     */
/*    @ApiModelProperty("炉台")
    @ExcelProperty(value = "炉台")  
    private Long equipmentId;*/
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private Long workCenterId;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")  
    private Long workshopId;
    /**
     * jke
     */
    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")  
    private Long jkeId;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")  
    private LocalDateTime switchStartTime;
    /**
     * 切换结束时间
     */
    @ApiModelProperty("切换结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换结束时间")  
    private LocalDateTime switchEndTime;
    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    @ExcelProperty(value = "顺序")  
    private Integer sequenceNo;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")  
    private Long dataType;

    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    private Integer equipmentQuantity;

    @Override
    public LocalDate getSwitchingStartDate() {
        return switchStartTime.toLocalDate();
    }

    @Override
    public LocalDate getSwitchingEndDate() {
        return switchEndTime.toLocalDate();
    }

    @Override
    public String getDecuKey() {
        return null;
    }

    @Override
    public String getDecuKey(ManufactureBomDTO bomDTO) {
        return StringUtils.join(new Object[]{workshopId,bomDTO.getThermalField(),bomDTO.getProductionId(), bomDTO.getDirectionalId(),
                jkeId},":");
    }

    @Override
    public Long getAnalysisId() {
        return jkeId;
    }

    @Override
    public Integer getMachineNumber() {
        return equipmentQuantity;
    }
}