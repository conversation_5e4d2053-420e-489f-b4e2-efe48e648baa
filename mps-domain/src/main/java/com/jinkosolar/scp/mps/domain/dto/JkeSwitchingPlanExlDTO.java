package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("jke切换计划数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JkeSwitchingPlanExlDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String versionNumber;
    /**
     * 炉台
     */
   /* @ApiModelProperty("炉台")
    @ExcelProperty(value = "炉台")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CRYSTAL_HEARTH, queryColumns = {"lovLineId"},
            from = {"lovValue"}, to = {"equipmentCode"})
    private Long equipmentId;

    @ApiModelProperty("炉台")
    @ExcelProperty(value = "炉台")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_CRYSTAL_HEARTH, queryColumns = {"lovValue"},
            from = {"lovLineId","attribute1"}, to = {"equipmentId","workCenterIdByEquipmentCode"})
    private String equipmentCode;

    @ApiModelProperty("炉台对应工作中心")
    @ExcelProperty(value = "炉台对应工作中心")
    private Long workCenterIdByEquipmentCode;*/
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","attribute1","lovName"}, to = {"workCenterCode","workshopId","workCenterName"})
    private Long workCenterId;

    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workCenterId"})
    private String workCenterCode;

    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId","lovValue"}, to = {"workCenterId","workCenterCode"})
    private String workCenterName;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"workshopIdName"})
    private Long workshopId;

    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    @Translate(unTranslate = true, DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"workshopId"}, required = true)
    private String workshopIdName;
    /**
     * jke
     */
    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(DictType = LovHeaderCodeConstant.MPS_JKE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"jke1IdName"})
    private Long jke1Id;

    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_JKE, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"jke1Id"}, required = true)
    private String jke1IdName;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")
    @Translate( required = true)
    private LocalDateTime switchStart1Time;
    /**
     * 切换结束时间
     */
    @ApiModelProperty("切换结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换结束时间")
    private LocalDateTime switchEnd1Time;

    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    @ExcelProperty(value = "炉台数")
    @Translate(required = true)
    private Integer equipment1Quantity;

    /**
     * jke
     */
    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(DictType = LovHeaderCodeConstant.MPS_JKE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"jke2IdName"})
    private Long jke2Id;

    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_JKE, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"jke2Id"})
    private String jke2IdName;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")
    private LocalDateTime switchStart2Time;
    /**
     * 切换结束时间
     */
    @ApiModelProperty("切换结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换结束时间")
    private LocalDateTime switchEnd2Time;

    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    @ExcelProperty(value = "炉台数")
    private Integer equipment2Quantity;

    /**
     * jke
     */
    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(DictType = LovHeaderCodeConstant.MPS_JKE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"jke3IdName"})
    private Long jke3Id;

    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_JKE, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"jke3Id"})
    private String jke3IdName;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")
    private LocalDateTime switchStart3Time;
    /**
     * 切换结束时间
     */
    @ApiModelProperty("切换结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换结束时间")
    private LocalDateTime switchEnd3Time;

    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    @ExcelProperty(value = "炉台数")
    private Integer equipment3Quantity;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    @ExcelProperty(value = "顺序")  
    private Integer sequenceNo;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovValue"},
            from = {"lovLineId","lovValue"}, to = {"dataType","dataTypeCode"}, required = true)
    private String dataTypeName;
    @ExcelIgnore
    @ApiModelProperty("数据分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataType;

    @ExcelIgnore
    @ApiModelProperty("数据分类Code")
    private String dataTypeCode;
}