package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@ApiModel("kpi管理数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KpiDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")
    private String kpiArea;
    /**
     * kpi
     */
    @ApiModelProperty("kpi")
    @ExcelProperty(value = "kpi")
    private String kpiName;


    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    @Dict(headerCode = "ATTR_TYPE_006_ATTR_1000")
    @ExcelProperty(value = "产品id")
    private Long productId;
    @ApiModelProperty("产品名称")
    @ExcelProperty(value = "产品id")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='ATTR_TYPE_006_ATTR_1000'", targetFieldName = "productId")
    private String productIdName;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")
    private Long workCenterId;
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心编码")
    @ImportExConvert(tableName = "mps_work_center", fkColumnName = "work_center_code", valueColumnName = "id", targetFieldName = "workCenterId")
    private String workCenterCode;
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;

    /**
     * kpi
     */
    @ApiModelProperty("kpi")
    private String columnName;


    /**
     * kpi
     */
    @ApiModelProperty("kpi")
    private String columnValue;

    Map<String,String> detail;

    List<String> columnNameList;

    /**
     * 工艺
     */
    @ApiModelProperty(value = "工艺")
    private String craft;
}
