package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;


/**
 * kpi管理实体
 *
 * @author: gencode 2024-05-13 13:56:39
 */
@ApiModel("kpi详情管理实体")
@ToString
@Data
public class KpiDetailDTO extends BasePO implements Serializable {
    /**
     * 主键
     */

    @ApiModelProperty("主键")
    private Long id;
    /**
     * 工作中心id
     */
    @ApiModelProperty("主数据id")
    private Long parentId;

    /**
     * kpi
     */
    @ApiModelProperty("属性名称")
    private String columnName;


    /**
     * kpi
     */
    @ApiModelProperty("属性值")
    private String columnValue;


}
