package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

import static com.jinkosolar.scp.mps.domain.constant.MpsLovConstant.ATTR_TYPE_006_ATTR_1000;
import static com.jinkosolar.scp.mps.domain.constant.MpsLovConstant.MPS_SESTEM_AREA;


@ApiModel("中长期电池损耗比例数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LongTermBatteryLossRatioDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 中长期统计区域
     */
    @ApiModelProperty("中长期统计区域")
    @ExcelProperty(value = "中长期统计区域")
    @Translate(DictType = MPS_SESTEM_AREA, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sestemAreaName"})
    private Long sestemAreaId;

    /**
     * 中长期统计区域名称
     */
    @ApiModelProperty("中长期统计区域名称")
    @ExcelProperty(value = "中长期统计区域名称")
    private String sestemAreaName;
    /**
     * 电池产品
     */
    @ApiModelProperty("电池产品")
    @ExcelProperty(value = "电池产品")
    @Translate(DictType = ATTR_TYPE_006_ATTR_1000, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"cellProductTypeName"})
    private Long cellProductTypeId;
    /**
     * 电池产品名称
     */
    @ApiModelProperty("电池产品名称")
    @ExcelProperty(value = "电池产品名称")
    private String cellProductTypeName;
    /**
     * 电池损耗比例
     */
    @ApiModelProperty("电池损耗比例")
    @ExcelProperty(value = "电池损耗比例")
    private BigDecimal lossRatio;
    /**
     * 备用1
     */
    @ApiModelProperty("备用1")
    @ExcelProperty(value = "备用1")
    private String attribute1;
    /**
     * 备用2
     */
    @ApiModelProperty("备用2")
    @ExcelProperty(value = "备用2")
    private String attribute2;
    /**
     * 备用3
     */
    @ApiModelProperty("备用3")
    @ExcelProperty(value = "备用3")
    private String attribute3;
    /**
     * 备用4
     */
    @ApiModelProperty("备用4")
    @ExcelProperty(value = "备用4")
    private String attribute4;
    /**
     * 备用5
     */
    @ApiModelProperty("备用5")
    @ExcelProperty(value = "备用5")
    private String attribute5;
}