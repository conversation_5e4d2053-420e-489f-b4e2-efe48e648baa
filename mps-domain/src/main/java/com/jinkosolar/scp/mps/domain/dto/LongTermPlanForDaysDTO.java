package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static com.jinkosolar.scp.mps.domain.constant.MpsLovConstant.MPS_SESTEM_AREA;


@ApiModel("中长期每月规划天数数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LongTermPlanForDaysDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 中长期统计区域
     */
    @ApiModelProperty("中长期统计区域")
    @ExcelProperty(value = "中长期统计区域")
    @Translate(DictType = MPS_SESTEM_AREA, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sestemAreaName"})
    private Long sestemAreaId;
    /**
     * 中长期统计区域名称
     */
    @ApiModelProperty("中长期统计区域名称")
    @ExcelProperty(value = "中长期统计区域名称")
    private String sestemAreaName;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 年月
     */
    @ApiModelProperty("年月")
    @ExcelProperty(value = "年月")
    private LocalDate month;
    /**
     * 天数
     */
    @ApiModelProperty("天数")
    @ExcelProperty(value = "天数")
    private BigDecimal monthValue;
    /**
     * 备用1
     */
    @ApiModelProperty("备用1")
    @ExcelProperty(value = "备用1")
    private String attribute1;
    /**
     * 备用2
     */
    @ApiModelProperty("备用2")
    @ExcelProperty(value = "备用2")
    private String attribute2;
    /**
     * 备用3
     */
    @ApiModelProperty("备用3")
    @ExcelProperty(value = "备用3")
    private String attribute3;
    /**
     * 备用4
     */
    @ApiModelProperty("备用4")
    @ExcelProperty(value = "备用4")
    private String attribute4;
    /**
     * 备用5
     */
    @ApiModelProperty("备用5")
    @ExcelProperty(value = "备用5")
    private String attribute5;

    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new HashMap<>();
}