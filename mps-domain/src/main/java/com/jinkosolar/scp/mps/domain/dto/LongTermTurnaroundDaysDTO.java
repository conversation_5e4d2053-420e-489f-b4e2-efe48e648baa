package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

import static com.jinkosolar.scp.mps.domain.constant.MpsLovConstant.LOCATION;


@ApiModel("中长期安全周转天数数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LongTermTurnaroundDaysDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")
    @Translate(DictType = LOCATION, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"domesticOverseaName"})
    private Long domesticOverseaId;

    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称")
    private String domesticOverseaName;
    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 周转天数
     */
    @ApiModelProperty("周转天数")
    @ExcelProperty(value = "周转天数")
    private BigDecimal turnaroundDays;
    /**
     * 备用1
     */
    @ApiModelProperty("备用1")
    @ExcelProperty(value = "备用1")
    private String attribute1;
    /**
     * 备用2
     */
    @ApiModelProperty("备用2")
    @ExcelProperty(value = "备用2")
    private String attribute2;
    /**
     * 备用3
     */
    @ApiModelProperty("备用3")
    @ExcelProperty(value = "备用3")
    private String attribute3;
    /**
     * 备用4
     */
    @ApiModelProperty("备用4")
    @ExcelProperty(value = "备用4")
    private String attribute4;
    /**
     * 备用5
     */
    @ApiModelProperty("备用5")
    @ExcelProperty(value = "备用5")
    private String attribute5;
}