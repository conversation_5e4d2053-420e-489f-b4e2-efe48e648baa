package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * [说明]一体化开工率报表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "一体化开工率报表DTO对象", description = "DTO对象")
public class LovWorkCenterVO {


    /**
     * 工段
     */
    @ApiModelProperty(value = "工段")
    private Long workNumId;

    /**
     * 工段名称
     */
    @ApiModelProperty(value = "工段名称")
    private String workNumIdName;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterId;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterCode;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterName;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeCode;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id")
    private Long areaId;
    
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaIdName;

    /**
     * 中长期统计区域id
     */
    @ApiModelProperty(value = "中长期统计区域id")
    private Long sestemAreaId;

    /**
     * 中长期统计区域名称
     */
    @ApiModelProperty(value = "中长期统计区域名称")
    private String sestemAreaIdName;
    
    /**
     * 车间Id
     */
    @ApiModelProperty(value = "车间Id")
    private Long workShopId;
    
    /**
     * 车间代码
     */
    @ApiModelProperty(value = "车间代码")
    private String workShopCode;
    
    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String workShopName;

    /**
     * 台数
     */
    @ApiModelProperty(value = "产线/机器 总数")
    private Integer productionLineNum;
    

}
