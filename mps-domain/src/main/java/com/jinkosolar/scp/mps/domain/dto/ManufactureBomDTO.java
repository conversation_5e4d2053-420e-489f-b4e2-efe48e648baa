package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.LovCustomBean;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;  


@ApiModel("制造BOM数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ManufactureBomDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂id")
    @ExcelProperty(value = "工厂id")
    @Translate(DictType = MpsLovConstant.FACTORY, queryColumns = {"lovLineId"},
            from = {"lovName","lovValue"}, to = {"factoryIdName","factoryIdCode"})
    private Long factoryId;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private String factoryIdName;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private String factoryIdCode;
    /**
     * 车间id
     */
    @ApiModelProperty("车间id")
    @ExcelProperty(value = "车间id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovName","lovValue"}, to = {"workShopIdName","workShopIdCode"})
    private Long workShopId;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private String workShopIdName;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private String workShopIdCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(DictType = MpsLovConstant.WORK_CENTER, queryColumns = {"lovLineId"},from = {"lovName","lovValue"},
            to = {"workCenterIdName","workCenterIdCode"})
    private Long workCenterId;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private String workCenterIdName;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private String workCenterIdCode;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    @ExcelProperty(value = "产品id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"lovLineId"},
            from = {"lovValue"}, to = {"productTypeCode"})
    private Long productionId;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String productTypeCode;
    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    @Translate(DictType = LovHeaderCodeConstant.MPS_HOTFIELD, queryColumns = {"lovLineId"},
            from = {"lovName","lovValue"}, to = {"thermalFieldName","thermalFieldCode"})
    private String thermalField;
    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    private String thermalFieldName;
    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    private String thermalFieldCode;

    @ApiModelProperty("机台数")
    @ExcelProperty(value = "机台数")
    private Integer machineNum;

    /**
     * 产能
     */
    @ApiModelProperty("产能")
    @ExcelProperty(value = "产能")  
    private BigDecimal capacityNum;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")  
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @ExcelProperty(value = "结束时间")  
    private LocalDate endTime;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, to = {"modelClassificationName"},unTranslate = false)
    private String modelClassification;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    private String modelClassificationName;
    /**
     * 产线/机器 总数
     */
    @ApiModelProperty("产线/机器 总数")
    @ExcelProperty(value = "产线/机器 总数")  
    private Integer productionLineNum;
    /**
     * 坩埚高度
     */
    @ApiModelProperty("坩埚高度")
    @ExcelProperty(value = "坩埚高度")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1300, queryColumns = {"lovLineId"},
            from = {"lovValue"}, to = {"heightIdCode"})
    private Long heightId;
    /**
     * 配方
     */
    @ApiModelProperty("坩埚高度")
    @ExcelProperty(value = "坩埚高度")
    private String heightIdCode;
    /**
     * 厂家
     */
    @ApiModelProperty("厂家")
    @ExcelProperty(value = "厂家")  
    private String vendorBrand;
    /**
     * 等级_lovID
     */
    @ApiModelProperty("等级_lovID")
    @ExcelProperty(value = "等级_lovID")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CRYSTAL_GGCLASS)
    private Long gradeId;
    /**
     * 等级_lovIDName
     */
    @ApiModelProperty("等级_lovIDName")
    @ExcelProperty(value = "等级_lovIDName")
    private String gradeIdName;

    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"jkeIdName"}, fields = {"jkeId"}, queryColumns = {"lovLineId"})
    private Long jkeId;
    /**
     * 配方
     */
    @ApiModelProperty("jke")
    @ExcelProperty(value = "jke")
    private String jkeIdName;

    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"lovLineId"},
            from = {"lovName","lovValue"}, to = {"formulaName","formulaIdCode"})
    private Long formulaId;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    private String formulaName;

    @ApiModelProperty("炉型")
    @ExcelProperty(value = "炉型")
    @Translate(DictType = LovHeaderCodeConstant.FURNACE_PROFILE, queryColumns = {"lovLineId"},
            from = {"lovValue"}, to = {"furnaceTypeName"})
    private Long furnaceType;

    /**
     * 炉型
     */
    @ApiModelProperty("炉型名称")
    @ExcelProperty(value = "炉型名称")
    private String furnaceTypeName;

    /**
     * 配方code
     */
    @ApiModelProperty("配方code")
    @ExcelProperty(value = "配方code")
    private String formulaIdCode;
    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directionalId;

    /**
     * 是否定向名称
     */
    @ApiModelProperty("是否定向名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"directionalName"},
            from = {"lovLineId"}, to = {"directionalId"})
    private String directionalName;

    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sizeName"})
    private Long sizeId;

    /**
     * 尺寸名称
     */
    @ApiModelProperty("尺寸名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"sizeName"},
            from = {"lovLineId"}, to = {"sizeId"})
    private String sizeName;

    /**
     * 高低阻
     */
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"crystalTypeName"})
    @ApiModelProperty("高低阻")
    private Long crystalTypeId;

    /**
     * 高低阻名称
     */
    @ApiModelProperty("高低阻名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"crystalTypeName"},
            from = {"lovLineId"}, to = {"crystalTypeId"})
    private String crystalTypeName;

    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"siliconSupplierName"})
    private Long siliconSupplierId;

    /**
     * 硅料供应商名称
     */
    @ApiModelProperty("硅料供应商名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"siliconSupplierName"},
            from = {"lovLineId"}, to = {"siliconSupplierId"})
    private String siliconSupplierName;


    @ApiModelProperty("标准单产")
    private BigDecimal standardYield;

    @ApiModelProperty("排产单产")
    private BigDecimal plannedYield;

    @ApiModelProperty("坩埚等级扣减分析")
    private String crucibleAnalysisId;

    @ApiModelProperty("坩埚扣减")
    private BigDecimal crucibleAnalysisDeduction;

    @ApiModelProperty("JKE扣减分析")
    private String jkeAnalysisId;

    @ApiModelProperty("JKE扣减")
    private BigDecimal jkeAnalysisDeduction;

    @ApiModelProperty("炉型扣减分析")
    private String furnaceAnalysisId;

    @ApiModelProperty("炉型扣减")
    private BigDecimal furnaceAnalysisDeduction;

    @ApiModelProperty("配方扣减分析")
    private String formulaAnalysisId;

    @ApiModelProperty("配方扣减")
    private BigDecimal formulaAnalysisDeduction;

    @ApiModelProperty("留埚扣减分析")
    private String furnaceCrucibleAnalysisId;

    @ApiModelProperty("留埚扣减")
    private BigDecimal furnaceCrucibleAnalysisDeduction;

    @ApiModelProperty("爬坡扣减")
    private BigDecimal rampDeduction;

    @ApiModelProperty("特殊扣减1")
    private BigDecimal specialDeduction1;
    @ApiModelProperty("特殊扣减2")
    private BigDecimal specialDeduction2;
    @ApiModelProperty("特殊扣减3")
    private BigDecimal specialDeduction3;
    @ApiModelProperty("坩埚鼓包扣减")
    private BigDecimal crucibleBulgeDeduction;

}