package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.LovCustomBean;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * [说明]制造BOM报表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-11-26
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "制造BOM报表DTO对象", description = "DTO对象")
public class ManufactureBomReportDTO extends BaseDTO {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    /**
     * 工厂id
     */
    @ApiModelProperty(value = "工厂id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"factoryName"})
    private Long factoryId;
    
    /**
     * 工厂code
     */
    @ApiModelProperty(value = "工厂code")
    private String factoryIdCode;

    @ApiModelProperty(value = "工厂code")
    private String factoryName;
    
    /**
     * 车间id
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workShopIdCode","workShopName"})
    @ApiModelProperty(value = "车间id")
    private Long workShopId;
    
    /**
     * 车间code
     */
    @ApiModelProperty(value = "车间code")
    private String workShopIdCode;

    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String workShopName;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovName","attribute8"}, to = {"workCenterName","workCenterShortName"})
    private Long workCenterId;
    @ApiModelProperty(value = "工作中心简称")
    private String workCenterShortName;
    @ApiModelProperty(value = "工作中心名称")
    private String workCenterName;

    /**
     * 工作中心code
     */
    @ApiModelProperty(value = "工作中心code")
    private String workCenterIdCode;
    
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long productionId;
    
    /**
     * 产品code
     */
    @ApiModelProperty(value = "产品code")
    private String productIdCode;
    
    /**
     * 热场
     */
    @ApiModelProperty(value = "热场")
    private String thermalField;
    
    /**
     * 热场
     */
    @ApiModelProperty(value = "热场")
    private String thermalFieldCode;
    
    /**
     * 产能
     */
    @ApiModelProperty(value = "产能")
    private BigDecimal capacityNum;
    
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;
    
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;
    
    /**
     * 模型分类
     */
    @ApiModelProperty(value = "模型分类")
    private String modelClassification;
    
    /**
     * 模型分类名称
     */
    @ApiModelProperty(value = "模型分类名称")
    private String modelClassificationName;
    
    /**
     * 产线/机器 总数
     */
    @ApiModelProperty(value = "产线/机器 总数")
    private Integer productionLineNum;
    
    /**
     * 坩埚高度
     */
    @ApiModelProperty(value = "坩埚高度")
    private Long heightId;
    
    /**
     * 高度code
     */
    @ApiModelProperty(value = "高度code")
    private String heightIdCode;
    
    /**
     * 厂家
     */
    @ApiModelProperty(value = "厂家")
    private String vendorBrand;
    
    /**
     * 等级_lovID
     */
    @ApiModelProperty(value = "等级_lovID")
    private Long gradeId;
    
    /**
     * 等级code
     */
    @ApiModelProperty(value = "等级code")
    private String gradeIdCode;
    
    /**
     * 配方
     */
    @ApiModelProperty(value = "配方")
    private Long formulaId;
    
    /**
     * 配方code
     */
    @ApiModelProperty(value = "配方code")
    private String formulaIdCode;
    
    /**
     * 定向
     */
    @ApiModelProperty(value = "定向")
    private Long directionalId;
    
    /**
     * 尺寸
     */
    @ApiModelProperty(value = "尺寸")
    private Long sizeId;
    
    /**
     * 高低阻
     */
    @ApiModelProperty(value = "高低阻")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"crystalTypeName"}, fields = {"crystalTypeId"}, queryColumns = {"lovLineId"})
    private Long crystalTypeId;
    
    /**
     * 硅料供应商
     */
    @ApiModelProperty(value = "硅料供应商")
    private Long siliconSupplierId;
    
    /**
     * 定向名称
     */
    @ApiModelProperty(value = "定向名称")
    private String directionalName;
    
    /**
     * 尺寸名称
     */
    @ApiModelProperty(value = "尺寸名称")
    private String sizeName;
    
    /**
     * 高低阻名称
     */
    @ApiModelProperty(value = "高低阻名称")
    private String crystalTypeName;
    
    /**
     * 硅料供应商名称
     */
    @ApiModelProperty(value = "硅料供应商名称")
    private String siliconSupplierName;
    
    /**
     * 标准单产
     */
    @ApiModelProperty(value = "标准单产")
    private BigDecimal standardYield;
    
    /**
     * 排产单产
     */
    @ApiModelProperty(value = "排产单产")
    private BigDecimal plannedYield;
    
    /**
     * 坩埚等级扣减分析
     */
    @ApiModelProperty(value = "坩埚等级扣减分析")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"crucibleAnalysisIdName"}, fields = {"crucibleAnalysisId"}, queryColumns = {"lovLineId"})
    private String crucibleAnalysisId;
    private String crucibleAnalysisIdName;

    /**
     * 坩埚扣减
     */
    @ApiModelProperty(value = "坩埚扣减")
    private BigDecimal crucibleAnalysisDeduction;
    
    /**
     * JKE扣减分析
     */
    @ApiModelProperty(value = "JKE扣减分析")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"jkeAnalysisIdName"}, fields = {"jkeAnalysisId"}, queryColumns = {"lovLineId"})
    private String jkeAnalysisId;
    private String jkeAnalysisIdName;

    /**
     * JKE扣减
     */
    @ApiModelProperty(value = "JKE扣减")
    private BigDecimal jkeAnalysisDeduction;
    
    /**
     * 炉型扣减分析
     */
    @ApiModelProperty(value = "炉型扣减分析")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"furnaceAnalysisIdName"}, fields = {"furnaceAnalysisId"}, queryColumns = {"lovLineId"})
    private String furnaceAnalysisId;
    private String furnaceAnalysisIdName;

    /**
     * 炉型扣减
     */
    @ApiModelProperty(value = "炉型扣减")
    private BigDecimal furnaceAnalysisDeduction;

    /**
     * 炉型扣减-供应商
     */
    @ApiModelProperty(value = "炉型扣减-供应商")
    private String furnaceAnalysisDeductionSupplierName;
    
    /**
     * 配方扣减分析
     */
    @ApiModelProperty(value = "配方扣减分析")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"formulaAnalysisIdName"}, fields = {"formulaAnalysisId"}, queryColumns = {"lovLineId"})
    private String formulaAnalysisId;
    private String formulaAnalysisIdName;

    /**
     * 配方扣减
     */
    @ApiModelProperty(value = "配方扣减")
    private BigDecimal formulaAnalysisDeduction;
    
    /**
     * 留埚扣减分析
     */
    @ApiModelProperty(value = "留埚扣减分析")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"furnaceCrucibleAnalysisIdName"}, fields = {"furnaceCrucibleAnalysisId"}, queryColumns = {"lovLineId"})
    private String furnaceCrucibleAnalysisId;
    private String furnaceCrucibleAnalysisIdName;

    /**
     * 留埚扣减
     */
    @ApiModelProperty(value = "留埚扣减")
    private BigDecimal furnaceCrucibleAnalysisDeduction;
    
    /**
     * 爬坡扣减
     */
    @ApiModelProperty(value = "爬坡扣减")
    private BigDecimal rampDeduction;
    
    /**
     * 特殊扣减1
     */
    @ApiModelProperty(value = "特殊扣减1")
    private BigDecimal specialDeduction1;
    
    /**
     * 特殊扣减2
     */
    @ApiModelProperty(value = "特殊扣减2")
    private BigDecimal specialDeduction2;
    
    /**
     * 特殊扣减3
     */
    @ApiModelProperty(value = "特殊扣减3")
    private BigDecimal specialDeduction3;

    @ApiModelProperty("坩埚鼓包扣减")
    private BigDecimal crucibleBulgeDeduction;

}
