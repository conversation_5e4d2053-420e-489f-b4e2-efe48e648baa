package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;  


@ApiModel("年度计划制造BOM数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ManufactureBomYearDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂id")
    @ExcelProperty(value = "工厂id")  
    private Long factoryId;
    /**
     * 工厂code
     */
    @ApiModelProperty("工厂code")
    @ExcelProperty(value = "工厂code")  
    private String factoryIdCode;
    /**
     * 车间id
     */
    @ApiModelProperty("车间id")
    @ExcelProperty(value = "车间id")  
    private Long workShopId;
    /**
     * 车间code
     */
    @ApiModelProperty("车间code")
    @ExcelProperty(value = "车间code")  
    private String workShopIdCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private Long workCenterId;
    /**
     * 工作中心code
     */
    @ApiModelProperty("工作中心code")
    @ExcelProperty(value = "工作中心code")  
    private String workCenterIdCode;
    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    @ExcelProperty(value = "产品id")  
    private Long productionId;
    /**
     * 产品code
     */
    @ApiModelProperty("产品code")
    @ExcelProperty(value = "产品code")  
    private String productIdCode;
    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")  
    private String thermalField;
    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")  
    private String thermalFieldCode;
    /**
     * 产能
     */
    @ApiModelProperty("产能")
    @ExcelProperty(value = "产能")  
    private BigDecimal capacityNum;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")  
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @ExcelProperty(value = "结束时间")  
    private LocalDate endTime;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")  
    private String modelClassification;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private String modelClassificationName;
    /**
     * 产线/机器 总数
     */
    @ApiModelProperty("产线/机器 总数")
    @ExcelProperty(value = "产线/机器 总数")  
    private Integer productionLineNum;
    /**
     * 坩埚高度
     */
    @ApiModelProperty("坩埚高度")
    @ExcelProperty(value = "坩埚高度")  
    private Long heightId;
    /**
     * 高度code
     */
    @ApiModelProperty("高度code")
    @ExcelProperty(value = "高度code")  
    private String heightIdCode;
    /**
     * 厂家
     */
    @ApiModelProperty("厂家")
    @ExcelProperty(value = "厂家")  
    private String vendorBrand;
    /**
     * 等级_lovID
     */
    @ApiModelProperty("等级_lovID")
    @ExcelProperty(value = "等级_lovID")  
    private Long gradeId;
    /**
     * 等级code
     */
    @ApiModelProperty("等级code")
    @ExcelProperty(value = "等级code")  
    private String gradeIdCode;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"lovLineId"},
            from = {"lovName","lovValue"}, to = {"formulaName","formulaIdCode"})
    private Long formulaId;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    private String formulaName;
    /**
     * 配方code
     */
    @ApiModelProperty("配方code")
    @ExcelProperty(value = "配方code")
    private String formulaIdCode;

    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directionalId;

    /**
     * 是否定向名称
     */
    @ApiModelProperty("是否定向名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"directionalName"},
            from = {"lovLineId"}, to = {"directionalId"})
    private String directionalName;

    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sizeName"})
    private Long sizeId;

    /**
     * 尺寸名称
     */
    @ApiModelProperty("尺寸名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"sizeName"},
            from = {"lovLineId"}, to = {"sizeId"})
    private String sizeName;

    /**
     * 高低阻
     */
    @ApiModelProperty("高低阻")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"crystalTypeName"})
    private Long crystalTypeId;

    /**
     * 高低阻名称
     */
    @ApiModelProperty("高低阻名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"crystalTypeName"},
            from = {"lovLineId"}, to = {"crystalTypeId"})
    private String crystalTypeName;

    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"siliconSupplierName"})
    private Long siliconSupplierId;

    /**
     * 硅料供应商名称
     */
    @ApiModelProperty("硅料供应商名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"siliconSupplierName"},
            from = {"lovLineId"}, to = {"siliconSupplierId"})
    private String siliconSupplierName;
}