package com.jinkosolar.scp.mps.domain.dto;


import io.swagger.annotations.ApiModel;
import lombok.*;


@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MaterialCertificationDTO对象", description = "DTO对象")
public class MaterialCertificationDTO {
    /**
     * id
     */
    private Long id;

    /**
     * 认证国家
     */
    private String certificationCountry;

    /**
     * 产品族
     */
    private String productFamily;

    /**
     * 材料属性
     */
    private String materialProperty;

    /**
     * 属性值
     */
    private String attributeValue;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 来源名称
     */
    private String sourceName;


    public static MaterialCertificationDTO build(MaterialCertificationDTO materialCertificationDTO) {
        MaterialCertificationDTO builder = new MaterialCertificationDTO();
        builder.setCertificationCountry(materialCertificationDTO.getCertificationCountry());
        builder.setProductFamily(materialCertificationDTO.getProductFamily());
        builder.setMaterialProperty(materialCertificationDTO.getMaterialProperty());
        return builder;
    }
}
