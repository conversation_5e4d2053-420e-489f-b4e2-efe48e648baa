package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;                 
import java.time.LocalDateTime;

@ApiModel("物控指定订单使用物料属性值数据转换对象")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialDpDemandLinesAttributeDTO extends BaseDTO implements Serializable {
    /**
     * 物控指定汇流条_lov_id
     */
    @ApiModelProperty("物控指定汇流条_lov_id")
    @ExcelProperty(value = "物控指定汇流条_lov_id")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1800)
    private Long busBarId;
    /**
     * 物控指定汇流条_描述
     */
    @ApiModelProperty("物控指定汇流条_描述")
    @ExcelProperty(value = "物控指定汇流条_描述")
    @ImportExConvert(required = false,sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 and t2.lov_code='"+LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1800+"'", targetFieldName = "busBarId")
    private String busBarIdName;
    /**
     * DP需求_id
     */
    @ApiModelProperty("DP需求_id")
    @ExcelProperty(value = "DP需求_id")
    private Long dpLinesId;
    /**
     * DP需求订单
     */
    @ApiModelProperty("DP需求订单")
    @ExcelProperty(value = "DP需求订单")
    @ImportExConvert()
    private String dpSapOrderNo;
    /**
     * DP需求订单行号
     */
    @ApiModelProperty("DP需求订单行号")
    @ExcelProperty(value = "DP需求订单行号")
    @ImportExConvert()
    private String dpSapLineId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 工厂代码_lov_id
     */
    @ApiModelProperty("工厂代码_lov_id")
    @ExcelProperty(value = "工厂代码_lov_id")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    private Long factoryId;
    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")
    @ImportExConvert(required = true,sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 and t2.lov_code='"+LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE+"'", targetFieldName = "factoryId")
    private String factoryIdName;
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * 物控指定焊接_lov_id
     */
    @ApiModelProperty("物控指定焊接_lov_id")
    @ExcelProperty(value = "物控指定焊接_lov_id")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1100)
    private Long weldingId;
    /**
     * 物控指定焊接_描述
     */
    @ApiModelProperty("物控指定焊接_描述")
    @ExcelProperty(value = "物控指定焊接_描述")
    @ImportExConvert(required = false,sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 and t2.lov_code='"+LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1100+"'", targetFieldName = "weldingId")
    private String weldingIdName;
    /**
     * 工作中心_编码
     */
    @ApiModelProperty("工作中心_编码")
    @ExcelProperty(value = "工作中心_编码")
    @ImportExConvert(required = true,sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_value = ?1 and t2.lov_code='"+LovHeaderCodeConstant.SYS_WORKCENTER+"'", targetFieldName = "workCenterId")
    private String workCenterCode;
    /**
     * 工作中心_lov_id
     */
    @ApiModelProperty("工作中心_lov_id")
    @ExcelProperty(value = "工作中心_lov_id")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKCENTER)
    private Long workCenterId;
    /**
     * 工作中心_描述
     */
    @ApiModelProperty("工作中心_描述")
    @ExcelProperty(value = "工作中心_描述")
    private String workCenterIdName;

}

