package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.jinkosolar.scp.mps.domain.constant.Constants.EMPTY_CAPACITY_NAME;


@ApiModel("物料信息")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MaterialInfoForModuleProductionPlanDTO implements Serializable {

    @ApiModelProperty("产品结构")
    private Long structureId;

    @ApiModelProperty("产品结构")
    private String structureIdName;

    /**
     * 物料编码
     */
    @ApiModelProperty(name = "物料编码", notes = "")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    private String materialDesc;

    /**
     * 供应商编码
     */
    @ApiModelProperty(name = "供应商编码", notes = "")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNo;

    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")
    private String sapLineId;

    /**
     * 供应商品牌
     */
    @ApiModelProperty(name = "供应商品牌", notes = "")
    private String supplierBrand;


    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshopsCode;

    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;


    /**
     * 版型
     */
    @ApiModelProperty("版型")
    @ExcelProperty(value = "版型")
    private String planLayout;
    
    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    @ExcelProperty(value = "计划开始时间")
    private LocalDateTime planStartTime;

    /**
     * 材料搭配结果版本号
     */
    @ApiModelProperty(name = "材料搭配结果版本号", notes = "")
    private String dataVersion;

    /**
     * 是否历史材料搭配结果
     */
    @ApiModelProperty(name = "是否历史材料搭配结果", notes = "")
    private String historyFlag;

    /**
     * 组合号
     */
    @ApiModelProperty("组合号")
    private String accessoryGroup;

    /**
     * 是否继承
     */
    @ApiModelProperty(name = "是否继承", notes = "")
    private String extendsFlag;

    /**
     * 落产数量
     */
    @ApiModelProperty(name = "落产数量", notes = "")
    private BigDecimal orderQuantity;

    @ApiModelProperty(name = "需求来源", notes = "")
    private String demandSource;

    @ApiModelProperty(name = "是否存在合格供应商", notes = "")
    private String qualifiedVendorFlag;

    @ApiModelProperty("五瓦分档")
    private String fiveWattGrade;

    @ApiModelProperty(name = "不符合材料搭配标识", notes = "")
    private String noMatchDesc;

    /**
     * 需再调整物料
     */
    @ApiModelProperty(value = "需再调整物料")
    private String needAdjustFlag;

    public String getAccessoryKey() {
        if (EMPTY_CAPACITY_NAME.equals(this.getSapOrderNo())) {
            return this.getSapOrderNo() + "-" + this.getSapLineId() + "-" + this.getFactoryCode() + "-" + this.getWorkshopsCode() + "-" + this.getWorkCenterCode() + "-" + this.getPlanLayout();
        }
        return this.getSapOrderNo() + "-" + this.getSapLineId() + "-" + this.getFactoryCode() + "-" + this.getWorkshopsCode() + "-" + this.getWorkCenterCode() + "-" + null;
    }
}
