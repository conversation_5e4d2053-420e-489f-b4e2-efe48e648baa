package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 电池物料号匹配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 02:27:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池物料号匹配DTO对象", description = "DTO对象")
public class MaterielMatchHeaderDTO extends BaseDTO {

    private static final long serialVersionUID = 8747486727706496073L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    @ApiModelProperty(value = "月份_国内海外Id")
    private  String monthAndOversea;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;

    /**
     * 美学
     */
    @ApiModelProperty(value = "美学")
    private String aesthetics;

    /**
     * 透明双玻
     */
    @ApiModelProperty(value = "透明双玻")
    private String transparentDoubleGlass;

    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    private String specialArea;

    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String hTrace;

    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String pcsSourceType;

    /**
     * 硅片等级
     */
    @ApiModelProperty(value = "硅片等级")
    private String pcsSourceLevel;

    /**
     * 是否有特殊要求
     */
    @ApiModelProperty(value = "是否有特殊要求")
    private String isSpecialRequirements;

    /**
     * 网版厂家
     */
    @ApiModelProperty(value = "网版厂家")
    private String screenManufacturer;

    /**
     * 硅料厂家
     */
    @ApiModelProperty(value = "硅料厂家")
    private String siliconMaterialManufacturer;

    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String batteryManufacturer;

    /**
     * 银浆厂家
     */
    @ApiModelProperty(value = "银浆厂家")
    private String silverSlurryManufacturer;

    /**
     * 低阻
     */
    @ApiModelProperty(value = "低阻")
    private String lowResistance;

    /**
     * 硅片购买方式
     */
    @ApiModelProperty(value = "硅片购买方式")
    private String siliconWaferPurchaseMethod;

    /**
     * 需求地
     */
    @ApiModelProperty(value = "需求地")
    private String demandPlace;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String basePlace;

    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 排产日期
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体")
    private BigDecimal line;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;

    /**
     * PN型
     */
    @ApiModelProperty(value = "PN型")
    private String pOrN;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    private String category;

    /**
     * 单双面
     */
    @ApiModelProperty(value = "单双面")
    private String singleDoubleFace;

    /**
     * 主栅数
     */
    @ApiModelProperty(value = "主栅数")
    private String numberMainGrids;

    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    private String shardingMode;

    /**
     * 搭配状态
     */
    @ApiModelProperty(value = "搭配状态")
    private String matchStatus;

    @ApiModelProperty(value = "行数据")
    private List<MaterielMatchLineDTO> lines;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 加工类别
     */
    @ApiModelProperty(value = "加工类别")
    private String processCategory;

    /**
     * 电池物料料号
     */
    @ApiModelProperty(value = "电池物料料号")
    private String itemCode;

    /**
     * 物料说明
     */
    @ApiModelProperty(value = "物料说明")
    private String itemDesc;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 产品等级
     */
    @ApiModelProperty(value = "产品等级")
    private String productionGrade;

    private List<CellPlanLineDTO> planDTOList;
    private List<Map<String,MaterielMatchLineDTO>> matchLineListMap;
    /**
     * 请求来源
     */
    @ApiModelProperty(value = "请求来源")
    private  String requestFlag;
}
