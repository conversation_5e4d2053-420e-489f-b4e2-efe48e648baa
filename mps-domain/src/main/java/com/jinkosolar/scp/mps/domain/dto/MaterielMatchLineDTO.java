package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.util.BizException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;


/**
 * 电池料号匹配明细行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-01-07 06:04:11
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池料号匹配明细行DTO对象", description = "DTO对象")
public class MaterielMatchLineDTO extends BaseDTO {

    private static final long serialVersionUID = -4145515235277745476L;

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;

    /**
     * 电池物料号匹配ID
     */
    @ApiModelProperty(value = "电池物料号匹配ID")
    private Long headerId;

    /**
     * 排产日期
     */
    @ApiModelProperty(value = "排产日期")
    private LocalDate scheduleDate;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;

    /**
     * 电池料号
     */
    @ApiModelProperty(value = "电池料号")
    private String itemCode;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatusName;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String isCatchProduction;

    /**
     * 切换网版料号
     */
    @ApiModelProperty(value = "切换网版料号")
    private String screenPlateItemCode;

    /**
     * 切换结束数据
     */
    @ApiModelProperty(value = "切换结束数据")
    private LocalDateTime switchEndDate;

    /**
     * 切换开始时间
     */
    @ApiModelProperty(value = "切换开始时间")
    private LocalDateTime switchStartDate;

    /**
     * 线体
     */
    @ApiModelProperty(value = "线体")
    private BigDecimal line;

    /**
     * 电池片数量
     */
    @ApiModelProperty(value = "电池片数量")
    private BigDecimal cellQty;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * BOM替代项
     */
    @ApiModelProperty(value = "BOM替代项")
    private String alternateBomDesignator;

    /**
     * 备注
     */
    @ApiModelProperty(value = "年月格式日期")
    private String month;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String batteryType;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String basePlace;

    /**
     * 排产车间
     */
    @ApiModelProperty(value = "排产车间")
    private String workshop;

    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String workunit;

    /**
     * 特殊区域
     */
    @ApiModelProperty(value = "特殊区域")
    private String specialArea;
    /**
     * 手工指定标识(Y/N)
     */
    @ApiModelProperty(value = "手工指定标识(Y/N)")
    private String handWorkFlag;

    @Getter
    @AllArgsConstructor
    public enum MatchStatus {
        NON_MATCH("NON_MATCH", "未匹配"),
        APPOINT_MATCH("APPOINT_MATCH", "手工指定"),
        MATCHED("MATCHED", "系统匹配"),
        MULTI_MATCH("MULTI_MATCH", "多个匹配");

        String code;

        String name;

        public static String getNameByCode(String code) {
            return Arrays.stream(MatchStatus.values()).filter(i -> i.getCode().equals(code)).findFirst().orElseThrow(() -> new BizException("不存在")).getName();
        }
    }
}
