package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;                 
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MDA差异限定物料厂家明细数据转换对象", description = "DTO对象")
public class MdaFactoryItemDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")  
    private String sapOrderNum;
    /**
     * SAP行号
     */
    @ApiModelProperty("SAP行号")
    @ExcelProperty(value = "SAP行号")  
    private Integer sapLineNum;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")  
    private String productType;
    /**
     * 合同编码
     */
    @ApiModelProperty("合同编码")
    @ExcelProperty(value = "合同编码")  
    private String contractNum;
    /**
     * 限定厂家
     */
    @ApiModelProperty("限定厂家")
    @ExcelProperty(value = "限定厂家")  
    private Long mdaFactory;
    /**
     * 物料结构
     */
    @ApiModelProperty("物料结构")
    @ExcelProperty(value = "物料结构")  
    private String itemName;
}