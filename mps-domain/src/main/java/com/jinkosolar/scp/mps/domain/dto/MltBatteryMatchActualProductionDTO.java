package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期电池匹配-历史实投
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-历史实投DTO对象", description = "DTO对象")
public class MltBatteryMatchActualProductionDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String sourceType;

    /**
     * 销售订单号
     */
    @ApiModelProperty(value = "销售订单号")
    private String sapOrderNo;

    /**
     * 销售订单行
     */
    @ApiModelProperty(value = "销售订单行")
    private String sapLineId;

    /**
     * 计划版型（带主栅）
     */
    @ApiModelProperty(value = "计划版型（带主栅）")
    private String planLayout;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterCode;

    /**
     * 投产日期
     */
    @ApiModelProperty(value = "投产日期")
    private LocalDate productionDate;

    /**
     * 实投或取消数量
     */
    @ApiModelProperty(value = "实投或取消数量")
    private BigDecimal actualProductionQuantity;

    /**
     * 实投兆瓦数
     */
    @ApiModelProperty(value = "实投兆瓦数")
    private BigDecimal actualProductionQuantityMw;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 功率
     */
    @ApiModelProperty(value = "功率")
    private BigDecimal power;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 目的地区域
     */
    @ApiModelProperty(value = "目的地区域")
    private String destAreaNo;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 电池损耗比例
     */
    @ApiModelProperty(value = "电池损耗比例")
    private BigDecimal lossRate;

    /**
     * 历史实际投产兆瓦数
     */
    @ApiModelProperty(value = "历史实际投产兆瓦数")
    private BigDecimal quantityMw;
}
