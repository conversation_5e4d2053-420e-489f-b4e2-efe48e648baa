package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期电池匹配-定线规划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-定线规划DTO对象", description = "DTO对象")
public class MltBatteryMatchAlignmentPlanDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 计划版型（带主栅）
     */
    @ApiModelProperty(value = "计划版型（带主栅）")
    private String planLayout;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterCode;

    /**
     * 规划日期
     */
    @ApiModelProperty(value = "规划日期")
    private LocalDate planDate;

    /**
     * 规划数量
     */
    @ApiModelProperty(value = "规划数量")
    private BigDecimal planMwQty;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 功率
     */
    @ApiModelProperty(value = "功率")
    private BigDecimal power;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 电池损耗比例
     */
    @ApiModelProperty(value = "电池损耗比例")
    private BigDecimal lossRate;

    /**
     * 下月规划量
     */
    @ApiModelProperty(value = "下月规划量")
    private BigDecimal nextMonthPlanMwQty;

    /**
     * 下月规划天数
     */
    @ApiModelProperty(value = "下月规划天数")
    private Integer nextMonthPlanMwDays;

    /**
     * 安全周转天数
     */
    @ApiModelProperty(value = "安全周转天数")
    private Integer safeDays;

    /**
     * 排产兆瓦数
     */
    @ApiModelProperty(value = "排产兆瓦数")
    private BigDecimal quantityMw;
}
