package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期电池匹配-ERP采购入库
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-ERP采购入库DTO对象", description = "DTO对象")
public class MltBatteryMatchBatteryInStockDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value = "采购订单号")
    private String poHeaderNum;

    /**
     * 采购订单行号
     */
    @ApiModelProperty(value = "采购订单行号")
    private String poHeaderLineNum;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String vendorCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 入库过账日期
     */
    @ApiModelProperty(value = "入库过账日期")
    private LocalDate transactionDate;

    /**
     * 净入库合计数量
     */
    @ApiModelProperty(value = "净入库合计数量")
    private BigDecimal netDeliverQuantit;

    /**
     * 采购订单类型
     */
    @ApiModelProperty(value = "采购订单类型")
    private String orderType;

    /**
     * 供货工厂代码
     */
    @ApiModelProperty(value = "供货工厂代码")
    private String supplyFactoryCode;

    /**
     * 工厂代码对应排产工段
     */
    @ApiModelProperty(value = "工厂代码对应排产工段")
    private Long factoryBuId;

    /**
     * 供货工厂代码对应排产工段
     */
    @ApiModelProperty(value = "供货工厂代码对应排产工段")
    private Long supplyFactoryBuId;

    /**
     * 工厂代码对应排产区域
     */
    @ApiModelProperty(value = "工厂代码对应排产区域")
    private String factoryDomesticOversea;

    /**
     * 供货工厂代码对应排产区域
     */
    @ApiModelProperty(value = "供货工厂代码对应排产区域")
    private String supplyFactoryDomesticOversea;

    /**
     * 工厂代码对应中长期统计区域
     */
    @ApiModelProperty(value = "工厂代码对应中长期统计区域")
    private String factoryStatisticalRegion;

    /**
     * 供货工厂代码对应中长期统计区域
     */
    @ApiModelProperty(value = "供货工厂代码对应中长期统计区域")
    private String supplyFactoryStatisticalRegion;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 转换后是否定向
     */
    @ApiModelProperty(value = "转换后是否定向")
    private String afterConversionDirectional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 电池低效产出占比 MPS.INEFFICIENCY_RATIO
     */
    @ApiModelProperty(value = "电池低效产出占比 MPS.INEFFICIENCY_RATIO")
    private BigDecimal inefficiencyRatio;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private BigDecimal batteryProductSegmentation;

    /**
     * 历史电池调拨兆瓦数
     */
    @ApiModelProperty(value = "历史电池调拨兆瓦数")
    private BigDecimal quantityMw;
}
