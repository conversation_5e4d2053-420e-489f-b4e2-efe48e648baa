package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期电池匹配-月到货计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-月到货计划DTO对象", description = "DTO对象")
public class MltBatteryMatchDeliveryPlanDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String sourceType;

    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 到货日期
     */
    @ApiModelProperty(value = "到货日期")
    private LocalDate deliveryDate;

    /**
     * 到货数量
     */
    @ApiModelProperty(value = "到货数量")
    private BigDecimal deliveryQty;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 到货数量
     */
    @ApiModelProperty(value = "到货数量")
    private BigDecimal quantityMw;
}
