package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期电池匹配-电池主计划排产
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-电池主计划排产DTO对象", description = "DTO对象")
public class MltBatteryMatchNonProductionPlanDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 事业部
     */
    @ApiModelProperty(value = "事业部")
    private Long buId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * APS排产日期
     */
    @ApiModelProperty(value = "APS排产日期")
    private LocalDate apsPlanDate;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String grade;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 电池低效产出占比 MPS.INEFFICIENCY_RATIO
     */
    @ApiModelProperty(value = "电池低效产出占比 MPS.INEFFICIENCY_RATIO")
    private BigDecimal inefficiencyRatio;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private BigDecimal batteryProductSegmentation;

    /**
     * 电池历史入库兆瓦数
     */
    @ApiModelProperty(value = "电池历史入库兆瓦数")
    private BigDecimal quantityMw;
}
