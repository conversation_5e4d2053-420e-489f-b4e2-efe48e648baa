package com.jinkosolar.scp.mps.domain.dto;

import com.jinkosolar.scp.mps.domain.enums.MltBatteryMatchReportTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
public class MltBatteryMatchReportContext {
    Map<MltBatteryMatchReportTypeEnum, List<MltBatteryMatchReportDetailDataDTO>> data = new HashMap<>();

    public List<MltBatteryMatchReportDetailDataDTO> getType10Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_10);
    }

    public void setType10Data(List<MltBatteryMatchReportDetailDataDTO> type10Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_10, type10Data);
    }

    public List<MltBatteryMatchReportDetailDataDTO> getType20Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_20);
    }

    public void setType20Data(List<MltBatteryMatchReportDetailDataDTO> type20Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_20, type20Data);
    }

    public List<MltBatteryMatchReportDetailDataDTO> getType30Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_30);
    }

    public void setType30Data(List<MltBatteryMatchReportDetailDataDTO> type30Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_30, type30Data);
    }

    public List<MltBatteryMatchReportDetailDataDTO> getType40Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_40);
    }

    public void setType40Data(List<MltBatteryMatchReportDetailDataDTO> type40Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_40, type40Data);
    }

    public List<MltBatteryMatchReportDetailDataDTO> getType50Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_50);
    }

    public void setType50Data(List<MltBatteryMatchReportDetailDataDTO> type50Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_50, type50Data);
    }

    public List<MltBatteryMatchReportDetailDataDTO> getType60Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_60);
    }

    public void setType60Data(List<MltBatteryMatchReportDetailDataDTO> type60Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_60, type60Data);
    }

    public List<MltBatteryMatchReportDetailDataDTO> getType70Data() {
        return data.get(MltBatteryMatchReportTypeEnum.TYPE_70);
    }

    public void setType70Data(List<MltBatteryMatchReportDetailDataDTO> type70Data) {
        data.put(MltBatteryMatchReportTypeEnum.TYPE_70, type70Data);
    }

}
