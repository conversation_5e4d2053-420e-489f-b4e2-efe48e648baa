package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;


/**
 * 中长期电池匹配-报表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-报表", description = "DTO对象")
public class MltBatteryMatchReportDTO {

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "类型")
    private List<MltBatteryMatchReportTypeDTO> typeList;

    @Data
    public static class MltBatteryMatchReportTypeDTO {
        @ApiModelProperty(value = "年份")
        private Integer year;

        @ApiModelProperty(value = "类型")
        private String type;

        @ApiModelProperty(value = "类型排序")
        private Integer typeSort;

        @ApiModelProperty(value = "中长期统计区域")
        private List<MltBatteryMatchReportRegionDTO> regionList;
    }

    @Data
    public static class MltBatteryMatchReportRegionDTO {

        /**
         * 中长期统计区域
         */
        @ApiModelProperty(value = "中长期统计区域")
        private String statisticalRegion;

        private List<MltBatteryMatchReportDetailDTO> regionDetailList;
    }

    @Data
    public static class MltBatteryMatchReportDetailDTO {
        /**
         * 电池产品
         */
        @ApiModelProperty(value = "电池产品")
        private String spec;

        /**
         * 主栅
         */
        @ApiModelProperty(value = "主栅")
        private String mainGridLine;

        /**
         * 是否定向
         */
        @ApiModelProperty(value = "是否定向")
        private String directional;

        /**
         * 是否供美
         */
        @ApiModelProperty(value = "是否供美")
        private String supplyUsFlag;

        @ApiModelProperty(value = "q1")
        private BigDecimal q1;

        @ApiModelProperty(value = "q2")
        private BigDecimal q2;

        @ApiModelProperty(value = "q3")
        private BigDecimal q3;

        @ApiModelProperty(value = "q4")
        private BigDecimal q4;

        @ApiModelProperty(value = "全年总计")
        private BigDecimal totalYear;

        @ApiModelProperty(value = "1月")
        private BigDecimal m1;

        @ApiModelProperty(value = "2月")
        private BigDecimal m2;

        @ApiModelProperty(value = "3月")
        private BigDecimal m3;

        @ApiModelProperty(value = "4月")
        private BigDecimal m4;

        @ApiModelProperty(value = "5月")
        private BigDecimal m5;

        @ApiModelProperty(value = "6月")
        private BigDecimal m6;

        @ApiModelProperty(value = "7月")
        private BigDecimal m7;

        @ApiModelProperty(value = "8月")
        private BigDecimal m8;

        @ApiModelProperty(value = "9月")
        private BigDecimal m9;

        @ApiModelProperty(value = "10月")
        private BigDecimal m10;

        @ApiModelProperty(value = "11月")
        private BigDecimal m11;

        @ApiModelProperty(value = "12月")
        private BigDecimal m12;
    }
}
