package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 中长期电池匹配-历史实投
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-Detail对象", description = "DTO对象")
public class MltBatteryMatchReportDetailDataDTO extends BaseDTO {
    private static final Joiner groupKeyJoiner = Joiner.on("_").useForNull("null");

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;


    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    @ApiModelProperty(value = "区域")
    private String region;

    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 历史实际投产兆瓦数
     */
    @ApiModelProperty(value = "历史实际投产兆瓦数")
    private BigDecimal quantityMw;

    /**
     * 处理日期
     */
    @ApiModelProperty(value = "处理日期")
    private LocalDate processDate;

    public MltBatteryMatchReportDetailDataDTO(MltBatteryMatchReportDetailDataDTO reportDetailDataDTO) {
        this.spec = reportDetailDataDTO.getSpec();
        this.mainGridLine = reportDetailDataDTO.getMainGridLine();
        this.directional = reportDetailDataDTO.getDirectional();
        this.supplyUsFlag = reportDetailDataDTO.getSupplyUsFlag();
        this.region = reportDetailDataDTO.getRegion();
        this.quantityMw = reportDetailDataDTO.getQuantityMw();
        this.processDate = reportDetailDataDTO.getProcessDate();
    }


    public static List<MltBatteryMatchReportDetailDataDTO> negate(List<MltBatteryMatchReportDetailDataDTO> all) {
        return all.stream().map(i -> {
            MltBatteryMatchReportDetailDataDTO dto = new MltBatteryMatchReportDetailDataDTO(i);
            dto.setQuantityMw(i.getQuantityMw().negate());
            return dto;
        }).collect(Collectors.toList());
    }

    public static List<MltBatteryMatchReportDetailDataDTO> copyStatisticalRegionToRegion(List<MltBatteryMatchReportDetailDataDTO> all) {
        return all.stream().map(i -> {
            MltBatteryMatchReportDetailDataDTO dto = new MltBatteryMatchReportDetailDataDTO(i);
            dto.setRegion(i.getStatisticalRegion());
            return dto;
        }).collect(Collectors.toList());
    }

    @JsonIgnore
    public String gGroupKey() {
        return groupKeyJoiner.join(this.getSpec(), this.getMainGridLine(), this.getDirectional(), this.getSupplyUsFlag(), this.getRegion(), this.getProcessDate());
    }

    @JsonIgnore
    public String gGroupKeyWithoutDirectional() {
        return groupKeyJoiner.join(this.getSpec(), this.getMainGridLine(), this.getSupplyUsFlag(), this.getRegion(), this.getProcessDate());
    }
}
