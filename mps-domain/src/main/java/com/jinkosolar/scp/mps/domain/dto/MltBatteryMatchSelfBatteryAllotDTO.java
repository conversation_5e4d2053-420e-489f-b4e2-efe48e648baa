package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期电池匹配-自产电池调拨
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:45
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期电池匹配-自产电池调拨DTO对象", description = "DTO对象")
public class MltBatteryMatchSelfBatteryAllotDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 发出工厂代码
     */
    @ApiModelProperty(value = "发出工厂代码")
    private String sendFactoryCode;

    /**
     * 接收工厂代码
     */
    @ApiModelProperty(value = "接收工厂代码")
    private String receiveFactoryCode;

    /**
     * 计划到货日期
     */
    @ApiModelProperty(value = "计划到货日期")
    private LocalDate planDate;

    /**
     * 计划到货数量
     */
    @ApiModelProperty(value = "计划到货数量")
    private BigDecimal planQty;

    /**
     * 发出工厂代码对应排产区域
     */
    @ApiModelProperty(value = "发出工厂代码对应排产区域")
    private String sendFactoryDomesticOversea;

    /**
     * 接收工厂代码对应排产区域
     */
    @ApiModelProperty(value = "接收工厂代码对应排产区域")
    private String receiveFactoryDomesticOversea;

    /**
     * 工厂代码对应中长期统计区域
     */
    @ApiModelProperty(value = "工厂代码对应中长期统计区域")
    private String sendFactoryStatisticalRegion;

    /**
     * 接收工厂代码对应中长期统计区域
     */
    @ApiModelProperty(value = "接收工厂代码对应中长期统计区域")
    private String receiveFactoryStatisticalRegion;

    /**
     * 电池低效产出占比 MPS.INEFFICIENCY_RATIO
     */
    @ApiModelProperty(value = "电池低效产出占比 MPS.INEFFICIENCY_RATIO")
    private BigDecimal inefficiencyRatio;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private BigDecimal batteryProductSegmentation;

    /**
     * 计划调拨兆瓦数
     */
    @ApiModelProperty(value = "计划调拨兆瓦数")
    private BigDecimal quantityMw;
}
