package com.jinkosolar.scp.mps.domain.dto;

import com.jinkosolar.scp.mps.domain.enums.MltWaferMatchSummaryReportDetailsTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 中长期电池匹配-历史实投
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期汇总报表-Detail对象", description = "DTO对象")
public class MltBatteryMatchSummaryReportDetailDataDTO {
    /**
     * 细项
     */
    @ApiModelProperty(value = "细项")
    private MltWaferMatchSummaryReportDetailsTypeEnum detailsName;

    /**
     * 历史实际投产兆瓦数
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantityMw;

    /**
     * 处理日期
     */
    @ApiModelProperty(value = "处理日期")
    private LocalDate processDate;

    public MltBatteryMatchSummaryReportDetailDataDTO(MltBatteryMatchSummaryReportDetailDataDTO reportDetailDataDTO) {
        this.detailsName = reportDetailDataDTO.getDetailsName();
        this.quantityMw = reportDetailDataDTO.getQuantityMw();
        this.processDate = reportDetailDataDTO.getProcessDate();
    }


    public static List<MltBatteryMatchSummaryReportDetailDataDTO> negate(List<MltBatteryMatchSummaryReportDetailDataDTO> all) {
        return all.stream().map(i -> {
            MltBatteryMatchSummaryReportDetailDataDTO dto = new MltBatteryMatchSummaryReportDetailDataDTO(i);
            dto.setQuantityMw(i.getQuantityMw().negate());
            return dto;
        }).collect(Collectors.toList());
    }

    public static List<MltBatteryMatchSummaryReportDetailDataDTO> copyStatisticalRegionToRegion(List<MltBatteryMatchSummaryReportDetailDataDTO> all) {
        return all.stream().map(i -> {
            MltBatteryMatchSummaryReportDetailDataDTO dto = new MltBatteryMatchSummaryReportDetailDataDTO(i);
            return dto;
        }).collect(Collectors.toList());
    }
}
