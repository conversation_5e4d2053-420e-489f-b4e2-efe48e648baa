package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 中长期硅片匹配-硅片历史发货-12
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-18 14:43:08
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期硅片匹配-硅片历史发货-12DTO对象", description = "DTO对象")
public class MltWaferHisDeliveryDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * SAP DN号
     */
    @ApiModelProperty(value = "SAP DN号")
    private String sapDn;

    /**
     * 交货类型
     */
    @ApiModelProperty(value = "交货类型")
    private String deliveryType;

    /**
     * 货物移动状态
     */
    @ApiModelProperty(value = "货物移动状态")
    private String moveStatus;

    /**
     * POD状态
     */
    @ApiModelProperty(value = "POD状态")
    private String podStatus;

    /**
     * 实际货物移动日期
     */
    @ApiModelProperty(value = "实际货物移动日期")
    private LocalDateTime realMoveDate;

    /**
     * POD日期
     */
    @ApiModelProperty(value = "POD日期")
    private LocalDateTime podDate;

    /**
     * 行号
     */
    @ApiModelProperty(value = "行号")
    private Long lineNum;

    /**
     * 料号（物料编码）
     */
    @ApiModelProperty(value = "料号（物料编码）")
    private String itemCode;

    /**
     * 发出工厂代码
     */
    @ApiModelProperty(value = "发出工厂代码")
    private String sendFactoryCode;

    /**
     * 接收工厂代码
     */
    @ApiModelProperty(value = "接收工厂代码")
    private String receiveFactoryCode;

    /**
     * 库存地点
     */
    @ApiModelProperty(value = "库存地点")
    private String stockLocation;

    /**
     * 销售数量
     */
    @ApiModelProperty(value = "销售数量")
    private String saleNum;

    /**
     * 销售单位
     */
    @ApiModelProperty(value = "销售单位")
    private String saleUnit;

    /**
     * 基本计量单位
     */
    @ApiModelProperty(value = "基本计量单位")
    private String baseUnit;

    /**
     * 批号
     */
    @ApiModelProperty(value = "批号")
    private String batchNum;

    /**
     * 分销渠道
     */
    @ApiModelProperty(value = "分销渠道")
    private String distributionChannel;

    /**
     * 分销渠道描述
     */
    @ApiModelProperty(value = "分销渠道描述")
    private String distributionChannelDesc;

    /**
     * 发出工厂代码对应排产区域
     */
    @ApiModelProperty(value = "发出工厂代码对应排产区域")
    private String sendFactoryDomesticOversea;

    /**
     * 接收工厂代码对应排产区域
     */
    @ApiModelProperty(value = "接收工厂代码对应排产区域")
    private String receiveFactoryDomesticOversea;

    /**
     * 工厂代码对应中长期统计区域
     */
    @ApiModelProperty(value = "工厂代码对应中长期统计区域")
    private String sendFactoryStatisticalRegion;

    /**
     * 接收工厂代码对应中长期统计区域
     */
    @ApiModelProperty(value = "接收工厂代码对应中长期统计区域")
    private String receiveFactoryStatisticalRegion;

    /**
     * 电池对硅片需求数量
     */
    @ApiModelProperty(value = "电池对硅片需求数量")
    private BigDecimal quantity;

    /**
     * APS排产日期
     */
    @ApiModelProperty(value = "APS排产日期")
    private LocalDate transactionDate;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String grade;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 硅料供应商品牌
     */
    @ApiModelProperty(value = "硅料供应商品牌")
    private String vendorBrand;

    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private BigDecimal batteryProductSegmentation;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 历史硅片发货兆瓦数
     */
    @ApiModelProperty(value = "历史硅片发货兆瓦数")
    private BigDecimal quantityMw;
}
