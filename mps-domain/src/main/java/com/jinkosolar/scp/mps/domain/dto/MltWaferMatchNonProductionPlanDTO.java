package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期硅片匹配-硅片未来产出
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-27 15:50:45
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期硅片匹配-硅片未来产出DTO对象", description = "DTO对象")
public class MltWaferMatchNonProductionPlanDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 事业部
     */
    @ApiModelProperty(value = "事业部")
    private Long buId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * APS排产日期
     */
    @ApiModelProperty(value = "APS排产日期")
    private LocalDate transactionDate;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 切片排产兆瓦数
     */
    @ApiModelProperty(value = "切片排产兆瓦数")
    private BigDecimal quantityMw;
}
