package com.jinkosolar.scp.mps.domain.dto;

import com.jinkosolar.scp.mps.domain.enums.MltWaferMatchSummaryReportTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
public class MltWaferMatchSummaryReportContext {
    MltBatteryMatchReportContext batteryContext;

    Map<MltWaferMatchSummaryReportTypeEnum, List<MltBatteryMatchSummaryReportDetailDataDTO>> data = new HashMap<>();

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType10Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_10);
    }

    public void setType10Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type10Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_10, type10Data);
    }

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType20Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_20);
    }

    public void setType20Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type20Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_20, type20Data);
    }

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType30Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_30);
    }

    public void setType30Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type30Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_30, type30Data);
    }

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType40Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_40);
    }

    public void setType40Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type40Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_40, type40Data);
    }

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType50Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_50);
    }

    public void setType50Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type50Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_50, type50Data);
    }

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType60Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_60);
    }

    public void setType60Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type60Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_60, type60Data);
    }

    public List<MltBatteryMatchSummaryReportDetailDataDTO> getType70Data() {
        return data.get(MltWaferMatchSummaryReportTypeEnum.TYPE_70);
    }

    public void setType70Data(List<MltBatteryMatchSummaryReportDetailDataDTO> type70Data) {
        data.put(MltWaferMatchSummaryReportTypeEnum.TYPE_70, type70Data);
    }
}
