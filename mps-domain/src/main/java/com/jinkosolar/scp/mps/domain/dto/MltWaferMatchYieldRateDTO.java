package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 中长期硅片匹配-历史实际投产
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-27 15:50:45
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "中长期硅片匹配-历史实际投产DTO对象", description = "DTO对象")
public class MltWaferMatchYieldRateDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private Long batchNo;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 车间代码
     */
    @ApiModelProperty(value = "车间代码")
    private String workshopCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 过账日期
     */
    @ApiModelProperty(value = "过账日期")
    private LocalDate transactionDate;

    /**
     * A+数量
     */
    @ApiModelProperty(value = "A+数量")
    private BigDecimal qtyAa;

    /**
     * A线数量
     */
    @ApiModelProperty(value = "A线数量")
    private BigDecimal qtyAx;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 历史实际投产兆瓦数
     */
    @ApiModelProperty(value = "历史实际投产兆瓦数")
    private BigDecimal quantityMw;
}
