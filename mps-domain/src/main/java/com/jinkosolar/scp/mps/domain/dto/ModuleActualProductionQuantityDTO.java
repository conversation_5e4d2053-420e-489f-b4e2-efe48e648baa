package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Joiner;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.SplitConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("MES组件实投数量数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class ModuleActualProductionQuantityDTO extends PageDTO implements Serializable {
    /**
     * 实投取消数量
     */
    @ApiModelProperty("实投取消数量")
    @ExcelProperty(value = "实投取消数量")
    private BigDecimal actualCancelledQuantity;
    /**
     * 实投取消数量对应串焊扫描日期
     */
    @ApiModelProperty("实投取消数量对应串焊扫描日期")
    @ExcelProperty(value = "实投取消数量对应串焊扫描日期")
    private LocalDate actualCancelledQuantityScanDate;
    /**
     * 实投数量
     */
    @ApiModelProperty("实投数量")
    @ExcelProperty(value = "实投数量")
    private BigDecimal actualProductionQuantity;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空", groups = ValidGroups.Insert.class)
    private String factoryCode;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 计划型号
     */
    @ApiModelProperty("计划型号")
    @ExcelProperty(value = "计划型号")
    @NotBlank(message = "计划型号不能为空", groups = ValidGroups.Insert.class)
    private String planModel;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")
    @NotBlank(message = "产品型号不能为空", groups = ValidGroups.Insert.class)
    private String productModel;
    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    @ExcelProperty(value = "销售订单行")
    @NotBlank(message = "销售订单行不能为空", groups = ValidGroups.Insert.class)
    private String salesOrderLineNo;
    /**
     * 销售订单
     */
    @ApiModelProperty("销售订单")
    @ExcelProperty(value = "销售订单")
    @NotBlank(message = "销售订单不能为空", groups = ValidGroups.Insert.class)
    private String salesOrderNo;
    /**
     * 上传日期
     */
    @ApiModelProperty("上传日期")
    @ExcelProperty(value = "上传日期")
    @JSONField(format = "yyyyMMdd")
    @NotNull(message = "上传日期不能为空", groups = ValidGroups.Insert.class)
    private LocalDate uploadDate;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @NotBlank(message = "工作中心不能为空", groups = ValidGroups.Insert.class)
    private String workCenterCode;
    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    @ExcelProperty(value = "工单号")
    @NotBlank(message = "工单号不能为空", groups = ValidGroups.Insert.class)
    private String workOrderNo;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    @NotBlank(message = "车间代码不能为空", groups = ValidGroups.Insert.class)
    private String workshopCode;
    public String groupByOrderAndWorkCenter() {
       return  Joiner.on(SplitConstant.SPLIT_SEPARATOR).join(this.salesOrderNo, this.salesOrderLineNo,this.workCenterCode);
    }
    public ModuleActualProductionQuantityDTO buildDTO() {
        return   ModuleActualProductionQuantityDTO.builder().salesOrderNo(this.salesOrderNo).salesOrderLineNo(this.salesOrderLineNo).workCenterCode(this.workCenterCode).build();
    }

    /**
     * 销售汇总
     * @return
     */
    public ModuleActualProductionQuantityDTO sales() {
        return ModuleActualProductionQuantityDTO.builder().salesOrderNo(this.salesOrderNo).salesOrderLineNo(this.salesOrderLineNo).build();
    }

    /**
     * 最终实投数量(原实投数量-取消数量)
     */
    @ApiModelProperty("最终实投数量")
    @ExcelProperty(value = "最终实投数量")
    private BigDecimal finalActualProductionQuantity;

    /**
     * 实投取消数量合计
     */
    @ApiModelProperty("实投取消数量合计")
    private BigDecimal totalActualCancelledQuantity;

    /**
     * 实投数量合计
     */
    @ApiModelProperty("实投数量合计")
    private BigDecimal totalActualProductionQuantity;
}