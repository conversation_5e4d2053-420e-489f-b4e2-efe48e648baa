package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;  


@ApiModel("MES组件实投数量快照表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleActualProductionQuantitySnapshotDTO extends BaseDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")  
    private Long id;
    /**
     * 计划发布版本
     */
    @ApiModelProperty("计划发布版本")
    @ExcelProperty(value = "计划发布版本")  
    private String planVersion;
    /**
     * 销售订单
     */
    @ApiModelProperty("销售订单")
    @ExcelProperty(value = "销售订单")  
    private String salesOrderNo;
    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    @ExcelProperty(value = "销售订单行")  
    private String salesOrderLineNo;
    /**
     * 工单号
     */
    @ApiModelProperty("工单号")
    @ExcelProperty(value = "工单号")  
    private String workOrderNo;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")  
    private String productModel;
    /**
     * 计划型号
     */
    @ApiModelProperty("计划型号")
    @ExcelProperty(value = "计划型号")  
    private String planModel;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshopCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private String workCenterCode;
    /**
     * 上传日期
     */
    @ApiModelProperty("上传日期")
    @ExcelProperty(value = "上传日期")  
    private LocalDate uploadDate;
    /**
     * 实投数量
     */
    @ApiModelProperty("实投数量")
    @ExcelProperty(value = "实投数量")  
    private BigDecimal actualProductionQuantity;
    /**
     * 实投取消数量
     */
    @ApiModelProperty("实投取消数量")
    @ExcelProperty(value = "实投取消数量")  
    private BigDecimal actualCancelledQuantity;
    /**
     * 实投取消数量对应串焊扫描日期
     */
    @ApiModelProperty("实投取消数量对应串焊扫描日期")
    @ExcelProperty(value = "实投取消数量对应串焊扫描日期")  
    private LocalDate actualCancelledQuantityScanDate;
    /**
     * 最新版本标识,Y/N
     */
    @ApiModelProperty("最新版本标识,Y/N")
    @ExcelProperty(value = "最新版本标识,Y/N")  
    private String status;
}