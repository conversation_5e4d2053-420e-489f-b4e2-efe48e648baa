package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("BI组件实际良率数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleActualYieldRateDTO extends BaseDTO implements Serializable {
    /**
     * 降级数量
     */
    @ApiModelProperty("降级数量")
    @ExcelProperty(value = "降级数量")
    @NotNull(message = "降级数量不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal downgradeQuantity;
    /**
     * 降级率
     */
    @ApiModelProperty("降级率")
    @ExcelProperty(value = "降级率")
    @NotNull(message = "降级率不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal downgradeRate;
    /**
     * 良品数量
     */
    @ApiModelProperty("良品数量")
    @ExcelProperty(value = "良品数量")
    @NotNull(message = "良品数量不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal goodProductQuantity;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    @NotBlank(message = "计划版型不能为空", groups = ValidGroups.Insert.class)
    private String planModel;
    /**
     * 总投入数量
     */
    @ApiModelProperty("总投入数量")
    @ExcelProperty(value = "总投入数量")
    @NotNull(message = "总投入数量不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal totalInputQuantity;
    /**
     * 转库存数量
     */
    @ApiModelProperty("转库存数量")
    @ExcelProperty(value = "转库存数量")
    @NotNull(message = "转库存数量不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal transferToInventoryQuantity;
    /**
     * 转库存率
     */
    @ApiModelProperty("转库存率")
    @ExcelProperty(value = "转库存率")
    @NotNull(message = "转库存率不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal transferToInventoryRate;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    @NotBlank(message = "车间代码不能为空", groups = ValidGroups.Insert.class)
    private String workshopCode;
    /**
     * 良率
     */
    @ApiModelProperty("良率")
    @ExcelProperty(value = "良率")
    @NotNull(message = "良率不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal yieldRate;
}