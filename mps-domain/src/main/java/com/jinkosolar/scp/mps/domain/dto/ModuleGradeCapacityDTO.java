package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("爬坡产能数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleGradeCapacityDTO extends PageDTO implements Serializable {
    /**
     * 3号
     */
    @ApiModelProperty("3号")
    @ExcelProperty(value = "3号")
    private BigDecimal d3Quantity;
    /**
     * 4号
     */
    @ApiModelProperty("4号")
    @ExcelProperty(value = "4号")
    private BigDecimal d4Quantity;
    /**
     * 5号
     */
    @ApiModelProperty("5号")
    @ExcelProperty(value = "5号")
    private BigDecimal d5Quantity;
    /**
     * 6号
     */
    @ApiModelProperty("6号")
    @ExcelProperty(value = "6号")
    private BigDecimal d6Quantity;
    /**
     * 7号
     */
    @ApiModelProperty("7号")
    @ExcelProperty(value = "7号")
    private BigDecimal d7Quantity;
    /**
     * 8号
     */
    @ApiModelProperty("8号")
    @ExcelProperty(value = "8号")
    private BigDecimal d8Quantity;
    /**
     * 9号
     */
    @ApiModelProperty("9号")
    @ExcelProperty(value = "9号")
    private BigDecimal d9Quantity;
    /**
     * 爬坡类型
     */
    @ApiModelProperty("爬坡类型")
    private Long gradeCapacityTypeId;

    /**
     * 爬坡类型
     */
    @ApiModelProperty("爬坡类型")
    @ExcelProperty(value = "爬坡类型")
    private String gradeCapacityTypeName;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 线体名称
     */
    @ApiModelProperty("线体名称")
    @ExcelProperty(value = "线体")
    private String lineBodyName;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型id")
    private String planVersion;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "版型")
    @ImportExConvert
    private String planVersionCode;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 数据类型：1：爬坡 2：实验
     */
    @ApiModelProperty("数据类型：1：爬坡 2：实验")
    private Integer type;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String version;
    /**
     * 工厂
     */
    @ExcelProperty(value = "工厂")
    @ApiModelProperty("工厂")
    private Long factoryId;
    /**
     * 工厂
     */
    @ExcelProperty(value = "工厂")
    @ApiModelProperty("工厂")
    @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + LovHeaderCodeConstant.MPS_FACTORY + "'", targetFieldName = "factoryId")
    private String factoryCode;
    /**
     * 工作中心Code
     */
    @ApiModelProperty("工作中心Code")
    private Long workCenterId;
    /**
     * 工作中心
     */
    @ExcelProperty(value = "工作中心")
    @ApiModelProperty("工作中心描述")
    private String workCenterDesc;

    @Dict(methodName = "getWorkCenterCodeById",fieldKey = "workCenterId",fieldName = "workCenterCode")
    @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + LovHeaderCodeConstant.SYS_WORKCENTER + "'", targetFieldName = "workCenterId")
    @ExcelProperty(value = "工作中心")
    @ApiModelProperty("工作中心描述")
    private String workCenterCode;
    /**
     * 生产月
     */
    @ApiModelProperty("生产月")
    @ExcelProperty(value = "生产月")
    @ImportExConvert
    private String workMonth;
    /**
     * 生产年
     */
    @ApiModelProperty("生产年")
    @ExcelProperty(value = "生产年")
    @ImportExConvert
    private String workYear;
    /**
     * 10号
     */
    @ApiModelProperty("10号")
    @ExcelProperty(value = "10号")
    private BigDecimal d10Quantity;
    /**
     * 11号
     */
    @ApiModelProperty("11号")
    @ExcelProperty(value = "11号")
    private BigDecimal d11Quantity;
    /**
     * 12号
     */
    @ApiModelProperty("12号")
    @ExcelProperty(value = "12号")
    private BigDecimal d12Quantity;
    /**
     * 13号
     */
    @ApiModelProperty("13号")
    @ExcelProperty(value = "13号")
    private BigDecimal d13Quantity;
    /**
     * 14号
     */
    @ApiModelProperty("14号")
    @ExcelProperty(value = "14号")
    private BigDecimal d14Quantity;
    /**
     * 15号
     */
    @ApiModelProperty("15号")
    @ExcelProperty(value = "15号")
    private BigDecimal d15Quantity;
    /**
     * 16号
     */
    @ApiModelProperty("16号")
    @ExcelProperty(value = "16号")
    private BigDecimal d16Quantity;
    /**
     * 17号
     */
    @ApiModelProperty("17号")
    @ExcelProperty(value = "17号")
    private BigDecimal d17Quantity;
    /**
     * 18号
     */
    @ApiModelProperty("18号")
    @ExcelProperty(value = "18号")
    private BigDecimal d18Quantity;
    /**
     * 19号
     */
    @ApiModelProperty("19号")
    @ExcelProperty(value = "19号")
    private BigDecimal d19Quantity;
    /**
     * 1号
     */
    @ApiModelProperty("1号")
    @ExcelProperty(value = "1号")
    private BigDecimal d1Quantity;
    /**
     * 20号
     */
    @ApiModelProperty("20号")
    @ExcelProperty(value = "20号")
    private BigDecimal d20Quantity;
    /**
     * 21号
     */
    @ApiModelProperty("21号")
    @ExcelProperty(value = "21号")
    private BigDecimal d21Quantity;
    /**
     * 22号
     */
    @ApiModelProperty("22号")
    @ExcelProperty(value = "22号")
    private BigDecimal d22Quantity;
    /**
     * 23号
     */
    @ApiModelProperty("23号")
    @ExcelProperty(value = "23号")
    private BigDecimal d23Quantity;
    /**
     * 24号
     */
    @ApiModelProperty("24号")
    @ExcelProperty(value = "24号")
    private BigDecimal d24Quantity;
    /**
     * 25号
     */
    @ApiModelProperty("25号")
    @ExcelProperty(value = "25号")
    private BigDecimal d25Quantity;
    /**
     * 26号
     */
    @ApiModelProperty("26号")
    @ExcelProperty(value = "26号")
    private BigDecimal d26Quantity;
    /**
     * 27号
     */
    @ApiModelProperty("27号")
    @ExcelProperty(value = "27号")
    private BigDecimal d27Quantity;
    /**
     * 28号
     */
    @ApiModelProperty("28号")
    @ExcelProperty(value = "28号")
    private BigDecimal d28Quantity;
    /**
     * 29号
     */
    @ApiModelProperty("29号")
    @ExcelProperty(value = "29号")
    private BigDecimal d29Quantity;
    /**
     * 2号
     */
    @ApiModelProperty("2号")
    @ExcelProperty(value = "2号")
    private BigDecimal d2Quantity;
    /**
     * 30号
     */
    @ApiModelProperty("30号")
    @ExcelProperty(value = "30号")
    private BigDecimal d30Quantity;
    /**
     * 31号
     */
    @ApiModelProperty("31号")
    @ExcelProperty(value = "31号")
    private BigDecimal d31Quantity;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "审批状态")
    private Long statusId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "审批状态")
    private String statusIdName;

    /**
     * 满产标记
     */
    @ApiModelProperty(value = "满产标记")
    private String fullFlag;

}
