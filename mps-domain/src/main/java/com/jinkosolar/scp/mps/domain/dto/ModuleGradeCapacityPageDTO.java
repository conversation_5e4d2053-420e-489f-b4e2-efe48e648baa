package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;


@EqualsAndHashCode(callSuper = true)
@ApiModel("爬坡产能页面数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleGradeCapacityPageDTO extends PageDTO implements Serializable {

         /**
          * 爬坡产能天数
          */
         List<String> capacityDays;
        /**
         * 爬坡产能按天分布数据map
         */
        Map<String, BigDecimal> capacityDaysMap;
        /**
         * 爬坡类型
         */
        @ApiModelProperty("爬坡类型")
        private Long gradeCapacityTypeId;

        /**
         * 爬坡类型
         */
        @ApiModelProperty("爬坡类型")
        @ExcelProperty(value = "爬坡类型")
        private String gradeCapacityTypeName;
        /**
         * 主键
         */
        @ApiModelProperty("主键")
        private Long id;
        /**
         * 线体名称
         */
        @ApiModelProperty("线体名称")
        @ExcelProperty(value = "线体")
        private String lineBodyName;
        /**
         * 计划版型
         */
        @ApiModelProperty("计划版型")
        @ExcelProperty(value = "版型")
        private String planVersion;

        /**
         * 年份
         */
        @ApiModelProperty("年份")
        @ExcelProperty(value = "年份")
        private String workYear;

        /**
         * 备注
         */
        @ApiModelProperty("备注")
        private String remark;
        /**
         * 数据类型：1：爬坡 2：实验
         */
        @ApiModelProperty("数据类型：1：爬坡 2：实验")
        private Integer type;
        /**
         * 单位
         */
        @ApiModelProperty("单位")
        private String unit;
        /**
         * 版本号
         */
        @ApiModelProperty("版本号")
        @ExcelProperty(value = "版本号")
        private String version;
        /**
         * 工作中心Code
         */
        @ApiModelProperty("工作中心Code")
        private Long workCenterId;
        /**
         * 工作中心
         */
        @ExcelProperty(value = "工作中心")
        @ApiModelProperty("工作中心描述")
        private String workCenterDesc;

        @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + LovHeaderCodeConstant.SYS_WORKCENTER + "'", targetFieldName = "workCenterId")
        @ExcelProperty(value = "工作中心")
        @ApiModelProperty("工作中心描述")
        private String workCenterCode;

}
