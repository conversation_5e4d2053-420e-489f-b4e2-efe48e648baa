package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;


@ApiModel("组件OEM产能-动态数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleOemCapacityDynamicDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 数据分类id
     */
    @ApiModelProperty("数据分类id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataType;

    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"dataTypeName"},
            from = {"lovLineId"}, to = {"dataType"}, required = true)
    private String dataTypeName;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String version;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂id")
    @ExcelProperty(value = "工厂id")
    @Translate(DictType = LovHeaderCodeConstant.MPS_FACTORY, queryColumns = {"lovLineId"},
            from = {"lovValue"}, to = {"factoryCode"})
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ExcelProperty(value = "工厂代码")
    @ApiModelProperty("工厂代码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_FACTORY, queryColumns = {"lovCode"},
            from = {"lovLineId"}, to = {"factoryId"}, required = true)
    private String factoryCode;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workCenterCode","workCenterDes"})
    private Long workCenterId;

    @ExcelProperty(value = "工作中心")
    @ApiModelProperty("工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, unTranslate = true,
            required = true, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workCenterId"})
    private String workCenterCode;

    /**
     * 工作描述
     * */
    @ApiModelProperty("工作描述")
    @ExcelProperty(value = "工作描述")
    private String workCenterDes;

    /**
     * 线体名称
     */
    @ApiModelProperty("线体名称")
    @ExcelProperty(value = "线体名称")
    private String lineBodyName;
    /**
     * 爬坡类型
     */
    @ApiModelProperty("爬坡类型")
    @ExcelProperty(value = "爬坡类型")
    private String gradeCapacityTypeName;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    private String planVersion;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "版型")
    @ImportExConvert
    private String planVersionCode;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型名称")
    @ExcelProperty(value = "计划版型名称")
    @ImportExConvert
    private String planVersionNameAlias;

    /**
     * 数据类型：1：爬坡 2：实验
     */
    @ApiModelProperty("数据类型：1：爬坡 2：实验")
    @ExcelProperty(value = "数据类型：1：爬坡 2：实验")
    private Integer type;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    @ExcelProperty(value = "日期")
    private LocalDate workDay;
    /**
     * 数据
     */
    @ApiModelProperty("数据")
    @ExcelProperty(value = "数据")
    private String workValue;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @ExcelProperty(value = "单位")
    private String unit;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 审批状态MRP.VERSION_STATUS 新建NEW|审核中IN_APPROVAL|已审批APPROVED|已拒绝REFUSED|关闭CLOSED
     */
    @ApiModelProperty("审批状态MRP.VERSION_STATUS 新建NEW|审核中IN_APPROVAL|已审批APPROVED|已拒绝REFUSED|关闭CLOSED")
    @ExcelProperty(value = "审批状态MRP.VERSION_STATUS 新建NEW|审核中IN_APPROVAL|已审批APPROVED|已拒绝REFUSED|关闭CLOSED")
    private String statusId;

    // 使用HashMap来存储ID和值
    private Map<String, String> dayValueMap = new HashMap<>();
}