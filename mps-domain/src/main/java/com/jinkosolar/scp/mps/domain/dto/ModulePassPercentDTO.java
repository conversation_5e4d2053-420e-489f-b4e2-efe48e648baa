package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ModulePassPercentDTO对象", description = "DTO对象")
public class ModulePassPercentDTO extends BaseDTO {

    private String workshop;

    private String dpId;

    private String productFamily;

    private String cellType;

    private String month;

    private String total;

    private String passPercent;

    private String powerPredictVersion;

    private String versionGap;

    private Integer isTotal;

    private String type;

    private String dpGroupId;

    private String isOversea;

    private String powerPredictVersion1;

    private String powerPredictVersion2;

    private String batchNo1;

    private String batchNo2;

    private String category;
    private String bb;
    private String baseCellType;

}
