package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;


@ApiModel("组件生产计划改单明细报表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModulePlanAmendmentReportDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 模拟日期
     */
    @ApiModelProperty("模拟日期")
    @ExcelProperty(value = "模拟日期")  
    private LocalDateTime simulatedDate;
    /**
     * 模拟天数
     */
    @ApiModelProperty("模拟天数")
    @ExcelProperty(value = "模拟天数")  
    private Integer simulatedDays;
    /**
     * 原工厂
     */
    @ApiModelProperty("原工厂")
    @ExcelProperty(value = "原工厂")  
    private Long originalFactoryId;
    /**
     * 原工厂code
     */
    @ApiModelProperty("原工厂code")
    @ExcelProperty(value = "原工厂code")
    private String originalFactoryCode;
    /**
     * 新工厂
     */
    @ApiModelProperty("新工厂")
    @ExcelProperty(value = "新工厂")  
    private Long factoryId;
    /**
     * 新工厂code
     */
    @ApiModelProperty("新工厂code")
    @ExcelProperty(value = "新工厂code")
    private String factoryCode;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")  
    private String sapOrderNum;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")  
    private String aspLineNum;
    /**
     * 是否拆行
     */
    @ApiModelProperty("是否拆行")
    @ExcelProperty(value = "是否拆行")  
    private String isSplitLine;
    /**
     * 模拟数量
     */
    @ApiModelProperty("模拟数量")
    @ExcelProperty(value = "模拟数量")
    private Long simulatedNum;
    /**
     * 数量
     */
    @ApiModelProperty("需求数量")
    @ExcelProperty(value = "需求数量")
    private Long num;
    /**
     * MW
     */
    @ApiModelProperty("MW")
    @ExcelProperty(value = "MW")  
    private BigDecimal mw;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称")  
    private String customerName;
    /**
     * 投产方案
     */
    @ApiModelProperty("投产方案")
    @ExcelProperty(value = "投产方案")  
    private String productionPlan;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")  
    private String planVersion;
    /**
     * 合同要求货好日期
     */
    @ApiModelProperty("合同要求货好日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "合同要求货好日期")  
    private LocalDate cargoReadyDate;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")  
    private BigDecimal kw;
    /**
     * 目的地区域
     */
    @ApiModelProperty("目的地区域")
    @ExcelProperty(value = "目的地区域")
    @Translate(DictType = MpsLovConstant.DP_DESTINATION_REGION, queryColumns = {"lovLineId"},from={"lovName"}, to = {"dataTypeName"})
    private String dataType;
    /**
     * 目的地区域
     */
    @ApiModelProperty("目的地区域名称")
    @ExcelProperty(value = "目的地区域名称")
    private String dataTypeName;
    /**
     * SCP发布版本号
     */
    @ApiModelProperty("SCP发布版本号")
    @ExcelProperty(value = "SCP发布版本号")  
    private String scpVersionNumber;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")  
    private String remark;
    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")
    @Translate(DictType = MpsLovConstant.LOCATION, queryColumns = {"lovValue"},from={"lovName"}, to = {"domesticOverseaName"})
    private String domesticOversea;
    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称")
    private String domesticOverseaName;
    /**
     * 生产通知书版本
     */
    @ApiModelProperty("生产通知书版本")
    @ExcelProperty(value = "生产通知书版本")
    private String productionOrderVersion;
    /**
     * 模拟拆分总数
     */
    @ApiModelProperty("模拟拆分总数")
    @ExcelProperty(value = "模拟拆分总数")
    private Long simulatedCount;
    /**
     * dp明细需求
     */
    @ApiModelProperty("dp明细需求")
    @ExcelProperty(value = "dp明细需求")
    private Long dpDetailNum;
    /**
     * 转化方向
     */
    @ApiModelProperty("转化方向")
    @ExcelProperty(value = "转化方向")
    private String transformDirection;
}