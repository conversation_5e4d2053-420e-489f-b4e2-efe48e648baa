package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("组件生产计划改单明细临时表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModulePlanAmendmentTempDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String versionNumber;
    /**
     * 模拟日期
     */
    @ApiModelProperty("模拟日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "模拟日期")  
    private LocalDateTime simulatedDate;
    /**
     * 模拟天数
     */
    @ApiModelProperty("模拟天数")
    @ExcelProperty(value = "模拟天数")  
    private String simulatedDays;
    /**
     * 模型代码
     */
    @ApiModelProperty("模型代码")
    @ExcelProperty(value = "模型代码")  
    private String modelCode;
    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")  
    private String domesticOversea;
    /**
     * 订单代码
     */
    @ApiModelProperty("订单代码")
    @ExcelProperty(value = "订单代码")  
    private String orderCode;
    /**
     * 原工厂code
     */
    @ApiModelProperty("原工厂code")
    @ExcelProperty(value = "原工厂code")  
    private String originalFactoryCode;
    /**
     * 新工厂code
     */
    @ApiModelProperty("新工厂code")
    @ExcelProperty(value = "新工厂code")  
    private String factoryCode;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")  
    private String sapOrderNum;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")  
    private String aspLineNum;
    /**
     * 模拟数量
     */
    @ApiModelProperty("模拟数量")
    @ExcelProperty(value = "模拟数量")  
    private Long simulatedNum;
    /**
     * 需求数量
     */
    @ApiModelProperty("需求数量")
    @ExcelProperty(value = "需求数量")  
    private Long num;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")  
    private String planVersion;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")  
    private String remark;
    /**
     * apsid
     */
    @ApiModelProperty("apsid")
    @ExcelProperty(value = "apsid")  
    private String apsId;
    /**
     * 生产通知书版本
     */
    @ApiModelProperty("生产通知书版本")
    @ExcelProperty(value = "生产通知书版本")  
    private String productionOrderVersion;
    /**
     * 模拟拆分总数
     */
    @ApiModelProperty("模拟拆分总数")
    @ExcelProperty(value = "模拟拆分总数")  
    private Long simulatedCount;
    /**
     * dp明细需求
     */
    @ApiModelProperty("dp明细需求")
    @ExcelProperty(value = "dp明细需求")
    private Long dpDetailNum;
}