package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("组件生产计划兜底报表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModulePlanRevealReportDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂ID")
    @ExcelProperty(value = "工厂ID")
    private Long factoryId;

    /**
     * 工厂Code
     */
    @ApiModelProperty("工厂Code")
    @ExcelProperty(value = "工厂Code")
    private String factoryCode;

    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNumber;
    /**
     * SAP行号
     */
    @ApiModelProperty("SAP行号")
    @ExcelProperty(value = "SAP行号")
    private Integer sapRownum;
    /**
     * asprova是否有
     */
    @ApiModelProperty("asprova是否有")
    @ExcelProperty(value = "asprova是否有")
    private String isAsprova;

    /**
     * DP生产通知书更新时间
     */
    @ApiModelProperty("DP生产通知书更新时间")
    @ExcelProperty(value = "DP生产通知书更新时间")
    private LocalDateTime dpProductUpdateTime;

    /**
     * Asprova生产通知书更新时间
     */
    @ApiModelProperty("Asprova生产通知书更新时间")
    @ExcelProperty(value = "Asprova生产通知书更新时间")
    private LocalDateTime asprovaProductUpdateTime;

    /**
     * 更新时间是否一致
     */
    @ApiModelProperty("更新时间是否一致")
    @ExcelProperty(value = "更新时间是否一致")
    private String isSameUpdateTime;

    /**
     * DP端版本号
     */
    @ApiModelProperty("DP端版本号")
    @ExcelProperty(value = "DP端版本号")
    private String dpVersionNumber;
    /**
     * Asprova版本号
     */
    @ApiModelProperty("Asprova版本号")
    @ExcelProperty(value = "Asprova版本号")
    private String asprovaVersionNumber;
    /**
     * 版本是否一致
     */
    @ApiModelProperty("版本是否一致")
    @ExcelProperty(value = "版本是否一致")
    private String isNumberAccordance;
    /**
     * DP需求数量
     */
    @ApiModelProperty("DP需求数量")
    @ExcelProperty(value = "DP需求数量")
    private Integer dpDemandNum;
    /**
     * asprova需求数量
     */
    @ApiModelProperty("asprova需求数量")
    @ExcelProperty(value = "asprova需求数量")
    private Integer asprovaDemandNum;
    /**
     * 需求数量是否一致
     */
    @ApiModelProperty("需求数量是否一致")
    @ExcelProperty(value = "需求数量是否一致")
    private String isDemandAccordance;
    /**
     * 生产类型
     */
    @ApiModelProperty("生产类型")
    @ExcelProperty(value = "生产类型")
    private String dpProductType;

    /**
     * 生产类型
     */
    @ApiModelProperty("生产类型")
    @ExcelProperty(value = "生产类型")
    private String asprovaProductType;

    /**
     * 生产类型是否一致
     */
    @ApiModelProperty("需求数量是否一致")
    @ExcelProperty(value = "需求数量是否一致")
    private String isProductType;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称")
    private String customerName;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    private String planVersion;
    /**
     * 投产方案
     */
    @ApiModelProperty("投产方案")
    @ExcelProperty(value = "投产方案")
    private String productionPlan;
    /**
     * 货好日期
     */
    @ApiModelProperty("货好日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "货好日期")
    private LocalDate cargoReadyDate;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")
    private String kw;

    /**
     * 物料
     * */
    @ApiModelProperty("物料新")
    @ExcelProperty(value = "物料新")
    private String productItemNew;
}