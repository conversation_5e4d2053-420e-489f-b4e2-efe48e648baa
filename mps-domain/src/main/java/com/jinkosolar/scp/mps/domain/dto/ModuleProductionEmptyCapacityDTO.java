package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("组件排产空产能表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionEmptyCapacityDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 计划发布版本
     */
    @ApiModelProperty("计划发布版本")
    @ExcelProperty(value = "计划发布版本")  
    private String planVersion;
    /**
     * 定线规划版本
     */
    @ApiModelProperty("定线规划版本")
    private String programVersion;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")  
    private String planLayout;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")  
    private String factoryName;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂ID")
    @ExcelProperty(value = "工厂ID")  
    private Long factory;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshopCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")  
    private String workshopName;
    /**
     * 车间ID
     */
    @ApiModelProperty("车间ID")
    @ExcelProperty(value = "车间ID")  
    private Long workshopId;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private String workCenterCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")  
    private String workCenterName;
    /**
     * 工作中心ID
     */
    @ApiModelProperty("工作中心ID")
    @ExcelProperty(value = "工作中心ID")  
    private Long workCenterId;
    /**
     * 计划日期
     */
    @ApiModelProperty("计划日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "计划日期")  
    private LocalDateTime planDate;
    /**
     * 计划排产量（工作表制造数量）
     */
    @ApiModelProperty("计划排产量（工作表制造数量）")
    @ExcelProperty(value = "计划排产量（工作表制造数量）")  
    private BigDecimal planQty;
    /**
     * 国内/海外/山西
     */
    @ApiModelProperty("国内/海外/山西")
    @ExcelProperty(value = "国内/海外/山西")  
    private String domesticOversea;
    /**
     * 产线/机器总数
     */
    @ApiModelProperty("产线/机器总数")
    @ExcelProperty(value = "产线/机器总数")  
    private Integer productLineNum;
    /**
     * 单块瓦数
     */
    @ApiModelProperty("单块瓦数")
    @ExcelProperty(value = "单块瓦数")  
    private BigDecimal singleW;
    /**
     * 组件块数
     */
    @ApiModelProperty("组件块数")
    @ExcelProperty(value = "组件块数")  
    private BigDecimal divisionBlock;
    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    @ExcelProperty(value = "优先级")  
    private String priority;
    /**
     * 产能开始日期
     */
    @ApiModelProperty("产能开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "产能开始日期")  
    private LocalDateTime planStartDate;
    /**
     * 利用率
     */
    @ApiModelProperty("利用率")
    @ExcelProperty(value = "利用率")  
    private BigDecimal useRate;
    /**
     * 每小时产能
     */
    @ApiModelProperty("每小时产能")
    @ExcelProperty(value = "每小时产能")  
    private BigDecimal capacity;
    /**
     * 产能单位
     */
    @ApiModelProperty("产能单位")
    @ExcelProperty(value = "产能单位")  
    private String capacityUnit;
    /**
     * 产能标识
     */
    @ApiModelProperty("产能标识")
    @ExcelProperty(value = "产能标识")  
    private Integer capacityType;
    /**
     * 指定开线数
     */
    @ApiModelProperty("指定开线数")
    @ExcelProperty(value = "指定开线数")  
    private BigDecimal openLineNum;
    /**
     * 损失比例（%）
     */
    @ApiModelProperty("损失比例（%）")
    @ExcelProperty(value = "损失比例（%）")  
    private BigDecimal lossRate;
    /**
     * APS出勤小时
     */
    @ApiModelProperty("APS出勤小时")
    @ExcelProperty(value = "APS出勤小时")  
    private Integer workHour;
    /**
     * 空产能使用标识
     */
    @ApiModelProperty("空产能使用标识")
    @ExcelProperty(value = "空产能使用标识")  
    private String useFlag;
    /**
     * 总制造时间
     */
    @ApiModelProperty("总制造时间")
    @ExcelProperty(value = "总制造时间")  
    private Integer totalHour;
    /**
     * 总切换时间
     */
    @ApiModelProperty("总切换时间")
    @ExcelProperty(value = "总切换时间")  
    private Integer switchHour;
    /**
     * 总占用时间
     */
    @ApiModelProperty("总占用时间")
    @ExcelProperty(value = "总占用时间")  
    private Integer holdUpHour;
    /**
     * 每日空产能块数
     */
    @ApiModelProperty("每日空产能块数")
    @ExcelProperty(value = "每日空产能块数")  
    private BigDecimal blockQuantity;
    /**
     * 每日空产能兆瓦数
     */
    @ApiModelProperty("每日空产能兆瓦数")
    @ExcelProperty(value = "每日空产能兆瓦数")  
    private BigDecimal mwQuantity;
    /**
     * 空产能版本号
     */
    @ApiModelProperty("空产能版本号")
    @ExcelProperty(value = "空产能版本号")  
    private String batchNo;
}