package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("基础信息缺失报表对象")
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanBaseInfoMissingReportDTO {
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;


    /**
     * 物料中类
     */
    @ApiModelProperty(value = "物料中类")
    @ExcelProperty(value = "物料中类")
    private String categorySegment2;

    /**
     * 物料编码（新）
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "料号描述")
    private String itemDesc;

    /**
     * 缺失合格供应商
     */
    @ApiModelProperty("缺失合格供应商")
    @ExcelProperty(value = "缺失合格供应商")
    private String missingQualifiedSupplier;

    /**
     * 缺失采购属性
     */
    @ApiModelProperty("缺失采购属性")
    @ExcelProperty(value = "缺失采购属性")
    private String missingPurchaseMaterial;
}
