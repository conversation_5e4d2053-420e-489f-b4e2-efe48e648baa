package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("组件计划生产日历-正式表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)  
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanCalendarDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private String workCenterCode;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "日期")  
    private LocalDateTime dayTime;
    /**
     * 产线总数
     */
    @ApiModelProperty("产线总数")
    @ExcelProperty(value = "产线总数")  
    private Integer productionLineNum;
    /**
     * 开线数
     */
    @ApiModelProperty("开线数")
    @ExcelProperty(value = "开线数")  
    private BigDecimal attendanceNum;
    /**
     * 出勤模式
     */
    @ApiModelProperty("出勤模式")
    @ExcelProperty(value = "出勤模式")  
    private String attendanceType;
    /**
     * APS出勤小时
     */
    @ApiModelProperty("APS出勤小时")
    @ExcelProperty(value = "APS出勤小时")  
    private BigDecimal workHour;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")  
    private String batchNo;
    /**
     * 计划发布版本
     */
    @ApiModelProperty("计划发布版本")
    @ExcelProperty(value = "计划发布版本")  
    private String planVersion;
    /**
     * aps模型代码
     */
    @ApiModelProperty("aps模型代码")
    @ExcelProperty(value = "aps模型代码")  
    private String modelType;
}