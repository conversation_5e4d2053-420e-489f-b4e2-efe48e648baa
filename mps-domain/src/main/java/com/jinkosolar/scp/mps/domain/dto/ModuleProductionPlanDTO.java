package com.jinkosolar.scp.mps.domain.dto;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Joiner;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.jip.api.dto.sap.SapPlanDto;
import com.jinkosolar.scp.mps.domain.constant.SplitConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.jinkosolar.scp.mps.domain.constant.Constants.EMPTY_CAPACITY_NAME;


@ApiModel("组件排产计划表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanDTO extends PageDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * DP_ID
     */
    @ApiModelProperty("DP_ID")
    @ExcelProperty(value = "DP_ID")
    private String dpId;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String versionNum;

    /**
     * 排产行Code
     */
    @ApiModelProperty("排产行Code")
    @ExcelProperty(value = "排产行Code")
    private String scheduleCode;

    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNo;

    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")
    private String sapLineId;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    private String planLayout;

    /**
     * 物料编码（新）
     */
    @ApiModelProperty("物料编码（新）")
    @ExcelProperty(value = "物料编码（新）")
    private String newItemCode;

    /**
     * 物料编码（旧）
     */
    @ApiModelProperty("物料编码（旧）")
    @ExcelProperty(value = "物料编码（旧）")
    private String oldItemCode;

    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")
    private BigDecimal power;

    /**
     * 需求数量
     */
    @ApiModelProperty("需求数量")
    @ExcelProperty(value = "需求数量")
    private BigDecimal demandQty;

    /**
     * 需求MW数
     */
    @ApiModelProperty("需求MW数")
    @ExcelProperty(value = "需求MW数")
    private BigDecimal mwQuantity;

    /**
     * 合同要求货好日期
     */
    @ApiModelProperty("合同要求货好日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "合同要求货好日期")
    private LocalDateTime plannedCompleteDate;

    /**
     * 合同要求货好日期
     */
    @ApiModelProperty("合同要求货好日期")
    @ExcelProperty(value = "合同要求货好日期")
    private String plannedCompleteDateStr;

    /**
     * 工厂更新货好时间
     */
    @ApiModelProperty("工厂更新货好时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "工厂更新货好时间")
    private LocalDateTime factoryCompleteDate;

    /**
     * 工厂更新货好时间
     */
    @ApiModelProperty("工厂更新货好时间")
    @ExcelProperty(value = "工厂更新货好时间")
    private String factoryCompleteDateStr;

    /**
     * 工厂
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;

    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 车间
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKSHOP, fieldName = "workshopsDesc")
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workshopId;

    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshopsCode;

    /**
     * 车间代码描述
     */
    @ApiModelProperty("车间代码描述")
    @ExcelProperty(value = "车间代码描述")
    private String workshopsDesc;

    /**
     * 工作中心
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKCENTER, fieldName = "workCenterDesc")
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenterId;

    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;

    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;

    /**
     * 计划排产量（工作表制造数量）
     */
    @ApiModelProperty("计划排产量（工作表制造数量）")
    @ExcelProperty(value = "计划排产量（工作表制造数量）")
    private BigDecimal planQty;

    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @JsonFormat(pattern = "yyyy/MM/dd")
    @ExcelProperty(value = "排产开始时间")
    private LocalDateTime scheduleStartDate;

    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    @JsonFormat(pattern = "yyyy/MM/dd")
    @ExcelProperty(value = "排产结束时间")
    private LocalDateTime scheduleEndDate;

    /**
     * aps排产日期
     */
    @ApiModelProperty(value = "aps排产日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate apsPlanDate;

    /**
     * aps排产开始日期
     */
    @ApiModelProperty(value = "aps排产开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate apsBeginDate;

    /**
     * aps排产结束日期
     */
    @ApiModelProperty(value = "aps排产结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate apsEndDate;

    /**
     * 事业部
     */
    @Dict(headerCode = LovHeaderCodeConstant.BUSINESS_DEPARTMENT)
    @ApiModelProperty("事业部")
    @ExcelProperty(value = "事业部")
    private String divisionId;

    /**
     * 事业部
     */
    @ApiModelProperty("事业部")
    @ExcelProperty(value = "事业部")
    private String divisionIdName;

    /**
     * 国内/海外/山西
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA)
    @ApiModelProperty("国内/海外/山西")
    @ExcelProperty(value = "国内/海外/山西")
    private String domesticOversea;

    /**
     * 国内/海外/山西
     */
    @ApiModelProperty("国内/海外/山西")
    @ExcelProperty(value = "国内/海外/山西")
    private String domesticOverseaName;

    /**
     * 是否特殊需求
     */
    @ApiModelProperty("是否特殊需求")
    @ExcelProperty(value = "是否特殊需求")
    private String demandSpecialFlag;

    /**
     * 指定硅料厂家
     */
    @ApiModelProperty("指定硅料厂家")
    @ExcelProperty(value = "指定硅料厂家")
    private String specifyFactory;

    /**
     * 电池尺寸
     */
    @ApiModelProperty("电池尺寸")
    @ExcelProperty(value = "电池尺寸")
    private String cellSize;

    /**
     * 单双玻
     */
    @ApiModelProperty("单双玻")
    @ExcelProperty(value = "单双玻")
    private String sinDouId;

    /**
     * 片串
     */
    @ApiModelProperty("片串")
    @ExcelProperty(value = "片串")
    private String chipStringId;

    /**
     * 工艺
     */
    @ApiModelProperty("工艺")
    @ExcelProperty(value = "工艺")
    private String technique;

    /**
     * 电池类型
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_BATTERY_TYPE)
    @ApiModelProperty("电池类型")
    @ExcelProperty(value = "电池类型")
    private String cellType;

    /**
     * 电池类型名称
     */
    @ApiModelProperty("电池类型名称")
    @ExcelProperty(value = "电池类型名称")
    private String cellTypeName;

    /**
     * 组件类型
     */
    @ApiModelProperty("组件类型")
    @ExcelProperty(value = "组件类型")
    private String moduleType;

    /**
     * 背板颜色
     */
    @ApiModelProperty("背板颜色")
    @ExcelProperty(value = "背板颜色")
    private String backSheetColorId;

    /**
     * PM_code
     */
    @ApiModelProperty("PM_code")
    @ExcelProperty(value = "PM_code")
    private String pmCode;

    /**
     * 组件特征代码
     */
    @ApiModelProperty("组件特征代码")
    @ExcelProperty(value = "组件特征代码")
    private String moduleFeaturesCode;

    /**
     * 组件特征描述
     */
    @ApiModelProperty("组件特征描述")
    @ExcelProperty(value = "组件特征描述")
    private String moduleFeaturesDesc;

    /**
     * 电压
     */
    @ApiModelProperty("电压")
    @ExcelProperty(value = "电压")
    private String voltage;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    @ExcelProperty(value = "订单类型")
    private String orderTypeId;

    /**
     * 目的地区域
     */
    @ApiModelProperty("目的地区域")
    @ExcelProperty(value = "目的地区域")
    private String destAreaNo;

    /**
     * 目的地国家
     */
    @ApiModelProperty("目的地国家")
    @ExcelProperty(value = "目的地国家")
    private String destCountry;

    /**
     * 港口/目的地
     */
    @ApiModelProperty("港口/目的地")
    @ExcelProperty(value = "港口/目的地")
    private String destPort;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称")
    private String customer;

    /**
     * 客户编码
     */
    @ApiModelProperty("客户编码")
    @ExcelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 追溯条款
     */
    @ApiModelProperty("追溯条款")
    @ExcelProperty(value = "追溯条款")
    private String tracedBackReason;

    /**
     * 验货描述
     */
    @ApiModelProperty("验货描述")
    @ExcelProperty(value = "验货描述")
    private String customerInspectionReason;

    /**
     * 是否特批订单
     */
    @ApiModelProperty("是否特批订单")
    @ExcelProperty(value = "是否特批订单")
    private String specialFlag;

    /**
     * 特批原因
     */
    @ApiModelProperty("特批原因")
    @ExcelProperty(value = "特批原因")
    private String specialOrderReason;

    /**
     * 变更原因
     */
    @ApiModelProperty("变更原因")
    @ExcelProperty(value = "变更原因")
    private String reqChangeReason;

    /**
     * 贸易方式
     */
    @ApiModelProperty("贸易方式")
    @ExcelProperty(value = "贸易方式")
    private String tradeMode;

    /**
     * 财务评级
     */
    @ApiModelProperty("财务评级")
    @ExcelProperty(value = "财务评级")
    private String financialRatings;

    /**
     * 客户评级
     */
    @ApiModelProperty("客户评级")
    @ExcelProperty(value = "客户评级")
    private String customerRatings;

    /**
     * Limited warranty
     */
    @ApiModelProperty("Limited warranty")
    @ExcelProperty(value = "Limited warranty")
    private String limitedWarranty;

    /**
     * 保险
     */
    @ApiModelProperty("保险")
    @ExcelProperty(value = "保险")
    private String insurance;

    /**
     * 报关主体
     */
    @ApiModelProperty("报关主体")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "报关主体")
    private LocalDateTime customsMainSubject;

    /**
     * 运输方式
     */
    @ApiModelProperty("运输方式")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "运输方式")
    private LocalDateTime transports;

    /**
     * 要求供货工厂
     */
    @ApiModelProperty("要求供货工厂")
    @ExcelProperty(value = "要求供货工厂")
    private String productFactory;

    /**
     * 拆分行
     */
    @ApiModelProperty("拆分行")
    @ExcelProperty(value = "拆分行")
    private String parentId;

    /**
     * CRM行号
     */
    @ApiModelProperty("CRM行号")
    @ExcelProperty(value = "CRM行号")
    private String crmLinesId;

    /**
     * 主/副产品
     */
    @ApiModelProperty("主/副产品")
    @ExcelProperty(value = "主/副产品")
    private String primaryProduct;

    /**
     * 要求离厂日期
     */
    @ApiModelProperty("要求离厂日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "要求离厂日期")
    private LocalDateTime scheduleShipDate;

    /**
     * 要求离港日期
     */
    @ApiModelProperty("要求离港日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "要求离港日期")
    private LocalDateTime etd;
//    /**
//     * 生产类型
//     */
//    @Dict(headerCode = LovHeaderCodeConstant.DP_PRD_TYPE)
//    @ApiModelProperty("生产类型")
//    @ExcelProperty(value = "生产类型")
//    private Long productionType;

    /**
     * 生产类型
     */
    @ApiModelProperty("生产类型")
    @ExcelProperty(value = "生产类型")
    private String productionType;

    /**
     * 生产基地
     */
    @ApiModelProperty(" 生产基地")
    @ExcelProperty(value = " 生产基地")
    private String specifyBase;

    /**
     * 投产方案（物料搭配）
     */
    @ApiModelProperty("投产方案（物料搭配）")
    @ExcelProperty(value = "投产方案（物料搭配）")
    private String startUpPlan;

    /**
     * 功率预测版本
     */
    @ApiModelProperty("功率预测版本")
    @ExcelProperty(value = "功率预测版本")
    private String powerForecastVersion;

    /**
     * 是否 定向
     */
    @ApiModelProperty(" 是否 定向")
    @ExcelProperty(value = " 是否 定向")
    private String batchDirectional;

    /**
     * 是否 追溯
     */
    @ApiModelProperty("是否 追溯")
    @ExcelProperty(value = "是否 追溯")
    private String tracedBack;

    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    @ExcelProperty(value = "是否监造")
    private String supervisionFlag;

    /**
     * 是否验货
     */
    @ApiModelProperty("是否验货")
    @ExcelProperty(value = "是否验货")
    private String customerInspectionFlag;

    /**
     * 是否5W分档
     */
    @ApiModelProperty("是否5W分档")
    @ExcelProperty(value = "是否5W分档")
    private String fiveWBinnedFlag;

    /**
     * 5W分档组
     */
    @ApiModelProperty("5W分档组")
    @ExcelProperty(value = "5W分档组")
    private String fiveWBinned;

    /**
     * 是否指定实验线
     */
    @ApiModelProperty("是否指定实验线")
    @ExcelProperty(value = "是否指定实验线")
    private String reviewFlag;

    /**
     * 是否标准评审
     */
    @ApiModelProperty(value = "是否标准评审")
    @ExcelProperty(value = "是否标准评审")
    private String placeOrderFlag;

    /**
     * 建议排产开始日期
     */
    @ApiModelProperty(value = "建议排产开始日期")
    @ExcelProperty(value = "建议排产开始日期")
    private LocalDateTime plannedWipDate;

    /**
     * 是否贴牌订单
     */
    @ApiModelProperty("是否贴牌订单")
    @ExcelProperty(value = "是否贴牌订单")
    private String productionPower;

    /**
     * 高档比例
     */
    @ApiModelProperty("高档比例")
    @ExcelProperty(value = "高档比例")
    private BigDecimal highGrade;

    /**
     * 低档比例
     */
    @ApiModelProperty("低档比例")
    @ExcelProperty(value = "低档比例")
    private BigDecimal lowGrade;

    /**
     * 客户合同交期/GDD
     */
    @ApiModelProperty("客户合同交期/GDD")
    @ExcelProperty(value = "客户合同交期/GDD")
    private String orderDate;

    /**
     * 产能替换订单号&行号
     */
    @ApiModelProperty("产能替换订单号&行号")
    @ExcelProperty(value = "产能替换订单号&行号")
    private String capacityReplacementLineId;

    /**
     * MDA ID
     */
    @ApiModelProperty("MDA ID")
    @ExcelProperty(value = "MDA ID")
    private String mdaId;

    /**
     * 组件id
     */
    @ApiModelProperty("组件id")
    @ExcelProperty(value = "组件id")
    private String moduleId;

    /**
     * 生产管理员
     */
    @ApiModelProperty("生产管理员")
    @ExcelProperty(value = "生产管理员")
    private String productionAdmin;

    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")
    private String batchNo;

    /**
     * 是否赠送功率-MDA表获取
     */
    @ApiModelProperty(" 是否赠送功率-MDA表获取 ")
    @ExcelProperty(value = " 是否赠送功率-MDA表获取 ")
    private String itemAttribute1;

    /**
     * 赠送功率（W/块）-MDA表获取
     */
    @ApiModelProperty("赠送功率（W/块）-MDA表获取 ")
    @ExcelProperty(value = "赠送功率（W/块）-MDA表获取 ")
    private String itemAttribute2;

    /**
     * 接线盒型号-MDA表获取
     */
    @ApiModelProperty("接线盒型号-MDA表获取 ")
    @ExcelProperty(value = "接线盒型号-MDA表获取 ")
    private String itemAttribute3;

    /**
     * 连接头型号-MDA表获取
     */
    @ApiModelProperty("连接头型号-MDA表获取 ")
    @ExcelProperty(value = "连接头型号-MDA表获取 ")
    private String itemAttribute4;

    /**
     * 导线长度-MDA表获取
     */
    @ApiModelProperty("导线长度-MDA表获取 ")
    @ExcelProperty(value = "导线长度-MDA表获取 ")
    private String itemAttribute5;

    /**
     * 边框颜色-MDA表获取
     */
    @ApiModelProperty("边框颜色-MDA表获取 ")
    @ExcelProperty(value = "边框颜色-MDA表获取 ")
    private String itemAttribute6;

    /**
     * 边框氧化膜厚度-MDA表获取
     */
    @ApiModelProperty("边框氧化膜厚度-MDA表获取 ")
    @ExcelProperty(value = "边框氧化膜厚度-MDA表获取 ")
    private String itemAttribute7;

    /**
     * 背板类型-MDA表获取
     */
    @ApiModelProperty("背板类型-MDA表获取 ")
    @ExcelProperty(value = "背板类型-MDA表获取 ")
    private String itemAttribute8;

    /**
     * 胶膜要求-MDA表获取
     */
    @ApiModelProperty("胶膜要求-MDA表获取 ")
    @ExcelProperty(value = "胶膜要求-MDA表获取 ")
    private String itemAttribute9;

    /**
     * 生产前验厂-MDA表获取
     */
    @ApiModelProperty("生产前验厂-MDA表获取 ")
    @ExcelProperty(value = "生产前验厂-MDA表获取 ")
    private String itemAttribute10;

    /**
     * 生产前验厂（备注）-MDA表获取
     */
    @ApiModelProperty("生产前验厂（备注）-MDA表获取 ")
    @ExcelProperty(value = "生产前验厂（备注）-MDA表获取 ")
    private String itemAttribute11;

    /**
     * 客户是否验货或驻厂监造-MDA表获取
     */
    @ApiModelProperty("客户是否验货或驻厂监造-MDA表获取")
    @ExcelProperty(value = "客户是否验货或驻厂监造-MDA表获取")
    private String itemAttribute12;

    /**
     * 客户是否验货或驻厂监造（备注)
     */
    @ApiModelProperty("客户是否验货或驻厂监造（备注)")
    @ExcelProperty(value = "客户是否验货或驻厂监造（备注)")
    private String itemAttribute13;

    /**
     * 工厂验货-MDA表获取
     */
    @ApiModelProperty("工厂验货-MDA表获取")
    @ExcelProperty(value = "工厂验货-MDA表获取")
    private String itemAttribute14;

    /**
     * 工厂验货（备注）-MDA表获取
     */
    @ApiModelProperty("工厂验货（备注）-MDA表获取")
    @ExcelProperty(value = "工厂验货（备注）-MDA表获取")
    private String itemAttribute15;

    /**
     * 实验测试-MDA表获取
     */
    @ApiModelProperty("实验测试-MDA表获取")
    @ExcelProperty(value = "实验测试-MDA表获取")
    private String itemAttribute16;

    /**
     * 颜色标签-MDA表获取
     */
    @ApiModelProperty("颜色标签-MDA表获取")
    @ExcelProperty(value = "颜色标签-MDA表获取")
    private String itemAttribute17;

    /**
     * 包装-MDA表获取
     */
    @ApiModelProperty("包装-MDA表获取")
    @ExcelProperty(value = "包装-MDA表获取")
    private String itemAttribute18;

    /**
     * 包装（备注）-MDA表获取
     */
    @ApiModelProperty("包装（备注）-MDA表获取")
    @ExcelProperty(value = "包装（备注）-MDA表获取")
    private String itemAttribute19;

    /**
     * 铭牌样式-MDA表获取
     */
    @ApiModelProperty("铭牌样式-MDA表获取")
    @ExcelProperty(value = "铭牌样式-MDA表获取")
    private String itemAttribute20;

    /**
     * 产品认证-MDA表获取
     */
    @ApiModelProperty("产品认证-MDA表获取")
    @ExcelProperty(value = "产品认证-MDA表获取")
    private String itemAttribute21;

    /**
     * 区域强制认证-MDA表获取
     */
    @ApiModelProperty("区域强制认证-MDA表获取")
    @ExcelProperty(value = "区域强制认证-MDA表获取")
    private String itemAttribute22;

    /**
     * 区域认证：CFP-MDA表获取
     */
    @ApiModelProperty("区域认证：CFP-MDA表获取")
    @ExcelProperty(value = "区域认证：CFP-MDA表获取")
    private String itemAttribute23;

    /**
     * 区域认证：NPB-MDA表获取
     */
    @ApiModelProperty("区域认证：NPB-MDA表获取")
    @ExcelProperty(value = "区域认证：NPB-MDA表获取")
    private String itemAttribute24;

    /**
     * 功率公差从-MDA表获取
     */
    @ApiModelProperty("功率公差从-MDA表获取")
    @ExcelProperty(value = "功率公差从-MDA表获取")
    private String itemAttribute25;

    /**
     * 功率公差至-MDA表获取
     */
    @ApiModelProperty("功率公差至-MDA表获取")
    @ExcelProperty(value = "功率公差至-MDA表获取")
    private String itemAttribute26;

    /**
     * 是否需使用缠绕膜-MDA表获取
     */
    @ApiModelProperty("是否需使用缠绕膜-MDA表获取")
    @ExcelProperty(value = "是否需使用缠绕膜-MDA表获取")
    private String itemAttribute27;

    /**
     * 是否需使用缠绕膜（备注）-MDA表获
     */
    @ApiModelProperty("是否需使用缠绕膜（备注）-MDA表获")
    @ExcelProperty(value = "是否需使用缠绕膜（备注）-MDA表获")
    private String itemAttribute28;

    /**
     * 是否需要防尘塞-MDA表获取
     */
    @Translate(DictType = "x")
    @ApiModelProperty(" 是否需要防尘塞-MDA表获取 ")
    @ExcelProperty(value = " 是否需要防尘塞-MDA表获取 ")
    private String itemAttribute29;

    /**
     * 是否需要防尘塞-MDA表获取
     */
    @ApiModelProperty(" 是否需要防尘塞-MDA表获取 ")
    @ExcelProperty(value = " 是否需要防尘塞-MDA表获取 ")
    private String itemAttribute29Name;

    /**
     * 是否需要防震标签-MDA表获取
     */
    @ApiModelProperty(" 是否需要防震标签-MDA表获取 ")
    @ExcelProperty(value = " 是否需要防震标签-MDA表获取 ")
    private String itemAttribute30;

    /**
     * ISO Achieved Later
     */
    @ApiModelProperty(" ISO Achieved Later")
    @ExcelProperty(value = " ISO Achieved Later")
    private String itemAttribute31;

    /**
     * ISO Certificate Ty
     */
    @ApiModelProperty(" ISO Certificate Ty ")
    @ExcelProperty(value = " ISO Certificate Ty ")
    private String itemAttribute32;

    /**
     * ISO Certificate-MD
     */
    @ApiModelProperty(" ISO Certificate-MD")
    @ExcelProperty(value = " ISO Certificate-MD")
    private String itemAttribute33;

    /**
     * DS版本-MDA表获取
     */
    @ApiModelProperty(" DS版本-MDA表获取 ")
    @ExcelProperty(value = " DS版本-MDA表获取 ")
    private String itemAttribute34;

    /**
     * 边框高度(mm)-MDA表获取
     */
    @ApiModelProperty(" 边框高度(mm)-MDA表获取")
    @ExcelProperty(value = " 边框高度(mm)-MDA表获取")
    private String itemAttribute35;

    /**
     * 短边C面宽度(mm)-MDA表获取
     */
    @ApiModelProperty(" 短边C面宽度(mm)-MDA表获取")
    @ExcelProperty(value = " 短边C面宽度(mm)-MDA表获取")
    private String itemAttribute36;

    /**
     * 长边C面宽度(mm)-MDA表获取
     */
    @ApiModelProperty(" 长边C面宽度(mm)-MDA表获取")
    @ExcelProperty(value = " 长边C面宽度(mm)-MDA表获取")
    private String itemAttribute37;

    /**
     * 边框安装孔距-MDA表获取
     */
    @ApiModelProperty(" 边框安装孔距-MDA表获取 ")
    @ExcelProperty(value = " 边框安装孔距-MDA表获取 ")
    private String itemAttribute38;

    /**
     * 组件重量(KG)-MDA表获取
     */
    @ApiModelProperty(" 组件重量(KG)-MDA表获取")
    @ExcelProperty(value = " 组件重量(KG)-MDA表获取")
    private String itemAttribute39;

    /**
     * 主栅-MDA表获取
     */
    @ApiModelProperty(" 主栅-MDA表获取")
    @ExcelProperty(value = " 主栅-MDA表获取")
    private String itemAttribute40;

    /**
     * 电池片长度(mm)-MDA表获取
     */
    @ApiModelProperty(" 电池片长度(mm)-MDA表获取 ")
    @ExcelProperty(value = " 电池片长度(mm)-MDA表获取 ")
    private String itemAttribute41;

    /**
     * 电池片宽度(mm)-MDA表获取
     */
    @ApiModelProperty(" 电池片宽度(mm)-MDA表获取 ")
    @ExcelProperty(value = " 电池片宽度(mm)-MDA表获取 ")
    private String itemAttribute42;

    /**
     * 包装数据(片）-MDA表获取
     */
    @ApiModelProperty(" 包装数据(片）-MDA表获取 ")
    @ExcelProperty(value = " 包装数据(片）-MDA表获取 ")
    private String itemAttribute43;

    /**
     * 转库存率-MDA表获取
     */
    @ApiModelProperty(" 转库存率-MDA表获取 ")
    @ExcelProperty(value = " 转库存率-MDA表获取 ")
    private String itemAttribute44;

    /**
     * 正面胶膜克重-MDA表获取
     */
    @ApiModelProperty(" 正面胶膜克重-MDA表获取 ")
    @ExcelProperty(value = " 正面胶膜克重-MDA表获取 ")
    private String itemAttribute45;

    /**
     * 背面胶膜克重-MDA表获取
     */
    @ApiModelProperty(" 背面胶膜克重-MDA表获取 ")
    @ExcelProperty(value = " 背面胶膜克重-MDA表获取 ")
    private String itemAttribute46;

    /**
     * 背玻镀膜-MDA表获取
     */
    @ApiModelProperty(" 背玻镀膜-MDA表获取 ")
    @ExcelProperty(value = " 背玻镀膜-MDA表获取 ")
    private String itemAttribute47;

    /**
     * 指定基地-MDA表获取
     */
    @ApiModelProperty(" 指定基地-MDA表获取 ")
    @ExcelProperty(value = " 指定基地-MDA表获取 ")
    private String itemAttribute48;

    /**
     * 指定车间-MDA表获取
     */
    @ApiModelProperty(" 指定车间-MDA表获取 ")
    @ExcelProperty(value = " 指定车间-MDA表获取 ")
    private String itemAttribute49;

    /**
     * 是否接受认证候补-MDA表获取
     */
    @ApiModelProperty(" 是否接受认证候补-MDA表获取")
    @ExcelProperty(value = " 是否接受认证候补-MDA表获取")
    private String itemAttribute50;

    /**
     * 扩展字段51
     */
    @ApiModelProperty(value = "扩展字段51")
    private String itemAttribute51;

    /**
     * 扩展字段52
     */
    @ApiModelProperty(value = "扩展字段52")
    private String itemAttribute52;

    /**
     * 扩展字段53
     */
    @ApiModelProperty(value = "扩展字段53")
    private String itemAttribute53;

    /**
     * 扩展字段54
     */
    @ApiModelProperty(value = "扩展字段54")
    private String itemAttribute54;

    /**
     * 扩展字段55
     */
    @ApiModelProperty(value = "扩展字段55")
    private String itemAttribute55;

    /**
     * 扩展字段56
     */
    @ApiModelProperty(value = "扩展字段56")
    private String itemAttribute56;

    /**
     * 扩展字段57
     */
    @ApiModelProperty(value = "扩展字段57")
    private String itemAttribute57;

    /**
     * 扩展字段58
     */
    @ApiModelProperty(value = "扩展字段58")
    @Column(name = "item_attribute58")
    private String itemAttribute58;

    /**
     * 扩展字段59
     */
    @ApiModelProperty(value = "扩展字段59")
    private String itemAttribute59;

    /**
     * 扩展字段60
     */
    @ApiModelProperty(value = "扩展字段60")
    private String itemAttribute60;

    /**
     * 扩展字段61
     */
    @ApiModelProperty(value = "扩展字段61")
    private String itemAttribute61;

    /**
     * 扩展字段62
     */
    @ApiModelProperty(value = "扩展字段62")
    @Column(name = "item_attribute62")
    private String itemAttribute62;

    /**
     * 扩展字段63
     */
    @ApiModelProperty(value = "扩展字段63")
    private String itemAttribute63;

    /**
     * 扩展字段64
     */
    @ApiModelProperty(value = "扩展字段64")
    @Column(name = "item_attribute64")
    private String itemAttribute64;

    /**
     * 扩展字段65
     */
    @ApiModelProperty(value = "扩展字段65")
    private String itemAttribute65;

    /**
     * 扩展字段66
     */
    @ApiModelProperty(value = "扩展字段66")
    private String itemAttribute66;

    /**
     * 扩展字段67
     */
    @ApiModelProperty(value = "扩展字段67")
    @Column(name = "item_attribute67")
    private String itemAttribute67;

    /**
     * 扩展字段68
     */
    @ApiModelProperty(value = "扩展字段68")
    private String itemAttribute68;

    /**
     * 扩展字段69
     */
    @ApiModelProperty(value = "扩展字段69")
    @Column(name = "item_attribute69")
    private String itemAttribute69;

    /**
     * 扩展字段70
     */
    @ApiModelProperty(value = "扩展字段70")
    private String itemAttribute70;

    /**
     * 扩展字段71
     */
    @ApiModelProperty(value = "扩展字段71")
    @Column(name = "item_attribute71")
    private String itemAttribute71;

    /**
     * 扩展字段72
     */
    @ApiModelProperty(value = "扩展字段72")
    private String itemAttribute72;

    /**
     * 扩展字段73:排产行排序号
     */
    @ApiModelProperty(value = "排产行排序号")
    @Column(name = "item_attribute73")
    private String itemAttribute73;

    /**
     * 扩展字段74
     */
    @ApiModelProperty(value = "扩展字段74")
    private String itemAttribute74;

    /**
     * 扩展字段75
     */
    @ApiModelProperty(value = "扩展字段75")
    private String itemAttribute75;

    /**
     * 扩展字段76
     */
    @ApiModelProperty(value = "扩展字段76")
    private String itemAttribute76;

    /**
     * 合同状态
     */
    @ApiModelProperty(value = "合同状态")
    private String itemAttribute77;

    /**
     * 扩展字段78
     */
    @ApiModelProperty(value = "预留截止日期")
    private String itemAttribute78;

    /**
     * 扩展字段80
     */
    @ApiModelProperty(value = "扩展字段80")
    private String itemAttribute80;

    /**
     * 扩展字段81
     */
    @ApiModelProperty(value = "产能预留天数")
    @ExcelProperty(value = "产能预留天数")
    private String itemAttribute81;

    /**
     * 动态列名
     */
    @ApiModelProperty("动态列名")
    private List<String> dynamicColumnList;

    /**
     * 动态日期列
     */
    @ApiModelProperty("动态日期列")
    private Map<String, Object> dynamicColumnMap;

    /**
     * 计划版本
     */
    @ApiModelProperty("计划版本")
    @ExcelProperty(value = "计划版本")
    private String planVersion;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")
    private String productCode;

    /**
     * 材料搭配结果描述
     */
    @ApiModelProperty("材料搭配结果描述")
    @ExcelProperty(value = "材料搭配结果描述")
    private String accessoryResultDesc;

    /**
     * 电池标准效率
     */
    @ExcelProperty(value = "电池标准效率")
    @ApiModelProperty(value = " 电池标准效率")
    private String standardCellEfficiency;

    /**
     * 排产良率
     */
    @ApiModelProperty(value = " 排产良率")
    @Column(name = "yield")
    private BigDecimal yield;

    /**
     * 空产能标记
     */
    @ApiModelProperty("空产能标记")
    @ExcelProperty(value = "空产能标记")
    private String emptyCapacityFlag;

    /**
     * 生产通知书更新时间
     */
    @DateTimeFormat(pattern = "yyyy/MM/dd HH:mm:ss")
    @ApiModelProperty("生产通知书更新时间")
    @ExcelProperty(value = "生产通知书更新时间")
    private LocalDateTime updatedTime;

    /**
     * 生产通知书更新时间
     */
    @ApiModelProperty("生产通知书更新时间")
    @ExcelProperty(value = "生产通知书更新时间")
    private String updatedTimeStr;

    /**
     * 实际良率
     */
    @ApiModelProperty(value = " 实际良率")
    @ExcelProperty(value = "实际良率")
    private BigDecimal actualYield;

    /**
     * 拆分数量
     */
    @ApiModelProperty(value = " 拆分数量")
    @ExcelProperty(value = "拆分数量")
    private BigDecimal splitQty;

    /**
     * 拆分产线
     */
    @ApiModelProperty(value = " 拆分产线")
    @ExcelProperty(value = "拆分产线")
    private String splitProductLine;

    /**
     * 输出焊带
     */
    @ApiModelProperty(value = " 输出焊带")
    @ExcelProperty(value = "输出焊带")
    private String outputSolder;

    /**
     * 合同号
     */
    @ApiModelProperty(value = " 合同号")
    @ExcelProperty(value = "合同号")
    private String contractNo;

    /**
     * 销售主体
     */
    @ApiModelProperty(value = " 销售主体")
    @ExcelProperty(value = "销售主体")
    private String saleBody;

    /**
     * 项目分类
     */
    @ApiModelProperty(value = " 项目分类")
    @ExcelProperty(value = "项目分类")
    private String projectType;

    /**
     * 延期天数
     */
    @ApiModelProperty(value = " 延期天数")
    @ExcelProperty(value = "延期天数")
    private long delayDays;

    /**
     * 延期月份数
     */
    @ApiModelProperty(value = " 延期月份数")
    @ExcelProperty(value = "延期月份数")
    private long delayMonths;

    /**
     * 虚拟订单号
     */
    @ApiModelProperty(value = " 虚拟订单号")
    @ExcelProperty(value = "虚拟订单号")
    private String itemAttribute79;

    /**
     * 虚拟订单号
     */
    @ApiModelProperty(value = " 虚拟订单号")
    @ExcelProperty(value = "虚拟订单号")
    private String apsOrderType;

    /**
     * OEM标识
     */
    @ApiModelProperty(value = "OEM标识")
    @ExcelProperty(value = "OEM标识")
    private String oemFlag;

    @ApiModelProperty(value = "工厂/车间/工作中心LOV排序")
    @ExcelProperty(value = "工厂/车间/工作中心LOV排序")
    private Integer sortIndex;

    @ApiModelProperty(value = "版本发布备注")
    @ExcelProperty(value = "版本发布备注")
    private String planVersionRemark;

    /**
     * 硅料类型
     */
    @ApiModelProperty(value = "硅料类型")
    @ExcelProperty(value = "硅料类型")
    private String siliconType;

    /**
     * 组件单双面
     */
    @ApiModelProperty("组件单双面")
    @ExcelProperty(value = "组件单双面")
    private String itemAttribute82;

    /**
     * 生产确认
     */
    @ApiModelProperty("生产确认")
    @ExcelProperty(value = "生产确认")
    private String productionReason;

    /**
     * 状态，P为预发布
     */
    private String status;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 扩展字段83
     */
    @ApiModelProperty(value = "扩展字段83")
    private String itemAttribute83;
    /**
     * 扩展字段84
     */
    @ApiModelProperty(value = "扩展字段84")
    private String itemAttribute84;
    /**
     * 扩展字段85
     */
    @ApiModelProperty(value = "扩展字段85")
    private String itemAttribute85;
    /**
     * 扩展字段86
     */
    @ApiModelProperty(value = "扩展字段86")
    private String itemAttribute86;
    /**
     * 扩展字段87
     */
    @ApiModelProperty(value = "扩展字段87")
    private String itemAttribute87;
    /**
     * 扩展字段88
     */
    @ApiModelProperty(value = "扩展字段88")
    private String itemAttribute88;
    /**
     * 扩展字段89
     */
    @ApiModelProperty(value = "扩展字段89")
    private String itemAttribute89;
    /**
     * 扩展字段90
     */
    @ApiModelProperty(value = "排产差异量")
    private String itemAttribute90;
    /**
     * 扩展字段91
     */
    @ApiModelProperty(value = "订单排产量")
    private String itemAttribute91;

    /**
     * 订单行原始数量
     */
    @ApiModelProperty(value = "订单行原始数量")
    private BigDecimal sapLineQty;

    /**
     * 发布日期
     */
    @ApiModelProperty(value = "发布日期")
    private LocalDate publishDate;

    /**
     * 最大排产结束时间
     */
    @ApiModelProperty(value = "最大排产结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate maxEndDate;

    /**
     * 当前批次最大排产结束时间
     */
    @ApiModelProperty(value = "当前批次最大排产结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate currMaxEndDate;

    public static SapPlanDto toSapPlanDto(ModuleProductionPlanDTO dto) {
        return SapPlanDto.builder().PLSCN(100L).ZSCJY(dto.getId() + "").
                VBELN(dto.getSapOrderNo()).
                POSNR(Optional.ofNullable(dto.getSapLineId()).map(Long::valueOf).orElse(null)).
                ZLB(dto.getApsOrderType()).
                ZOEM(dto.getOemFlag()).
                ZDX(dto.getBatchDirectional()).
                MATERIAL(dto.getNewItemCode()).
                WERKS(dto.getFactoryCode()).
                ZJYSL(dto.getPlanQty()).
                ARBPL(dto.getWorkCenterCode()).
                PSTTR(dto.getScheduleStartDate().toLocalDate()).
                PEDTR(dto.getScheduleEndDate().toLocalDate()).
                ZCJ(Optional.ofNullable(dto.getWorkshopsCode()).orElse("no-name")).
                ZCJMS(Optional.ofNullable(dto.getWorkshopsDesc()).orElse("no-name")).
                build();
    }

    public String groupByOrderAndWorkCenter() {
        return Joiner.on(SplitConstant.SPLIT_SEPARATOR).useForNull("null")
                .join(this.sapOrderNo, this.sapLineId, this.factoryCode, this.workCenterCode, this.itemAttribute71);
    }


    public String getAccessoryKey() {
        if (EMPTY_CAPACITY_NAME.equals(this.getSapOrderNo())) {
            return this.getSapOrderNo() + "-" + this.getSapLineId() + "-" + this.getFactoryCode() + "-" + this.getWorkshopsCode() + "-" + this.getWorkCenterCode() + "-" + this.getPlanLayout();
        }
        return this.getSapOrderNo() + "-" + this.getSapLineId() + "-" + this.getFactoryCode() + "-" + this.getWorkshopsCode() + "-" + this.getWorkCenterCode() + "-" + null;
    }

    public ModuleProductionSuggestionDTO buildSuggestionDTO() {

        return ModuleProductionSuggestionDTO.builder()
                .productionPlanId(this.getId())
                .sapOrderNo(this.getSapOrderNo())
                .sapLineId(this.getSapLineId())
                .iud("I")
                .demandType(this.getOrderTypeId())
                .planNum(this.getPlanLayout())
                .apsPlanDate(this.getApsPlanDate())
                .factory(this.getFactory())
                .factoryCode(this.getFactoryCode())
                .suggestionQty(this.getPlanQty())
                .demandQty(this.getDemandQty())
                .startTime(this.getScheduleStartDate())
                .endTime(this.getScheduleEndDate())
                .workshop(this.getWorkshopId())
                .workshopDesc(this.getWorkshopsDesc())
                .workshopCode(this.getWorkshopsCode())
                .version(this.getPlanVersion())
                .workCenter(this.getWorkCenterId())
                .workCenterCode(this.getWorkCenterCode())
                .workshopCode(this.getWorkshopsCode())
                .itemCode(this.getNewItemCode())
                .oem(this.getOemFlag())
                .destAreaNo(this.getDestAreaNo())
                .itemAttribute71(this.getItemAttribute71())
                .itemAttribute72(this.getItemAttribute72())
                .workCenterCode(this.getWorkCenterCode())
                .workshopCode(this.getWorkshopsCode())
                .workshopDesc(this.getWorkshopsDesc())
                .batchDirectional(this.getBatchDirectional())
                .zpkgNum(this.getBomGroupNum())
                .zpkgLineId("10")
                .inheritFlag("N")
                .sapSyncSeq(IdUtil.getSnowflakeNextId())
                .powerForecastVersion(this.getPowerForecastVersion())
                .build();
    }

    /**
     * APS原始工作中心代码
     */
    @ApiModelProperty(value = "APS原始工作中心代码")
    @ExcelProperty(value = "APS原始工作中心代码")
    private String apsWorkCenterCode;

    /**
     * BOM头ID
     */
    @Column(name = "bom_header_id")
    private Long bomHeaderId;

    /**
     * 组合号ID
     */
    @Column(name = "bom_group_id")
    private Long bomGroupId;

    /**
     * 组合号CODE
     */
    @Column(name = "bom_group_num")
    private String bomGroupNum;

    private List<String> errors;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商id列表")
    private String specifySupplier;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商名称")
    @ExcelProperty(value = "指定硅料供应商名称")
    private String specifySupplierName;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("是否为高标准订单")
    @ExcelProperty(value = "是否为高标准订单")
    private String highStandardOrder;

   /* @ApiModelProperty(value = "产能预留天数")
    @ExcelProperty(value = "产能预留天数")
    private String reservationDays;
    @ApiModelProperty(value = "预留截止日期")
    @ExcelProperty(value = "预留截止日期")
//    LocalDate
    private String reservationDate;

    @ApiModelProperty(value = "合同状态")
    @ExcelProperty(value = "合同状态")
    private String contractStatus;*/
}
