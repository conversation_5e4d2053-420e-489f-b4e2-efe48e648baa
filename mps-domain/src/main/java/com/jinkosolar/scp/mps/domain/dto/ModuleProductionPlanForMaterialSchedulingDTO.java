package com.jinkosolar.scp.mps.domain.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@ApiModel("组件排产计划表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanForMaterialSchedulingDTO extends ModuleProductionPlanDTO implements Serializable {

    @ApiModelProperty("无bom标识")
    @ExcelProperty(value = "无bom标识")
    private String noBomFlag;

    /**
     * 材料搭配结果版本号
     */
    @ApiModelProperty(name = "材料搭配结果版本号", notes = "")
    private String dataVersion;

    /**
     * 是否历史材料搭配结果
     */
    @ApiModelProperty(name = "是否历史材料搭配结果", notes = "")
    private String historyFlag;

    /**
     * 是否继承
     */
    @ApiModelProperty(name = "是否继承", notes = "")
    private String extendsFlag;

    /**
     * bom更新时间
     */
    @ApiModelProperty(name = "bom更新时间", notes = "")
    private LocalDateTime bomUpdatedTime;

    /**
     * 落产数量
     */
    @ApiModelProperty(name = "落产数量", notes = "")
    private BigDecimal orderQuantity;

    @ApiModelProperty("动态产品结构体")
    List<String> structureTitleList;

    @ApiModelProperty("动态产品结构数据")
    List<MaterialInfoForModuleProductionPlanDTO> moduleProductionPlanWithMaterialInfoDTO;

    @ApiModelProperty("合计行")
    ModuleProductionPlanForMaterialSchedulingDTO totalLine;

    /**
     * 是否继承
     */
    @ApiModelProperty(name = "是否继承", notes = "")
    private String appointGroup;

    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "版本开始日期")
    private String planVersionStartDate;
    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "版本结束日期")
    private String planVersionEndDate;

    /**
     * 报表版本号
     */
    @ApiModelProperty(name = "报表版本号", notes = "")
    private String reportPlanVersion;

    /**
     * 有实投无排产订单标识
     */
    @ApiModelProperty("有实投无排产订单标识")
    private String actualNoOrderFlag;

    /**
     * 3.1没有五瓦分档订单按原逻辑工厂+车间+工作中心+订单号+订单行号进行合并
     * 3.2有五瓦分档订单按工厂+车间+工作中心+五瓦分档+组合号进行合并
     */
    public void updateAppointGroup() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(this.getFiveWBinned())) {
            this.setAppointGroup(this.getFiveWBinned() + "_" + this.getBomGroupNum());
        } else {
            this.setAppointGroup(this.getSapOrderNo() + "_" + this.getSapLineId());
        }
    }

    @ApiModelProperty("动态产品结构Map")
    Map<String,Long> structureMap;
}
