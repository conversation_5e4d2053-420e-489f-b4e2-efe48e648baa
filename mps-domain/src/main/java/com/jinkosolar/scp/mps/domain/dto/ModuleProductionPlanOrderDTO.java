package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ApiModel("组件排产计划报表订单数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanOrderDTO extends PageDTO implements Serializable {

    @ApiModelProperty("报表版本号")
    @ExcelProperty(value = "报表版本号")
    private String reportPlanVersion;
    /**
     * 排产版本号
     */
    @ApiModelProperty("排产版本号")
    @ExcelProperty(value = "排产版本号")
    private String planVersion;
    /**
     * DP_ID
     */
    @ApiModelProperty("DP_ID")
    @ExcelProperty(value = "DP_ID")
    private String dpId;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String versionNum;

    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNo;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")
    private String sapLineId;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workshopId;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshopsCode;
    /**
     * 车间代码描述
     */
    @ApiModelProperty("车间代码描述")
    @ExcelProperty(value = "车间代码描述")
    private String workshopsDesc;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenterId;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;

}
