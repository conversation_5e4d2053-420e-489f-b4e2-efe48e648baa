package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@ApiModel("组件排产计划预发布数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanPrePublishDTO implements Serializable {


    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private String startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private String endDate;

    /**
     * 预发布数据集合
     */
    @ApiModelProperty("预发布数据集合")
    private List<ModuleProductionPlanPublishDTO> planPublishList;
}
