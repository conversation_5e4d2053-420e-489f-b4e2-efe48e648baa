package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.LinkedHashMap;


@ApiModel("组件排产计划表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanReportCompareDTO extends PageDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    private String planLayout;

    /**
     * 工厂
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 车间
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKSHOP, fieldName = "workshopsDesc")
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workshopId;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshopsCode;
    /**
     * 车间代码描述
     */
    @ApiModelProperty("车间代码描述")
    @ExcelProperty(value = "车间代码描述")
    private String workshopsDesc;
    /**
     * 工作中心
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKCENTER, fieldName = "workCenterDesc")
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenterId;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;

    /**
     * 动态列头
     */
    @ApiModelProperty("动态列头")
    @ExcelProperty(value = "动态列头")
    private LinkedHashMap<String,String> dynamicColumnHeaderMap;

    /**
     * 动态列值
     */
    @ApiModelProperty("动态列值")
    @ExcelProperty(value = "动态列值")
    private LinkedHashMap<String, Object> dynamicColumnValueMap;

    /**
     * 动态列值
     */
    @ApiModelProperty("动态列值")
    @ExcelProperty(value = "动态列值")
    private LinkedHashMap<String, Object> dynamicColumnValueMapExcel;

    /**
     * 行颜色
     */
    @ApiModelProperty("总计颜色")
    private String lineColorFlag;

}
