package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import com.jinkosolar.scp.mps.domain.util.MathUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

@ApiModel("组件排产计划报表数据转换对象")
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanReportDTO extends ModuleProductionPlanDTO {
    /**
     * 报表版本
     */
    @ApiModelProperty("报表版本")
    private String reportPlanVersion;
    /**
     * 实投数量
     */
    @ApiModelProperty("实投总数量")
    private BigDecimal actualQtySum;
    /**
     * 计划产能总数量
     */
    @ApiModelProperty("计划产能总数量")
    private BigDecimal planQtySum;

    /**
     * aps实投开始日期
     */
    @ApiModelProperty(value = "aps实投开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate apsActualBeginDate;
    /**
     * aps实投结束日期
     */
    @ApiModelProperty(value = "aps实投结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate apsActualEndDate;

    /**
     * 显示颜色
     */
    @ApiModelProperty("显示颜色")
    @ExcelProperty(value = "显示颜色")
    private String displayColor;

    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "版本开始日期")
    private String planVersionStartDate;
    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "版本结束日期")
    private String planVersionEndDate;

    /**
     * 实际客户名称 db dp_demand_lines 查询
     */
    @ApiModelProperty(name = "实际客户名称")
    @ExcelProperty(value = "实际客户名称")
    private String actualCustomer;

    /**
     * 指定硅料厂家
     */
    @ApiModelProperty("指定硅料厂家名称")
    @ExcelProperty(value = "指定硅料厂家")
    private String specifyFactoryName;
    /**
     * 有实投无排产订单标识
     */
    @ApiModelProperty("有实投无排产订单标识")
    private String actualNoOrderFlag;

    /**
     * 产能明细信息
     */
    LinkedHashMap<LocalDate, BigDecimal> planDayQtyMap;

    /**
     * 产能明细信息(JSON)
     */
    String planDayQtyInfo;
    
    private Map<LocalDate, BigDecimal> actualPlanDayQtyMap;


    public void convertMw() {
        Map<String, Object> dynamicColumnMap = this.getDynamicColumnMap();
        BigDecimal power = this.getPower();
        if (MapUtils.isNotEmpty(dynamicColumnMap) && !MathUtils.checkIsZero(power)) {
            dynamicColumnMap.forEach((k, v) -> {
                if (DateUtil.isDateStringValid(k) && Objects.nonNull(v) && StringUtils.isNumeric(v.toString())) {
                    BigDecimal quantity = MathUtils.strToBigDecimal(v.toString());
                    BigDecimal quantityMw = quantity.multiply(power).divide(new BigDecimal(1000000), 6, BigDecimal.ROUND_HALF_UP);
                    dynamicColumnMap.put(k, quantityMw);
                }
            });
        }
    }
}
