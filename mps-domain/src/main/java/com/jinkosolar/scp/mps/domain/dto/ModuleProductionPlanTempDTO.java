package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("组件排产计划临时表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanTempDTO extends BaseDTO implements Serializable {
    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    @ExcelIgnore
    private String factoryCode;
    /**
     * 补充要求
     */
    @ApiModelProperty("补充要求")
    @ExcelProperty(value = "补充要求")
    private String appendRemark;
    /**
     * 销售区域
     */
    @ApiModelProperty("销售区域")
    @ExcelProperty(value = "销售区域")
    private String area;
    /**
     * erp工单号
     */
    @ApiModelProperty("erp工单号")
    @ExcelProperty(value = "erp工单号")
    private String attribute1;
    /**
     * 弹性域10
     */
    @ApiModelProperty("弹性域10")
    @ExcelProperty(value = "弹性域10")
    private String attribute10;
    /**
     * 弹性域11
     */
    @ApiModelProperty("弹性域11")
    @ExcelProperty(value = "弹性域11")
    private String attribute11;
    /**
     * 弹性域12
     */
    @ApiModelProperty("弹性域12")
    @ExcelProperty(value = "弹性域12")
    private String attribute12;
    /**
     * 弹性域13
     */
    @ApiModelProperty("弹性域13")
    @ExcelProperty(value = "弹性域13")
    private String attribute13;
    /**
     * scp工单号
     */
    @ApiModelProperty("scp工单号")
    @ExcelProperty(value = "scp工单号")
    private String attribute2;
    /**
     * 功率需求
     */
    @ApiModelProperty("功率需求")
    @ExcelProperty(value = "功率需求")
    private String attribute3;
    /**
     * 数据版本号
     */
    @ApiModelProperty("数据版本号")
    @ExcelProperty(value = "数据版本号")
    private String attribute4;
    /**
     * 是否最新版本  Y/N
     */
    @ApiModelProperty("是否最新版本  Y/N")
    @ExcelProperty(value = "是否最新版本  Y/N")
    private String attribute5;
    /**
     * 弹性域6
     */
    @ApiModelProperty("弹性域6")
    @ExcelProperty(value = "弹性域6")
    private String attribute6;
    /**
     * 弹性域7
     */
    @ApiModelProperty("弹性域7")
    @ExcelProperty(value = "弹性域7")
    private String attribute7;
    /**
     * 弹性域8
     */
    @ApiModelProperty("弹性域8")
    @ExcelProperty(value = "弹性域8")
    private String attribute8;
    /**
     * 弹性域9
     */
    @ApiModelProperty("弹性域9")
    @ExcelProperty(value = "弹性域9")
    private String attribute9;
    /**
     * 平均功率
     */
    @ApiModelProperty("平均功率")
    @ExcelProperty(value = "平均功率")
    private BigDecimal avePower;
    /**
     * 批次
     */
    @ApiModelProperty("批次")
    @ExcelProperty(value = "批次")
    private String batchNo;
    /**
     * 更新材料
     */
    @ApiModelProperty("更新材料")
    @ExcelProperty(value = "更新材料")
    private String changFlag;
    /**
     * 切换结束时间
     */
    @ApiModelProperty("切换结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换结束时间")
    private LocalDateTime changeCompleteDate;
    /**
     * 切换结束时间1
     */
    @ApiModelProperty("切换结束时间1")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换结束时间1")
    private LocalDateTime changeCompleteDate1;
    /**
     * 切换时间
     */
    @ApiModelProperty("切换时间")
    @ExcelProperty(value = "切换时间")
    private BigDecimal changeDate;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")
    private LocalDateTime changeStartDate;
    /**
     * 切换开始时间1
     */
    @ApiModelProperty("切换开始时间1")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间1")
    private LocalDateTime changeStartDate1;
    /**
     * 销售渠道
     */
    @ApiModelProperty("销售渠道")
    @ExcelProperty(value = "销售渠道")
    private String channel;
    /**
     * 是否确认排产  Y/N null为审核
     */
    @ApiModelProperty("是否确认排产  Y/N null为审核")
    @ExcelProperty(value = "是否确认排产  Y/N null为审核")
    private String confirmFlag;
    /**
     * 项目地国家
     */
    @ApiModelProperty("项目地国家")
    @ExcelProperty(value = "项目地国家")
    private String country;
    /**
     * 客户ID
     */
    @ApiModelProperty("客户ID")
    @ExcelProperty(value = "客户ID")
    private Long customerId;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称")
    private String customerName;
    /**
     * 是否研发标识
     */
    @ApiModelProperty("是否研发标识")
    @ExcelProperty(value = "是否研发标识")
    private String developmentFlag;
    /**
     * DP计划排产ID
     */
    @ApiModelProperty("DP计划排产ID")
    @ExcelProperty(value = "DP计划排产ID")
    private Long dpDetailId;
    /**
     * DP分组ID
     */
    @ApiModelProperty("DP分组ID")
    @ExcelProperty(value = "DP分组ID")
    private Long dpGroupId;
    /**
     * dpId
     */
    @ApiModelProperty("dpId")
    @ExcelProperty(value = "dpId")
    private String dpId;
    /**
     * DP行ID
     */
    @ApiModelProperty("DP行ID")
    @ExcelProperty(value = "DP行ID")
    private Long dpLineId;
    /**
     * APS是否完工
     */
    @ApiModelProperty("APS是否完工")
    @ExcelProperty(value = "APS是否完工")
    private String finishFlag;
    /**
     * SCP是否处理到正式表
     */
    @ApiModelProperty("SCP是否处理到正式表")
    @ExcelProperty(value = "SCP是否处理到正式表")
    private String flag;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 导入时间
     */
    @ApiModelProperty("导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "导入时间")
    private LocalDateTime importTime;
    /**
     * 入库数量
     */
    @ApiModelProperty("入库数量")
    @ExcelProperty(value = "入库数量")
    private BigDecimal inInventoryQty;
    /**
     * 配料版本
     */
    @ApiModelProperty("配料版本")
    @ExcelProperty(value = "配料版本")
    private String ingredientsVersion;
    /**
     * 组件ID
     */
    @ApiModelProperty("组件ID")
    @ExcelProperty(value = "组件ID")
    private String inventoryItemId;
    /**
     * 组件料号
     */
    @ApiModelProperty("组件料号")
    @ExcelProperty(value = "组件料号")
    private String inventoryItemNo;
    /**
     * 是否ODM
     */
    @ApiModelProperty("是否ODM")
    @ExcelProperty(value = "是否ODM")
    private String isOdm;
    /**
     * 国内/海外
     */
    @ApiModelProperty("国内/海外")
    @ExcelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * BB数
     */
    @ApiModelProperty("BB数")
    @ExcelProperty(value = "BB数")
    private String itemAttribute1;
    /**
     * 接线盒电流（a）
     */
    @ApiModelProperty("接线盒电流（a）")
    @ExcelProperty(value = "接线盒电流（a）")
    private String itemAttribute10;
    /**
     * 出线方式
     */
    @ApiModelProperty("出线方式")
    @ExcelProperty(value = "出线方式")
    private String itemAttribute11;
    /**
     * 电流分档
     */
    @ApiModelProperty("电流分档")
    @ExcelProperty(value = "电流分档")
    private String itemAttribute12;
    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    @ExcelProperty(value = "是否监造")
    private String itemAttribute13;
    /**
     * 气候性策略
     */
    @ApiModelProperty("气候性策略")
    @ExcelProperty(value = "气候性策略")
    private String itemAttribute14;
    /**
     * 电池工艺(null)
     */
    @ApiModelProperty("电池工艺(null)")
    @ExcelProperty(value = "电池工艺(null)")
    private String itemAttribute15;
    /**
     * 片源种类
     */
    @ApiModelProperty("片源种类")
    @ExcelProperty(value = "片源种类")
    private String itemAttribute16;
    /**
     * 玻璃厚度（mm）
     */
    @ApiModelProperty("玻璃厚度（mm）")
    @ExcelProperty(value = "玻璃厚度（mm）")
    private String itemAttribute17;
    /**
     * DP属性
     */
    @ApiModelProperty("DP属性")
    @ExcelProperty(value = "DP属性")
    private String itemAttribute18;
    /**
     * 背板颜色
     */
    @ApiModelProperty("背板颜色")
    @ExcelProperty(value = "背板颜色")
    private String itemAttribute19;
    /**
     * 背板结构
     */
    @ApiModelProperty("背板结构")
    @ExcelProperty(value = "背板结构")
    private String itemAttribute20;
    /**
     * 包装片数
     */
    @ApiModelProperty("包装片数")
    @ExcelProperty(value = "包装片数")
    private String itemAttribute21;
    /**
     * LRF
     */
    @ApiModelProperty("LRF")
    @ExcelProperty(value = "LRF")
    private String itemAttribute22;
    /**
     * 组件厚度
     */
    @ApiModelProperty("组件厚度")
    @ExcelProperty(value = "组件厚度")
    private String itemAttribute24;
    /**
     * 边框颜色
     */
    @ApiModelProperty("边框颜色")
    @ExcelProperty(value = "边框颜色")
    private String itemAttribute25;
    /**
     * 电池尺寸
     */
    @ApiModelProperty("电池尺寸")
    @ExcelProperty(value = "电池尺寸")
    private String itemAttribute26;
    /**
     * 玻璃种类(后)
     */
    @ApiModelProperty("玻璃种类(后)")
    @ExcelProperty(value = "玻璃种类(后)")
    private String itemAttribute27;
    /**
     * eva属性（后）
     */
    @ApiModelProperty("eva属性（后）")
    @ExcelProperty(value = "eva属性（后）")
    private String itemAttribute28;
    /**
     * eva属性（前）
     */
    @ApiModelProperty("eva属性（前）")
    @ExcelProperty(value = "eva属性（前）")
    private String itemAttribute3;
    /**
     * el等级
     */
    @ApiModelProperty("el等级")
    @ExcelProperty(value = "el等级")
    private String itemAttribute31;
    /**
     * 网版图型-分类
     */
    @ApiModelProperty("网版图型-分类")
    @ExcelProperty(value = "网版图型-分类")
    private String itemAttribute32;
    /**
     * 焊带(null)
     */
    @ApiModelProperty("焊带(null)")
    @ExcelProperty(value = "焊带(null)")
    private String itemAttribute33;
    /**
     * 防尘塞
     */
    @ApiModelProperty("防尘塞")
    @ExcelProperty(value = "防尘塞")
    private String itemAttribute34;
    /**
     * 护角
     */
    @ApiModelProperty("护角")
    @ExcelProperty(value = "护角")
    private String itemAttribute36;
    /**
     * 封边
     */
    @ApiModelProperty("封边")
    @ExcelProperty(value = "封边")
    private String itemAttribute37;
    /**
     * 电池特殊等级
     */
    @ApiModelProperty("电池特殊等级")
    @ExcelProperty(value = "电池特殊等级")
    private String itemAttribute38;
    /**
     * 接受副产品比例
     */
    @ApiModelProperty("接受副产品比例")
    @ExcelProperty(value = "接受副产品比例")
    private BigDecimal itemAttribute39;
    /**
     * 组件尺寸
     */
    @ApiModelProperty("组件尺寸")
    @ExcelProperty(value = "组件尺寸")
    private String itemAttribute4;
    /**
     * 配件袋
     */
    @ApiModelProperty("配件袋")
    @ExcelProperty(value = "配件袋")
    private String itemAttribute41;
    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @ExcelProperty(value = "硅料供应商")
    private String itemAttribute42;
    /**
     * 电池厂家
     */
    @ApiModelProperty("电池厂家")
    @ExcelProperty(value = "电池厂家")
    private String itemAttribute43;
    /**
     * 型材规格
     */
    @ApiModelProperty("型材规格")
    @ExcelProperty(value = "型材规格")
    private String itemAttribute47;
    /**
     * 横竖装
     */
    @ApiModelProperty("横竖装")
    @ExcelProperty(value = "横竖装")
    private String itemAttribute5;
    /**
     * 边框类型
     */
    @ApiModelProperty("边框类型")
    @ExcelProperty(value = "边框类型")
    private String itemAttribute6;
    /**
     * 线缆长度（mm）
     */
    @ApiModelProperty("线缆长度（mm）")
    @ExcelProperty(value = "线缆长度（mm）")
    private String itemAttribute7;
    /**
     * 端子
     */
    @ApiModelProperty("端子")
    @ExcelProperty(value = "端子")
    private String itemAttribute8;
    /**
     * 接线盒型号
     */
    @ApiModelProperty("接线盒型号")
    @ExcelProperty(value = "接线盒型号")
    private String itemAttribute9;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 代工账套
     */
    @ApiModelProperty("代工账套")
    @ExcelProperty(value = "代工账套")
    private String oemAccount;
    /**
     * 库存组织
     */
    @ApiModelProperty("库存组织")
    @ExcelProperty(value = "库存组织")
    private Long organizationId;
    /**
     * 逾期时长
     */
    @ApiModelProperty("逾期时长")
    @ExcelProperty(value = "逾期时长")
    private Integer overDatetime;
    /**
     * 计划排产数量
     */
    @ApiModelProperty("计划排产数量")
    @ExcelProperty(value = "计划排产数量")
    private BigDecimal planQty;
    /**
     * 计划完成日期
     */
    @ApiModelProperty("计划完成日期")
    @ExcelProperty(value = "计划完成日期")
    private LocalDate plannedCompleteDate;
    /**
     * 计划开始日期
     */
    @ApiModelProperty("计划开始日期")
    @ExcelProperty(value = "计划开始日期")
    private LocalDate plannedStartDate;
    /**
     * 计划开始时间
     */
    @ApiModelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "计划开始时间")
    private LocalDateTime plannedStartDatetime;
    /**
     * 是否标准评审
     */
    @ApiModelProperty(value = "是否标准评审")
    @ExcelProperty(value = "是否标准评审")
    private String placeOrderFlag;
    /**
     * 交货期
     */
    @ApiModelProperty("交货期")
    @ExcelProperty(value = "交货期")
    private LocalDate plannedWipDate;
    /**
     * 降档要求
     */
    @ApiModelProperty("降档要求")
    @ExcelProperty(value = "降档要求")
    private String powerChangeRemark;
    /**
     * 功率符合率
     */
    @ApiModelProperty("功率符合率")
    @ExcelProperty(value = "功率符合率")
    private BigDecimal powerCoincidenceRate;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private String productFactory;
    /**
     * 产品族
     */
    @ApiModelProperty("产品族")
    @ExcelProperty(value = "产品族")
    private String productFamily;
    /**
     * 销售料号
     */
    @ApiModelProperty("销售料号")
    @ExcelProperty(value = "销售料号")
    private String productItemNo;
    /**
     * 排产基地
     */
    @ApiModelProperty("排产基地")
    @ExcelProperty(value = "排产基地")
    private String productPlace;
    /**
     * 需求功率
     */
    @ApiModelProperty("需求功率")
    @ExcelProperty(value = "需求功率")
    private String productPower;
    /**
     * 产品系列
     */
    @ApiModelProperty("产品系列")
    @ExcelProperty(value = "产品系列")
    private String productSeries;
    /**
     * 组件良率
     */
    @ApiModelProperty("组件良率")
    @ExcelProperty(value = "组件良率")
    private BigDecimal productYield;
    /**
     * 需求功率比列
     */
    @ApiModelProperty("需求功率比列")
    @ExcelProperty(value = "需求功率比列")
    private String quantity2Ratio;
    /**
     * 排产行code
     */
    @ApiModelProperty("排产行code")
    @ExcelProperty(value = "排产行code")
    private String scheduleCode;
    /**
     * 排产行ID
     */
    @ApiModelProperty("排产行ID")
    @ExcelProperty(value = "排产行ID")
    private Long scheduleId;
    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @ExcelProperty(value = "排产数量")
    private BigDecimal scheduleQty;
    /**
     * DP_ID加计划排产序号组合
     */
    @ApiModelProperty("DP_ID加计划排产序号组合")
    @ExcelProperty(value = "DP_ID加计划排产序号组合")
    private String scheduleSimpleNo;
    /**
     * 排产状态
     */
    @ApiModelProperty("排产状态")
    @ExcelProperty(value = "排产状态")
    private String scheduleStatus;
    /**
     * 排产单元
     */
    @ApiModelProperty("排产单元")
    @ExcelProperty(value = "排产单元")
    private String scheduleUnit;
    /**
     * 特殊单号
     */
    @ApiModelProperty("特殊单号")
    @ExcelProperty(value = "特殊单号")
    private String specialNo;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @ExcelProperty(value = "单位")
    private String uom;
    /**
     * 工作分钟数
     */
    @ApiModelProperty("工作分钟数")
    @ExcelProperty(value = "工作分钟数")
    private Integer workMinutes;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workshopId;

    /**
     * 电池标准效率
     */
    @ApiModelProperty(value = " 电池标准效率")
    private String standardCellEfficiency;

    /**
     * 排产良率
     */
    @ApiModelProperty(value = " 排产良率")
    @Column(name = "yield")
    private BigDecimal yield;

    /**
     * 实际良率
     */
    @ApiModelProperty(value = " 实际良率")
    @ExcelProperty(value = "实际良率")
    private BigDecimal actualYield;
    /**
     * 拆分数量
     */
    @ApiModelProperty(value = " 拆分数量")
    @ExcelProperty(value = "拆分数量")
    private BigDecimal splitQty;
    /**
     * 拆分产线
     */
    @ApiModelProperty(value = " 拆分产线")
    @ExcelProperty(value = "拆分产线")
    private String splitProductLine;
    /**
     * 输出焊带
     */
    @ApiModelProperty(value = " 输出焊带")
    @ExcelProperty(value = "输出焊带")
    private String outputSolder;
    /**
     * 合同号
     */
    @ApiModelProperty(value = " 合同号")
    @ExcelProperty(value = "合同号")
    private String contractNo;
    /**
     * 销售主体
     */
    @ApiModelProperty(value = " 销售主体")
    @ExcelProperty(value = "销售主体")
    private String saleBody;
    /**
     * 项目分类
     */
    @ApiModelProperty(value = " 项目分类")
    @ExcelProperty(value = "项目分类")
    private String projectType;

    /**
     * 硅料类型
     */
    @ApiModelProperty(value = "硅料类型")
    @ExcelProperty(value = "硅料类型")
    private String siliconType;

    /**
     * 组件单双面
     */
    @ApiModelProperty("组件单双面")
    @ExcelProperty(value = "组件单双面")
    private String itemAttribute82;

    /**
     * 生产确认
     */
    @ApiModelProperty("生产确认")
    @ExcelProperty(value = "生产确认")
    private String productionReason;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商")
    private Long specifySupplier;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商名称")
    @ExcelProperty(value = "指定硅料供应商名称")
    private String specifySupplierName;
    /**
     * 扩展字段83
     */
    @ApiModelProperty(value = "扩展字段83")
    private String itemAttribute83;
    /**
     * 扩展字段84
     */
    @ApiModelProperty(value = "扩展字段84")
    private String itemAttribute84;
    /**
     * 扩展字段85
     */
    @ApiModelProperty(value = "扩展字段85")
    private String itemAttribute85;
    /**
     * 扩展字段86
     */
    @ApiModelProperty(value = "扩展字段86")
    private String itemAttribute86;
    /**
     * 扩展字段87
     */
    @ApiModelProperty(value = "扩展字段87")
    private String itemAttribute87;
    /**
     * 扩展字段88
     */
    @ApiModelProperty(value = "扩展字段88")
    private String itemAttribute88;
    /**
     * 扩展字段89
     */
    @ApiModelProperty(value = "扩展字段89")
    private String itemAttribute89;
    /**
     * 扩展字段90
     */
    @ApiModelProperty(value = "排产差异量")
    private String itemAttribute90;
    /**
     * 扩展字段91
     */
    @ApiModelProperty(value = "订单排产量")
    private String itemAttribute91;
}