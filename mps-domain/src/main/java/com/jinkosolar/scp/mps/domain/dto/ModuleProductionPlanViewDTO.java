package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel("组件排产计划发布数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleProductionPlanViewDTO implements Serializable {
    /** 事业部*/
    @ApiModelProperty(name = "/** 事业部 */",notes = "")
    private String division ;
    /**
     * 国内/海外/山西
     */
    @ApiModelProperty("国内/海外/山西")
    private String domesticOversea;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    private Long factory;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    private String factoryCode;
    /**
     * 版本号集合
     */
    @ApiModelProperty("版本号")
    private List<BatchNo> batchNoList;

    /**
     * 版本
     */
    @ApiModelProperty("版本")
    private String batchNo;

    @Data
    public static class BatchNo {
        /**
         * 版本
         */
        @ApiModelProperty("版本")
        private String batchNo;
        /**
         * 版本描述
         */
        @ApiModelProperty("版本描述")
        private String batchNoDesc;

        /**
         * 更新时间
         */
        @ApiModelProperty("更新时间")
        private LocalDateTime updatedTime;


        /**
         * 开始日期
         */
        @ApiModelProperty("开始日期")
        private String startDate;
        /**
         * 结束日期
         */
        @ApiModelProperty("结束日期")
        private String endDate;

    }

}
