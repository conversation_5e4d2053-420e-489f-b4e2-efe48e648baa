package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Joiner;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.enums.YesOrNoEnum;
import com.jinkosolar.scp.jip.api.dto.sap.SapPlanDto;
import com.jinkosolar.scp.mps.domain.constant.SplitConstant;
import com.jinkosolar.scp.mps.domain.dto.dp.DemandLinesDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Optional;


@ApiModel("生产建议表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class ModuleProductionSuggestionDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 销售订单
     */
    @ApiModelProperty("销售订单")
    @ExcelProperty(value = "销售订单")
    private String sapOrderNo;

    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    @ExcelProperty(value = "销售订单行")
    private String sapLineId;

    /**
     * IUD标识
     */
    @ApiModelProperty("IUD标识")
    @ExcelProperty(value = "IUD标识")
    private String iud;

    /**
     * 生产建议
     */
    @ApiModelProperty("生产建议")
    @ExcelProperty(value = "生产建议")
    private String suggestionsNo;

    /**
     * 组件生产计划行id
     */
    @ApiModelProperty("组件生产计划行id")
    private Long productionPlanId;

    /**
     * 组合号
     */
    @ApiModelProperty("组合号")
    @ExcelProperty(value = "组合号")
    private String zpkgNum;

    /**
     * 组合号行项目号
     */
    @ApiModelProperty("组合号行项目号")
    @ExcelProperty(value = "组合号行项目号")
    private String zpkgLineId;

    /**
     * 需求类别
     */
    @ApiModelProperty("需求类别")
    @ExcelProperty(value = "需求类别")
    private String demandType;

    /**
     * 是否 定向
     */
    @ApiModelProperty(" 是否 定向")
    @ExcelProperty(value = " 是否 定向")
    private String batchDirectional;

    /**
     * 物料编号
     */
    @ApiModelProperty("物料编号")
    @ExcelProperty(value = "物料编号")
    private String itemCode;

    /**
     * OEM
     */
    @ApiModelProperty("OEM")
    @ExcelProperty(value = "OEM")
    private String oem;

    /**
     * 工厂
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryName;

    /**
     * 生产建议数量
     */
    @ApiModelProperty("生产建议数量")
    @ExcelProperty(value = "生产建议数量")
    private BigDecimal suggestionQty;


    /**
     * 上版生产建议数量
     */
    @ApiModelProperty("上版生产建议数量")
    @ExcelProperty(value = "上版生产建议数量")
    private BigDecimal preSuggestionQty;

    /**
     * 扣减量
     */
    @ApiModelProperty("扣减量")
    @ExcelProperty(value = "扣减量")
    private BigDecimal deductionQuantity;

    /**
     * 销售订单行需求数量
     */
    @ApiModelProperty("销售订单行需求数量")
    @ExcelProperty(value = "销售订单行需求数量")
    private BigDecimal demandQty;

    private DemandLinesDTO demandLine;

    /**
     * 计划型号
     */
    @ApiModelProperty("计划型号")
    @ExcelProperty(value = "计划型号")
    private String planNum;

    /**
     * 工厂
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKSHOP, fieldName = "workshopDesc")
    @ApiModelProperty("生产车间")
    @ExcelProperty(value = "生产车间")
    private Long workshop;

    /**
     * 生产车间CODE
     */
    @ApiModelProperty("生产车间CODE")
    @ExcelProperty(value = "生产车间CODE")
    private String workshopCode;

    /**
     * 生产车间描述
     */
    @ApiModelProperty("生产车间描述")
    @ExcelProperty(value = "生产车间描述")
    private String workshopDesc;

    /**
     * 工厂
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKCENTER)
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenter;

    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterCode;

    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    private String workCenterName;

    /**
     * 计划订单开始日期
     */
    @ApiModelProperty("计划订单开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "计划订单开始日期")
    private LocalDateTime startTime;

    /**
     * 计划订单結束日期
     */
    @ApiModelProperty("计划订单結束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "计划订单結束日期")
    private LocalDateTime endTime;

    /**
     * 生产建议状态标识 由ERP返回, C代表创建 D代表删除
     */
    @ApiModelProperty("生产建议状态标识")
    @ExcelProperty(value = "生产建议状态标识")
    private String status;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String version;

    /**
     * sap返回状态
     */
    @ApiModelProperty("sap返回状态")
    private String sapStatus;

    /**
     * sap返回信息
     */
    @ApiModelProperty("sap返回信息")
    private String sapMsg;

    /**
     * sap成功数量
     */
    @ApiModelProperty("sap成功数量")
    private BigDecimal sapQuantity;

    /**
     * 是否继承上版：继承为Y 新增为N
     */
    @ApiModelProperty("是否继承上版：继承为Y 新增为N")
    private String inheritFlag;

    /**
     * 同步给sap的标注序列号
     */
    @ApiModelProperty("同步给sap的标注序列号")
    private Long sapSyncSeq;

    public String groupByOrderAndFactory() {
        String factoryCode1 = this.getFactoryCode();
        if (YesOrNoEnum.YES.getCode().equals(oem)) {
            factoryCode1 = this.getItemAttribute72();
        }
        return Joiner.on(SplitConstant.SPLIT_SEPARATOR).useForNull("null")
                .join(this.getSapOrderNo(), this.getSapLineId(), factoryCode1);

    }

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String itemAttribute1;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String itemAttribute2;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String itemAttribute3;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String itemAttribute4;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1 ")
    @ExcelProperty(value = "扩展字段1 ")
    private String itemAttribute5;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String itemAttribute6;

    /**
     * 输出拆分标识
     */
    @ApiModelProperty(value = "输出拆分标识")
    @ExcelProperty(value = "输出拆分标识")
    private String itemAttribute71;

    /**
     * OEM原工厂代码
     */
    @ApiModelProperty(value = "OEM原工厂代码")
    @ExcelProperty(value = "OEM原工厂代码")
    private String itemAttribute72;

    /**
     * fi数量
     */
    @ApiModelProperty("fi数量")
    @ExcelProperty(value = "fi数量")
    private BigDecimal fiQty;

    /**
     * 匹配类型:1.未匹配到任何实投、计划 2.只匹配到实投 3.只匹配到计划 4.匹配到实投和计划
     */
    @ApiModelProperty("匹配类型")
    @ExcelProperty(value = "匹配类型")
    private int matchType;

    @ApiModelProperty("目的地区域")
    @ExcelProperty(value = "目的地区域")
    private String destAreaNo;

    // 已经占用的PlanQty  临时占用字段
    private BigDecimal planQty;

    private LocalDate apsPlanDate;

    /**
     * 功率预测版本
     */
    @ApiModelProperty(value = "功率预测版本")
    @ExcelProperty(value = "功率预测版本")
    private String powerForecastVersion;
    public String groupByOrderAndWorkCenter() {
        return Joiner.on(SplitConstant.SPLIT_SEPARATOR).useForNull("null").join(this.getSapOrderNo(), this.getSapLineId(), this.getFactoryCode(), this.getWorkCenterCode(), this.itemAttribute71);
    }

    public SapPlanDto toSapPlanDto() {
        String curWorkCenterCode = this.getWorkCenterCode();
        if (StringUtils.isNotBlank(curWorkCenterCode)) {
            String[] split = curWorkCenterCode.split("-");
            if (split.length > 0) {
                curWorkCenterCode = split[0];
            }
        }

        SapPlanDto dto = SapPlanDto.builder().PLSCN(100L).ZSCJY(this.getId().toString()).VBELN(this.getSapOrderNo()).POSNR(Optional.ofNullable(this.getSapLineId()).map(Long::valueOf).orElse(null)).ZLB(this.getDemandType()).ZOEM(this.getOem()).ZDX(this.getBatchDirectional()).MATERIAL(this.getItemCode()).WERKS(this.getFactoryCode()).ZJYSL(this.getSuggestionQty())
                .ARBPL(curWorkCenterCode).PSTTR(this.getStartTime().toLocalDate()).PEDTR(this.getEndTime().toLocalDate()).ZIUD(Optional.ofNullable(this.getIud()).orElse("I")).ZCJ(Optional.ofNullable(this.getWorkshopCode()).orElse("no-name")).ZCJMS(Optional.ofNullable(this.getWorkshopDesc()).orElse("no-name")).PKGNUM(this.getZpkgNum()).PKGITM(this.getZpkgLineId()).ZXQSL(this.getDemandQty()).ZJHXH(this.getPlanNum())
                .ZGLYCBB(this.getPowerForecastVersion()).build();
        // 如果OEM为Y的时候,工厂区Attribute72
        if ("Y".equals(this.getOem())) {
            dto.setWERKS(this.getItemAttribute72());
        }
        return dto;
    }
}