package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 生产建议Erp下发数量
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-09-19 15:36:03
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "生产建议Erp下发数量DTO对象", description = "DTO对象")
public class ModuleProductionSuggestionErpQtyDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 销售订单
     */
    @ApiModelProperty(value = "销售订单")
    private String sapOrderNo;

    /**
     * 销售订单行
     */
    @ApiModelProperty(value = "销售订单行")
    private String sapLineId;

    /**
     * 工厂编码
     */
    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    /**
     * 工作中心代码
     */
    @ApiModelProperty(value = "工作中心代码")
    private String workCenterCode;

    /**
     * 输出拆分标识
     */
    @ApiModelProperty(value = "输出拆分标识")
    private String itemAttribute71;

    /**
     * 下发总量
     */
    @ApiModelProperty(value = "下发总量")
    private BigDecimal issuedQuantity;
}
