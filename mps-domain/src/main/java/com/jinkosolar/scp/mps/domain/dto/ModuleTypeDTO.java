package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("组件版型数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ModuleTypeDTO extends PageDTO implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @ExcelProperty("名称")
    private String fullName;

    @ApiModelProperty("名称(不带主栅)")
    private String moduleName;

    /**
     * 版型类型,CG：常规，TS：特殊
     */
    @ApiModelProperty("版型类型,CG：常规，TS：特殊")
    @ExcelProperty(value = "版型类型,CG：常规，TS：特殊")
    private String category;
    /**
     * 电池尺寸
     */
    @ApiModelProperty("电池尺寸")
    @ExcelProperty(value = "电池尺寸")
    private String cellSize;
    /**
     * 电池片类型
     */
    @ApiModelProperty("电池片类型")
    @ExcelProperty(value = "电池片类型")
    private String cellType;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 单/双面
     */
    @ApiModelProperty("单/双面")
    @ExcelProperty(value = "单/双面")
    private String oddEven;


    @ApiModelProperty("单/双面")
    private Long oddEvenId;
    /**
     * 片串
     */
    @ApiModelProperty("片串")
    @ExcelProperty(value = "片串")
    private Integer piece;
    /**
     * 型号
     */
    @ApiModelProperty("型号")
    @ExcelProperty(value = "型号")
    private String spec;
    /**
     * 工艺
     */
    @ApiModelProperty("工艺")
    @ExcelProperty(value = "工艺")
    private String technique;

    @ApiModelProperty("工艺")
    private Long techniqueId;
    /**
     * 单块瓦数
     */
    @ApiModelProperty("单块瓦数")
    @ExcelProperty(value = "单块瓦数")
    private BigDecimal wattage;

    @ApiModelProperty("主栅数")
    private String mainGridLine;
}
