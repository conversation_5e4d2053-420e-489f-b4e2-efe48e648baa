package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @USER: MWZ
 * @DATE: 2022/6/20
 */
@Data
@ApiModel(value = "工单下发 日数据DTO", description = "DTO对象")
public class MothDataDTO {
    //实际排产ID
    private List<String> ids;
    //时间
    private String date;
    //数量
    private BigDecimal number;
    //是否 生成erp工单号  1:生成 0:未生成
    private String attribute1;
}
