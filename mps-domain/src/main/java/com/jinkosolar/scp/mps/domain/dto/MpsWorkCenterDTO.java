package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.persistence.Column;
import java.io.Serializable;
/**
 * 客户;
 *
 * <AUTHOR> darke
 * @date : 2022-4-24
 */
@ApiModel(value = "工作中心主表DTO", description = "工作中心主表DTO")
@ToString
@Data
public class MpsWorkCenterDTO extends PageDTO implements Serializable {

    /** 主键 */
    @ApiModelProperty(name = "主键",notes = "")
    private Long id ;

    /** 工厂代码 */
    @ApiModelProperty(name = "工厂代码",notes = "")
    private String factoryCode ;

    /** 工厂代码 */
    @ApiModelProperty(name = "工厂描述",notes = "")
    @Dict(methodName = "getFactoryNameByCode",fieldKey = "factoryCode",fieldName = "factoryDesc")
    private String factoryDesc ;

    /** 工作中心代码 */
    @ApiModelProperty(name = "工作中心代码",notes = "")
    private String workCenterCode ;

    /** 工作中心描述 */
    @ApiModelProperty(name = "工作中心描述",notes = "")
    private String workCenterDesc;

    @ApiModelProperty(name = "产线/机台总数",notes = "")
    private Integer productionLineNum ;

    /** 租户 */
    @ApiModelProperty(name = "租户",notes = "")
    private String tenantId ;
}
