package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 客户;
 *
 * <AUTHOR> darke
 * @date : 2022-4-24
 */
@ApiModel(value = "工作中心详情DTO", description = "工作中心详情DTO")
@ToString
@Data
public class MpsWorkCenterDetailDTO extends PageDTO implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(name = "主键", notes = "")
    private Long id;

    /**
     * 生产车间代码
     */
    @ApiModelProperty(name = "车间", notes = "")
    @Translate(DictType =  MpsLovConstant.WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workshopCode","workshopIdName"})
    private String workshopId;
    /**
     * 生产车间代码
     */
    @ApiModelProperty(name = "车间", notes = "")
    @ExcelProperty(value = "车间代码")
    @Translate(DictType = MpsLovConstant.WORKSHOP, unTranslate = true, required = true, to = {"workshopId"})
    private String workshopCode;

    @Translate(DictType = MpsLovConstant.WORKSHOP, unTranslate = true, required = true)
    @ExcelProperty(value = "车间")
    private String workshopIdName;

    /**
     * 产线/机器 总数
     */
    @ApiModelProperty(name = "产线/机台总数", notes = "")
    @ExcelProperty(value = "产线/机台 总数")
    private Integer productionLineNum;

    @ApiModelProperty(name = "工厂id", notes = "")
    @ExcelProperty(value = "工厂id")
    private String factoryId;

    /**
     * 工厂代码
     */
    @ApiModelProperty(name = "工厂代码", notes = "")
    @ExcelProperty(value = "工厂代码")
    @Translate(DictType = MpsLovConstant.FACTORY, unTranslate = true, required = true, to = {"factoryId"})
    private String factoryCode;

    /**
     * 工厂描述
     */
    @ApiModelProperty(name = "工厂描述", notes = "")
    @ExcelProperty(value = "工厂代码描述")
    private String factoryDesc;

    /**
     * 工作中心ID
     */
    @ApiModelProperty(name = "工作中心ID", notes = "")
    private Long workCenterId;

    /**
     * 工作中心代码
     */
    @ApiModelProperty(name = "工作中心代码", notes = "")
    @ExcelProperty(value = "工作中心")
    private String workCenterCode;

    /**
     * 工作中心描述
     */
    @ApiModelProperty(name = "工作中心描述", notes = "")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;

    /**
     * 计划工作中心
     */
    @ApiModelProperty(name = "计划工作中心", notes = "")
    @ExcelProperty(value = "计划工作中心")
    private String planWorkCenterCode;

    /**
     * 事业部id
     */
    @ApiModelProperty(name = "工段id", notes = "")
    @Translate(DictType = MpsLovConstant.BUSINESS_DEPRTMENT)
    private String divisionId;

    /**
     * 事业部名称
     */
    @ApiModelProperty(name = "工段名称", notes = "")
    @Translate(DictType = MpsLovConstant.BUSINESS_DEPRTMENT, unTranslate = true, required = true)
    @ExcelProperty(value = "工段")
    private String divisionIdName;

    /**
     * 地址|国内,海外,山西
     */
    @ApiModelProperty(name = "地址|国内,海外,山西", notes = "")
    @Translate(DictType = MpsLovConstant.LOCATION)
    private String addressId;


    /**
     * 地址|国内,海外,山西
     */
    @ApiModelProperty(name = "地址|国内,海外,山西", notes = "")
    @ExcelProperty(value = "国内/海外山西")
    @Translate(DictType = MpsLovConstant.LOCATION, unTranslate = true, required = true)
    private String addressIdName;

    /**
     * 租户
     */
    @ApiModelProperty(name = "租户", notes = "")
    private String tenantId;

}
