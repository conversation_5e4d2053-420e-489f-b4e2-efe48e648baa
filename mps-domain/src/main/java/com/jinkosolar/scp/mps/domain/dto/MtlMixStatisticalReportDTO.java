package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "MtlMixStatisticalReportDTO对象", description = "DTO对象")
public class MtlMixStatisticalReportDTO {
    @ApiModelProperty(value = "报表内容")
    private List<MtlMixStatisticalReport1DTO> datas;

    @ApiModelProperty(value = "展示日期")
    List<String> displayDates;

    @Data
    public static class MtlMixStatisticalReport1DTO {
        /**
         * 国内/海外
         */
        @ApiModelProperty(value = "国内/海外")
        private String isOversea;

        @ApiModelProperty(value = "月份")
        List<MtlMixStatisticalReport2DTO> child;
    }

    @Data
    public static class MtlMixStatisticalReport2DTO {
        @ApiModelProperty(value = "月份")
        private String month;

        @ApiModelProperty(value = "产品系列")
        List<MtlMixStatisticalReport3DTO> child;
    }

    @Data
    public static class MtlMixStatisticalReport3DTO {
        @ApiModelProperty(value = "产品系列")
        private String productSeries;

        @ApiModelProperty(value = "单玻数量")
        private BigDecimal backsheetFramedQuantity;
        @ApiModelProperty(value = "双玻数量")
        private BigDecimal dualGlassFramedQuantity;

        @ApiModelProperty(value = "材料类型")
        List<MtlMixStatisticalReport4DTO> child;

        @ApiModelProperty(value = "汇总")
        List<MtlMixStatisticalReportSummary> summaries;
    }

    @Data
    public static class MtlMixStatisticalReportSummary {
        @ApiModelProperty(value = "汇总行标题")
        private String title;

        @ApiModelProperty(value = "数据时间行映射")
        Map<String, String> child;
    }

    @Data
    public static class MtlMixStatisticalReport4DTO {
        @ApiModelProperty(value = "材料类型")
        private String materialType;

        @ApiModelProperty(value = "子行")
        List<MtlMixStatisticalReport5DTO> child;
    }

    @Data
    public static class MtlMixStatisticalReport5DTO {
        @ApiModelProperty(value = "单/双玻")
        private String moduleStack;

        @ApiModelProperty(value = "材料")
        private String material;

        @ApiModelProperty(value = "数据时间行映射")
        Map<String, String> child;

        @ApiModelProperty(value = "d6")
        @JSONField(serialize = false)
        Map<String, MtlMixStatisticalReport6DTO> d6;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MtlMixStatisticalReport6DTO {
        BigDecimal demandQuantity;
        String powerCount;
        Set<Long> powerDetailIds;

        public MtlMixStatisticalReport6DTO(BigDecimal demandQuantity, String powerCount) {
            this.demandQuantity = demandQuantity;
            this.powerCount = powerCount;
        }
    }
}

