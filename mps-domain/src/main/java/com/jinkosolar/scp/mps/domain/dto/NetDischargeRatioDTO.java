package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @USER: MWZ
 * @DATE: 2022/6/29
 */
@Data
public class NetDischargeRatioDTO {
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * dpId
     */
    @ApiModelProperty(value = "dpId")
    private String dpId;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String productPlace;
    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String scheduleUnit;
    /**
     * 需求(MW)
     */
    @ApiModelProperty(value = "需求(MW)")
    private BigDecimal demandMw;
    /**
     * 排产(MW)
     */
    @ApiModelProperty(value = "排产(MW)")
    private BigDecimal scheduleMw;
    /**
     * Dp/排产(MW)
     */
    @ApiModelProperty(value = " Dp/排产(MW)")
    private String dpScheduleMw;
}
