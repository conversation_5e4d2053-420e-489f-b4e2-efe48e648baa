package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * [说明]料号匹配历史记录表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-07-30
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "料号匹配历史记录表DTO对象", description = "DTO对象")
public class NonModuleItemMatchLogDTO extends BaseDTO {


    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    /**
     * 排产ID
     */
    @ApiModelProperty(value = "排产ID")
    private Long productionPlanId;
    
    /**
     * 料号ID
     */
    @ApiModelProperty(value = "料号ID")
    private Long itemId;
    
    /**
     * 料号编码
     */
    @ApiModelProperty(value = "料号编码")
    private String itemCode;
    
    /**
     * 料号描述
     */
    @ApiModelProperty(value = "料号描述")
    private String itemDesc;
    
    /**
     * 料号匹配版本
     */
    @ApiModelProperty(value = "料号匹配版本")
    private Long itemMatchVersion;
    

}
