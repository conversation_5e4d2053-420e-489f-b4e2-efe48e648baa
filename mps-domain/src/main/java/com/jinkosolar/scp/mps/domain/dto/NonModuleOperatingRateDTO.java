package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

import static com.jinkosolar.scp.mps.domain.constant.MpsLovConstant.LOV_CODE_MPS_BUSINESS_DEPARTMENT;


@ApiModel("非组件开工率基础数据数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NonModuleOperatingRateDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 排产区域ID
     */
    @ApiModelProperty("排产区域ID")
    @ExcelProperty(value = "排产区域ID")
    //@Translate(DictType = SYS_DOMESTIC_OVERSEA, queryColumns = {"lovLineId"},from = {"lovName"}, to = {"processName"})
    private Long domesticOverseaId;
    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称")
    // @Translate(unTranslate = true,required = true,DictType = SYS_DOMESTIC_OVERSEA, queryColumns = {"lovName"},from = {"lovLineId"}, to = {"domesticOverseaId"})
    private String domesticOverseaName;
    /**
     * 工段ID
     */
    @ApiModelProperty("工段ID")
    @ExcelProperty(value = "工段ID")
    @Translate(DictType = LOV_CODE_MPS_BUSINESS_DEPARTMENT, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"processName"})
    private Long processId;
    /**
     * 工段名称
     */
    @ApiModelProperty("工段名称")
    @ExcelProperty(value = "工段名称")
    //  @Translate(unTranslate = true,required = true,DictType = LOV_CODE_MPS_BUSINESS_DEPARTMENT, queryColumns = {"lovName"},from = {"lovLineId"}, to = {"processId"})
    private String processName;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂ID")
    @ExcelProperty(value = "工厂ID")
    // @Translate(DictType = SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},from = {"lovValue","lovName"}, to = {"factoryCode","factoryName"})
    private Long factoryId;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    //@Translate(unTranslate = true,required = true,DictType = SYS_ORG_ARCHITECTURE, queryColumns = {"lovValue"},from = {"lovLineId","lovName"}, to = {"factoryId","factoryName"})
    private String factoryCode;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 车间ID
     */
    @ApiModelProperty("车间ID")
    @ExcelProperty(value = "车间ID")
    //@Translate(DictType = SYS_WORKSHOP, queryColumns = {"lovLineId"},from = {"lovValue","lovName"}, to = {"workShopCode","workShopName"})
    private Long workShopId;
    /**
     * 车间编码
     */
    @ApiModelProperty("车间编码")
    @ExcelProperty(value = "车间编码")
    // @Translate(unTranslate = true,required = true,DictType = SYS_WORKSHOP, queryColumns = {"lovValue"},from = {"lovLineId","lovName"}, to = {"workShopId","workShopName"})
    private String workShopCode;
    /**
     * 车间名称
     */
    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    private String workShopName;
    /**
     * 分类ID
     */
    @ApiModelProperty("分类ID")
    @ExcelProperty(value = "分类ID")
    // @Translate(DictType = MPS_NON_MODULE_OPERATING_RATE_TYPE, queryColumns = {"lovLineId"},from = {"lovName"}, to = {"typeName"})
    private Long typeId;

    /**
     * 分类ID
     */
    @ApiModelProperty("分类名称")
    @ExcelProperty(value = "分类名称")
    // @Translate(unTranslate = true,required = true,DictType = SYS_WORKSHOP, queryColumns = {"lovName"},from = {"lovLineId"}, to = {"typeId"})
    private String typeName;
    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 月
     */
    @ApiModelProperty("月")
    @ExcelProperty(value = "月")
    private Integer month;
    /**
     * 开工率
     */
    @ApiModelProperty("开工率")
    @ExcelProperty(value = "开工率")
    private BigDecimal operatingRate;
    /**
     * 冗余字段1
     */
    @ApiModelProperty("冗余字段1")
    @ExcelProperty(value = "冗余字段1")
    private String attribute1;
    /**
     * 冗余字段2
     */
    @ApiModelProperty("冗余字段2")
    @ExcelProperty(value = "冗余字段2")
    private String attribute2;
    /**
     * 冗余字段3
     */
    @ApiModelProperty("冗余字段3")
    @ExcelProperty(value = "冗余字段3")
    private String attribute3;
    /**
     * 冗余字段4
     */
    @ApiModelProperty("冗余字段4")
    @ExcelProperty(value = "冗余字段4")
    private String attribute4;
    /**
     * 冗余字段5
     */
    @ApiModelProperty("冗余字段5")
    @ExcelProperty(value = "冗余字段5")
    private String attribute5;

}