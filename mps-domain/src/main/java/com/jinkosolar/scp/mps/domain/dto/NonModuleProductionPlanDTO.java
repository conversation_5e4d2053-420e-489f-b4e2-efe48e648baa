package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.annotation.TranslateSpecifyFactory;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.BomItemCustomBean;
import com.jinkosolar.scp.jip.api.dto.sap.SapPlanDto;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@ApiModel("非组件排产计划数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NonModuleProductionPlanDTO extends BaseDTO implements Serializable {

    @ApiModelProperty("前端序号")
    private String rowIndex;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    @Translate(DictType = "x", queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"attribute1Code", "attribute1Name"})
    private String attribute1;

    private String attribute1Code;

    private String attribute1Name;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute10;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute11;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute12;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute13;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute14;
    /**
     * 上饶调拨+委外需求
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute15;
    /**
     * 组件端可用结存万片数
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute16;
    /**
     * 结存天数
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute17;
    /**
     * 组件对电池需求
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute18;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute19;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    @Translate(DictType = "x", queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"attribute2Code", "attribute2Name"})
    private String attribute2;

    private String attribute2Code;

    private String attribute2Name;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute20;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute3;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute4;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute5;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute6;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute7;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute8;
    /**
     *
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")
    private String attribute9;
    /**
     * 事业部
     */
    @ApiModelProperty("事业部")
    @ExcelProperty(value = "事业部")
    @Translate(DictType = MpsLovConstant.BUSINESS_DEPRTMENT)
    private Long buId;
    /**
     * 发布状态
     */
    @ApiModelProperty("发布状态")
    @ExcelProperty(value = "发布状态")
    @Translate(DictType = MpsLovConstant.LOV_CODE_BOM_PUBLISHED_STATUS)
    private Long confirmStatus;
    /**
     * 坩埚规格
     */
    @ApiModelProperty("坩埚规格")
    @ExcelProperty(value = "坩埚规格")
    private String crucibleSpecification;
    /**
     * 晶棒类型1
     */
    @ApiModelProperty("晶棒类型1")
    @ExcelProperty(value = "晶棒类型1")
    @Translate(DictType = "x")
    private Long crystalType1;
    private String crystalType1Name;
    /**
     * 晶棒类型2
     */
    @ApiModelProperty("晶棒类型2")
    @ExcelProperty(value = "晶棒类型2")
    @Translate(DictType = "x")
    private Long crystalType2;
    private String crystalType2Name;
    /**
     * 实验/发货类型
     */
    @ApiModelProperty("实验/发货类型")
    @ExcelProperty(value = "实验/发货类型")
    @Translate(DictType = "x")
    private Long deliveryType;
    /**
     * 需求类别（研发、大货）
     */
    @ApiModelProperty("需求类别（研发、大货）")
    @ExcelProperty(value = "需求类别（研发、大货）")
    //@Translate(DictType = "x")
    @Translate(DictType = "x", queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"demandCategoryCode", "demandCategoryName"})
    private Long demandCategory;


    private String demandCategoryName;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    @Translate(DictType = "x")
    private Long directional;

    /**
     * 定向/非定向 Y OR N的值
     */
    private String isDirectional;

    /**
     * DPID
     */
    @ApiModelProperty("DPID")
    @ExcelProperty(value = "DPID")
    private String dpId;
    /**
     * 电性能
     */
    @ApiModelProperty("电性能")
    @ExcelProperty(value = "电性能")
    @Translate(DictType = "x")
    private Long electricalPerformance;
    private String electricalPerformanceName;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 电池工厂
     */
    @ApiModelProperty("电池工厂")
    @ExcelProperty(value = "电池工厂")
    @Translate(DictType = "x")
    private Long factoryId;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    @Translate(DictType = "x")
    private Long formula;
    /**
     * 炉型
     */
    @ApiModelProperty("炉型")
    @ExcelProperty(value = "炉型")
    @Translate(DictType = "x")
    private Long furnaceType;
    /**
     * 等级
     */
    @ApiModelProperty("等级")
    @ExcelProperty(value = "等级")
    @Translate(DictType = "x")
    private String grade;
    /**
     * 主栅
     */
    @ApiModelProperty("主栅")
    @ExcelProperty(value = "主栅")
    @Translate(DictType = "x")
    private Long gridLine;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 国内/海外/山西区分	目前分为：国内、海外、山西
     */
    @ApiModelProperty("国内/海外/山西区分	目前分为：国内、海外、山西")
    @ExcelProperty(value = "国内/海外/山西区分	目前分为：国内、海外、山西")
    @Translate(DictType = "x")
    private Long isOversea;

    /**
     * 物料ID
     */
    @ApiModelProperty("物料ID")
    @ExcelProperty(value = "物料ID")
    @Translate(customBean = BomItemCustomBean.class, customMethod = "getBomItemsByFactoryCodeAndItemCode",
            from = {"itemCode", "itemDesc"}, to = {"itemCode", "itemDesc"}, fields = {"attribute11", "itemCode"}, queryColumns = {"factoryCode", "itemCode"})
    private Long itemId;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")
    @Translate(unTranslate = true, customBean = BomItemCustomBean.class, customMethod = "getBomItemsByFactoryCodeAndItemCode",
            from = {"itemId"}, to = {"itemId"}, fields = {"factoryCode", "itemCode"}, queryColumns = {"organizationId", "itemCode"})
    private String itemCode;

    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 机台数
     */
    @ApiModelProperty("机台数")
    @ExcelProperty(value = "机台数")
    private Integer machineQty;
    /**
     * 匹配信息
     */
    @ApiModelProperty("匹配信息")
    @ExcelProperty(value = "匹配信息")
    private String matchRemark;
    /**
     * 匹配状态
     */
    @ApiModelProperty("匹配状态")
    @ExcelProperty(value = "匹配状态")
    @Translate(DictType = MpsLovConstant.LOV_CODE_BOM_ITEM_MAPPING_STATUS)
    private Long matchStatus;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    private String modelType;
    /**
     * 订单交货期
     */
    @ApiModelProperty("订单交货期")
    @ExcelProperty(value = "订单交货期")
    private LocalDateTime orderDeliveryDate;
    /**
     * 其它属性
     */
    @ApiModelProperty("其它属性")
    @ExcelProperty(value = "其它属性")
    @Translate(DictType = "x")
    private Long other;
    /**
     * 计划版本号
     */
    @ApiModelProperty("计划版本号")
    @ExcelProperty(value = "计划版本号")
    private String planVersion;

    /**
     * aps计划版本号
     */
    @ApiModelProperty("aps计划版本号")
    private String apsPlanVersion;

    /**
     * 计划工作中心
     */
    @ApiModelProperty("计划工作中心")
    @ExcelProperty(value = "计划工作中心")
    private String planWorkCenter;
    /**
     * 计划良率
     */
    @ApiModelProperty("计划良率")
    @ExcelProperty(value = "计划良率")
    private BigDecimal planYieldRate;
    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @ExcelProperty(value = "工序编号")
    private String processNo;
    /**
     * 工艺类型
     */
    @ApiModelProperty("工艺类型")
    @ExcelProperty(value = "工艺类型")
    @Translate(DictType = "x")
    private Long processType;
    /**
     * 硅片产地
     */
    @ApiModelProperty("硅片产地")
    @ExcelProperty(value = "硅片产地")
    private String productPlace;
    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    @ExcelProperty(value = "产品类型")
    @Translate(DictType = "x")
    private String productType;
    /**
     * 投产量 电池端计算硅片需求数量需要
     */
    @ApiModelProperty("投产量 电池端计算硅片需求数量需要")
    @ExcelProperty(value = "投产量 电池端计算硅片需求数量需要")
    private BigDecimal productionVolume;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产结束时间")
    private LocalDateTime schedulingEndTime;
    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @ExcelProperty(value = "排产数量")
    private BigDecimal schedulingQty;

    /**
     * 海外数量
     */
    @ApiModelProperty("海外数量")
    @ExcelProperty(value = "海外数量")
    private BigDecimal overseaQty;

    /**
     * 排产单位
     */
    @ApiModelProperty(value = "排产单位")
    @ExcelProperty(value = "排产单位")
    private String schedulingUom;

    /**
     * 是否追溯
     */
    @ApiModelProperty(value = "是否追溯")
    @ExcelProperty(value = "是否追溯")
    private String tracedBackFlag;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产开始时间")
    private LocalDateTime schedulingStartTime;
    /**
     * 排产时间
     */
    @ApiModelProperty("排产时间")
    @ExcelProperty(value = "排产时间")
    private String schedulingTime;
    /**
     * 硅料供应商
     */
    @ApiModelProperty("指定硅料供应商品牌")
    @ExcelProperty(value = "指定硅料供应商品牌")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_024_ATTR_1400, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"siliconSupplierName"}, splitChar = ",")
    private String siliconSupplier;
    /**
     * 单产
     */
    @ApiModelProperty("单产")
    @ExcelProperty(value = "单产")
    private BigDecimal singleProduction;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    @Translate(DictType = "x")
    private Long size;
    /**
     * 是否特殊需求
     */
    @ApiModelProperty("是否特殊需求")
    @ExcelProperty(value = "是否特殊需求")
    private String specialFlag;
    /**
     * 供美/非美
     */
    @ApiModelProperty("供美/非美")
    @ExcelProperty(value = "供美/非美")
    @Translate(DictType = "x")
    private Long supplyUs;
    /**
     * 技术图形
     */
    @ApiModelProperty("技术图形")
    @ExcelProperty(value = "技术图形")
    @Translate(DictType = "x")
    private String technicalFigure;
    /**
     * 热场尺寸
     */
    @ApiModelProperty("热场尺寸")
    @ExcelProperty(value = "热场尺寸")
    private String thermalFieldSize;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    @Translate(DictType = "x")
    private Long thickness;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER)
    private Long workCenterId;
    /**
     * 工作编码
     */
    @ApiModelProperty("工作编码")
    @ExcelProperty(value = "工作编码")
    private String workCode;
    /**
     * 工序
     */
    @ApiModelProperty("工序")
    @ExcelProperty(value = "工序")
    @Translate(DictType = MpsLovConstant.LOV_CODE_MPS_PROCESS_ID)
    private Long workNum;

    /**
     * 生产车间
     */
    @ApiModelProperty("生产车间")
    @ExcelProperty(value = "生产车间")
    @Translate(DictType = "x", queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"workshopCode", "workshopIdName"})
    private Long workshopId;


    private String matchStatusName;
    private String confirmStatusName;

    private String directionalName;
    private String buIdName;
    private String workCenterIdName;
    private String workshopIdName;

    //    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_024_ATTR_1400, queryColumns = {"lovName"}, from = {"lovLineId"}, to = {"siliconSupplier"}, splitChar = ",")
    private String siliconSupplierName;
    private String deliveryTypeName;
    private String supplyUsName;
    private String isOverseaName;
    private String workNumName;
    private String productTypeName;
    private String gridLineName;
    private String sizeName;
    private String month;
    /**
     * 晶棒投入量
     */
    @ApiModelProperty("晶棒投入量")
    @ExcelProperty(value = "晶棒投入量")
    private BigDecimal polysiliconIngotInput;

    private List<NonModuleProductionPlanDTO> lines;

    private List<String> dayList;

    private List<String> monthList;

    private Map<String, String> dataMap;

    private Map<String, BigDecimal> columnsDataMap;
    private Map<String, String> columnsMap;

    /**
     * SAP_生产计划下发返回文本
     */
    @ApiModelProperty("生产计划下发返回文本")
    private String sapResultMsg;

    /**
     * SAP_生产计划下发返回状态
     */
    @ApiModelProperty("生产计划下发返回状态")
    private String sapResultType;

    private String sapBatchNo;

    private List<NonModuleProductionPlanDTO> groupPlanList;

    /**
     * 产品类型ID
     */
    @ApiModelProperty("产品类型ID")
    private Long productTypeId;

    /**
     * 法碳标识
     */
    @ApiModelProperty(value = "法碳标识")
    private String frenchCarbonLabel;

    /**
     * 零碳标识
     */
    @ApiModelProperty(value = "零碳标识")
    private String zeroCarbonLabel;

    @ApiModelProperty(value = "效率")
    private BigDecimal efficiency;

    @ApiModelProperty(value = "转换MRP排产数量")
    private BigDecimal transitionqty;

    /**
     * 实际机台数
     */
    @ApiModelProperty(value = "实际机台数")
    private BigDecimal actualMachineQty;

    @ApiModelProperty(value = "产出")
    private BigDecimal inventoryForecastingQty1;
    @ApiModelProperty(value = "前一天切片库存")
    private BigDecimal inventoryForecastingQty2;
    @ApiModelProperty(value = "前一天电池库切片库存")
    private BigDecimal inventoryForecastingQty3;
    @ApiModelProperty(value = "在途库存")
    private BigDecimal inventoryForecastingQty4;
    @ApiModelProperty(value = "电池投产量")
    private BigDecimal inventoryForecastingQty5;
    @ApiModelProperty(value = "商务外发排产量")
    private BigDecimal inventoryForecastingQty6;
    @ApiModelProperty(value = "综合采购招标量")
    private BigDecimal inventoryForecastingQty7;
    @ApiModelProperty(value = "预测库存")
    private BigDecimal inventoryForecastingQty8;

    /**
     * 动态列数据
     */
    @ExcelProperty(value = "动态列数据")
    private Map<String, Map<Object, Object>> dynamicColumnMap;
    /**
     * 动态列名列表
     */
    @ExcelProperty(value = "动态列名列表")
    private List<String> dynamicColumnList;

    /**
     * 是否特殊需求名称
     */
    @ApiModelProperty("是否特殊需求名称")
    @ExcelProperty(value = "是否特殊需求名称")
    private String specialFlagName;

    private LocalDate apsPlanDate;

    // 组件端可用结存万片数
    private BigDecimal moduleQuantity;

    // 结存天数
    private BigDecimal dayNum;

    //组件对电池需求
    private BigDecimal moduleCellDemandQty;

    public static SapPlanDto toSapPlanDto(NonModuleProductionPlanDTO dto) {
        return SapPlanDto.builder().PLSCN(100L).ZSCJY(dto.getId() + "").
                ZLB(dto.getDemandCategoryName()).
                ZDX(dto.getDirectionalName()).
                MATERIAL(dto.getItemCode()).
                WERKS(dto.getFactoryCode()).
                ZJYSL(dto.getSchedulingQty()).
                ARBPL(dto.getWorkCenterIdName()).
                PSTTR(dto.getSchedulingStartTime().toLocalDate()).
                PEDTR(dto.getSchedulingEndTime().toLocalDate()).
                build();
    }

    /**
     * 倒角 有Lov
     */
    @Translate(DictType = "x")
    @ApiModelProperty(value = "倒角")
    private Long chamferId;
    /**
     * 倒角名称
     */
    @ApiModelProperty(value = "倒角名称")
    private String chamferName;

    /**
     * 数据来源 WD 老基地委代理加工
     */
    @ApiModelProperty(value = "数据来源")
    private String dataSource;

    /**
     * 最新版本标记 Y 表示最新版本
     */
    @ApiModelProperty(value = "最新版本标记")
    private String latestVersionFlag;


    /**
     * 大片区
     */
    @ApiModelProperty(value = "大片区")
    private Long bigAreaId;

    /**
     * 大片区名称
     */
    @ApiModelProperty(value = "大片区名称")
    private String bigAreaName;

    /**
     * 方棒采购方式
     */
    @Translate(DictType = LovHeaderCodeConstant.DP_PURCHASE)
    @ApiModelProperty(value = "方棒采购方式")
    private String squareRodPurchase;

    @ApiModelProperty(value = "方棒采购方式描述")
    private String squareRodPurchaseName;


    @ApiModelProperty(value = "是否自制名称")
    private String homemadeOrPurchaseName;

//    @Translate(DictType = LovHeaderCodeConstant.MPS_CELL_PROCUREMENT_METHOD, queryColumns = {"lovLineId"},
    /**
     * 排产计划分配开始时间
     */
    @ApiModelProperty(value = "排产计划分配开始时间")
    private LocalDateTime assignStartTime;

    @ApiModelProperty(value = "电池原始尺寸")
    private String dcSize;

    @ApiModelProperty(value = "结存推移数量")
    private BigDecimal inventoryTransferQty;

    /**
     * 辅材采购方式
     */
    @Translate(DictType = LovHeaderCodeConstant.DP_WAFER_DEMAND_TYPE)
    @ApiModelProperty(value = "辅材采购方式")
    private Long ancillaryMaterialsType;

    @ApiModelProperty(value = "辅材采购方式")
    private String ancillaryMaterialsTypeName;

    @ApiModelProperty(value = "起算电池效率")
    private String attribute21;

    @ApiModelProperty(value = "年度计划")
    private String yearplan;

    @ApiModelProperty(value = "排产区域")
    private String domesticOversea;

    @ApiModelProperty(value = "切方产量 ---单位：KG（等于上一日拉晶产量乘以良率）")
    private BigDecimal sliceSchedulingQty;

    @ApiModelProperty(value = "当日方棒产出结存数量")
    private BigDecimal barCarryOverQty;

    @ApiModelProperty(value = "当日硅片产出结存数量")
    private BigDecimal sliceCarryOverQty;

    @ApiModelProperty(value = "左工作")
    private String leftOperation;

    @ApiModelProperty(value = "期初库存")
    private BigDecimal openingInventory;

    @ApiModelProperty(value = "物料用数量")
    private BigDecimal materialQty;

    /**
     * 工艺类型
     */
    @ApiModelProperty("工艺类型")
    private String processTypeName;

    @ApiModelProperty(value = "工艺类型(规划)")
    private String processPlanTypeName;

    /**
     * 电池效率预测版本号
     */
    @ApiModelProperty("电池效率预测版本号")
    private String efficiencyVersion;

    @ApiModelProperty(value = "是否自制")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CELL_PROCUREMENT_METHOD)
    private Long homemadeOrPurchase;

    /**
     * 工艺类型(规划)
     */
    @ApiModelProperty(value = "工艺类型(规划)")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CRAFT)
    private Long processPlanType;

    @ApiModelProperty(value = "变更信息")
    private String changeInfo;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商")
    @TranslateSpecifyFactory(to = "specifySupplierName")
    private Long specifySupplier;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商名称")
    @ExcelProperty(value = "指定硅料供应商名称")
    private String specifySupplierName;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;


    /**
     * 生产模式
     */
    @ApiModelProperty("生产模式")
    private Long  productionMode;

    @ApiModelProperty("生产模式名称")
    private String productionModeName;

    /**
     * 委托方区域
     */
    @ApiModelProperty("委托方区域")
    private Long clientRegion ;

    /**
     * 委托方区域名称
     */
    @ApiModelProperty("生产模式名称")
    private String clientRegionName;

    /**
     * 受委托方区域
     */
    @ApiModelProperty("受委托方区域")
    private Long assigneeRegion;


    @ApiModelProperty("受委托方区域名称")
    private String assigneeRegionName;



}
