package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@ApiModel("老基地拉晶产量及单产报表实体")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NonModuleProductionPlanOldBaseCrystalReportDTO {
    /**
     * 类型
     * */
    private String type;
    /**
     *动态列头
     * */
    private List<String> columnHeadList;

    /**
     *动态数据
     * */
    private List<Map<String,Object>> columnDateList;

}
