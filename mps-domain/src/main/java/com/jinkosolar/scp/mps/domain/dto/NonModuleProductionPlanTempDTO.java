package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.jip.api.dto.sap.SapPlanDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@ApiModel("非组件排产临时表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NonModuleProductionPlanTempDTO extends BaseDTO implements Serializable {

    @ApiModelProperty(value = "plan_id")
    private Long planId;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 工作编码
     */
    @ApiModelProperty(value = "工作编码")
    private String workCode;

    /**
     * DPID
     */
    @ApiModelProperty(value = "DPID")
    private String dpId;

    /**
     * 工序编号
     */
    @ApiModelProperty(value = "工序编号")
    private String processNo;

    /**
     * 事业部
     */
    @ApiModelProperty(value = "事业部")
    private Long buId;

    /**
     * 事业部名称
     */
    @ApiModelProperty(value = "事业部名称")
    private String buIdName;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "产品类型ID")
    private Long productId;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterId;

    /**
     * 工作中心代码
     */
    @ApiModelProperty(value = "工作中心代码")
    private String workCenterCode;

    /**
     * 工作中心描述
     */
    @ApiModelProperty(value = "工作中心描述")
    private String workCenterIdName;

    /**
     * 计划工作中心
     */
    @ApiModelProperty(value = "计划工作中心")
    private String planWorkCenter;

    /**
     * 排产开始时间
     */
    @ApiModelProperty(value = "排产开始时间")
    private LocalDateTime schedulingStartTime;

    /**
     * 排产结束时间
     */
    @ApiModelProperty(value = "排产结束时间")
    private LocalDateTime schedulingEndTime;

    /**
     * 排产时长(h)
     */
    @ApiModelProperty(value = "排产时长(h)")
    private String schedulingTime;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal schedulingQty;

    /**
     * 排产单位
     */
    @ApiModelProperty(value = "排产单位")
    private String schedulingUom;

    /**
     * 是否追溯
     */
    @ApiModelProperty(value = "是否追溯")
    private String tracedBackFlag;

    /**
     * 定向/非定向
     */
    @ApiModelProperty(value = "定向/非定向")
    private Long directional;

    /**
     * 定向/非定向名称
     */
    @ApiModelProperty(value = "定向/非定向名称")
    private String directionalName;

    /**
     * 单产
     */
    @ApiModelProperty(value = "单产")
    private BigDecimal singleProduction;

    /**
     * 机台数
     */
    @ApiModelProperty(value = "机台数")
    private Integer machineQty;

    /**
     * 订单交货期
     */
    @ApiModelProperty(value = "订单交货期")
    private LocalDateTime orderDeliveryDate;

    /**
     * 模型分类
     */
    @ApiModelProperty(value = "模型分类")
    private String modelType;

    /**
     * 尺寸
     */
    @ApiModelProperty(value = "尺寸")
    private String size;

    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private String thickness;

    /**
     * 配方
     */
    @ApiModelProperty(value = "配方")
    private String formula;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private Long workshopId;

    /**
     * 生产车间名称
     */
    @ApiModelProperty(value = "生产车间名称")
    private String workshopIdName;

    /**
     * 电池工厂
     */
    @ApiModelProperty(value = "电池工厂")
    private Long factoryId;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 计划版本号
     */
    @ApiModelProperty(value = "计划版本号")
    private String planVersion;

    /**
     * aps计划版本号
     */
    @ApiModelProperty("aps计划版本号")
    private String apsPlanVersion;

    /**
     * 硅料供应商
     */
    @ApiModelProperty(value = "硅料供应商")
    private String siliconSupplier;

    /**
     * 硅料供应商名称
     */
    @ApiModelProperty(value = "硅料供应商名称")
    private String siliconSupplierName;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String grade;

    /**
     * 电性能
     */
    @ApiModelProperty(value = "电性能")
    private String electricalPerformance;

    /**
     * 晶棒类型1
     */
    @ApiModelProperty(value = "晶棒类型1")
    private String crystalType1;

    /**
     * 晶棒类型2
     */
    @ApiModelProperty(value = "晶棒类型2")
    private String crystalType2;

    /**
     * 实验/发货类型
     */
    @ApiModelProperty(value = "实验/发货类型")
    private Long deliveryType;

    /**
     * 实验/发货类型
     */
    @ApiModelProperty(value = "实验/发货类型")
    private String deliveryTypeName;

    /**
     * 供美/非美
     */
    @ApiModelProperty(value = "供美/非美")
    private Long supplyUs;

    /**
     * 供美/非美 名称
     * 名称
     */
    @ApiModelProperty(value = "供美/非美 名称 名称")
    private String supplyUsName;

    /**
     * 炉型
     */
    @ApiModelProperty(value = "炉型")
    private String furnaceType;

    /**
     * 热场尺寸
     */
    @ApiModelProperty(value = "热场尺寸")
    private String thermalFieldSize;

    /**
     * 坩埚规格
     */
    @ApiModelProperty(value = "坩埚规格")
    private String crucibleSpecification;

    /**
     * 计划良率
     */
    @ApiModelProperty(value = "计划良率")
    private BigDecimal planYieldRate;

    /**
     * 需求类别（研发、大货）
     */
    @ApiModelProperty(value = "需求类别（研发、大货）")
    private Long demandCategory;

    /**
     * 需求类别（研发、大货）名称
     */
    @ApiModelProperty(value = "需求类别（研发、大货）名称")
    private String demandCategoryName;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    private Long itemId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 国内/海外/山西区分   目前分为：国内、海外、山西
     */
    @ApiModelProperty(value = "国内/海外/山西区分   目前分为：国内、海外、山西")
    private Long isOversea;

    /**
     * 国内/海外/山西区分名称 目前分为：国内、海外、山西
     */
    @ApiModelProperty(value = "国内/海外/山西区分名称 目前分为：国内、海外、山西")
    private String isOverseaName;

    /**
     * 投产量 电池端计算硅片需求数量需要
     */
    @ApiModelProperty(value = "投产量 电池端计算硅片需求数量需要")
    private BigDecimal productionVolume;

    /**
     * 是否特殊需求
     */
    @ApiModelProperty(value = "是否特殊需求")
    private String specialFlag;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String gridLine;

    /**
     * 技术图形
     */
    @ApiModelProperty(value = "技术图形")
    private String technicalFigure;

    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    private String processType;

    /**
     * 工艺类型(规划)
     */
    @ApiModelProperty(value = "工艺类型(规划)")
    private String processPlanType;

    /**
     * 其它属性
     */
    @ApiModelProperty(value = "其它属性")
    private String other;

    /**
     * 硅片产地
     */
    @ApiModelProperty(value = "硅片产地")
    private String productPlace;

    /**
     * 工序:MPS.PROCESS_ID
     */
    @ApiModelProperty(value = "工序:MPS.PROCESS_ID")
    private Long workNum;

    /**
     * 工序名称
     */
    @ApiModelProperty(value = "工序名称")
    private String workNumName;

    /**
     * 匹配状态
     */
    @ApiModelProperty(value = "匹配状态")
    private String matchStatus;

    /**
     * 匹配信息
     */
    @ApiModelProperty(value = "匹配信息")
    private String matchRemark;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    private String confirmStatus;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute1;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute2;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute3;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute4;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute5;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute6;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute7;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute8;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute9;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute10;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute11;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute12;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute13;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute14;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute15;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute16;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute17;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute18;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute19;

    /**
     *
     */
    @ApiModelProperty(value = "")
    private String attribute20;

    @ApiModelProperty(value = "效率")
    private BigDecimal efficiency;

    @ApiModelProperty(value = "转换MRP排产数量")
    private BigDecimal transitionqty;

    /**
     * 最新版本标记 Y 表示最新版本
     */
    @ApiModelProperty(value = "最新版本标记")
    private String latestVersionFlag;

    /**
     * 动态列数据
     */
    @ExcelProperty(value = "动态列数据")
    private Map<String, Map<Object, Object>> dynamicColumnMap;
    /**
     * 动态列名列表
     */
    @ExcelProperty(value = "动态列名列表")
    private List<String> dynamicColumnList;
    /**
     *计划排产日期
     */
    @ApiModelProperty(value = "计划排产日期")
    private LocalDate apsPlanDate;
    public static SapPlanDto toSapPlanDto(NonModuleProductionPlanTempDTO dto) {
        return SapPlanDto.builder().PLSCN(100L).ZSCJY(dto.getId()+"").
                ZLB(dto.getDemandCategoryName()).
                ZDX(dto.getDirectionalName()).
                MATERIAL(dto.getItemCode()).
                WERKS(dto.getFactoryCode()).
                ZJYSL(dto.getSchedulingQty()).
                ARBPL(dto.getWorkCenterIdName()).
                PSTTR(dto.getSchedulingStartTime().toLocalDate()).
                PEDTR(dto.getSchedulingEndTime().toLocalDate()).
                build();
    }

    /**
     * 晶棒投入量
     */
    @ApiModelProperty("晶棒投入量")
    @ExcelProperty(value = "晶棒投入量")
    private BigDecimal polysiliconIngotInput;


    /**
     * 实际机台数
     */
    @ApiModelProperty(value = "实际机台数")
    private BigDecimal actualMachineQty;
    /**
     * 倒角 有Lov
     */
    @ApiModelProperty(value = "倒角")
    private String chamfer;

    /**
     *倒角lovId
     */
    @ApiModelProperty(value = "倒角lovId")
    private Long chamferId;


    private String schedulingStartDate;

    /**
     * 数据来源 WD 老基地委代理加工
     */
    @ApiModelProperty(value = "数据来源")
    @Column(name = "data_source")
    private String dataSource;

    private List<NonModuleProductionPlanTempDTO> lines;

    private List<String> dayList;

    private List<Long> lineIds;
    private Map<String, String> dataMap;
    //记录导入的行号
    private Integer rowNum;
    //标记导入类型
    private String type;

    /**
     * 方棒采购方式
     */
    @ApiModelProperty(value = "方棒采购方式")
    private String squareRodPurchase;

    /**
     * 排产计划分配开始时间
     */
    @ApiModelProperty(value = "排产计划分配开始时间")
    private LocalDateTime assignStartTime;

    @ApiModelProperty(value = "结存推移数量")
    private BigDecimal inventoryTransferQty;

    @ApiModelProperty(value = "电池原始尺寸")
    private String dcSize;

    /**
     * 辅材采购方式
     */
    @ApiModelProperty(value = "辅材采购方式")
    private String ancillaryMaterialsType;

    @ApiModelProperty(value = "起算电池效率")
    private String attribute21;

    @ApiModelProperty(value = "年度计划")
    private String yearplan;

    @ApiModelProperty(value = "切方产量 ---单位：KG（等于上一日拉晶产量乘以良率）")
    private BigDecimal sliceSchedulingQty;

    @ApiModelProperty(value = "左工作")
    private String leftOperation;

    @ApiModelProperty(value = "物料用数量")
    private BigDecimal materialQty;

    @ApiModelProperty(value = "是否自制")
    private String homemadeOrPurchase;

    @ApiModelProperty(value = "变更信息")
    private String changeInfo;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商")
    private Long specifySupplier;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商名称")
    @ExcelProperty(value = "指定硅料供应商名称")
    private String specifySupplierName;

    /**
     * 生产模式
     */
    @ApiModelProperty("生产模式")
    private Long  productionMode;

    @ApiModelProperty("生产模式名称")
    private String productionModeName;

    /**
     * 委托方区域
     */
    @ApiModelProperty("委托方区域")
    private Long clientRegion ;

    /**
     * 委托方区域名称
     */
    @ApiModelProperty("生产模式名称")
    private String clientRegionName;

    /**
     * 受委托方区域
     */
    @ApiModelProperty("受委托方区域")
    private Long assigneeRegion;


    @ApiModelProperty("受委托方区域名称")
    private String assigneeRegionName;
}