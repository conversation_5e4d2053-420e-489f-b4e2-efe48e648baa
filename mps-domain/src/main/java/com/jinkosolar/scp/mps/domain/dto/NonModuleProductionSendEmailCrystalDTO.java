package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.dto.message.ModuleMsgReceiverDto;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanQuery;
import com.jinkosolar.scp.mps.domain.query.WeeklyPlanReportQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ApiModel("组件排产计划发送邮件入参对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NonModuleProductionSendEmailCrystalDTO extends BaseDTO implements Serializable {
    /**
     * 电池排产计划查询对象
     */
    @ApiModelProperty("电池排产计划查询对象")
    private WeeklyPlanReportQuery query;
    /**
     * 工作中心
     */
    @ApiModelProperty("邮件入参对象")
    private ModuleMsgReceiverDto msgReceiverDto;

    /**
     * 基地id
     */
    @ApiModelProperty("基地id")
    private Long baseId;
}