package com.jinkosolar.scp.mps.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @USER: MWZ
 * @DATE: 2022/7/6
 */
@Data
public class NormalDistributionDTO {

    private Double x;
    private Double y;
    private Double z;

    public NormalDistributionDTO(double x, double y, double z) {
        this.x = x;
        this.y = y;
        this.z = z;
    }

    public static NormalDistributionDTO createDistributionDTO(double x, double y, double z) {
        return new NormalDistributionDTO(x, y, z);
    }

    public static NormalDistributionDTO createDistributionDTO(BigDecimal x, BigDecimal y, BigDecimal z) {

        return new NormalDistributionDTO(x.doubleValue(), y.doubleValue(), z.doubleValue());
    }
}
