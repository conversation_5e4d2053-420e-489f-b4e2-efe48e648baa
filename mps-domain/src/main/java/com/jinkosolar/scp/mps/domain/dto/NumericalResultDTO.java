package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 数值结果表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 08:02:10
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "数值结果表DTO对象", description = "DTO对象")
public class NumericalResultDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private Long targetId;
    /**
     * 数值
     */
    @ApiModelProperty(value = "数值")
    private BigDecimal numerical;
    /**
     * 数值结果
     */
    @ApiModelProperty(value = "数值结果")
    private BigDecimal numericalValue;
}
