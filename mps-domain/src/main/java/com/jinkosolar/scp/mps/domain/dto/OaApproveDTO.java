package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ApiModel("物料采购建议审核数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OaApproveDTO implements Serializable {

    /**
     * OA系统单据号
     */
    @ApiModelProperty("OA系统单据号")
    @ExcelProperty(value = "OA系统单据号")
    private String requestId;
    /**
     * OA审批状态
     */
    @ApiModelProperty("OA审批状态")
    @ExcelProperty(value = "OA审批状态")
    private String oaStatus;
    /**
     * OA系统单据号
     */
    @ApiModelProperty("OA系统单据号")
    @ExcelProperty(value = "OA系统单据号")
    private String oaReceiptNo;
    /**
     * OA审批消息
     */
    @ApiModelProperty("OA审批消息")
    @ExcelProperty(value = "OA审批消息")
    private String message;

}