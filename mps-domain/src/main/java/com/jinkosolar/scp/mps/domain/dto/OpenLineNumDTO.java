package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("调整开线数量表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenLineNumDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心Id")
    @ExcelProperty(value = "工作中心")
    private Long workCenterId;
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心")
    @ImportExConvert(sql = "SELECT id FROM mps_work_center t1 WHERE t1.is_deleted = 0 AND t1.work_center_code = ?1 ",targetFieldName="workCenterId")
    private String workCenterCode;
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心")
    private String workCenterDesc;
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    @ImportExConvert
    private String factoryCode;
    @ApiModelProperty("产线总数")
    @ExcelProperty(value = "产线总数")
    @ImportExConvert()
    private Integer productionLineNum ;
    /**
     * 出勤模式
     */
    @ApiModelProperty("出勤模式id")
    private Long attendanceId;
    @ApiModelProperty("出勤模式")
    @ExcelProperty(value = "出勤模式")
    @ImportExConvert(sql = "SELECT id FROM mps_attendance t1 WHERE t1.is_deleted = 0 AND t1.attendance_mode = ?1 AND t1.business_department = 'ZJ' ",targetFieldName="attendanceId")
    private String attendanceIdName;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    @ImportExConvert
    private String attendanceMonth;
    /**
     * 1号资源量
     */
    @ApiModelProperty("1号资源量")
    @ExcelProperty(value = "1号资源量")
    private BigDecimal attendanceDay1;
    /**
     * 10号资源量
     */
    @ApiModelProperty("10号资源量")
    @ExcelProperty(value = "10号资源量")
    private BigDecimal attendanceDay10;
    /**
     * 11号资源量
     */
    @ApiModelProperty("11号资源量")
    @ExcelProperty(value = "11号资源量")
    private BigDecimal attendanceDay11;
    /**
     * 12号资源量
     */
    @ApiModelProperty("12号资源量")
    @ExcelProperty(value = "12号资源量")
    private BigDecimal attendanceDay12;
    /**
     * 13号资源量
     */
    @ApiModelProperty("13号资源量")
    @ExcelProperty(value = "13号资源量")
    private BigDecimal attendanceDay13;
    /**
     * 14号资源量
     */
    @ApiModelProperty("14号资源量")
    @ExcelProperty(value = "14号资源量")
    private BigDecimal attendanceDay14;
    /**
     * 15号资源量
     */
    @ApiModelProperty("15号资源量")
    @ExcelProperty(value = "15号资源量")
    private BigDecimal attendanceDay15;
    /**
     * 16号资源量
     */
    @ApiModelProperty("16号资源量")
    @ExcelProperty(value = "16号资源量")
    private BigDecimal attendanceDay16;
    /**
     * 17号资源量
     */
    @ApiModelProperty("17号资源量")
    @ExcelProperty(value = "17号资源量")
    private BigDecimal attendanceDay17;
    /**
     * 18号资源量
     */
    @ApiModelProperty("18号资源量")
    @ExcelProperty(value = "18号资源量")
    private BigDecimal attendanceDay18;
    /**
     * 19号资源量
     */
    @ApiModelProperty("19号资源量")
    @ExcelProperty(value = "19号资源量")
    private BigDecimal attendanceDay19;
    /**
     * 2号资源量
     */
    @ApiModelProperty("2号资源量")
    @ExcelProperty(value = "2号资源量")
    private BigDecimal attendanceDay2;
    /**
     * 20号资源量
     */
    @ApiModelProperty("20号资源量")
    @ExcelProperty(value = "20号资源量")
    private BigDecimal attendanceDay20;
    /**
     * 21号资源量
     */
    @ApiModelProperty("21号资源量")
    @ExcelProperty(value = "21号资源量")
    private BigDecimal attendanceDay21;
    /**
     * 22号资源量
     */
    @ApiModelProperty("22号资源量")
    @ExcelProperty(value = "22号资源量")
    private BigDecimal attendanceDay22;
    /**
     * 23号资源量
     */
    @ApiModelProperty("23号资源量")
    @ExcelProperty(value = "23号资源量")
    private BigDecimal attendanceDay23;
    /**
     * 24号资源量
     */
    @ApiModelProperty("24号资源量")
    @ExcelProperty(value = "24号资源量")
    private BigDecimal attendanceDay24;
    /**
     * 25号资源量
     */
    @ApiModelProperty("25号资源量")
    @ExcelProperty(value = "25号资源量")
    private BigDecimal attendanceDay25;
    /**
     * 26号资源量
     */
    @ApiModelProperty("26号资源量")
    @ExcelProperty(value = "26号资源量")
    private BigDecimal attendanceDay26;
    /**
     * 27号资源量
     */
    @ApiModelProperty("27号资源量")
    @ExcelProperty(value = "27号资源量")
    private BigDecimal attendanceDay27;
    /**
     * 28号资源量
     */
    @ApiModelProperty("28号资源量")
    @ExcelProperty(value = "28号资源量")
    private BigDecimal attendanceDay28;
    /**
     * 29号资源量
     */
    @ApiModelProperty("29号资源量")
    @ExcelProperty(value = "29号资源量")
    private BigDecimal attendanceDay29;
    /**
     * 3号资源量
     */
    @ApiModelProperty("3号资源量")
    @ExcelProperty(value = "3号资源量")
    private BigDecimal attendanceDay3;
    /**
     * 30号资源量
     */
    @ApiModelProperty("30号资源量")
    @ExcelProperty(value = "30号资源量")
    private BigDecimal attendanceDay30;
    /**
     * 31号资源量
     */
    @ApiModelProperty("31号资源量")
    @ExcelProperty(value = "31号资源量")
    private BigDecimal attendanceDay31;
    /**
     * 4号资源量
     */
    @ApiModelProperty("4号资源量")
    @ExcelProperty(value = "4号资源量")
    private BigDecimal attendanceDay4;
    /**
     * 5号资源量
     */
    @ApiModelProperty("5号资源量")
    @ExcelProperty(value = "5号资源量")
    private BigDecimal attendanceDay5;
    /**
     * 6号资源量
     */
    @ApiModelProperty("6号资源量")
    @ExcelProperty(value = "6号资源量")
    private BigDecimal attendanceDay6;
    /**
     * 7号资源量
     */
    @ApiModelProperty("7号资源量")
    @ExcelProperty(value = "7号资源量")
    private BigDecimal attendanceDay7;
    /**
     * 8号资源量
     */
    @ApiModelProperty("8号资源量")
    @ExcelProperty(value = "8号资源量")
    private BigDecimal attendanceDay8;
    /**
     * 9号资源量
     */
    @ApiModelProperty("9号资源量")
    @ExcelProperty(value = "9号资源量")
    private BigDecimal attendanceDay9;
}
