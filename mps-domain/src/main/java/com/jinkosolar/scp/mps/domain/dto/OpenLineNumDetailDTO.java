package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("调整开线数量明细表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenLineNumDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;
    /**
     * 开线数量
     */
    @ApiModelProperty("开线数量")
    @ExcelProperty(value = "开线数量")
    private BigDecimal openLineNum;
    /**
     * 调整开线数量id
     */
    @ApiModelProperty("调整开线数量id")
    @ExcelProperty(value = "调整开线数量id")
    private Long openLineNumId;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")
    private Long workCenterId;
    /**
     * 工作时间起
     */
    @ApiModelProperty("工作时间起")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "工作时间起")
    private LocalDateTime workTimeFrom;
    /**
     * 工作时间止
     */
    @ApiModelProperty("工作时间止")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "工作时间止")
    private LocalDateTime workTimeTo;

    /**
     * 工作日期
     */
    @ApiModelProperty("工作日期")
    private LocalDate workDate;
}
