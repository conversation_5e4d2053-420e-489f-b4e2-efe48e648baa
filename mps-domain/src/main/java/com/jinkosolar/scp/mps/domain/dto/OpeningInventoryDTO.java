package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 期初库存表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-11-18 16:32:44
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "期初库存表DTO对象", description = "DTO对象")
public class OpeningInventoryDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String factoryDecs;

    /**
     * 库存地点
     */
    @ApiModelProperty(value = "库存地点")
    private String invSiteCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 入库日期
     */
    @ApiModelProperty(value = "入库日期")
    private LocalDate inventoryDate;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String inventoryLot;

    /**
     * 数量-非限制
     */
    @ApiModelProperty(value = "数量-非限制")
    private BigDecimal quantity;

    /**
     * 数量-质检
     */
    @ApiModelProperty(value = "数量-质检")
    private BigDecimal quantityX;

    /**
     * 库存月份（例如：202401）
     */
    @ApiModelProperty(value = "库存月份（例如：202401）")
    private Integer inventoryMonth;

    /**
     * 电池产品
     */
    @ApiModelProperty(value = "电池产品")
    private String spec;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private String grade;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGridLine;

    /**
     * 是否定向
     */
    @ApiModelProperty(value = "是否定向")
    private String directional;

    /**
     * 转换后是否定向
     */
    @ApiModelProperty(value = "转换后是否定向")
    private String afterConversionDirectional;

    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsFlag;

    /**
     * 硅料供应商品牌
     */
    @ApiModelProperty(value = "硅料供应商品牌")
    private String siliconVendorBrand;

    /**
     * 生产工厂代码
     */
    @ApiModelProperty(value = "生产工厂代码")
    private String productionFactoryCode;

    /**
     * 工厂排产区域
     */
    @ApiModelProperty(value = "工厂排产区域")
    private String factoryDomesticOversea;

    /**
     * 生产工厂排产区域
     */
    @ApiModelProperty(value = "生产工厂排产区域")
    private String productionFactoryDomesticOversea;

    /**
     * 中长期统计区域
     */
    @ApiModelProperty(value = "中长期统计区域")
    private String statisticalRegion;

    /**
     * 电池单片瓦数
     */
    @ApiModelProperty(value = "电池单片瓦数")
    private BigDecimal batteryWattage;

    /**
     * 分片数
     */
    @ApiModelProperty(value = "分片数")
    private BigDecimal batteryProductSegmentation;

    /**
     * 电池历史入库兆瓦数
     */
    @ApiModelProperty(value = "电池历史入库兆瓦数")
    private BigDecimal quantityMw;
}
