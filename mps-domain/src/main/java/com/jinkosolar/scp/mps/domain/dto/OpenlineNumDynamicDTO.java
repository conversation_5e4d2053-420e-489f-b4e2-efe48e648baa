package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@ApiModel("调整开线数量动态展示数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenlineNumDynamicDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;

    @ApiModelProperty("工厂id")
    @ExcelProperty(value = "工厂id")
    private Long factoryId;


    @ApiModelProperty("工厂code")
    @ExcelProperty(value = "工厂code")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"factoryCode"},
            from = {"lovLineId"}, to = {"factoryId"}, required = true)
    private String factoryCode;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private Long workCenterId;

    /**
     * 工作中心Code
     */
    @ApiModelProperty("工作中心Code")
    @ExcelProperty(value = "工作中心Code")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"workCenterCode"},
            from = {"lovLineId"}, to = {"workCenterId"}, required = true)
    private String workCenterCode;
    /**
     * 产线总数
     */
    @ApiModelProperty("产线总数")
    @ExcelProperty(value = "产线总数")  
    private Integer productionLineNum;
    /**
     * 出勤模式
     */
    @ApiModelProperty("出勤模式")
    @ExcelProperty(value = "出勤模式")  
    private Long attendanceId;

    /**
     * 出勤模式名称
     */
    @ApiModelProperty("出勤模式名称")
    @ExcelProperty(value = "出勤模式名称")
    private String attendanceIdNameAlias;



    /**
     * 出勤模式
     */
    @ApiModelProperty("出勤模式")
    @ExcelProperty(value = "出勤模式")
    private String attendanceName;

    /**
     * 资源量
     */
    @ApiModelProperty("资源量")
    @ExcelProperty(value = "资源量")  
    private BigDecimal attendanceNum;
    @ApiModelProperty("日期")
    @ExcelProperty(value = "日期")
    private LocalDate attendanceDate;

    // 使用HashMap来存储月份和月份值
    private Map<String, BigDecimal> attendanceDateList = new HashMap<>();

    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    @ExcelProperty(value = "结束时间")
    private LocalDate endDate;

    private int row;
}