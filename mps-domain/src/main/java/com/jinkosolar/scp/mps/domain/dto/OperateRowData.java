package com.jinkosolar.scp.mps.domain.dto;

import com.jinkosolar.scp.mps.domain.util.MathUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: OperateRowData
 * @date 2023/11/23 10:24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OperateRowData<T> {
    /**
     * 实体对象
     */
    private T rowData;
    /**
     * 动态列1
     */
    private Object column1;
    /**
     * 动态列2
     */
    private Object column2;


    /**
     * 解析带有%的数值，去除%，并转换为BigDecimal
     *
     * @param cellValue
     * @return
     */
    public static BigDecimal parsingDataToBigDecimal(Object cellValue) {
        if (Objects.nonNull(cellValue) && StringUtils.isNotBlank(cellValue.toString())) {
            String value = cellValue.toString();
            if (value.indexOf("%") > -1) {
                value = value.replace("%", "");
                BigDecimal decimal = new BigDecimal(value);
                return MathUtils.checkIsZero(decimal) ? BigDecimal.ZERO : decimal.divide(MathUtils.ONE_HUNDRED, 4, BigDecimal.ROUND_HALF_UP);
            }
            return new BigDecimal(value);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 解析日期，并去除日期格式中的-
     *
     * @param cellValue
     * @return
     */
    public static String parsingDataToString(Object cellValue) {
        if (Objects.nonNull(cellValue) && StringUtils.isNotBlank(cellValue.toString())) {
            String cellValueStr = cellValue.toString();
            if(cellValueStr.indexOf("-") > -1){
                String[] split = cellValueStr.split("-");
                String value = split[0] + (split[1].length() < 2 ? "0" + split[1] : split[1]);
                return value;
            }
           return cellValueStr;
        }
        return null;
    }

    /**
     * 解析日期
     *
     * @param cellValue
     * @return
     */
    public static Integer parsingDataToInteger(Object cellValue) {
        if (Objects.nonNull(cellValue) && StringUtils.isNotBlank(cellValue.toString())) {
            String cellValueStr = cellValue.toString();
            return Integer.valueOf(cellValueStr);
        }
        return null;
    }

    /**
     * 校验字段是否为季度
     *
     * @return
     */
    public boolean verifyColumn() {
        if (Objects.nonNull(this.column1)) {
            return column1.toString().indexOf("季度") > -1;
        }
        return true;
    }

}
