package com.jinkosolar.scp.mps.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 电池外购计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-14 01:37:34
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "电池外购计划DTO对象", description = "DTO对象")
public class OverseaPurchasePlanDTO extends BaseDTO {

    /**
     * ID主键
     */
    @ApiModelProperty(value = "ID主键")
    private Long id;
    /**
     * 是否国内
     */
 //   @ExportConvert(lovCode = LovHeaderCodeConstant.IS_OVERSEA)
    @ApiModelProperty(value = "是否国内")
    private String isOversea;
    /**
     * 是否国内
     */
  //  @ExcelHeader(index = 0)
  //  @ImportConvert(lovCode = LovHeaderCodeConstant.IS_OVERSEA)
    @ApiModelProperty(value = "是否国内")
    private String isOverseaName;
    /**
     * 供应方
     */
  //  @ExportConvert(lovCode = LovHeaderCodeConstant.POWER_EFFICIENCY_SUPPLIER)
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 供应方
     */
 //   @ExcelHeader(index = 1)
  //  @ImportConvert(lovCode = LovHeaderCodeConstant.POWER_EFFICIENCY_SUPPLIER)
    @ApiModelProperty(value = "供应方")
    private String supplierName;
    /**
     * 发货城市
     */
    @ApiModelProperty(value = "发货城市")
    private String city;
    /**
     * 电池类型
     */
  //  @ExportConvert(lovCode = LovHeaderCodeConstant.CELL_TYPE)
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
//    @ExcelHeader(index = 2)
  //  @ImportConvert(lovCode = LovHeaderCodeConstant.CELL_TYPE)
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 电池料号
     */
  //  @ExcelHeader(index = 3)
    @ApiModelProperty(value = "电池料号")
    private String cellNo;
    /**
     * 效率值
     */
    @ApiModelProperty(value = "效率值")
    private BigDecimal efficiency;
    /**
     * 月份
     */
 //   @ExcelHeader(index = 4)
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private Integer day;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;


    /**
     * 动态列 1-31号
     */
    private List<String> subList;

    /**
     * 动态列 1-31号
     */
    private Map<String, BigDecimal> subMap;

    public OverseaPurchasePlanDTO(String isOversea, String supplier, String cellType, String month) {
        this.isOversea = isOversea;
        this.supplier = supplier;
        this.cellType = cellType;
        this.month = month;
    }

    public Map<String, Object> convertMap() {
        Map<String, Object> objectMap = BeanUtil.beanToMap(this);
        objectMap.putAll(this.subMap);
        return objectMap;
    }

    public String buildPrompt() {
        return StringUtils.join(this.isOverseaName, ",", this.supplierName, ",", this.cellTypeName, ",", this.cellNo, ",", this.month);
    }

    public OverseaPurchasePlanDTO build(){
        return new OverseaPurchasePlanDTO(this.isOversea, this.supplier, this.cellType, this.month);
    }
}
