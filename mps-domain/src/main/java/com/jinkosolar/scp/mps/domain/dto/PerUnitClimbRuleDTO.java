package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("单产爬坡规则数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PerUnitClimbRuleDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类id")
    @ExcelProperty(value = "数据分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE,unTranslate = false)
    private Long dataTypeId;

    @ApiModelProperty("数据分类名称")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE,unTranslate = true)
    @ExcelProperty(index = 7)
    private String dataTypeIdName;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    @ExcelProperty(value = "版本")
    private String versionNumber;
    /**
     * 晶棒分类
     */
    @ApiModelProperty("左产品型号id")
    @Translate(DictType = LovHeaderCodeConstant.PRODUCT_TYPE_LOV,unTranslate = false)
    private Long productTypeLeftId;

    @ApiModelProperty("左产品型号名称")
    @ExcelProperty(index = 0)
    @Translate(DictType = LovHeaderCodeConstant.PRODUCT_TYPE_LOV,unTranslate = true)
    private String productTypeLeftIdName;
    /**
     * 物料id
     */
    @ApiModelProperty("右产品型号")
    @Translate(DictType = LovHeaderCodeConstant.PRODUCT_TYPE_LOV,unTranslate = false)
    private Long productTypeRightId;
    @ApiModelProperty("右产品型号")
    @ExcelProperty(index = 1)
    @Translate(DictType = LovHeaderCodeConstant.PRODUCT_TYPE_LOV,unTranslate = true)
    private String productTypeRightIdName;
    /**
     * 第1轮(%)
     */
    @ApiModelProperty("第1轮(%)")
    @ExcelProperty(index = 2)
    private BigDecimal rule1Factor;
    /**
     * 第2轮(%)
     */
    @ApiModelProperty("第2轮(%)")
    @ExcelProperty(index = 3)
    private BigDecimal rule2Factor;
    /**
     * 第3轮(%)
     */

    @ApiModelProperty("第3轮(%)")
    @ExcelProperty(index = 4)
    private BigDecimal rule3Factor;
    /**
     * 第4轮(%)
     */
    @ApiModelProperty("第4轮(%)")
    @ExcelProperty(index = 5)
    private BigDecimal rule4Factor;
    /**
     * 第5轮(%)
     */
    @ApiModelProperty("第5轮(%)")
    @ExcelProperty(index = 6)
    private BigDecimal rule5Factor;


    @ApiModelProperty(value = "车间id")
    @Translate(DictType = MpsLovConstant.WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"workShopCode", "workShopName"})
    private Long workshopId;

    @ApiModelProperty("车间")
    @ExcelProperty(index = 8)
    private String workShopName;



    @ApiModelProperty("车间编码")
    @ExcelProperty(index =9)
    @Translate(unTranslate = true, DictType = MpsLovConstant.WORKSHOP, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workshopId"}, required = true)
    private String workShopCode;





}
