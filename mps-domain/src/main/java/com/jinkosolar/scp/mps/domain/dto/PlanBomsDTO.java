package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("组件计划的标准BOM行数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlanBomsDTO extends BaseDTO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty("id")
    @ExcelProperty(value = "id")  
    private Long id;
    /**
     * MPS生产计划行id
     */
    @ApiModelProperty("MPS生产计划行id")
    @ExcelProperty(value = "MPS生产计划行id")  
    private Long productionPlanId;
    /**
     * 生产建议
     */
    @ApiModelProperty("生产建议编号")
    @ExcelProperty(value = "生产建议编号")
    private String suggestionsNo;
    /**
     * 取订单BOM行的ranking
     */
    @ApiModelProperty("取订单BOM行的ranking")
    @ExcelProperty(value = "取订单BOM行的ranking")  
    private String componentSequenceId;
    /**
     * 取订单BOM行的结构
     */
    @ApiModelProperty("取订单BOM行的结构")
    @ExcelProperty(value = "取订单BOM行的结构")  
    private String bomStructure;
    /**
     * 子件物料编码
     */
    @ApiModelProperty("子件物料编码")
    @ExcelProperty(value = "子件物料编码")  
    private String itemCode;
    /**
     * 子件物料数量
     */
    @ApiModelProperty("子件物料数量")
    @ExcelProperty(value = "子件物料数量")  
    private BigDecimal componentQuantity;
    /**
     * 子件物料单位
     */
    @ApiModelProperty("子件物料单位")
    @ExcelProperty(value = "子件物料单位")  
    private String componentUom;
    /**
     * SAP正背面标识
     */
    @ApiModelProperty("SAP正背面标识")
    @ExcelProperty(value = "SAP正背面标识")  
    private String frontBackFlag;
    /**
     * 损耗率
     */
    @ApiModelProperty("损耗率")
    @ExcelProperty(value = "损耗率")  
    private BigDecimal outputRate;
    /**
     * SAP主替料分组号
     */
    @ApiModelProperty("SAP主替料分组号")
    @ExcelProperty(value = "SAP主替料分组号")  
    private String bomItemGroup;
    /**
     * SAP使用百分比
     */
    @ApiModelProperty("SAP使用百分比")
    @ExcelProperty(value = "SAP使用百分比")  
    private BigDecimal bomItemUsage;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")  
    private String remark;
    /**
     * 扩展属性分类
     */
    @ApiModelProperty("扩展属性分类")
    @ExcelProperty(value = "扩展属性分类")  
    private String attributeCategory;
    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")  
    private String attribute1;
    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    @ExcelProperty(value = "扩展字段2")  
    private String attribute2;
    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    @ExcelProperty(value = "扩展字段3")  
    private String attribute3;
    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    @ExcelProperty(value = "扩展字段4")  
    private String attribute4;
    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    @ExcelProperty(value = "扩展字段5")  
    private String attribute5;
    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    @ExcelProperty(value = "扩展字段6")  
    private String attribute6;
    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    @ExcelProperty(value = "扩展字段7")  
    private String attribute7;
    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    @ExcelProperty(value = "扩展字段8")  
    private String attribute8;
    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    @ExcelProperty(value = "扩展字段9")  
    private String attribute9;
    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    @ExcelProperty(value = "扩展字段10")  
    private String attribute10;
    /**
     * 扩展字段11
     */
    @ApiModelProperty("扩展字段11")
    @ExcelProperty(value = "扩展字段11")  
    private String attribute11;
    /**
     * 扩展字段12
     */
    @ApiModelProperty("扩展字段12")
    @ExcelProperty(value = "扩展字段12")  
    private String attribute12;
    /**
     * 扩展字段13
     */
    @ApiModelProperty("扩展字段13")
    @ExcelProperty(value = "扩展字段13")  
    private String attribute13;
    /**
     * 扩展字段14
     */
    @ApiModelProperty("扩展字段14")
    @ExcelProperty(value = "扩展字段14")  
    private String attribute14;
    /**
     * 扩展字段15
     */
    @ApiModelProperty("扩展字段15")
    @ExcelProperty(value = "扩展字段15")  
    private String attribute15;

    /**
     * 搭配结果标识
     */
    @ApiModelProperty("搭配结果标识")
    @ExcelProperty(value = "搭配结果标识")
    private String resultFlag;
}