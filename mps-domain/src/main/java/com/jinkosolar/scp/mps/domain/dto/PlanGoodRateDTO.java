package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("计划良率表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlanGoodRateDTO extends PageDTO implements Serializable {
    /**
     * 历史转库存率
     */
    @ApiModelProperty("历史转库存率")
    @ExcelProperty(value = "历史转库存率")
    private BigDecimal historyToInventoryRate;
    /**
     * 历史降级率
     */
    @ApiModelProperty("历史降级率")
    @ExcelProperty(value = "历史降级率")
    private BigDecimal historyToReduceRate;
    /**
     * 历史工单总数量
     */
    @ApiModelProperty("历史工单总数量")
    @ExcelProperty(value = "历史工单总数量")
    private BigDecimal historyTotalInputQuantity;
    /**
     * 历史良品数量
     */
    @ApiModelProperty("历史良品数量")
    @ExcelProperty(value = "历史良品数量")
    private BigDecimal historyGoodProductQuantity;

    /**
     * 历史转库存数量
     */
    @ApiModelProperty("历史转库存数量")
    @ExcelProperty(value = "历史转库存数量")
    private BigDecimal historyToInventoryQuantity;

    /**
     * 历史降级数量
     */
    @ApiModelProperty("历史降级数量")
    @ExcelProperty(value = "历史降级数量")
    private BigDecimal historyDowngradeQuantity;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 计划转库存率
     */
    @ApiModelProperty("计划转库存率")
    @ExcelProperty(value = "计划转库存率")
    private BigDecimal planToInventoryRate;
    /**
     * 计划降级率
     */
    @ApiModelProperty("计划降级率")
    @ExcelProperty(value = "计划降级率")
    private BigDecimal planToReduceRate;
    /**
     * 计划版型id
     */
    @ApiModelProperty("计划版型id")
    private String planVersion;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    @Dict(methodName = "getPlanVersionCode",fieldKey = "planVersion",fieldName = "planVersionCode")
    private String planVersionCode;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    @ImportExConvert(sql = "SELECT lov_value FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_value = ?1 and t2.lov_code='"+LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE+"'")
    private String productFactoryCode;

    /**
     * 生产车间
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKSHOP)
    @ApiModelProperty("生产车间=work_shop")
    private Long workshopId;

    /**
     * 车间 代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_value = ?1 and t2.lov_code='"+LovHeaderCodeConstant.SYS_WORKSHOP+"'", targetFieldName = "workshopId")
    private String workshopCode;

    /**
     * 生产车间描述
     */
    @ApiModelProperty("生产车间描述")
    @ExcelProperty(value = "生产车间描述")
    private String workshopIdName;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id", notes = "")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;
}