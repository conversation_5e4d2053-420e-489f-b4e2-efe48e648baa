package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;


@ApiModel("排产版本控制表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlanVerisonControllDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    @ExcelIgnore
    private String domesticOversea;
    /**
     * 排产类型
     */
    @ApiModelProperty("排产类型")
    @ExcelProperty(value = "排产类型")
    private String planType;
    /**
     * 排产版本号
     */
    @ApiModelProperty("排产版本号")
    @ExcelProperty(value = "排产版本号")
    private String planVersion;
    /**
     * 当前版本标识
     */
    @ApiModelProperty("当前版本标识")
    @ExcelProperty(value = "当前版本标识")
    private String status;

    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String attribute1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    @ExcelProperty(value = "扩展字段2")
    private String attribute2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    @ExcelProperty(value = "扩展字段3")
    private String attribute3;

    /**
     * 扩展字段3
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 上一个排产版本号
     */
    @ApiModelProperty("上一个排产版本号")
    @ExcelProperty(value = "上一个排产版本号")
    private String oldPlanVersion;

    public void setupDomesticOversea() {
        List<String> strList = Arrays.asList(this.planType.split("-"));
        if (strList.size() > 1) {
            this.domesticOversea = strList.get(1);
        }
    }
}