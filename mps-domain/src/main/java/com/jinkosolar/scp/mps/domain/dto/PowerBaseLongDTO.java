package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 长期功率预测维护
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:51
 */
@Data
//@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false, of = {"productFamily", "installType", "projectPlace", "beginMonth", "endMonth"})
@ApiModel(value = "PowerBaseLongDTO对象", description = "DTO对象")
public class PowerBaseLongDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 财务最优ID
     */
    @ApiModelProperty(value = "财务最优ID")
    private Long financeId;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    private String projectPlace;
    /**
     * 效率步长
     */
    @ApiModelProperty(value = "效率步长")
    private String efficiencyRange;
    /**
     * 功率步长
     */
    @ApiModelProperty(value = "功率步长")
    private BigDecimal powerRange;
    /**
     * 档位范围
     */
    @ApiModelProperty(value = "档位范围")
    private String gear;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 起始月份
     */
    @ApiModelProperty(value = "起始月份")
    private String beginMonth;
    /**
     * 终止月份
     */
    @ApiModelProperty(value = "终止月份")
    private String endMonth;
    /**
     * 基准效率
     */
    @ApiModelProperty(value = "基准效率")
    private String beginEfficiency;
    /**
     * 基准功率
     */
    @ApiModelProperty(value = "基准功率")
    private BigDecimal beginPower;
    /**
     * 标准差
     */
    @ApiModelProperty(value = "标准差")
    private BigDecimal difference;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 中气候性条件
     */
    @ApiModelProperty(value = "中气候性条件")
    private String  climateConditions;
    /**
     * 小区域认证
     */
    @ApiModelProperty(value = "小区域认证")
    private String  smallAreaCertification;
    /**
     * 功率版本字段
     */
    @ApiModelProperty(value = "功率版本字段")
    private String  powerVersionField;
    /**
     *供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private Integer sortField;;

    List<Map<String, String>> dataStructures;

    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20;
}
