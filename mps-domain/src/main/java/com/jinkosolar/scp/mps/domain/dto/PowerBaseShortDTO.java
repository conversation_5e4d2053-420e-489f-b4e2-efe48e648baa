package com.jinkosolar.scp.mps.domain.dto;


import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.LovLineDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MathUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 预测基准档位功率
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:07
 */
@Data
@EqualsAndHashCode(callSuper = false, of = {"productFamily", "installType", "projectPlace", "pieceRequirements", "powerReserve", "cableLength", "month", "dataVersion","itemAttribute14"})
//@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerBaseShortDTO对象", description = "DTO对象")
public class PowerBaseShortDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    private String projectPlace;
    /**
     * 标片需求
     */
    @ApiModelProperty(value = "标片需求")
    private String pieceRequirements;
    /**
     * 功率预留
     */
    @ApiModelProperty(value = "功率预留")
    private String powerReserve;
    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度")
    private String cableLength;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String dataVersion;
    /**
     * 功率范围
     */
    @ApiModelProperty(value = "功率范围")
    private String powerRange;
    /**
     * 结果数据需要更新
     */
    @ApiModelProperty(value = "结果数据需要更新")
    private Integer isRecalcResultData;

    private String sourceDataMonth;

    private String targetDataMonth;

    private Integer isConfirmUpd;

    /**
     * 起始功率1
     */
    @ApiModelProperty(value = "起始功率1")
    private BigDecimal beginPower1;
    /**
     * 起始效率1
     */
    @ApiModelProperty(value = "起始效率1")
    private String beginEfficiency1;
    /**
     * 最后效率1
     */
    @ApiModelProperty(value = "最后效率1")
    private String finallyEfficiency1;
    /**
     * 效率步长1
     */
    @ApiModelProperty(value = "效率步长1")
    private String efficiencyRange1;
    /**
     * 功率步长1
     */
    @ApiModelProperty(value = "功率步长1")
    private BigDecimal powerRange1;
    /**
     * 标准差1
     */
    @ApiModelProperty(value = "标准差1")
    private BigDecimal difference1;
    /**
     * 起始功率2
     */
    @ApiModelProperty(value = "起始功率2")
    private BigDecimal beginPower2;
    /**
     * 起始效率2
     */
    @ApiModelProperty(value = "起始效率2")
    private String beginEfficiency2;
    /**
     * 最后效率2
     */
    @ApiModelProperty(value = "最后效率2")
    private String finallyEfficiency2;
    /**
     * 效率步长2
     */
    @ApiModelProperty(value = "效率步长2")
    private String efficiencyRange2;
    /**
     * 功率步长2
     */
    @ApiModelProperty(value = "功率步长2")
    private BigDecimal powerRange2;
    /**
     * 标准差2
     */
    @ApiModelProperty(value = "标准差2")
    private BigDecimal difference2;
    /**
     * 起始功率3
     */
    @ApiModelProperty(value = "起始功率3")
    private BigDecimal beginPower3;
    /**
     * 起始效率3
     */
    @ApiModelProperty(value = "起始效率3")
    private String beginEfficiency3;
    /**
     * 最后效率3
     */
    @ApiModelProperty(value = "最后效率3")
    private String finallyEfficiency3;
    /**
     * 效率步长3
     */
    @ApiModelProperty(value = "效率步长3")
    private String efficiencyRange3;
    /**
     * 功率步长3
     */
    @ApiModelProperty(value = "功率步长3")
    private BigDecimal powerRange3;
    /**
     * 标准差3
     */
    @ApiModelProperty(value = "标准差3")
    private BigDecimal difference3;
    /**
     * 起始功率4
     */
    @ApiModelProperty(value = "起始功率4")
    private BigDecimal beginPower4;
    /**
     * 起始效率4
     */
    @ApiModelProperty(value = "起始效率4")
    private String beginEfficiency4;
    /**
     * 最后效率4
     */
    @ApiModelProperty(value = "最后效率4")
    private String finallyEfficiency4;
    /**
     * 效率步长4
     */
    @ApiModelProperty(value = "效率步长4")
    private String efficiencyRange4;
    /**
     * 功率步长4
     */
    @ApiModelProperty(value = "功率步长4")
    private BigDecimal powerRange4;
    /**
     * 标准差4
     */
    @ApiModelProperty(value = "标准差4")
    private BigDecimal difference4;
    /**
     * 起始功率5
     */
    @ApiModelProperty(value = "起始功率5")
    private BigDecimal beginPower5;
    /**
     * 起始效率5
     */
    @ApiModelProperty(value = "起始效率5")
    private String beginEfficiency5;
    /**
     * 最后效率5
     */
    @ApiModelProperty(value = "最后效率5")
    private String finallyEfficiency5;
    /**
     * 效率步长5
     */
    @ApiModelProperty(value = "效率步长5")
    private String efficiencyRange5;
    /**
     * 功率步长5
     */
    @ApiModelProperty(value = "功率步长5")
    private BigDecimal powerRange5;
    /**
     * 标准差5
     */
    @ApiModelProperty(value = "标准差5")
    private BigDecimal difference5;
    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20;

    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String moduleSize;

    /**
     * 字段显示
     */
    private List<Map<String, String>> dataColumn;

    public String compatePoStr() {

        return "PowerBaseShort{" +
                ", productFamily='" + this.bleankToNull(productFamily) +
                ", installType='" + this.bleankToNull(installType) +
                ", projectPlace='" + this.bleankToNull(projectPlace) +
                ", cableLength=" + this.bleankToNull(cableLength) +
                ", moduleSize='" + this.bleankToNull(moduleSize) +
                ", month='" + this.bleankToNull(month) +
                ", dataVersion='" + this.bleankToNull(dataVersion) +
                ", powerRange='" + this.bleankToNull(powerRange) +
                ", beginPower1=" + MathUtils.obj2DecimalFormat(beginPower1) +
                ", beginEfficiency1=" + MathUtils.obj2DecimalFormat(beginEfficiency1) +
                ", finallyEfficiency1=" + MathUtils.obj2DecimalFormat(finallyEfficiency1) +
                ", efficiencyRange1=" + MathUtils.obj2DecimalFormat(efficiencyRange1) +
                ", powerRange1=" + MathUtils.obj2DecimalFormat(powerRange1) +
                ", difference1=" + MathUtils.obj2DecimalFormat(difference1) +
                ", beginPower2=" + MathUtils.obj2DecimalFormat(beginPower2) +
                ", beginEfficiency2=" + MathUtils.obj2DecimalFormat(beginEfficiency2) +
                ", finallyEfficiency2=" + MathUtils.obj2DecimalFormat(finallyEfficiency2) +
                ", efficiencyRange2=" + MathUtils.obj2DecimalFormat(efficiencyRange2) +
                ", powerRange2=" + MathUtils.obj2DecimalFormat(powerRange2) +
                ", difference2=" + MathUtils.obj2DecimalFormat(difference2) +
                ", beginPower3=" + MathUtils.obj2DecimalFormat(beginPower3) +
                ", beginEfficiency3=" + MathUtils.obj2DecimalFormat(beginEfficiency3) +
                ", finallyEfficiency3=" + MathUtils.obj2DecimalFormat(finallyEfficiency3) +
                ", efficiencyRange3=" + MathUtils.obj2DecimalFormat(efficiencyRange3) +
                ", powerRange3=" + MathUtils.obj2DecimalFormat(powerRange3) +
                ", difference3=" + MathUtils.obj2DecimalFormat(difference3) +
                ", beginPower4=" + MathUtils.obj2DecimalFormat(beginPower4) +
                ", beginEfficiency4=" + MathUtils.obj2DecimalFormat(beginEfficiency4) +
                ", finallyEfficiency4=" + MathUtils.obj2DecimalFormat(finallyEfficiency4) +
                ", efficiencyRange4=" + MathUtils.obj2DecimalFormat(efficiencyRange4) +
                ", powerRange4=" + MathUtils.obj2DecimalFormat(powerRange4) +
                ", difference4=" + MathUtils.obj2DecimalFormat(difference4) +
                ", beginPower5=" + MathUtils.obj2DecimalFormat(beginPower5) +
                ", beginEfficiency5=" + MathUtils.obj2DecimalFormat(beginEfficiency5) +
                ", finallyEfficiency5=" + MathUtils.obj2DecimalFormat(finallyEfficiency5) +
                ", efficiencyRange5=" + MathUtils.obj2DecimalFormat(efficiencyRange5) +
                ", powerRange5=" + MathUtils.obj2DecimalFormat(powerRange5) +
                ", difference5=" + MathUtils.obj2DecimalFormat(difference5) +
                ", itemAttribute1='" + this.bleankToNull(itemAttribute1) +
                ", itemAttribute2='" + this.bleankToNull(itemAttribute2) +
                ", itemAttribute3='" + this.bleankToNull(itemAttribute3) +
                ", itemAttribute4='" + this.bleankToNull(itemAttribute4) +
                ", itemAttribute5='" + this.bleankToNull(itemAttribute5) +
                ", itemAttribute6='" + this.bleankToNull(itemAttribute6) +
                ", itemAttribute7='" + this.bleankToNull(itemAttribute7) +
                ", itemAttribute8='" + this.bleankToNull(itemAttribute8) +
                ", itemAttribute9='" + this.bleankToNull(itemAttribute9) +
                ", itemAttribute10='" + this.bleankToNull(itemAttribute10) +
                ", itemAttribute11='" + this.bleankToNull(itemAttribute11) +
                ", itemAttribute12='" + this.bleankToNull(itemAttribute12) +
                ", itemAttribute13='" + this.bleankToNull(itemAttribute13) +
                ", itemAttribute14='" + this.bleankToNull(itemAttribute14) +
                ", itemAttribute15='" + this.bleankToNull(itemAttribute15) +
                ", itemAttribute16='" + this.bleankToNull(itemAttribute16) +
                ", itemAttribute17='" + this.bleankToNull(itemAttribute17) +
                ", itemAttribute18='" + this.bleankToNull(itemAttribute18) +
                ", itemAttribute19='" + this.bleankToNull(itemAttribute19) +
                ", itemAttribute20='" + this.bleankToNull(itemAttribute20) +
                '}';
    }

    public String compatePoStr2() {
        return getDataVersion() + getProductFamily() + getInstallType() + getMonth() + getProjectPlace() +
                getCableLength() + getModuleSize() + getPowerReserve() + getPieceRequirements();
    }

    private String bleankToNull(String str) {
        return StringUtils.isBlank(str) ? null : str;
    }

    public void fillLovName(Map<String, LovLineDTO> lovMap) {
        //产品族
        if (lovMap.get(LovHeaderCodeConstant.FAMILY_CODE + getProductFamily()) != null) {
            setProductFamily(lovMap.get(LovHeaderCodeConstant.FAMILY_CODE + getProductFamily()).getLovName());
        }
        //横竖装
        if (lovMap.get(LovHeaderCodeConstant.CROSS_VERTICAL + getInstallType()) != null) {
            setInstallType(lovMap.get(LovHeaderCodeConstant.CROSS_VERTICAL + getInstallType()).getLovName());
        }
        //版本号
        if (lovMap.get(LovHeaderCodeConstant.DATA_VERSION+getDataVersion()) != null) {
            setDataVersion(lovMap.get(LovHeaderCodeConstant.DATA_VERSION+getDataVersion()).getLovName());
        }
        if (lovMap.get(LovHeaderCodeConstant.COUNTRY+getProjectPlace()) != null) {
            setProjectPlace(lovMap.get(LovHeaderCodeConstant.COUNTRY+getProjectPlace()).getLovName());
        }
    }
}
