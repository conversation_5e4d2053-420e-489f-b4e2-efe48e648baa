package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 电池分类规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerCellRuleDTO对象", description = "DTO对象")
public class PowerCellRuleDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 解析顺序
     */
    @ApiModelProperty(value = "解析顺序")
    private Integer sortNo;
    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;
    /**
     * lovName
     */
    @ApiModelProperty(value = "lovName")
    private String fieldName;
    /**
     * lovId
     */
    @ApiModelProperty(value = "lovId")
    private String fieldId;
    /**
     * lovValue
     */
    @ApiModelProperty(value = "lovValue")
    private String fieldValue;

    /**
     * java 字段
     */
    @ApiModelProperty(value = "java字段")
    private String field;
    /**
     * 规则
     */
    @ApiModelProperty(value = "规则")
    private String rule;
    /**
     * 结果
     */
    @ApiModelProperty(value = "结果")
    private String result;
}
