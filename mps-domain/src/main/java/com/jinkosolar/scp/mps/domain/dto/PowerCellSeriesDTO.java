package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("电池片系列基表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerCellSeriesDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 产品系列代码
     */
    @ApiModelProperty("产品系列代码")
    @ExcelProperty(value = "产品系列代码")
    private String cellSeriesCode;
    /**
     * 电池片系列描述
     */
    @ApiModelProperty("产品系列描述")
    @ExcelProperty(value = "产品系列描述")
    @NotBlank(message = "产品系列描述不能为空", groups = ValidGroups.Insert.class)
    private String cellSeriesDesc;
    /**
     * 晶体类型 LOV：ATTR_TYPE_027_ATTR_1200
     */
    @ApiModelProperty("晶体类型id LOV：ATTR_TYPE_027_ATTR_1200")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1200)
    @NotNull(message = "晶体类型不能为空", groups = ValidGroups.Insert.class)
    private Long crystalTypeId;
    /**
     * 晶体类型 LOV：ATTR_TYPE_027_ATTR_1200
     */
    @ApiModelProperty("晶体类型 LOV：ATTR_TYPE_027_ATTR_1200")
    @ExcelProperty(value = "晶体类型")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1200 + "'")
    private String crystalTypeIdName;
    /**
     * 电池尺寸 LOV：SYS.BATTERY_SIZE
     */
    @ApiModelProperty("电池尺寸id LOV：SYS.BATTERY_SIZE")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_BATTERY_SIZE)
    @NotNull(message = "电池尺寸不能为空", groups = ValidGroups.Insert.class)
    private Long cellSizeId;
    /**
     * 电池尺寸 LOV：SYS.BATTERY_SIZE
     */
    @ApiModelProperty("电池尺寸 LOV：SYS.BATTERY_SIZE")
    @ExcelProperty(value = "电池尺寸")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.SYS_BATTERY_SIZE + "'")
    private String cellSizeIdName;
    /**
     * 主栅数 LOV：SYS.MAIN_GRID
     */
    @ApiModelProperty("主栅数id LOV：SYS.MAIN_GRID")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_MAIN_GRID)
    @NotNull(message = "主栅数不能为空", groups = ValidGroups.Insert.class)
    private Long mainGridId;
    /**
     * 主栅数 LOV：SYS.MAIN_GRID
     */
    @ApiModelProperty("主栅数 LOV：SYS.MAIN_GRID")
    @ExcelProperty(value = "主栅数")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.SYS_MAIN_GRID + "'")
    private String mainGridIdName;
    /**
     * 单双面 LOV：SYS.SIDED
     */
    @ApiModelProperty("单双面id LOV：SYS.SIDED")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_SIDED)
    @NotNull(message = "单双面不能为空", groups = ValidGroups.Insert.class)
    private Long sealTypeId;
    /**
     * 单双面 LOV：SYS.SIDED
     */
    @ApiModelProperty("单双面 LOV：SYS.SIDED")
    @ExcelProperty(value = "单双面")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.SYS_SIDED + "'")
    private String sealTypeIdName;
    /**
     * 归档标识
     */
    @ApiModelProperty("归档标识值")
    @ExcelProperty(value = "归档标识值")
    private String archiveFlag;
    /**
     * 归档标识
     */
    @ApiModelProperty("归档标识")
    @ExcelProperty(value = "归档标识")
    private String archiveFlagName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id", notes = "")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;

    /**
     * 单双玻 SYS.REAR_COVER_TYPE
     */
    @ApiModelProperty(value = "单双玻 SYS.REAR_COVER_TYPE")
    @Dict(headerCode = "SYS.REAR_COVER_TYPE")
    @NotNull(message = "单双玻不能为空", groups = ValidGroups.Insert.class)
    private Long sinDouId;
    /**
     * 单双玻描述
     */
    @ApiModelProperty(value = "单双玻描述")
    @ExcelProperty(value = "单双玻")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'SYS.REAR_COVER_TYPE'")
    private String sinDouIdName;
    /**
     * 电池尺寸
     */
    @ApiModelProperty(value = "电池尺寸")
    @ExcelProperty(value = "电池尺寸")
    private String cellDimension;
    /**
     * 电池面积
     */
    @ApiModelProperty(value = "电池面积")
    @ExcelProperty(value = "电池面积")
    private String cellArea;
    /**
     * 主栅分类
     */
    @ApiModelProperty("主栅分类")
    @ExcelProperty(value = "主栅分类")
    private String gridLineType;
}
