package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 材料变动规则
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:42
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerChangeRuleDTO对象", description = "DTO对象")
public class PowerChangeRuleDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 功率影响属性
     */
    @ApiModelProperty(value = "功率影响属性")
    private String attribute;
    /**
     * 转换前
     */
    @ApiModelProperty(value = "转换前")
    private String oldValue;
    /**
     * 转换后
     */
    @ApiModelProperty(value = "转换后")
    private String newValue;
    /**
     * 转换损失
     */
    @ApiModelProperty(value = "转换损失")
    private BigDecimal loss;
    /**
     * 基准值
     */
    @ApiModelProperty(value = "基准值")
    private String baseValue;
    /**
     * 转换类型：状态转换/步长转换
     */
    @ApiModelProperty(value = "转换类型：状态转换/步长转换")
    private String type;
    /**
     * 业务场景,1:基础档位表（月度）,2:横竖装功率转换损失
     */
    @ApiModelProperty(value = "业务场景,1:基础档位表（月度）,2:横竖装功率转换损失")
    private Integer businessType;
}
