package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("功率落档基表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerDeratingDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")
    @NotNull(message = "功率不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal power;
    /**
     * 落档1
     */
    @ApiModelProperty("落档1")
    @ExcelProperty(value = "落档1")
    @NotBlank(message = "落档1不能为空", groups = ValidGroups.Insert.class)
    private String derating1;
    /**
     * 落档1比率
     */
    @ApiModelProperty("落档1比率")
    @ColumnWidth(16)
    @ExcelProperty(value = "落档1比率")
    @NotBlank(message = "落档1比率不能为空", groups = ValidGroups.Insert.class)
    private String derating1Ratio;
    /**
     * 落档2
     */
    @ApiModelProperty("落档2")
    @ExcelProperty(value = "落档2")
    @NotBlank(message = "落档2不能为空", groups = ValidGroups.Insert.class)
    private String derating2;
    /**
     * 落档2比率
     */
    @ApiModelProperty("落档2比率")
    @ColumnWidth(value = 16)
    @ExcelProperty(value = "落档2比率")
    @NotBlank(message = "落档2比率不能为空", groups = ValidGroups.Insert.class)
    private String derating2Ratio;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ColumnWidth(value = 14)
    @ExcelProperty(value = "版本号")
    private String version;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}
