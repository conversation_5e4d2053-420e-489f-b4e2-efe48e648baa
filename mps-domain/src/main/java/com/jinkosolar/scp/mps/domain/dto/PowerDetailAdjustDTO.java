package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 功率预测明细调整
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:32:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerDetailAdjustDTO对象", description = "DTO对象")
public class PowerDetailAdjustDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 明细ID
     */
    @ApiModelProperty(value = "明细ID")
    private Long detailId;
    /**
     * dpGroupId
     */
    @ApiModelProperty(value = "dpGroupId")
    private Long dpGroupId;
    /**
     * dp id
     */
    @ApiModelProperty(value = "dp id")
    private String dpId;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    private BigDecimal passPercent;
    /**
     * 副产物比例
     */
    @ApiModelProperty(value = "副产物比例")
    private BigDecimal byProductPercent;
    /**
     * 降档比例
     */
    @ApiModelProperty(value = "降档比例")
    private BigDecimal downshiftPercent;
    /**
     * 需求满足
     */
    @ApiModelProperty(value = "需求满足")
    private String demandFlag;
    /**
     * 标记
     */
    @ApiModelProperty(value = "标记")
    private String flag;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;

    /**
     * 是否修改了效率段
     */
    @ApiModelProperty(value = "是否修改了效率段")
    private Integer isUpdVersion;

    /**
     * 是否修改了正态分布
     */
    @ApiModelProperty(value = "是否修改了正态分布")
    private Integer isUpdEfficiencys;

    private List<PowerDetailDTO> powerDetailDTOList;

    /**
     * 是否需要保存
     */
    @ApiModelProperty(value = "是否需要保存")
    private Integer isSave;

    private List<Map<String, String>> efficiencyStructures;

    private Map<String,BigDecimal> efficiencyRange;

    @ApiModelProperty(value = "调整的版本")
    private String adjustVersion;

    /**
     * 效率值1段范围
     */
    @ApiModelProperty(value = "效率值1段范围")
    private String range1;
    /**
     * 效率值1段使用版本
     */
    @ApiModelProperty(value = "效率值1段使用版本")
    private String range1Version;
    /**
     * 效率值2段范围
     */
    @ApiModelProperty(value = "效率值2段范围")
    private String range2;
    /**
     * 效率值2段使用版本
     */
    @ApiModelProperty(value = "效率值2段使用版本")
    private String range2Version;
    /**
     * 效率值3段范围
     */
    @ApiModelProperty(value = "效率值3段范围")
    private String range3;
    /**
     * 效率值4段使用版本
     */
    @ApiModelProperty(value = "效率值3段使用版本")
    private String range3Version;
    /**
     * 效率值4段范围
     */
    @ApiModelProperty(value = "效率值4段范围")
    private String range4;
    /**
     * 效率值4段使用版本
     */
    @ApiModelProperty(value = "效率值4段使用版本")
    private String range4Version;
    /**
     * 效率值3段范围
     */
    @ApiModelProperty(value = "效率值5段范围")
    private String range5;
    /**
     * 效率值4段使用版本
     */
    @ApiModelProperty(value = "效率值5段使用版本")
    private String range5Version;
    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1")
    private BigDecimal efficiency1;

    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency2;

    /**
     * 效率3
     */
    @ApiModelProperty(value = "效率3")
    private BigDecimal efficiency3;

    /**
     * 效率4
     */
    @ApiModelProperty(value = "效率4")
    private BigDecimal efficiency4;

    /**
     * 效率5
     */
    @ApiModelProperty(value = "效率5")
    private BigDecimal efficiency5;

    /**
     * 效率6
     */
    @ApiModelProperty(value = "效率6")
    private BigDecimal efficiency6;

    /**
     * 效率7
     */
    @ApiModelProperty(value = "效率7")
    private BigDecimal efficiency7;

    /**
     * 效率8
     */
    @ApiModelProperty(value = "效率8")
    private BigDecimal efficiency8;

    /**
     * 效率9
     */
    @ApiModelProperty(value = "效率9")
    private BigDecimal efficiency9;

    /**
     * 效率10
     */
    @ApiModelProperty(value = "效率10")
    private BigDecimal efficiency10;

    /**
     * 效率11
     */
    @ApiModelProperty(value = "效率11")
    private BigDecimal efficiency11;

    /**
     * 效率12
     */
    @ApiModelProperty(value = "效率12")
    private BigDecimal efficiency12;

    /**
     * 效率13
     */
    @ApiModelProperty(value = "效率13")
    private BigDecimal efficiency13;

    /**
     * 效率14
     */
    @ApiModelProperty(value = "效率14")
    private BigDecimal efficiency14;

    /**
     * 效率15
     */
    @ApiModelProperty(value = "效率15")
    private BigDecimal efficiency15;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16")
    private BigDecimal efficiency16;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17")
    private BigDecimal efficiency17;
    /**
     * 效率18
     */
    @ApiModelProperty(value = "效率18")
    private BigDecimal efficiency18;
    /**
     * 效率19
     */
    @ApiModelProperty(value = "效率19")
    private BigDecimal efficiency19;
    /**
     * 效率20
     */
    @ApiModelProperty(value = "效率20")
    private BigDecimal efficiency20;
    /**
     * 效率21
     */
    @ApiModelProperty(value = "效率21")
    private BigDecimal efficiency21;
    /**
     * 效率22
     */
    @ApiModelProperty(value = "效率22")
    private BigDecimal efficiency22;
    /**
     * 效率23
     */
    @ApiModelProperty(value = "效率23")
    private BigDecimal efficiency23;
    /**
     * 效率24
     */
    @ApiModelProperty(value = "效率24")
    private BigDecimal efficiency24;
    /**
     * 效率25
     */
    @ApiModelProperty(value = "效率25")
    private BigDecimal efficiency25;
    /**
     * 效率26
     */
    @ApiModelProperty(value = "效率26")
    private BigDecimal efficiency26;
    /**
     * 效率27
     */
    @ApiModelProperty(value = "效率27")
    private BigDecimal efficiency27;
    /**
     * 效率28
     */
    @ApiModelProperty(value = "效率28")
    private BigDecimal efficiency28;
    /**
     * 效率29
     */
    @ApiModelProperty(value = "效率29")
    private BigDecimal efficiency29;
    /**
     * 效率30
     */
    @ApiModelProperty(value = "效率30")
    private BigDecimal efficiency30;


    /**
     * 效率31
     */
    @ApiModelProperty(value = "效率31")
    private BigDecimal efficiency31;
    /**
     * 效率32
     */
    @ApiModelProperty(value = "效率32")
    private BigDecimal efficiency32;
    /**
     * 效率33
     */
    @ApiModelProperty(value = "效率33")
    private BigDecimal efficiency33;
    /**
     * 效率34
     */
    @ApiModelProperty(value = "效率34")
    private BigDecimal efficiency34;
    /**
     * 效率35
     */
    @ApiModelProperty(value = "效率35")
    private BigDecimal efficiency35;
    /**
     * 效率36
     */
    @ApiModelProperty(value = "效率36")
    private BigDecimal efficiency36;
    /**
     * 效率37
     */
    @ApiModelProperty(value = "效率37")
    private BigDecimal efficiency37;
    /**
     * 效率38
     */
    @ApiModelProperty(value = "效率38")
    private BigDecimal efficiency38;
    /**
     * 效率39
     */
    @ApiModelProperty(value = "效率39")
    private BigDecimal efficiency39;
    /**
     * 效率40
     */
    @ApiModelProperty(value = "效率40")
    private BigDecimal efficiency40;


    /**
     * 副标题1
     */
    @ApiModelProperty(value = "副标题1")
    private BigDecimal subTitle1;

    /**
     * 副标题2
     */
    @ApiModelProperty(value = "副标题2")
    private BigDecimal subTitle2;

    /**
     * 副标题3
     */
    @ApiModelProperty(value = "副标题3")
    private BigDecimal subTitle3;

    /**
     * 副标题4
     */
    @ApiModelProperty(value = "副标题4")
    private BigDecimal subTitle4;

    /**
     * 副标题5
     */
    @ApiModelProperty(value = "副标题5")
    private BigDecimal subTitle5;

    /**
     * 副标题6
     */
    @ApiModelProperty(value = "副标题6")
    private BigDecimal subTitle6;

    /**
     * 副标题7
     */
    @ApiModelProperty(value = "副标题7")
    private BigDecimal subTitle7;

    /**
     * 副标题8
     */
    @ApiModelProperty(value = "副标题8")
    private BigDecimal subTitle8;

    /**
     * 副标题9
     */
    @ApiModelProperty(value = "副标题9")
    private BigDecimal subTitle9;

    /**
     * 副标题10
     */
    @ApiModelProperty(value = "副标题10")
    private BigDecimal subTitle10;

    /**
     * 副标题11
     */
    @ApiModelProperty(value = "副标题11")
    private BigDecimal subTitle11;

    /**
     * 副标题12
     */
    @ApiModelProperty(value = "副标题12")
    private BigDecimal subTitle12;

    /**
     * 副标题13
     */
    @ApiModelProperty(value = "副标题13")
    private BigDecimal subTitle13;

    /**
     * 副标题14
     */
    @ApiModelProperty(value = "副标题14")
    private BigDecimal subTitle14;

    /**
     * 副标题15
     */
    @ApiModelProperty(value = "副标题15")
    private BigDecimal subTitle15;

    /**
     * 副标题16
     */
    @ApiModelProperty(value = "副标题16")
    private BigDecimal subTitle16;
    /**
     * 副标题17
     */
    @ApiModelProperty(value = "副标题17")
    private BigDecimal subTitle17;
    /**
     * 副标题18
     */
    @ApiModelProperty(value = "副标题18")
    private BigDecimal subTitle18;
    /**
     * 副标题19
     */
    @ApiModelProperty(value = "副标题19")
    private BigDecimal subTitle19;
    /**
     * 副标题20
     */
    @ApiModelProperty(value = "副标题20")
    private BigDecimal subTitle20;
    /**
     * 副标题21
     */
    @ApiModelProperty(value = "副标题21")
    private BigDecimal subTitle21;
    /**
     * 副标题22
     */
    @ApiModelProperty(value = "副标题22")
    private BigDecimal subTitle22;
    /**
     * 副标题23
     */
    @ApiModelProperty(value = "副标题23")
    private BigDecimal subTitle23;
    /**
     * 副标题24
     */
    @ApiModelProperty(value = "副标题24")
    private BigDecimal subTitle24;
    /**
     * 副标题25
     */
    @ApiModelProperty(value = "副标题25")
    private BigDecimal subTitle25;
    /**
     * 副标题26
     */
    @ApiModelProperty(value = "副标题26")
    private BigDecimal subTitle26;
    /**
     * 副标题27
     */
    @ApiModelProperty(value = "副标题27")
    private BigDecimal subTitle27;
    /**
     * 副标题28
     */
    @ApiModelProperty(value = "副标题28")
    private BigDecimal subTitle28;
    /**
     * 副标题29
     */
    @ApiModelProperty(value = "副标题29")
    private BigDecimal subTitle29;
    /**
     * 副标题30
     */
    @ApiModelProperty(value = "副标题30")
    private BigDecimal subTitle30;

    /**
     * 副标题31
     */
    @ApiModelProperty(value = "副标题31")
    private BigDecimal subTitle31;
    /**
     * 副标题32
     */
    @ApiModelProperty(value = "副标题32")
    private BigDecimal subTitle32;
    /**
     * 副标题33
     */
    @ApiModelProperty(value = "副标题33")
    private BigDecimal subTitle33;
    /**
     * 副标题34
     */
    @ApiModelProperty(value = "副标题34")
    private BigDecimal subTitle34;
    /**
     * 副标题35
     */
    @ApiModelProperty(value = "副标题35")
    private BigDecimal subTitle35;
    /**
     * 副标题36
     */
    @ApiModelProperty(value = "副标题36")
    private BigDecimal subTitle36;
    /**
     * 副标题37
     */
    @ApiModelProperty(value = "副标题37")
    private BigDecimal subTitle37;
    /**
     * 副标题38
     */
    @ApiModelProperty(value = "副标题38")
    private BigDecimal subTitle38;
    /**
     * 副标题39
     */
    @ApiModelProperty(value = "副标题39")
    private BigDecimal subTitle39;
    /**
     * 副标题40
     */
    @ApiModelProperty(value = "副标题40")
    private BigDecimal subTitle40;
}
