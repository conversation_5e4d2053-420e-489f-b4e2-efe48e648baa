package com.jinkosolar.scp.mps.domain.dto;

import com.google.common.collect.Lists;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.tuple.Pair;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 功率预测明细
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:32:44
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
//@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerDetailDTO对象", description = "DTO对象")
public class PowerDetailDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 正态分布主键
     */
    @ApiModelProperty(value = "正态分布主键")
    private Long normalDistributionId;
    /**
     * 步骤主键
     */
    @ApiModelProperty(value = "步骤主键")
    private Long stepId;
    /**
     * dp id
     */
    @ApiModelProperty(value = "dp id")
    private String dpId;

    /**
     * dpGroupId
     */
    @ApiModelProperty(value = "dpGroupId")
    private String dpGroupId;

    /**
     * dpGroupIdList
     */
    @ApiModelProperty(value = "dpGroupIdList")
    private List<Long> dpGroupIdList;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    private String projectPlace;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地名称")
    private String projectPlaceName;

    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String dataVersion;
    /**
     * 整单符合率
     */
    @ApiModelProperty(value = "整单符合率")
    private BigDecimal allPassPercent;
    /**
     * 整单降档
     */
    @ApiModelProperty(value = "整单降档")
    private BigDecimal allDownshiftPercent;
    /**
     * 计划区间
     */
    @ApiModelProperty(value = "计划区间")
    private String planRange;
    /**
     * 排产MW
     */
    @ApiModelProperty(value = "排产MW")
    private BigDecimal schedulingQuantity;
    /**
     * 效率（起始）
     */
    @ApiModelProperty(value = "效率（起始）")
    private BigDecimal beginEfficiency;

    private String beginEfficiencyName;

    /**
     * 效率（终止）
     */
    @ApiModelProperty(value = "效率（终止）")
    private BigDecimal endEfficiency;

    private String endEfficiencyName;
    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    private BigDecimal passPercent;

    private String passPercentName;

    /**
     * 副产物比例
     */
    @ApiModelProperty(value = "副产物比例")
    private BigDecimal byProductPercent;

    private String byProductPercentName;
    /**
     * 降档比例
     */
    @ApiModelProperty(value = "降档比例")
    private BigDecimal downshiftPercent;
    private String downshiftPercentName;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 标记
     */
    @ApiModelProperty(value = "标记")
    private String flag;
    /**
     * 类型:电池正态比例/电池还原比例
     */
    @ApiModelProperty(value = "类型:电池正态比例/电池还原比例")
    private String type;

    private String typeName;

    /**
     * 需求MW
     */
    @ApiModelProperty(value = "需求MW")
    private BigDecimal demandQuantity;

    /**
     * 需求比例
     */
    @ApiModelProperty(value = "需求比例")
    private BigDecimal demandRate;

    /**
     * 汇总
     */
    @ApiModelProperty(value = "汇总")
    private BigDecimal rateSummary;

    /**
     * 需求MW名称
     */
    @ApiModelProperty(value = "需求MW名称")
    private String demandQuantityName;

    /**
     * 需求比例名称
     */
    @ApiModelProperty(value = "需求比例名称")
    private String demandRateName;

    /**
     * 汇总名称
     */
    @ApiModelProperty(value = "汇总名称")
    private String rateSummaryName;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;


    @ApiModelProperty(value = "功率预测数据版本")
    private String powerPredictVersion;

    private List<Map<String, String>> efficiencyStructures;

    private List<Map<String, String>> dataStructures;

    /**
     * EVA克重要求
     */
    @ApiModelProperty(value = "片源匹配")
    private String evaGRequire;

    /**
     * 横竖装名称
     */
    @ApiModelProperty(value = "横竖装名称")
    private String installTypeName;

    /**
     * 版本名称
     */
    @ApiModelProperty(value = "版本名称")
    private String dataVersionName;

    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度")
    private String cableLength;

    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度")
    private String cableLengthName;

    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String moduleSize;
    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String moduleSizeName;

    /**
     * 实际用料
     */
    @ApiModelProperty(value = "实际用料")
    private String item2RealityMaterialName;

    /**
     * 计划排产区间
     */
    @ApiModelProperty(value = "计划排产区间")
    private String plannedCompleteDateInterval;
    /**
     * 排产WM
     */
    @ApiModelProperty(value = "排产WM")
    private String scheduleQty;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String dpChannel;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String dpChannelName;


    /**
     * dp特殊需求单号
     */
    @ApiModelProperty(value = "dp特殊需求单号")
    private String dpSpecialSn;


    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private Long dpCustomerId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String dpCustomerName;


    /**
     * 具体信息
     */
    @ApiModelProperty(value = "具体信息")
    private String detailedInformation;

    /**
     * 是否在BOM中
     */
    @ApiModelProperty(value = "是否在BOM中")
    private String isBom;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1")
    private BigDecimal efficiency1;

    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency2;

    /**
     * 效率3
     */
    @ApiModelProperty(value = "效率3")
    private BigDecimal efficiency3;

    /**
     * 效率4
     */
    @ApiModelProperty(value = "效率4")
    private BigDecimal efficiency4;

    /**
     * 效率5
     */
    @ApiModelProperty(value = "效率5")
    private BigDecimal efficiency5;

    /**
     * 效率6
     */
    @ApiModelProperty(value = "效率6")
    private BigDecimal efficiency6;

    /**
     * 效率7
     */
    @ApiModelProperty(value = "效率7")
    private BigDecimal efficiency7;

    /**
     * 效率8
     */
    @ApiModelProperty(value = "效率8")
    private BigDecimal efficiency8;

    /**
     * 效率9
     */
    @ApiModelProperty(value = "效率9")
    private BigDecimal efficiency9;

    /**
     * 效率10
     */
    @ApiModelProperty(value = "效率10")
    private BigDecimal efficiency10;

    /**
     * 效率11
     */
    @ApiModelProperty(value = "效率11")
    private BigDecimal efficiency11;

    /**
     * 效率12
     */
    @ApiModelProperty(value = "效率12")
    private BigDecimal efficiency12;

    /**
     * 效率13
     */
    @ApiModelProperty(value = "效率13")
    private BigDecimal efficiency13;

    /**
     * 效率14
     */
    @ApiModelProperty(value = "效率14")
    private BigDecimal efficiency14;

    /**
     * 效率15
     */
    @ApiModelProperty(value = "效率15")
    private BigDecimal efficiency15;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16")
    private BigDecimal efficiency16;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17")
    private BigDecimal efficiency17;
    /**
     * 效率18
     */
    @ApiModelProperty(value = "效率18")
    private BigDecimal efficiency18;
    /**
     * 效率19
     */
    @ApiModelProperty(value = "效率19")
    private BigDecimal efficiency19;
    /**
     * 效率20
     */
    @ApiModelProperty(value = "效率20")
    private BigDecimal efficiency20;
    /**
     * 效率21
     */
    @ApiModelProperty(value = "效率21")
    private BigDecimal efficiency21;
    /**
     * 效率22
     */
    @ApiModelProperty(value = "效率22")
    private BigDecimal efficiency22;
    /**
     * 效率23
     */
    @ApiModelProperty(value = "效率23")
    private BigDecimal efficiency23;
    /**
     * 效率24
     */
    @ApiModelProperty(value = "效率24")
    private BigDecimal efficiency24;
    /**
     * 效率25
     */
    @ApiModelProperty(value = "效率25")
    private BigDecimal efficiency25;
    /**
     * 效率26
     */
    @ApiModelProperty(value = "效率26")
    private BigDecimal efficiency26;
    /**
     * 效率27
     */
    @ApiModelProperty(value = "效率27")
    private BigDecimal efficiency27;
    /**
     * 效率28
     */
    @ApiModelProperty(value = "效率28")
    private BigDecimal efficiency28;
    /**
     * 效率29
     */
    @ApiModelProperty(value = "效率29")
    private BigDecimal efficiency29;
    /**
     * 效率30
     */
    @ApiModelProperty(value = "效率30")
    private BigDecimal efficiency30;


    /**
     * 效率31
     */
    @ApiModelProperty(value = "效率31")
    private BigDecimal efficiency31;
    /**
     * 效率32
     */
    @ApiModelProperty(value = "效率32")
    private BigDecimal efficiency32;
    /**
     * 效率33
     */
    @ApiModelProperty(value = "效率33")
    private BigDecimal efficiency33;
    /**
     * 效率34
     */
    @ApiModelProperty(value = "效率34")
    private BigDecimal efficiency34;
    /**
     * 效率35
     */
    @ApiModelProperty(value = "效率35")
    private BigDecimal efficiency35;
    /**
     * 效率36
     */
    @ApiModelProperty(value = "效率36")
    private BigDecimal efficiency36;
    /**
     * 效率37
     */
    @ApiModelProperty(value = "效率37")
    private BigDecimal efficiency37;
    /**
     * 效率38
     */
    @ApiModelProperty(value = "效率38")
    private BigDecimal efficiency38;
    /**
     * 效率39
     */
    @ApiModelProperty(value = "效率39")
    private BigDecimal efficiency39;
    /**
     * 效率40
     */
    @ApiModelProperty(value = "效率40")
    private BigDecimal efficiency40;


    /**
     * 副标题1
     */
    @ApiModelProperty(value = "副标题1")
    private BigDecimal subTitle1;

    /**
     * 副标题2
     */
    @ApiModelProperty(value = "副标题2")
    private BigDecimal subTitle2;

    /**
     * 副标题3
     */
    @ApiModelProperty(value = "副标题3")
    private BigDecimal subTitle3;

    /**
     * 副标题4
     */
    @ApiModelProperty(value = "副标题4")
    private BigDecimal subTitle4;

    /**
     * 副标题5
     */
    @ApiModelProperty(value = "副标题5")
    private BigDecimal subTitle5;

    /**
     * 副标题6
     */
    @ApiModelProperty(value = "副标题6")
    private BigDecimal subTitle6;

    /**
     * 副标题7
     */
    @ApiModelProperty(value = "副标题7")
    private BigDecimal subTitle7;

    /**
     * 副标题8
     */
    @ApiModelProperty(value = "副标题8")
    private BigDecimal subTitle8;

    /**
     * 副标题9
     */
    @ApiModelProperty(value = "副标题9")
    private BigDecimal subTitle9;

    /**
     * 副标题10
     */
    @ApiModelProperty(value = "副标题10")
    private BigDecimal subTitle10;

    /**
     * 副标题11
     */
    @ApiModelProperty(value = "副标题11")
    private BigDecimal subTitle11;

    /**
     * 副标题12
     */
    @ApiModelProperty(value = "副标题12")
    private BigDecimal subTitle12;

    /**
     * 副标题13
     */
    @ApiModelProperty(value = "副标题13")
    private BigDecimal subTitle13;

    /**
     * 副标题14
     */
    @ApiModelProperty(value = "副标题14")
    private BigDecimal subTitle14;

    /**
     * 副标题15
     */
    @ApiModelProperty(value = "副标题15")
    private BigDecimal subTitle15;

    /**
     * 副标题16
     */
    @ApiModelProperty(value = "副标题16")
    private BigDecimal subTitle16;
    /**
     * 副标题17
     */
    @ApiModelProperty(value = "副标题17")
    private BigDecimal subTitle17;
    /**
     * 副标题18
     */
    @ApiModelProperty(value = "副标题18")
    private BigDecimal subTitle18;
    /**
     * 副标题19
     */
    @ApiModelProperty(value = "副标题19")
    private BigDecimal subTitle19;
    /**
     * 副标题20
     */
    @ApiModelProperty(value = "副标题20")
    private BigDecimal subTitle20;
    /**
     * 副标题21
     */
    @ApiModelProperty(value = "副标题21")
    private BigDecimal subTitle21;
    /**
     * 副标题22
     */
    @ApiModelProperty(value = "副标题22")
    private BigDecimal subTitle22;
    /**
     * 副标题23
     */
    @ApiModelProperty(value = "副标题23")
    private BigDecimal subTitle23;
    /**
     * 副标题24
     */
    @ApiModelProperty(value = "副标题24")
    private BigDecimal subTitle24;
    /**
     * 副标题25
     */
    @ApiModelProperty(value = "副标题25")
    private BigDecimal subTitle25;
    /**
     * 副标题26
     */
    @ApiModelProperty(value = "副标题26")
    private BigDecimal subTitle26;
    /**
     * 副标题27
     */
    @ApiModelProperty(value = "副标题27")
    private BigDecimal subTitle27;
    /**
     * 副标题28
     */
    @ApiModelProperty(value = "副标题28")
    private BigDecimal subTitle28;
    /**
     * 副标题29
     */
    @ApiModelProperty(value = "副标题29")
    private BigDecimal subTitle29;
    /**
     * 副标题30
     */
    @ApiModelProperty(value = "副标题30")
    private BigDecimal subTitle30;

    /**
     * 副标题31
     */
    @ApiModelProperty(value = "副标题31")
    private BigDecimal subTitle31;
    /**
     * 副标题32
     */
    @ApiModelProperty(value = "副标题32")
    private BigDecimal subTitle32;
    /**
     * 副标题33
     */
    @ApiModelProperty(value = "副标题33")
    private BigDecimal subTitle33;
    /**
     * 副标题34
     */
    @ApiModelProperty(value = "副标题34")
    private BigDecimal subTitle34;
    /**
     * 副标题35
     */
    @ApiModelProperty(value = "副标题35")
    private BigDecimal subTitle35;
    /**
     * 副标题36
     */
    @ApiModelProperty(value = "副标题36")
    private BigDecimal subTitle36;
    /**
     * 副标题37
     */
    @ApiModelProperty(value = "副标题37")
    private BigDecimal subTitle37;
    /**
     * 副标题38
     */
    @ApiModelProperty(value = "副标题38")
    private BigDecimal subTitle38;
    /**
     * 副标题39
     */
    @ApiModelProperty(value = "副标题39")
    private BigDecimal subTitle39;
    /**
     * 副标题40
     */
    @ApiModelProperty(value = "副标题40")
    private BigDecimal subTitle40;

    private BigDecimal 	subTitle41;
    private BigDecimal 	subTitle42;
    private BigDecimal 	subTitle43;
    private BigDecimal 	subTitle44;
    private BigDecimal 	subTitle45;
    private BigDecimal 	subTitle46;
    private BigDecimal 	subTitle47;
    private BigDecimal 	subTitle48;
    private BigDecimal 	subTitle49;
    private BigDecimal 	subTitle50;
    private BigDecimal 	subTitle51;
    private BigDecimal 	subTitle52;
    private BigDecimal 	subTitle53;
    private BigDecimal 	subTitle54;
    private BigDecimal 	subTitle55;
    private BigDecimal 	subTitle56;
    private BigDecimal 	subTitle57;
    private BigDecimal 	subTitle58;
    private BigDecimal 	subTitle59;
    private BigDecimal 	subTitle60;
    private BigDecimal 	subTitle61;
    private BigDecimal 	subTitle62;
    private BigDecimal 	subTitle63;
    private BigDecimal 	subTitle64;
    private BigDecimal 	subTitle65;
    private BigDecimal 	subTitle66;
    private BigDecimal 	subTitle67;
    private BigDecimal 	subTitle68;
    private BigDecimal 	subTitle69;
    private BigDecimal 	subTitle70;
    private BigDecimal 	subTitle71;
    private BigDecimal 	subTitle72;
    private BigDecimal 	subTitle73;
    private BigDecimal 	subTitle74;
    private BigDecimal 	subTitle75;
    private BigDecimal 	subTitle76;
    private BigDecimal 	subTitle77;
    private BigDecimal 	subTitle78;
    private BigDecimal 	subTitle79;
    private BigDecimal 	subTitle80;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1名称")
    private String efficiency1Name;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2名称")
    private String efficiency2Name;
    /**
     * 效率3名称
     */
    @ApiModelProperty(value = "效率3名称")
    private String efficiency3Name;
    /**
     * 效率4名称
     */
    @ApiModelProperty(value = "效率4名称")
    private String efficiency4Name;
    /**
     * 效率5名称
     */
    @ApiModelProperty(value = "效率5名称")
    private String efficiency5Name;
    /**
     * 效率6名称
     */
    @ApiModelProperty(value = "效率6名称")
    private String efficiency6Name;
    /**
     * 效率7名称
     */
    @ApiModelProperty(value = "效率7名称")
    private String efficiency7Name;
    /**
     * 效率8名称
     */
    @ApiModelProperty(value = "效率8名称")
    private String efficiency8Name;
    /**
     * 效率9名称
     */
    @ApiModelProperty(value = "效率9名称")
    private String efficiency9Name;
    /**
     * 效率10名称
     */
    @ApiModelProperty(value = "效率10名称")
    private String efficiency10Name;
    /**
     * 效率11名称
     */
    @ApiModelProperty(value = "效率11名称")
    private String efficiency11Name;
    /**
     * 效率12名称
     */
    @ApiModelProperty(value = "效率12名称")
    private String efficiency12Name;
    /**
     * 效率13名称
     */
    @ApiModelProperty(value = "效率13名称")
    private String efficiency13Name;
    /**
     * 效率14名称
     */
    @ApiModelProperty(value = "效率14名称")
    private String efficiency14Name;
    /**
     * 效率15名称
     */
    @ApiModelProperty(value = "效率15名称")
    private String efficiency15Name;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16名称")
    private String efficiency16Name;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17名称")
    private String efficiency17Name;
    /**
     * 效率18名称
     */
    @ApiModelProperty(value = "效率18名称")
    private String efficiency18Name;
    /**
     * 效率19名称
     */
    @ApiModelProperty(value = "效率19名称")
    private String efficiency19Name;
    /**
     * 效率20名称
     */
    @ApiModelProperty(value = "效率20名称")
    private String efficiency20Name;
    /**
     * 效率21名称
     */
    @ApiModelProperty(value = "效率21名称")
    private String efficiency21Name;
    /**
     * 效率22名称
     */
    @ApiModelProperty(value = "效率22名称")
    private String efficiency22Name;
    /**
     * 效率23名称
     */
    @ApiModelProperty(value = "效率23名称")
    private String efficiency23Name;
    /**
     * 效率24名称
     */
    @ApiModelProperty(value = "效率24名称")
    private String efficiency24Name;
    /**
     * 效率25名称
     */
    @ApiModelProperty(value = "效率25名称")
    private String efficiency25Name;
    /**
     * 效率26名称
     */
    @ApiModelProperty(value = "效率26名称")
    private String efficiency26Name;
    /**
     * 效率27名称
     */
    @ApiModelProperty(value = "效率27名称")
    private String efficiency27Name;
    /**
     * 效率28名称
     */
    @ApiModelProperty(value = "效率28名称")
    private String efficiency28Name;
    /**
     * 效率29名称
     */
    @ApiModelProperty(value = "效率29名称")
    private String efficiency29Name;
    /**
     * 效率30名称
     */
    @ApiModelProperty(value = "效率30名称")
    private String efficiency30Name;

    /**
     * 效率31名称
     */
    @ApiModelProperty(value = "效率31名称")
    private String efficiency31Name;
    /**
     * 效率32名称
     */
    @ApiModelProperty(value = "效率32名称")
    private String efficiency32Name;
    /**
     * 效率33名称
     */
    @ApiModelProperty(value = "效率33名称")
    private String efficiency33Name;
    /**
     * 效率34名称
     */
    @ApiModelProperty(value = "效率34名称")
    private String efficiency34Name;
    /**
     * 效率35名称
     */
    @ApiModelProperty(value = "效率35名称")
    private String efficiency35Name;
    /**
     * 效率36名称
     */
    @ApiModelProperty(value = "效率36名称")
    private String efficiency36Name;
    /**
     * 效率37名称
     */
    @ApiModelProperty(value = "效率37名称")
    private String efficiency37Name;
    /**
     * 效率38名称
     */
    @ApiModelProperty(value = "效率38名称")
    private String efficiency38Name;
    /**
     * 效率39名称
     */
    @ApiModelProperty(value = "效率39名称")
    private String efficiency39Name;
    /**
     * 效率40名称
     */
    @ApiModelProperty(value = "效率40名称")
    private String efficiency40Name;


    private String 	efficiency41Name;
    private String 	efficiency42Name;
    private String 	efficiency43Name;
    private String 	efficiency44Name;
    private String 	efficiency45Name;
    private String 	efficiency46Name;
    private String 	efficiency47Name;
    private String 	efficiency48Name;
    private String 	efficiency49Name;
    private String 	efficiency50Name;
    private String 	efficiency51Name;
    private String 	efficiency52Name;
    private String 	efficiency53Name;
    private String 	efficiency54Name;
    private String 	efficiency55Name;
    private String 	efficiency56Name;
    private String 	efficiency57Name;
    private String 	efficiency58Name;
    private String 	efficiency59Name;
    private String 	efficiency60Name;
    private String 	efficiency61Name;
    private String 	efficiency62Name;
    private String 	efficiency63Name;
    private String 	efficiency64Name;
    private String 	efficiency65Name;
    private String 	efficiency66Name;
    private String 	efficiency67Name;
    private String 	efficiency68Name;
    private String 	efficiency69Name;
    private String 	efficiency70Name;
    private String 	efficiency71Name;
    private String 	efficiency72Name;
    private String 	efficiency73Name;
    private String 	efficiency74Name;
    private String 	efficiency75Name;
    private String 	efficiency76Name;
    private String 	efficiency77Name;
    private String 	efficiency78Name;
    private String 	efficiency79Name;
    private String 	efficiency80Name;

    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20;

    @ApiModelProperty(value = "预留21")
    private String itemAttribute21;

    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1Name;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2Name;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3Name;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4Name;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5Name;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6Name;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7Name;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8Name;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9Name;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10Name;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11Name;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12Name;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13Name;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14Name;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15Name;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16Name;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17Name;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18Name;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19Name;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20Name;

    /**
     * 预留21
     */
    @ApiModelProperty(value = "预留21")
    private String itemAttribute21Name;


    public List<Pair<BigDecimal, BigDecimal>> titleAndEfficiencyList() {
        return Lists.newArrayList(Pair.of(getSubTitle1(), getEfficiency1()), Pair.of(getSubTitle2(), getEfficiency2()),
                        Pair.of(getSubTitle3(), getEfficiency3()), Pair.of(getSubTitle4(), getEfficiency4()),
                        Pair.of(getSubTitle5(), getEfficiency5()), Pair.of(getSubTitle6(), getEfficiency6()),
                        Pair.of(getSubTitle7(), getEfficiency7()), Pair.of(getSubTitle8(), getEfficiency8()),
                        Pair.of(getSubTitle9(), getEfficiency9()), Pair.of(getSubTitle10(), getEfficiency10()),
                        Pair.of(getSubTitle11(), getEfficiency11()), Pair.of(getSubTitle12(), getEfficiency12()),
                        Pair.of(getSubTitle13(), getEfficiency13()), Pair.of(getSubTitle14(), getEfficiency14()),
                        Pair.of(getSubTitle15(), getEfficiency15())
                        , Pair.of(getSubTitle16(), getEfficiency16())
                        , Pair.of(getSubTitle17(), getEfficiency17())
                        , Pair.of(getSubTitle18(), getEfficiency18())
                        , Pair.of(getSubTitle19(), getEfficiency19())
                        , Pair.of(getSubTitle20(), getEfficiency20())
                        , Pair.of(getSubTitle21(), getEfficiency21())
                        , Pair.of(getSubTitle22(), getEfficiency22())
                        , Pair.of(getSubTitle23(), getEfficiency23())
                        , Pair.of(getSubTitle24(), getEfficiency24())
                        , Pair.of(getSubTitle25(), getEfficiency25())
                        , Pair.of(getSubTitle26(), getEfficiency26())
                        , Pair.of(getSubTitle27(), getEfficiency27())
                        , Pair.of(getSubTitle28(), getEfficiency28())
                        , Pair.of(getSubTitle29(), getEfficiency29())
                        , Pair.of(getSubTitle30(), getEfficiency30())
                        , Pair.of(getSubTitle31(), getEfficiency31())
                        , Pair.of(getSubTitle32(), getEfficiency32())
                        , Pair.of(getSubTitle33(), getEfficiency33())
                        , Pair.of(getSubTitle34(), getEfficiency34())
                        , Pair.of(getSubTitle35(), getEfficiency35())
                        , Pair.of(getSubTitle36(), getEfficiency36())
                        , Pair.of(getSubTitle37(), getEfficiency37())
                        , Pair.of(getSubTitle38(), getEfficiency38())
                        , Pair.of(getSubTitle39(), getEfficiency39())
                        , Pair.of(getSubTitle40(), getEfficiency40())).stream()
                .filter(p -> Objects.nonNull(p.getLeft()) && Objects.nonNull(p.getRight()) && p.getRight().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
    }

}
