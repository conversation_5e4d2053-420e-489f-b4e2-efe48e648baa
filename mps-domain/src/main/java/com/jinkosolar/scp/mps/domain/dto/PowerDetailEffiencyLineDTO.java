package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2022-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerDetailEffiencyLineDTO对象", description = "DTO对象")
public class PowerDetailEffiencyLineDTO {
    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private Long id ;

    /**
    *月份
    */
    @ApiModelProperty(value = "月份")
    private String month ;

    /**
    *车间
    */
    @ApiModelProperty(value = "车间")
    private String workshop ;

    /**
    *dp_id
    */
    @ApiModelProperty(value = "dp_id")
    private String dpId ;

    /**
    *订单名称
    */
    @ApiModelProperty(value = "订单名称")
    private String orderName ;

    /**
    *产品族
    */
    @ApiModelProperty(value = "产品族")
    private String productFamily ;

    /**
    *标题
    */
    @ApiModelProperty(value = "标题")
    private String subTitle ;

    /**
    *效率值
    */
    @ApiModelProperty(value = "效率值")
    private BigDecimal effiency ;

    /**
     * 效率动态列
     */
    private Map<String, String> efficiencyStructures;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1")
    private BigDecimal efficiency1;

    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency2;

    /**
     * 效率3
     */
    @ApiModelProperty(value = "效率3")
    private BigDecimal efficiency3;

    /**
     * 效率4
     */
    @ApiModelProperty(value = "效率4")
    private BigDecimal efficiency4;

    /**
     * 效率5
     */
    @ApiModelProperty(value = "效率5")
    private BigDecimal efficiency5;

    /**
     * 效率6
     */
    @ApiModelProperty(value = "效率6")
    private BigDecimal efficiency6;

    /**
     * 效率7
     */
    @ApiModelProperty(value = "效率7")
    private BigDecimal efficiency7;

    /**
     * 效率8
     */
    @ApiModelProperty(value = "效率8")
    private BigDecimal efficiency8;

    /**
     * 效率9
     */
    @ApiModelProperty(value = "效率9")
    private BigDecimal efficiency9;

    /**
     * 效率10
     */
    @ApiModelProperty(value = "效率10")
    private BigDecimal efficiency10;

    /**
     * 效率11
     */
    @ApiModelProperty(value = "效率11")
    private BigDecimal efficiency11;

    /**
     * 效率12
     */
    @ApiModelProperty(value = "效率12")
    private BigDecimal efficiency12;

    /**
     * 效率13
     */
    @ApiModelProperty(value = "效率13")
    private BigDecimal efficiency13;

    /**
     * 效率14
     */
    @ApiModelProperty(value = "效率14")
    private BigDecimal efficiency14;

    /**
     * 效率15
     */
    @ApiModelProperty(value = "效率15")
    private BigDecimal efficiency15;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16")
    private BigDecimal efficiency16;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17")
    private BigDecimal efficiency17;
    /**
     * 效率18
     */
    @ApiModelProperty(value = "效率18")
    private BigDecimal efficiency18;
    /**
     * 效率19
     */
    @ApiModelProperty(value = "效率19")
    private BigDecimal efficiency19;
    /**
     * 效率20
     */
    @ApiModelProperty(value = "效率20")
    private BigDecimal efficiency20;
    /**
     * 效率21
     */
    @ApiModelProperty(value = "效率21")
    private BigDecimal efficiency21;
    /**
     * 效率22
     */
    @ApiModelProperty(value = "效率22")
    private BigDecimal efficiency22;
    /**
     * 效率23
     */
    @ApiModelProperty(value = "效率23")
    private BigDecimal efficiency23;
    /**
     * 效率24
     */
    @ApiModelProperty(value = "效率24")
    private BigDecimal efficiency24;
    /**
     * 效率25
     */
    @ApiModelProperty(value = "效率25")
    private BigDecimal efficiency25;
    /**
     * 效率26
     */
    @ApiModelProperty(value = "效率26")
    private BigDecimal efficiency26;
    /**
     * 效率27
     */
    @ApiModelProperty(value = "效率27")
    private BigDecimal efficiency27;
    /**
     * 效率28
     */
    @ApiModelProperty(value = "效率28")
    private BigDecimal efficiency28;
    /**
     * 效率29
     */
    @ApiModelProperty(value = "效率29")
    private BigDecimal efficiency29;
    /**
     * 效率30
     */
    @ApiModelProperty(value = "效率30")
    private BigDecimal efficiency30;


    /**
     * 效率31
     */
    @ApiModelProperty(value = "效率31")
    private BigDecimal efficiency31;
    /**
     * 效率32
     */
    @ApiModelProperty(value = "效率32")
    private BigDecimal efficiency32;
    /**
     * 效率33
     */
    @ApiModelProperty(value = "效率33")
    private BigDecimal efficiency33;
    /**
     * 效率34
     */
    @ApiModelProperty(value = "效率34")
    private BigDecimal efficiency34;
    /**
     * 效率35
     */
    @ApiModelProperty(value = "效率35")
    private BigDecimal efficiency35;
    /**
     * 效率36
     */
    @ApiModelProperty(value = "效率36")
    private BigDecimal efficiency36;
    /**
     * 效率37
     */
    @ApiModelProperty(value = "效率37")
    private BigDecimal efficiency37;
    /**
     * 效率38
     */
    @ApiModelProperty(value = "效率38")
    private BigDecimal efficiency38;
    /**
     * 效率39
     */
    @ApiModelProperty(value = "效率39")
    private BigDecimal efficiency39;
    /**
     * 效率40
     */
    @ApiModelProperty(value = "效率40")
    private BigDecimal efficiency40;

    /**
     * 效率41
     */
    @ApiModelProperty(value = "效率41")
    private BigDecimal efficiency41;

    /**
     * 效率42
     */
    @ApiModelProperty(value = "效率42")
    private BigDecimal efficiency42;

    /**
     * 效率43
     */
    @ApiModelProperty(value = "效率43")
    private BigDecimal efficiency43;

    /**
     * 效率44
     */
    @ApiModelProperty(value = "效率44")
    private BigDecimal efficiency44;

    /**
     * 效率45
     */
    @ApiModelProperty(value = "效率45")
    private BigDecimal efficiency45;

    /**
     * 效率46
     */
    @ApiModelProperty(value = "效率46")
    private BigDecimal efficiency46;

    /**
     * 效率47
     */
    @ApiModelProperty(value = "效率47")
    private BigDecimal efficiency47;

    /**
     * 效率48
     */
    @ApiModelProperty(value = "效率48")
    private BigDecimal efficiency48;

    /**
     * 效率49
     */
    @ApiModelProperty(value = "效率49")
    private BigDecimal efficiency49;

    /**
     * 效率50
     */
    @ApiModelProperty(value = "效率50")
    private BigDecimal efficiency50;

    @ApiModelProperty(value = "效率51")
    private BigDecimal efficiency51;
    @ApiModelProperty(value = "效率52")
    private BigDecimal efficiency52;
    @ApiModelProperty(value = "效率53")
    private BigDecimal efficiency53;
    @ApiModelProperty(value = "效率54")
    private BigDecimal efficiency54;
    @ApiModelProperty(value = "效率55")
    private BigDecimal efficiency55;
    @ApiModelProperty(value = "效率56")
    private BigDecimal efficiency56;
    @ApiModelProperty(value = "效率57")
    private BigDecimal efficiency57;
    @ApiModelProperty(value = "效率58")
    private BigDecimal efficiency58;
    @ApiModelProperty(value = "效率59")
    private BigDecimal efficiency59;
    @ApiModelProperty(value = "效率60")
    private BigDecimal efficiency60;

    /**
     * 副标题1
     */
    @ApiModelProperty(value = "副标题1")
    private BigDecimal subTitle1;

    /**
     * 副标题2
     */
    @ApiModelProperty(value = "副标题2")
    private BigDecimal subTitle2;

    /**
     * 副标题3
     */
    @ApiModelProperty(value = "副标题3")
    private BigDecimal subTitle3;

    /**
     * 副标题4
     */
    @ApiModelProperty(value = "副标题4")
    private BigDecimal subTitle4;

    /**
     * 副标题5
     */
    @ApiModelProperty(value = "副标题5")
    private BigDecimal subTitle5;

    /**
     * 副标题6
     */
    @ApiModelProperty(value = "副标题6")
    private BigDecimal subTitle6;

    /**
     * 副标题7
     */
    @ApiModelProperty(value = "副标题7")
    private BigDecimal subTitle7;

    /**
     * 副标题8
     */
    @ApiModelProperty(value = "副标题8")
    private BigDecimal subTitle8;

    /**
     * 副标题9
     */
    @ApiModelProperty(value = "副标题9")
    private BigDecimal subTitle9;

    /**
     * 副标题10
     */
    @ApiModelProperty(value = "副标题10")
    private BigDecimal subTitle10;

    /**
     * 副标题11
     */
    @ApiModelProperty(value = "副标题11")
    private BigDecimal subTitle11;

    /**
     * 副标题12
     */
    @ApiModelProperty(value = "副标题12")
    private BigDecimal subTitle12;

    /**
     * 副标题13
     */
    @ApiModelProperty(value = "副标题13")
    private BigDecimal subTitle13;

    /**
     * 副标题14
     */
    @ApiModelProperty(value = "副标题14")
    private BigDecimal subTitle14;

    /**
     * 副标题15
     */
    @ApiModelProperty(value = "副标题15")
    private BigDecimal subTitle15;

    /**
     * 副标题16
     */
    @ApiModelProperty(value = "副标题16")
    private BigDecimal subTitle16;
    /**
     * 副标题17
     */
    @ApiModelProperty(value = "副标题17")
    private BigDecimal subTitle17;
    /**
     * 副标题18
     */
    @ApiModelProperty(value = "副标题18")
    private BigDecimal subTitle18;
    /**
     * 副标题19
     */
    @ApiModelProperty(value = "副标题19")
    private BigDecimal subTitle19;
    /**
     * 副标题20
     */
    @ApiModelProperty(value = "副标题20")
    private BigDecimal subTitle20;
    /**
     * 副标题21
     */
    @ApiModelProperty(value = "副标题21")
    private BigDecimal subTitle21;
    /**
     * 副标题22
     */
    @ApiModelProperty(value = "副标题22")
    private BigDecimal subTitle22;
    /**
     * 副标题23
     */
    @ApiModelProperty(value = "副标题23")
    private BigDecimal subTitle23;
    /**
     * 副标题24
     */
    @ApiModelProperty(value = "副标题24")
    private BigDecimal subTitle24;
    /**
     * 副标题25
     */
    @ApiModelProperty(value = "副标题25")
    private BigDecimal subTitle25;
    /**
     * 副标题26
     */
    @ApiModelProperty(value = "副标题26")
    private BigDecimal subTitle26;
    /**
     * 副标题27
     */
    @ApiModelProperty(value = "副标题27")
    private BigDecimal subTitle27;
    /**
     * 副标题28
     */
    @ApiModelProperty(value = "副标题28")
    private BigDecimal subTitle28;
    /**
     * 副标题29
     */
    @ApiModelProperty(value = "副标题29")
    private BigDecimal subTitle29;
    /**
     * 副标题30
     */
    @ApiModelProperty(value = "副标题30")
    private BigDecimal subTitle30;

    /**
     * 副标题31
     */
    @ApiModelProperty(value = "副标题31")
    private BigDecimal subTitle31;
    /**
     * 副标题32
     */
    @ApiModelProperty(value = "副标题32")
    private BigDecimal subTitle32;
    /**
     * 副标题33
     */
    @ApiModelProperty(value = "副标题33")
    private BigDecimal subTitle33;
    /**
     * 副标题34
     */
    @ApiModelProperty(value = "副标题34")
    private BigDecimal subTitle34;
    /**
     * 副标题35
     */
    @ApiModelProperty(value = "副标题35")
    private BigDecimal subTitle35;
    /**
     * 副标题36
     */
    @ApiModelProperty(value = "副标题36")
    private BigDecimal subTitle36;
    /**
     * 副标题37
     */
    @ApiModelProperty(value = "副标题37")
    private BigDecimal subTitle37;
    /**
     * 副标题38
     */
    @ApiModelProperty(value = "副标题38")
    private BigDecimal subTitle38;
    /**
     * 副标题39
     */
    @ApiModelProperty(value = "副标题39")
    private BigDecimal subTitle39;
    /**
     * 副标题40
     */
    @ApiModelProperty(value = "副标题40")
    private BigDecimal subTitle40;

    private BigDecimal 	subTitle41;
    private BigDecimal 	subTitle42;
    private BigDecimal 	subTitle43;
    private BigDecimal 	subTitle44;
    private BigDecimal 	subTitle45;
    private BigDecimal 	subTitle46;
    private BigDecimal 	subTitle47;
    private BigDecimal 	subTitle48;
    private BigDecimal 	subTitle49;
    private BigDecimal 	subTitle50;
    private BigDecimal 	subTitle51;
    private BigDecimal 	subTitle52;
    private BigDecimal 	subTitle53;
    private BigDecimal 	subTitle54;
    private BigDecimal 	subTitle55;
    private BigDecimal 	subTitle56;
    private BigDecimal 	subTitle57;
    private BigDecimal 	subTitle58;
    private BigDecimal 	subTitle59;
    private BigDecimal 	subTitle60;
    private BigDecimal 	subTitle61;
    private BigDecimal 	subTitle62;
    private BigDecimal 	subTitle63;
    private BigDecimal 	subTitle64;
    private BigDecimal 	subTitle65;
    private BigDecimal 	subTitle66;
    private BigDecimal 	subTitle67;
    private BigDecimal 	subTitle68;
    private BigDecimal 	subTitle69;
    private BigDecimal 	subTitle70;
    private BigDecimal 	subTitle71;
    private BigDecimal 	subTitle72;
    private BigDecimal 	subTitle73;
    private BigDecimal 	subTitle74;
    private BigDecimal 	subTitle75;
    private BigDecimal 	subTitle76;
    private BigDecimal 	subTitle77;
    private BigDecimal 	subTitle78;
    private BigDecimal 	subTitle79;
    private BigDecimal 	subTitle80;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1名称")
    private String efficiency1Name;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2名称")
    private String efficiency2Name;
    /**
     * 效率3名称
     */
    @ApiModelProperty(value = "效率3名称")
    private String efficiency3Name;
    /**
     * 效率4名称
     */
    @ApiModelProperty(value = "效率4名称")
    private String efficiency4Name;
    /**
     * 效率5名称
     */
    @ApiModelProperty(value = "效率5名称")
    private String efficiency5Name;
    /**
     * 效率6名称
     */
    @ApiModelProperty(value = "效率6名称")
    private String efficiency6Name;
    /**
     * 效率7名称
     */
    @ApiModelProperty(value = "效率7名称")
    private String efficiency7Name;
    /**
     * 效率8名称
     */
    @ApiModelProperty(value = "效率8名称")
    private String efficiency8Name;
    /**
     * 效率9名称
     */
    @ApiModelProperty(value = "效率9名称")
    private String efficiency9Name;
    /**
     * 效率10名称
     */
    @ApiModelProperty(value = "效率10名称")
    private String efficiency10Name;
    /**
     * 效率11名称
     */
    @ApiModelProperty(value = "效率11名称")
    private String efficiency11Name;
    /**
     * 效率12名称
     */
    @ApiModelProperty(value = "效率12名称")
    private String efficiency12Name;
    /**
     * 效率13名称
     */
    @ApiModelProperty(value = "效率13名称")
    private String efficiency13Name;
    /**
     * 效率14名称
     */
    @ApiModelProperty(value = "效率14名称")
    private String efficiency14Name;
    /**
     * 效率15名称
     */
    @ApiModelProperty(value = "效率15名称")
    private String efficiency15Name;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16名称")
    private String efficiency16Name;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17名称")
    private String efficiency17Name;
    /**
     * 效率18名称
     */
    @ApiModelProperty(value = "效率18名称")
    private String efficiency18Name;
    /**
     * 效率19名称
     */
    @ApiModelProperty(value = "效率19名称")
    private String efficiency19Name;
    /**
     * 效率20名称
     */
    @ApiModelProperty(value = "效率20名称")
    private String efficiency20Name;
    /**
     * 效率21名称
     */
    @ApiModelProperty(value = "效率21名称")
    private String efficiency21Name;
    /**
     * 效率22名称
     */
    @ApiModelProperty(value = "效率22名称")
    private String efficiency22Name;
    /**
     * 效率23名称
     */
    @ApiModelProperty(value = "效率23名称")
    private String efficiency23Name;
    /**
     * 效率24名称
     */
    @ApiModelProperty(value = "效率24名称")
    private String efficiency24Name;
    /**
     * 效率25名称
     */
    @ApiModelProperty(value = "效率25名称")
    private String efficiency25Name;
    /**
     * 效率26名称
     */
    @ApiModelProperty(value = "效率26名称")
    private String efficiency26Name;
    /**
     * 效率27名称
     */
    @ApiModelProperty(value = "效率27名称")
    private String efficiency27Name;
    /**
     * 效率28名称
     */
    @ApiModelProperty(value = "效率28名称")
    private String efficiency28Name;
    /**
     * 效率29名称
     */
    @ApiModelProperty(value = "效率29名称")
    private String efficiency29Name;
    /**
     * 效率30名称
     */
    @ApiModelProperty(value = "效率30名称")
    private String efficiency30Name;

    /**
     * 效率31名称
     */
    @ApiModelProperty(value = "效率31名称")
    private String efficiency31Name;
    /**
     * 效率32名称
     */
    @ApiModelProperty(value = "效率32名称")
    private String efficiency32Name;
    /**
     * 效率33名称
     */
    @ApiModelProperty(value = "效率33名称")
    private String efficiency33Name;
    /**
     * 效率34名称
     */
    @ApiModelProperty(value = "效率34名称")
    private String efficiency34Name;
    /**
     * 效率35名称
     */
    @ApiModelProperty(value = "效率35名称")
    private String efficiency35Name;
    /**
     * 效率36名称
     */
    @ApiModelProperty(value = "效率36名称")
    private String efficiency36Name;
    /**
     * 效率37名称
     */
    @ApiModelProperty(value = "效率37名称")
    private String efficiency37Name;
    /**
     * 效率38名称
     */
    @ApiModelProperty(value = "效率38名称")
    private String efficiency38Name;
    /**
     * 效率39名称
     */
    @ApiModelProperty(value = "效率39名称")
    private String efficiency39Name;
    /**
     * 效率40名称
     */
    @ApiModelProperty(value = "效率40名称")
    private String efficiency40Name;


    private String 	efficiency41Name;
    private String 	efficiency42Name;
    private String 	efficiency43Name;
    private String 	efficiency44Name;
    private String 	efficiency45Name;
    private String 	efficiency46Name;
    private String 	efficiency47Name;
    private String 	efficiency48Name;
    private String 	efficiency49Name;
    private String 	efficiency50Name;
    private String 	efficiency51Name;
    private String 	efficiency52Name;
    private String 	efficiency53Name;
    private String 	efficiency54Name;
    private String 	efficiency55Name;
    private String 	efficiency56Name;
    private String 	efficiency57Name;
    private String 	efficiency58Name;
    private String 	efficiency59Name;
    private String 	efficiency60Name;


}
