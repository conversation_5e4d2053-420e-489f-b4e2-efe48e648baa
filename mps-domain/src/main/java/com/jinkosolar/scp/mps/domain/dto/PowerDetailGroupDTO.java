package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerDetailGroupDTO对象", description = "DTO对象")
public class PowerDetailGroupDTO {

    private Long id;

    @ApiModelProperty(value = "功率预测批量编码")
    private String groupCode;

    @ApiModelProperty(value = "业务错误信息")
    private String businessErrMsg;

    @ApiModelProperty(value = "程序错误信息")
    private String errMsg;

    @ApiModelProperty(value = "预测的月份")
    private String predictMonth;

    @ApiModelProperty(value = "预测状态")
    private Integer status;

    private String statusName;

    @ApiModelProperty(value = "预测开始时间")
    private LocalDateTime predictTime;

}
