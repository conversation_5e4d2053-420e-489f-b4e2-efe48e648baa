package com.jinkosolar.scp.mps.domain.dto;


import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 *
 */
@Data
@ApiModel(value = "PowerDetailGroup查询条件", description = "查询条件")
@Accessors(chain = true)
public class PowerDetailGroupQuery extends PageDTO implements Serializable {
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
