package com.jinkosolar.scp.mps.domain.dto;


import com.ibm.scp.common.api.util.BizException;
import com.ibm.scp.common.api.util.ExcelPara;
import com.jinkosolar.scp.mps.domain.entity.PowerDetail;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public abstract class PowerDetailImportAbstract<T> {

    protected abstract List<T> readData(MultipartFile multipartFile, ExcelPara excelPara);

    protected abstract String validData(List<T> list);

    protected abstract void delOldData(List<T> list);

    protected abstract void saveData(List<T> list);

    public void importData(MultipartFile multipartFile, ExcelPara excelPara) {
        List<T> list = readData(multipartFile,excelPara);
        String errMsg = validData(list);
        if(StringUtils.isNotBlank(errMsg)){
            throw new BizException(errMsg);
        }
        delOldData(list);
        saveData(list);
    }

    public Integer mergeSort(){
        return 100;
    }

    public abstract List<PowerDetail> mergeDetailDataByMonth(List<PowerDetail> powerDetailList, String month);

    public abstract List<PowerDetail> fillDetailData(List<PowerDetail> powerDetailList,List<T> list);

}
