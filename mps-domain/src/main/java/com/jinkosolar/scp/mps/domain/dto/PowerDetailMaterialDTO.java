package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;

/**
 *
 * <AUTHOR>
 * @date 2022-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerDetailMaterialDTO对象", description = "DTO对象")
public class PowerDetailMaterialDTO {
    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private Long id ;

    /**
    *月份
    */
    @ApiModelProperty(value = "月份")
    private String month ;

    /**
    *车间
    */
    @ApiModelProperty(value = "车间")
    private String workshop ;

    /**
    *dp_id
    */
    @ApiModelProperty(value = "dp_id")
    private String dpId ;

    /**
    *订单名称
    */
    @ApiModelProperty(value = "订单名称")
    private String orderName ;

    /**
    *产品族
    */
    @ApiModelProperty(value = "产品族")
    private String productFamily ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute1 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute2 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute3 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute4 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute5 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute6 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute7 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute8 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute9 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute10 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute11 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute12 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute13 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute14 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute15 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute16 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute17 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute18 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute19 ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute20 ;

    @ApiModelProperty(value = "预留21")
    private String itemAttribute21;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute1Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute2Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute3Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute4Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute5Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute6Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute7Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute8Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute9Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute10Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute11Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute12Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute13Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute14Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute15Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute16Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute17Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute18Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute19Rate ;

    /**
    *
    */
    @ApiModelProperty(value = "")
    private String itemAttribute20Rate ;

}
