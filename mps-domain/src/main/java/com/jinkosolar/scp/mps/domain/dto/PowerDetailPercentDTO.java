package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 *
 * <AUTHOR>
 * @date 2022-12-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerDetailPercentDTO对象", description = "DTO对象")
public class PowerDetailPercentDTO {
    /**
    *主键
    */
    @ApiModelProperty(value = "主键")
    private Long id ;

    /**
    *月份
    */
    @ApiModelProperty(value = "月份")
    private String month ;

    /**
    *车间
    */
    @ApiModelProperty(value = "车间")
    private String workshop ;

    /**
    *dp_id
    */
    @ApiModelProperty(value = "dp_id")
    private String dpId ;

    /**
    *订单名称
    */
    @ApiModelProperty(value = "订单名称")
    private String orderName ;

    /**
    *产品族
    */
    @ApiModelProperty(value = "产品族")
    private String productFamily ;

    /**
    *符合率
    */
    @ApiModelProperty(value = "符合率")
    private String passPercent ;

}
