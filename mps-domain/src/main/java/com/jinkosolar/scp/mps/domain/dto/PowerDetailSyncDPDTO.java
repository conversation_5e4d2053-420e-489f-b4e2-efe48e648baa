package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@ApiModel(value = "PowerDetailSyncDPDTO", description = "预测结果材料同步DP")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PowerDetailSyncDPDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long dpGroupId;

    private String powerPredictVersion;

    private String productFamily;

    private String month;

    private String workshop;

    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    @Column(name = "item_attribute1")
    private String itemAttribute1;

    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    @Column(name = "item_attribute2")
    private String itemAttribute2;

    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    @Column(name = "item_attribute3")
    private String itemAttribute3;

    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    @Column(name = "item_attribute4")
    private String itemAttribute4;

    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    @Column(name = "item_attribute5")
    private String itemAttribute5;

    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    @Column(name = "item_attribute6")
    private String itemAttribute6;

    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    @Column(name = "item_attribute7")
    private String itemAttribute7;

    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    @Column(name = "item_attribute8")
    private String itemAttribute8;

    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    @Column(name = "item_attribute9")
    private String itemAttribute9;

    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    @Column(name = "item_attribute10")
    private String itemAttribute10;

    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    @Column(name = "item_attribute11")
    private String itemAttribute11;

    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    @Column(name = "item_attribute12")
    private String itemAttribute12;

    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    @Column(name = "item_attribute13")
    private String itemAttribute13;

    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    @Column(name = "item_attribute14")
    private String itemAttribute14;

    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    @Column(name = "item_attribute15")
    private String itemAttribute15;

    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    @Column(name = "item_attribute16")
    private String itemAttribute16;

    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    @Column(name = "item_attribute17")
    private String itemAttribute17;

    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    @Column(name = "item_attribute18")
    private String itemAttribute18;

    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    @Column(name = "item_attribute19")
    private String itemAttribute19;

    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    @Column(name = "item_attribute20")
    private String itemAttribute20;

    @ApiModelProperty(value = "预留21")
    @Column(name = "item_attribute21")
    private String itemAttribute21;

}
