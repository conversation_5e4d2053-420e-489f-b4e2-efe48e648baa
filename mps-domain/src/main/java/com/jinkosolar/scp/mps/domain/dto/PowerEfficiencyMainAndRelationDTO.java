package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 全年效率值
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerEfficiencyMainDTO对象", description = "DTO对象")
@Slf4j
public class PowerEfficiencyMainAndRelationDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 增/减规则
     */
    @ApiModelProperty(value = "增/减规则")
    private String rule;
    /**
     * 供应类型:自产/外购
     */
    @ApiModelProperty(value = "供应类型:自产/外购")
    private String supplyType;
    /**
     * 月度起
     */
    @ApiModelProperty(value = "月度起")
    private String beginMonth;
    /**
     * 月度止
     */
    @ApiModelProperty(value = "月度止")
    private String endMonth;
    /**
     * 目标效率
     */
    @ApiModelProperty(value = "目标效率")
    private BigDecimal targetEfficiency;

    /**
     * 实际效率
     */
    @ApiModelProperty(value = "实际效率")
    private BigDecimal actualEfficiency;

    /**
     * 档位-实际效率
     */
    @ApiModelProperty(value = "档位")
    private BigDecimal subTitle;

    /**
     * 效率-效率比例
     */
    @ApiModelProperty(value = "效率")
    private BigDecimal efficiency;
}
