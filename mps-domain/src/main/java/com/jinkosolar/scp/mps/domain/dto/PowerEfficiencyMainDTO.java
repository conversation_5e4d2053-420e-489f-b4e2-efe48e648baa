package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.LovLineDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerEfficiencyMainRelation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 全年效率值
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerEfficiencyMainDTO对象", description = "DTO对象")
@Slf4j
public class PowerEfficiencyMainDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 工厂id
     */
    @ApiModelProperty(value = "工厂id")
    private Long factoryId;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    private String factoryName;

    /**
     * 工作中心id
     */
    @ApiModelProperty(value = "工作中心id")
    private Long workCenterId;

    /**
     * 工作中心编码
     */
    @ApiModelProperty(value = "工作中心编码")
    private String workCenterCode;

    /**
     * 工作中心描述
     */
    @ApiModelProperty(value = "工作中心描述")
    private String workCenterDesc;

    /**
     * 车间id
     */
    @ApiModelProperty(value = "车间id")
    private Long workshopId;

    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String workshopName;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long productId;

    /**
     * 产品
     */
    @ApiModelProperty(value = "产品")
    private String productCode;

    /**
     * 规格
     */
    @ApiModelProperty(value = "规格")
    private String specification;

    /**
     * 年月
     */
    @ApiModelProperty(value = "年月")
    private String efficiencyYearMonth;

    /**
     * 目标效率
     */
    @ApiModelProperty(value = "目标效率")
    private BigDecimal targetEfficiency;

    /**
     * 实际效率
     */
    @ApiModelProperty(value = "实际效率")
    private BigDecimal actualEfficiency;

    /**
     * 实际效率
     */
    @ApiModelProperty(value = "入库效率")
    private BigDecimal storageEfficiency;
    /**
     * 基准档位
     */
    @ApiModelProperty(value = "基准档位")
    private String baseGear;

    /**
     * 实际档位
     */
    @ApiModelProperty(value = "实际档位")
    private String realGear;

    private String efficiencyYear;
    private String efficiencyMonth;

    @ApiModelProperty("预测生产率")
    private BigDecimal efficiency;

    private BigDecimal subTitle;

    /**
     * 中心编码+日期+V1-VN
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 主栅
     */
    @ApiModelProperty("主栅")
    private Long mainGridLine;

    /**
     * 工艺
     */
    @ApiModelProperty(value = "工艺")
    private Long craft;

    /**
     * 电性能
     */
    @ApiModelProperty("电性能")
    private Long electricalPerformance;

    /**
     * 自制/自产
     */
    @ApiModelProperty("自制/自产")
    private Long homemadePurchase;

    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private Long thickness;

    /**
     * 导入时间
     */
    @ApiModelProperty(value = "导入时间")
    private LocalDateTime importDate;



    @ApiModelProperty("生效开始日期")
    private  String efficiencyStartDate;


    @ApiModelProperty("生效截止日期")
    private  String efficiencyEndDate;

    /**
     * 主栅
     */
    @ApiModelProperty("主栅")
    private String mainGridLineName;

    /**
     * 电性能
     */
    @ApiModelProperty("电性能")
    private String electricalPerformanceName;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private String thicknessName;
    /**
     * 自制/自产
     */
    @ApiModelProperty("自制/自产")
    private String homemadePurchaseName;
    List<PowerEfficiencyMainRelationDTO> efficiencyMainRelationList;

    public PowerEfficiencyDTO fillLovName(Map<String, LovLineDTO> lovMap) {
        BigDecimal decimal = new BigDecimal("100");
        PowerEfficiencyDTO dto = new PowerEfficiencyDTO();
        dto.setId(getId());
        return dto;
    }

    /**
     * DP 查询预测排产率
     */
    @Data
    public static class DpGroup {
        @ApiModelProperty("分组")
        private String groupIds;

        @ApiModelProperty("预测生产率")
        private BigDecimal efficiency;
    }

}
