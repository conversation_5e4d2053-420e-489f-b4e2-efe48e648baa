package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 全年效率值-档位关系
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerEfficiencyMainRelationDTO对象", description = "DTO对象")
@Slf4j
public class PowerEfficiencyMainRelationDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 效率分布id
     */
    @ApiModelProperty(value = "效率分布id")
    private Long powerEfficiencyId;

    /**
     * 档位
     */
    @ApiModelProperty(value = "档位")
    private BigDecimal subTitle;

    /**
     * 效率
     */
    @ApiModelProperty(value = "效率")
    private BigDecimal efficiency;
}
