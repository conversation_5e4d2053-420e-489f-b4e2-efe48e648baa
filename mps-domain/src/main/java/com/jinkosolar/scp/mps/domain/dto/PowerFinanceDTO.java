package com.jinkosolar.scp.mps.domain.dto;

import cn.hutool.core.util.ReflectUtil;
import com.ibm.scp.common.api.annotation.ExportConvert;
import com.ibm.scp.common.api.annotation.ImportConvert;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.LovLineDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * 材料组合
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerFinanceDTO对象", description = "DTO对象")
public class PowerFinanceDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.IS_OVERSEA)
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.IS_OVERSEA)
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 产品族
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.FAMILY_CODE)
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 产品族
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.FAMILY_CODE)
    @ApiModelProperty(value = "产品族")
    private String productFamilyName;
    /**
     * 相同功率产品族
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.FAMILY_CODE, required = false)
    @ApiModelProperty(value = "相同功率产品族")
    private String resemblanceProductFamily;
    /**
     * 相同功率产品族
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.FAMILY_CODE, required = false)
    @ApiModelProperty(value = "相同功率产品族")
    private String resemblanceProductFamilyName;

    /**
     * 参与计算标记
     */
    @ApiModelProperty(value = "参与计算标记")
    private String flag;

    /**
     * 代号
     */
    @ApiModelProperty(value = "代号")
    private String code;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 功率版本
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.POWER_VERSION)
    @ApiModelProperty(value = "功率版本")
    private String versions;

    /**
     * 功率版本
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.POWER_VERSION)
    @ApiModelProperty(value = "功率版本")
    private String versionsName;
    /**
     * 状态：有效/无效
     */
    @ApiModelProperty(value = "状态：有效/无效")
    private String status;
    /**
     * 横竖装
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.CROSS_VERTICAL)
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 横竖装
     */
    @ImportConvert(lovCode = LovHeaderCodeConstant.CROSS_VERTICAL)
    @ApiModelProperty(value = "横竖装")
    private String installTypeName;
    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20;
    /**
     * 字段显示
     */
    private List<Map<String, String>> dataColumn;

    /**
     * 属性map
     */
    private Map<String, String> attrMap;

    public void fillLovName(Map<String, LovLineDTO> lovMap) {
//        //产品族
//        LovLineDTO productFamilyLov = lovMap.get(LovHeaderCodeConstant.FAMILY_CODE + getProductFamily());
//        if (productFamilyLov != null) {
//            setProductFamily(productFamilyLov.getLovName());
//        }
//        //类型
//        LovLineDTO typeLov = lovMap.get(LovHeaderCodeConstant.POWER_TYPE + getType());
//        if (typeLov != null) {
//            setType(typeLov.getLovName());
//        }
//        //状态
//        LovLineDTO statusLov = lovMap.get(LovHeaderCodeConstant.POWER_FINANCE_STATUS + getStatus());
//        if (statusLov != null) {
//            setStatus(statusLov.getLovName());
//        }
//        //国内/海外
//        LovLineDTO isOverseaLov = lovMap.get(LovHeaderCodeConstant.IS_OVERSEA + getIsOversea());
//        if (isOverseaLov != null) {
//            setIsOversea(isOverseaLov.getLovName());
//        }
//
//        //小区域
//        if (StringUtils.isNotBlank(getSmallAreaCertification())) {
//            StringBuilder sb = null;
//            for (String s : getSmallAreaCertification().split(",")) {
//                LovLineDTO lovLineDTO = lovMap.get(LovHeaderCodeConstant.COUNTRY + s);
//
//                if(lovLineDTO != null){
//                    if (sb == null) {
//                        sb = new StringBuilder(lovLineDTO.getLovName());
//                    }else {
//                        sb.append(",").append(lovLineDTO.getLovName());
//                    }
//                }
//            }
//
//            if(sb != null){
//                setSmallAreaCertification(sb.toString());
//            }
//
//        }
    }

    public void fillLovValue(Map<String, LovLineDTO> lovMap) {
//        //产品族
//        LovLineDTO productFamilyLov = lovMap.get(LovHeaderCodeConstant.FAMILY_CODE + getProductFamily());
//        if (productFamilyLov == null) {
//            throw new BizException(String.format("mps.error.productionFamilyErr", getProductFamily()));
//        }
//        //类型
//        LovLineDTO typeLov = lovMap.get(LovHeaderCodeConstant.POWER_TYPE + getType());
//        if (typeLov == null) {
//            throw new BizException(String.format("mps.error.typeErr", getType()));
//        }
//        if (StringUtils.isNotBlank(getStatus())) {
//            //状态
//            LovLineDTO statusLov = lovMap.get(LovHeaderCodeConstant.POWER_FINANCE_STATUS + getStatus());
//            if (statusLov == null) {
//                throw new BizException(String.format("mps.error.statusErr", getStatus()));
//            }
//            setStatus(statusLov.getLovValue());
//        }
//
//        //国内/海外
//        LovLineDTO isOverseaLov = lovMap.get(LovHeaderCodeConstant.IS_OVERSEA + getIsOversea());
//        if (isOverseaLov == null) {
//            throw new BizException(String.format("mps.error.isOverseaErr", getIsOversea()));
//        }
//
//        //日期
//        if (StringUtils.isNotBlank(getMonth())) {
//            if (getMonth().length() != 6) {
//                throw new BizException(String.format("mps.error.invalidMonth", getMonth()));
//            }
//            try {
//                DateUtil.getMoth(getMonth());
//            } catch (Exception e) {
//                throw new BizException(String.format("mps.error.invalidMonth", getMonth()));
//            }
//        }
//
//        //小区域
//        if (StringUtils.isNotBlank(getSmallAreaCertification())) {
//            StringBuilder sb = null;
//            for (String s : getSmallAreaCertification().split(",")) {
//                LovLineDTO lovLineDTO = lovMap.get(LovHeaderCodeConstant.COUNTRY + s);
//
//                if(lovLineDTO != null){
//                    if (sb == null) {
//                        sb = new StringBuilder(lovLineDTO.getLovValue());
//                    }else {
//                        sb.append(",").append(lovLineDTO.getLovValue());
//                    }
//                }
//            }
//
//            if(sb != null){
//                setSmallAreaCertification(sb.toString());
//            }
//
//        }
//
//        setProductFamily(productFamilyLov.getLovValue());
//        setType(typeLov.getLovValue());
//        setIsOversea(isOverseaLov.getLovValue());

    }

    public void fillAttLov(Map<String, LovLineDTO> lovAttMap) {
        LovLineDTO itemAtt1Lov = lovAttMap.get(getItemAttribute1());
        if (itemAtt1Lov != null) {
            setItemAttribute1(itemAtt1Lov.getLovName());
        }
        LovLineDTO itemAtt2Lov = lovAttMap.get(getItemAttribute2());
        if (itemAtt2Lov != null) {
            setItemAttribute2(itemAtt2Lov.getLovName());
        }
        LovLineDTO itemAtt3Lov = lovAttMap.get(getItemAttribute3());
        if (itemAtt3Lov != null) {
            setItemAttribute3(itemAtt3Lov.getLovName());
        }
        LovLineDTO itemAtt4Lov = lovAttMap.get(getItemAttribute4());
        if (itemAtt4Lov != null) {
            setItemAttribute4(itemAtt4Lov.getLovName());
        }
        LovLineDTO itemAtt5Lov = lovAttMap.get(getItemAttribute5());
        if (itemAtt5Lov != null) {
            setItemAttribute5(itemAtt5Lov.getLovName());
        }
        LovLineDTO itemAtt6Lov = lovAttMap.get(getItemAttribute6());
        if (itemAtt6Lov != null) {
            setItemAttribute6(itemAtt6Lov.getLovName());
        }
        LovLineDTO itemAtt7Lov = lovAttMap.get(getItemAttribute7());
        if (itemAtt7Lov != null) {
            setItemAttribute7(itemAtt7Lov.getLovName());
        }
        LovLineDTO itemAtt8Lov = lovAttMap.get(getItemAttribute8());
        if (itemAtt8Lov != null) {
            setItemAttribute8(itemAtt8Lov.getLovName());
        }
        LovLineDTO itemAtt9Lov = lovAttMap.get(getItemAttribute9());
        if (itemAtt9Lov != null) {
            setItemAttribute9(itemAtt9Lov.getLovName());
        }
        LovLineDTO itemAtt10Lov = lovAttMap.get(getItemAttribute10());
        if (itemAtt10Lov != null) {
            setItemAttribute10(itemAtt10Lov.getLovName());
        }
        LovLineDTO itemAtt11Lov = lovAttMap.get(getItemAttribute11());
        if (itemAtt11Lov != null) {
            setItemAttribute11(itemAtt11Lov.getLovName());
        }
        LovLineDTO itemAtt12Lov = lovAttMap.get(getItemAttribute12());
        if (itemAtt12Lov != null) {
            setItemAttribute12(itemAtt12Lov.getLovName());
        }
        LovLineDTO itemAtt13Lov = lovAttMap.get(getItemAttribute13());
        if (itemAtt13Lov != null) {
            setItemAttribute13(itemAtt13Lov.getLovName());
        }
        LovLineDTO itemAtt14Lov = lovAttMap.get(getItemAttribute14());
        if (itemAtt14Lov != null) {
            setItemAttribute14(itemAtt14Lov.getLovName());
        }
        LovLineDTO itemAtt15Lov = lovAttMap.get(getItemAttribute15());
        if (itemAtt15Lov != null) {
            setItemAttribute15(itemAtt15Lov.getLovName());
        }
    }

    public String materialCombinationStr() {
        StringBuilder str = new StringBuilder();
        if(MapUtils.isNotEmpty(attrMap)){
            attrMap.forEach((field, name) ->{
                Object fieldValue = ReflectUtil.getFieldValue(this, field);
                if(Objects.nonNull(fieldValue)){
                    str.append(name).append(fieldValue).append("-");
                }
            });
        }
        if(StringUtils.isNotBlank(str)){
            return str.substring(0, str.length() -1);
        }
        return "";
    }
}
