package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("材料搭配组合描述基表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerMaterialCombinationDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 材料搭配组合代码
     */
    @ApiModelProperty("材料搭配组合代码")
    @ExcelProperty(value = "材料搭配组合代码")
    private String materialCombinationCode;
    /**
     * 材料搭配组合描述
     */
    @ApiModelProperty("材料搭配组合描述")
    @ExcelProperty(value = "材料搭配组合描述")
    //@ImportExConvert(required = false)
    //@NotBlank(message = "材料搭配组合描述不能为空", groups = ValidGroups.Insert.class)
    private String materialCombinationDesc;
    /**
     * 投产方案 LOV：SYS.Production_Plan
     */
    @ApiModelProperty(value = "投产方案")
    @NotNull(message = "投产方案不能为空")
    private String productionPlan;

    @ApiModelProperty(value = "投产方案编码")
    private String productionPlanCode;

    /**
     * 电池工艺 LOV：SYS.ROUTE
     */
    @ApiModelProperty("电池工艺id LOV：SYS.ROUTE")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_ROUTE)
    //@NotNull(message = "电池工艺不能为空", groups = ValidGroups.Insert.class)
    private Long cellProcessId;
    /**
     * 电池工艺 LOV：SYS.ROUTE
     */
    @ApiModelProperty("电池工艺")
    @ExcelProperty("电池工艺")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.SYS_ROUTE + "'", required = false)
    private String cellProcessIdName;
    /**
     * 胶膜颜色 LOV：ATTR_TYPE_011_ATTR_1100
     */
    @ApiModelProperty("胶膜颜色id LOV：ATTR_TYPE_011_ATTR_1100")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_011_ATTR_1100)
    //@NotNull(message = "胶膜颜色不能为空", groups = ValidGroups.Insert.class)
    private Long filmColorId;
    /**
     * 胶膜颜色 LOV：ATTR_TYPE_011_ATTR_1100
     */
    @ApiModelProperty("胶膜颜色")
    @ExcelProperty("胶膜颜色")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_011_ATTR_1100 + "'", required = false)
    private String filmColorIdName;
    /**
     * 线缆长度 LOV：MPS.CABLE_LENGTH
     */
    @ApiModelProperty("线缆长度id LOV：MPS.CABLE_LENGTH")
    @Dict(headerCode = MpsLovConstant.CABLE_LENGTH)
    //@NotNull(message = "线缆长度不能为空", groups = ValidGroups.Insert.class)
    private Long cableLengthId;
    /**
     * 线缆长度 LOV：MPS.CABLE_LENGTH
     */
    @ApiModelProperty("线缆长度")
    @ExcelProperty("线缆长度")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + MpsLovConstant.CABLE_LENGTH + "'", required = false)
    private String cableLengthIdName;
    /**
     * 膜带-是否贴膜 LOV：MPS.TIEMO
     */
    @ApiModelProperty("膜带-是否贴膜id LOV：MPS.TIEMO")
    @Dict(headerCode = MpsLovConstant.TIEMO)
    //@NotNull(message = "膜袋-是否贴膜不能为空", groups = ValidGroups.Insert.class)
    private Long filmBagCoatedId;
    /**
     * 膜袋-是否贴膜 LOV：MPS.TIEMO
     */
    @ApiModelProperty("膜带")
    @ExcelProperty("膜带")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + MpsLovConstant.TIEMO + "'", required = false)
    private String filmBagCoatedIdName;
    /**
     * 背板颜色 LOV：ATTR_TYPE_002_ATTR_1100
     */
    @ApiModelProperty("背板颜色id LOV：ATTR_TYPE_002_ATTR_1100")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_002_ATTR_1100)
    //@NotNull(message = "背板颜色不能为空", groups = ValidGroups.Insert.class)
    private Long backsheetColorId;
    /**
     * 背板颜色 LOV：ATTR_TYPE_002_ATTR_1100
     */
    @ApiModelProperty("背板颜色")
    @ExcelProperty("背板颜色")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_002_ATTR_1100 + "'", required = false)
    private String backsheetColorIdName;
    /**
     * 正面玻璃-镀膜 LOV：ATTR_TYPE_005_ATTR_1400
     */
    @ApiModelProperty("正面玻璃-镀膜id LOV：ATTR_TYPE_005_ATTR_1400")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1400)
    //@NotNull(message = "正面玻璃-镀膜不能为空", groups = ValidGroups.Insert.class)
    private Long frontGlassCoatedId;
    /**
     * 正面玻璃-镀膜 LOV：ATTR_TYPE_005_ATTR_1400
     */
    @ApiModelProperty("正面玻璃")
    @ExcelProperty("正面玻璃")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1400 + "'", required = false)
    private String frontGlassCoatedIdName;
    /**
     * 背面玻璃-是否网格 LOV：ATTR_TYPE_005_ATTR_1200
     */
    @ApiModelProperty("背面玻璃-是否网格id LOV：ATTR_TYPE_005_ATTR_1200")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1200)
    //@NotNull(message = "背面玻璃-是否网格不能为空", groups = ValidGroups.Insert.class)
    private Long backGlassGridId;
    /**
     * 背面玻璃-是否网格 LOV：ATTR_TYPE_005_ATTR_1200
     */
    @ApiModelProperty("背面玻璃")
    @ExcelProperty("背面玻璃")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1200 + "'", required = false)
    private String backGlassGridIdName;
    /**
     * 汇流条-是否反光 LOV：ATTR_TYPE_009_ATTR_1800
     */
    @ApiModelProperty("汇流条-是否反光id LOV：ATTR_TYPE_009_ATTR_1800")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1800)
    //@NotNull(message = "汇流条-是否反光不能为空", groups = ValidGroups.Insert.class)
    private Long busbarReflectiveId;
    /**
     * 汇流条-是否反光 LOV：ATTR_TYPE_009_ATTR_1800
     */
    @ApiModelProperty("汇流条")
    @ExcelProperty("汇流条")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1800 + "'", required = false)
    private String busbarReflectiveIdName;
    /**
     * 互联条-直径 LOV：ATTR_TYPE_010_ATTR_1000
     */
    @ApiModelProperty("互联条-直径id LOV：ATTR_TYPE_009_ATTR_1100")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1100)
    //@NotNull(message = "互联条-直径不能为空", groups = ValidGroups.Insert.class)
    private Long interconnectDiameterId;
    /**
     * 互联条-直径 LOV：ATTR_TYPE_010_ATTR_1000
     */
    @ApiModelProperty("互联条直径")
    @ExcelProperty("互联条直径")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_1100 + "'", required = false)
    private String interconnectDiameterIdName;

    /**
     * 围字贴：MPS.WEIZITIEMO
     */
    @ApiModelProperty(value = "围字贴：MPS.WEIZITIEMO")
    @Dict(headerCode = LovHeaderCodeConstant.MPS_WEIZITIEMO)
    private Long surroundingFilmId;

    @ApiModelProperty("围字贴")
    @ExcelProperty("围字贴")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.MPS_WEIZITIEMO + "'", required = false)
    private String surroundingFilmIdName;

    /**
     * 膜带性能：ATTR_TYPE_015_ATTR_1900
     */
    @ApiModelProperty(value = "膜带性能：ATTR_TYPE_015_ATTR_1900")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_015_ATTR_1900)
    private Long filmBagPerfId;

    @ApiModelProperty("膜带性能")
    @ExcelProperty("膜带性能")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_015_ATTR_1900 + "'", required = false)
    private String filmBagPerfIdName;

    /**
     * 正玻厚度：ATTR_TYPE_005_ATTR_1500
     */
    @ApiModelProperty(value = "正玻厚度：ATTR_TYPE_005_ATTR_1500")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1500)
    private Long frontGlassThicknessId;

    @ApiModelProperty("正玻厚度")
    @ExcelProperty("正玻厚度")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1500 + "'", required = false)
    private String frontGlassThicknessIdName;

    /**
     * 汇流条规格：ATTR_TYPE_009_ATTR_2000
     */
    @ApiModelProperty(value = "汇流条规格：ATTR_TYPE_009_ATTR_2000")
    @Dict(headerCode = LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_2000)
    private Long busBarSpecId;

    @ApiModelProperty("汇流条规格")
    @ExcelProperty("汇流条规格")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_2000 + "'", required = false)
    private String busBarSpecIdName;
    /**
     * 归档标识
     */
    @ApiModelProperty("归档标识值")
    @ExcelProperty(value = "归档标识值")
    private String archiveFlag;
    /**
     * 归档标识
     */
    @ApiModelProperty("归档标识")
    @ExcelProperty(value = "归档标识")
    private String archiveFlagName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id", notes = "")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;

    /**
     * 材料搭配组合描述-英文
     */
    @ApiModelProperty(value = "材料搭配组合描述-英文")
    private String materialCombinationDescEn;

    public String getGroupKey() {
        return StringUtils.join(this.getCellProcessId(),
                this.getFrontGlassCoatedId(),
                this.getFilmColorId(),
                this.getBacksheetColorId(),
                this.getFilmBagCoatedId(),
                this.getInterconnectDiameterId(),
                this.getBusbarReflectiveId(),
                this.getBackGlassGridId(),
                this.getSurroundingFilmId(),
                this.getFilmBagPerfId(),
                this.getCableLengthId(),
                this.getFrontGlassThicknessId(),
                this.getBusBarSpecId());
    }

    public String getGroupKey1() {
        return StringUtils.join(this.getCellProcessIdName(),
                this.getFrontGlassCoatedIdName(),
                this.getFilmColorIdName(),
                this.getBacksheetColorIdName(),
                this.getFilmBagCoatedIdName(),
                this.getInterconnectDiameterIdName(),
                this.getBusbarReflectiveIdName(),
                this.getBackGlassGridIdName(),
                this.getSurroundingFilmIdName(),
                this.getFilmBagPerfIdName(),
                this.getCableLengthIdName(),
                this.getFrontGlassThicknessIdName(),
                this.getBusBarSpecIdName());
    }
}
