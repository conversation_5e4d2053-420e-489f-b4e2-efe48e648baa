package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 正态分布计算与查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerNormalDistributionDTO对象", description = "DTO对象")
public class PowerNormalDistributionDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    private String supplier;
    /**
     * 供应类型:当月/次月
     */
    @ApiModelProperty(value = "供应类型:当月/次月")
    private String supplyType;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private BigDecimal supplyQuantity;
    /**
     * 供应量
     */
    @ApiModelProperty(value = "供应量")
    private String supplyQuantityName;
    /**
     * 平均效率
     */
    @ApiModelProperty(value = "平均效率")
    private BigDecimal averageEfficiency;

    /**
     * 平均效率名称
     */
    @ApiModelProperty(value = "平均效率名称")
    private String averageEfficiencyName;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @Column(name = "sort")
    private Integer sort;

    private List<Map<String, String>> dataStructures;
    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1")
    private BigDecimal efficiency1;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency2;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency3;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency4;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency5;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency6;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency7;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency8;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency9;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency10;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency11;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency12;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency13;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency14;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency15;


    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16")
    private BigDecimal efficiency16;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17")
    private BigDecimal efficiency17;
    /**
     * 效率18
     */
    @ApiModelProperty(value = "效率18")
    private BigDecimal efficiency18;
    /**
     * 效率19
     */
    @ApiModelProperty(value = "效率19")
    private BigDecimal efficiency19;
    /**
     * 效率20
     */
    @ApiModelProperty(value = "效率20")
    private BigDecimal efficiency20;
    /**
     * 效率21
     */
    @ApiModelProperty(value = "效率21")
    private BigDecimal efficiency21;
    /**
     * 效率22
     */
    @ApiModelProperty(value = "效率22")
    private BigDecimal efficiency22;
    /**
     * 效率23
     */
    @ApiModelProperty(value = "效率23")
    private BigDecimal efficiency23;
    /**
     * 效率24
     */
    @ApiModelProperty(value = "效率24")
    private BigDecimal efficiency24;
    /**
     * 效率25
     */
    @ApiModelProperty(value = "效率25")
    private BigDecimal efficiency25;
    /**
     * 效率26
     */
    @ApiModelProperty(value = "效率26")
    private BigDecimal efficiency26;
    /**
     * 效率27
     */
    @ApiModelProperty(value = "效率27")
    private BigDecimal efficiency27;
    /**
     * 效率28
     */
    @ApiModelProperty(value = "效率28")
    private BigDecimal efficiency28;
    /**
     * 效率29
     */
    @ApiModelProperty(value = "效率29")
    private BigDecimal efficiency29;
    /**
     * 效率30
     */
    @ApiModelProperty(value = "效率30")
    private BigDecimal efficiency30;

    /**
     * 效率31
     */
    @ApiModelProperty(value = "效率31")
    private BigDecimal efficiency31;
    /**
     * 效率32
     */
    @ApiModelProperty(value = "效率32")
    private BigDecimal efficiency32;
    /**
     * 效率33
     */
    @ApiModelProperty(value = "效率33")
    private BigDecimal efficiency33;
    /**
     * 效率34
     */
    @ApiModelProperty(value = "效率34")
    private BigDecimal efficiency34;
    /**
     * 效率35
     */
    @ApiModelProperty(value = "效率35")
    private BigDecimal efficiency35;
    /**
     * 效率36
     */
    @ApiModelProperty(value = "效率36")
    private BigDecimal efficiency36;
    /**
     * 效率37
     */
    @ApiModelProperty(value = "效率37")
    private BigDecimal efficiency37;
    /**
     * 效率38
     */
    @ApiModelProperty(value = "效率38")
    private BigDecimal efficiency38;
    /**
     * 效率39
     */
    @ApiModelProperty(value = "效率39")
    private BigDecimal efficiency39;
    /**
     * 效率40
     */
    @ApiModelProperty(value = "效率40")
    private BigDecimal efficiency40;

    private String 	efficiency41;
    private String 	efficiency42;
    private String 	efficiency43;
    private String 	efficiency44;
    private String 	efficiency45;
    private String 	efficiency46;
    private String 	efficiency47;
    private String 	efficiency48;
    private String 	efficiency49;
    private String 	efficiency50;
    private String 	efficiency51;
    private String 	efficiency52;
    private String 	efficiency53;
    private String 	efficiency54;
    private String 	efficiency55;
    private String 	efficiency56;
    private String 	efficiency57;
    private String 	efficiency58;
    private String 	efficiency59;
    private String 	efficiency60;
    private String 	efficiency61;
    private String 	efficiency62;
    private String 	efficiency63;
    private String 	efficiency64;
    private String 	efficiency65;
    private String 	efficiency66;
    private String 	efficiency67;
    private String 	efficiency68;
    private String 	efficiency69;
    private String 	efficiency70;
    private String 	efficiency71;
    private String 	efficiency72;
    private String 	efficiency73;
    private String 	efficiency74;
    private String 	efficiency75;
    private String 	efficiency76;
    private String 	efficiency77;
    private String 	efficiency78;
    private String 	efficiency79;
    private String 	efficiency80;

    /**
     * 副标题1
     */
    @ApiModelProperty(value = "副标题1")
    private String subTitle1;
    /**
     * 副标题2
     */
    @ApiModelProperty(value = "副标题2")
    private String subTitle2;
    /**
     * 副标题3
     */
    @ApiModelProperty(value = "副标题3")
    private String subTitle3;
    /**
     * 副标题4
     */
    @ApiModelProperty(value = "副标题4")
    private String subTitle4;
    /**
     * 副标题5
     */
    @ApiModelProperty(value = "副标题5")
    private String subTitle5;
    /**
     * 副标题6
     */
    @ApiModelProperty(value = "副标题6")
    private String subTitle6;
    /**
     * 副标题7
     */
    @ApiModelProperty(value = "副标题7")
    private String subTitle7;
    /**
     * 副标题8
     */
    @ApiModelProperty(value = "副标题8")
    private String subTitle8;
    /**
     * 副标题9
     */
    @ApiModelProperty(value = "副标题9")
    private String subTitle9;
    /**
     * 副标题10
     */
    @ApiModelProperty(value = "副标题10")
    private String subTitle10;
    /**
     * 副标题11
     */
    @ApiModelProperty(value = "副标题11")
    private String subTitle11;
    /**
     * 副标题12
     */
    @ApiModelProperty(value = "副标题12")
    private String subTitle12;
    /**
     * 副标题13
     */
    @ApiModelProperty(value = "副标题13")
    private String subTitle13;
    /**
     * 副标题14
     */
    @ApiModelProperty(value = "副标题14")
    private String subTitle14;
    /**
     * 副标题15
     */
    @ApiModelProperty(value = "副标题15")
    private String subTitle15;

    /**
     * 副标题16
     */
    @ApiModelProperty(value = "副标题16")
    private String subTitle16;
    /**
     * 副标题17
     */
    @ApiModelProperty(value = "副标题17")
    private String subTitle17;
    /**
     * 副标题18
     */
    @ApiModelProperty(value = "副标题18")
    private String subTitle18;
    /**
     * 副标题19
     */
    @ApiModelProperty(value = "副标题19")
    private String subTitle19;
    /**
     * 副标题20
     */
    @ApiModelProperty(value = "副标题20")
    private String subTitle20;
    /**
     * 副标题21
     */
    @ApiModelProperty(value = "副标题21")
    private String subTitle21;
    /**
     * 副标题22
     */
    @ApiModelProperty(value = "副标题22")
    private String subTitle22;
    /**
     * 副标题23
     */
    @ApiModelProperty(value = "副标题23")
    private String subTitle23;
    /**
     * 副标题24
     */
    @ApiModelProperty(value = "副标题24")
    private String subTitle24;
    /**
     * 副标题25
     */
    @ApiModelProperty(value = "副标题25")
    private String subTitle25;
    /**
     * 副标题26
     */
    @ApiModelProperty(value = "副标题26")
    private String subTitle26;
    /**
     * 副标题27
     */
    @ApiModelProperty(value = "副标题27")
    private String subTitle27;
    /**
     * 副标题28
     */
    @ApiModelProperty(value = "副标题28")
    private String subTitle28;
    /**
     * 副标题29
     */
    @ApiModelProperty(value = "副标题29")
    private String subTitle29;
    /**
     * 副标题30
     */
    @ApiModelProperty(value = "副标题30")
    private String subTitle30;

    /**
     * 副标题31
     */
    @ApiModelProperty(value = "副标题31")
    private String subTitle31;
    /**
     * 副标题32
     */
    @ApiModelProperty(value = "副标题32")
    private String subTitle32;
    /**
     * 副标题33
     */
    @ApiModelProperty(value = "副标题33")
    private String subTitle33;
    /**
     * 副标题34
     */
    @ApiModelProperty(value = "副标题34")
    private String subTitle34;
    /**
     * 副标题35
     */
    @ApiModelProperty(value = "副标题35")
    private String subTitle35;
    /**
     * 副标题36
     */
    @ApiModelProperty(value = "副标题36")
    private String subTitle36;
    /**
     * 副标题37
     */
    @ApiModelProperty(value = "副标题37")
    private String subTitle37;
    /**
     * 副标题38
     */
    @ApiModelProperty(value = "副标题38")
    private String subTitle38;
    /**
     * 副标题39
     */
    @ApiModelProperty(value = "副标题39")
    private String subTitle39;
    /**
     * 副标题40
     */
    @ApiModelProperty(value = "副标题40")
    private String subTitle40;

    private String 	subTitle41;
    private String 	subTitle42;
    private String 	subTitle43;
    private String 	subTitle44;
    private String 	subTitle45;
    private String 	subTitle46;
    private String 	subTitle47;
    private String 	subTitle48;
    private String 	subTitle49;
    private String 	subTitle50;
    private String 	subTitle51;
    private String 	subTitle52;
    private String 	subTitle53;
    private String 	subTitle54;
    private String 	subTitle55;
    private String 	subTitle56;
    private String 	subTitle57;
    private String 	subTitle58;
    private String 	subTitle59;
    private String 	subTitle60;
    private String 	subTitle61;
    private String 	subTitle62;
    private String 	subTitle63;
    private String 	subTitle64;
    private String 	subTitle65;
    private String 	subTitle66;
    private String 	subTitle67;
    private String 	subTitle68;
    private String 	subTitle69;
    private String 	subTitle70;
    private String 	subTitle71;
    private String 	subTitle72;
    private String 	subTitle73;
    private String 	subTitle74;
    private String 	subTitle75;
    private String 	subTitle76;
    private String 	subTitle77;
    private String 	subTitle78;
    private String 	subTitle79;
    private String 	subTitle80;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1名称")
    private String efficiency1Name;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2名称")
    private String efficiency2Name;
    /**
     * 效率3名称
     */
    @ApiModelProperty(value = "效率3名称")
    private String efficiency3Name;
    /**
     * 效率4名称
     */
    @ApiModelProperty(value = "效率4名称")
    private String efficiency4Name;
    /**
     * 效率5名称
     */
    @ApiModelProperty(value = "效率5名称")
    private String efficiency5Name;
    /**
     * 效率6名称
     */
    @ApiModelProperty(value = "效率6名称")
    private String efficiency6Name;
    /**
     * 效率7名称
     */
    @ApiModelProperty(value = "效率7名称")
    private String efficiency7Name;
    /**
     * 效率8名称
     */
    @ApiModelProperty(value = "效率8名称")
    private String efficiency8Name;
    /**
     * 效率9名称
     */
    @ApiModelProperty(value = "效率9名称")
    private String efficiency9Name;
    /**
     * 效率10名称
     */
    @ApiModelProperty(value = "效率10名称")
    private String efficiency10Name;
    /**
     * 效率11名称
     */
    @ApiModelProperty(value = "效率11名称")
    private String efficiency11Name;
    /**
     * 效率12名称
     */
    @ApiModelProperty(value = "效率12名称")
    private String efficiency12Name;
    /**
     * 效率13名称
     */
    @ApiModelProperty(value = "效率13名称")
    private String efficiency13Name;
    /**
     * 效率14名称
     */
    @ApiModelProperty(value = "效率14名称")
    private String efficiency14Name;
    /**
     * 效率15名称
     */
    @ApiModelProperty(value = "效率15名称")
    private String efficiency15Name;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16名称")
    private String efficiency16Name;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17名称")
    private String efficiency17Name;
    /**
     * 效率18名称
     */
    @ApiModelProperty(value = "效率18名称")
    private String efficiency18Name;
    /**
     * 效率19名称
     */
    @ApiModelProperty(value = "效率19名称")
    private String efficiency19Name;
    /**
     * 效率20名称
     */
    @ApiModelProperty(value = "效率20名称")
    private String efficiency20Name;
    /**
     * 效率21名称
     */
    @ApiModelProperty(value = "效率21名称")
    private String efficiency21Name;
    /**
     * 效率22名称
     */
    @ApiModelProperty(value = "效率22名称")
    private String efficiency22Name;
    /**
     * 效率23名称
     */
    @ApiModelProperty(value = "效率23名称")
    private String efficiency23Name;
    /**
     * 效率24名称
     */
    @ApiModelProperty(value = "效率24名称")
    private String efficiency24Name;
    /**
     * 效率25名称
     */
    @ApiModelProperty(value = "效率25名称")
    private String efficiency25Name;
    /**
     * 效率26名称
     */
    @ApiModelProperty(value = "效率26名称")
    private String efficiency26Name;
    /**
     * 效率27名称
     */
    @ApiModelProperty(value = "效率27名称")
    private String efficiency27Name;
    /**
     * 效率28名称
     */
    @ApiModelProperty(value = "效率28名称")
    private String efficiency28Name;
    /**
     * 效率29名称
     */
    @ApiModelProperty(value = "效率29名称")
    private String efficiency29Name;
    /**
     * 效率30名称
     */
    @ApiModelProperty(value = "效率30名称")
    private String efficiency30Name;

    /**
     * 效率31名称
     */
    @ApiModelProperty(value = "效率31名称")
    private String efficiency31Name;
    /**
     * 效率32名称
     */
    @ApiModelProperty(value = "效率32名称")
    private String efficiency32Name;
    /**
     * 效率33名称
     */
    @ApiModelProperty(value = "效率33名称")
    private String efficiency33Name;
    /**
     * 效率34名称
     */
    @ApiModelProperty(value = "效率34名称")
    private String efficiency34Name;
    /**
     * 效率35名称
     */
    @ApiModelProperty(value = "效率35名称")
    private String efficiency35Name;
    /**
     * 效率36名称
     */
    @ApiModelProperty(value = "效率36名称")
    private String efficiency36Name;
    /**
     * 效率37名称
     */
    @ApiModelProperty(value = "效率37名称")
    private String efficiency37Name;
    /**
     * 效率38名称
     */
    @ApiModelProperty(value = "效率38名称")
    private String efficiency38Name;
    /**
     * 效率39名称
     */
    @ApiModelProperty(value = "效率39名称")
    private String efficiency39Name;
    /**
     * 效率40名称
     */
    @ApiModelProperty(value = "效率40名称")
    private String efficiency40Name;


    private String 	efficiency41Name;
    private String 	efficiency42Name;
    private String 	efficiency43Name;
    private String 	efficiency44Name;
    private String 	efficiency45Name;
    private String 	efficiency46Name;
    private String 	efficiency47Name;
    private String 	efficiency48Name;
    private String 	efficiency49Name;
    private String 	efficiency50Name;
    private String 	efficiency51Name;
    private String 	efficiency52Name;
    private String 	efficiency53Name;
    private String 	efficiency54Name;
    private String 	efficiency55Name;
    private String 	efficiency56Name;
    private String 	efficiency57Name;
    private String 	efficiency58Name;
    private String 	efficiency59Name;
    private String 	efficiency60Name;
    private String 	efficiency61Name;
    private String 	efficiency62Name;
    private String 	efficiency63Name;
    private String 	efficiency64Name;
    private String 	efficiency65Name;
    private String 	efficiency66Name;
    private String 	efficiency67Name;
    private String 	efficiency68Name;
    private String 	efficiency69Name;
    private String 	efficiency70Name;
    private String 	efficiency71Name;
    private String 	efficiency72Name;
    private String 	efficiency73Name;
    private String 	efficiency74Name;
    private String 	efficiency75Name;
    private String 	efficiency76Name;
    private String 	efficiency77Name;
    private String 	efficiency78Name;
    private String 	efficiency79Name;
    private String 	efficiency80Name;
}
