package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerPredictSuperBOMMaterialLineReqDTO对象", description = "PowerPredictSuperBOMMaterialLineReqDTO对象")
@Builder
public class PowerPredictSuperBOMMaterialLineReqDTO extends BaseDTO {

    /**
     * 材料名称
     */
    @ApiModelProperty(value = "材料名称")
    private String materialName;

    /**
     * bom列名
     */
    @ApiModelProperty(value = "bom列名")
    private List<String> itemNameList;

    /**
     * 材料值
     */
    @ApiModelProperty(value = "材料值")
    private String materialValue;



}
