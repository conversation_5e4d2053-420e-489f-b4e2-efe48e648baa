package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("产品功率预测基准数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionBaseDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 产品系列基表id
     */
    @ApiModelProperty("产品系列基表id")
    @NotNull(message = "产品系列不能为空", groups = ValidGroups.Insert.class)
    private Long cellSeriesId;
    /**
     * 产品系列代码
     */
    @ApiModelProperty("产品系列代码")
    @ExcelProperty("产品系列代码")
    private String cellSeriesCode;
    /**
     * 产品系列代码
     */
    @ApiModelProperty("产品系列代码")
    @ExcelProperty("产品系列描述")
    private String cellSeriesDesc;
    /**
     * 材料搭配组合描述基表id
     */
    @ApiModelProperty("材料搭配组合描述基表id")
    @ExcelProperty(value = "材料搭配组合描述基表id")
    @NotNull(message = "材料搭配组合不能为空", groups = ValidGroups.Insert.class)
    private Long materialCombinationId;
    /**
     * 材料搭配组合代码
     */
    @ApiModelProperty("材料搭配组合代码")
    @ExcelProperty(value = "材料搭配组合代码")
    private String materialCombinationCode;
    /**
     * 材料搭配组合描述
     */
    @ApiModelProperty("材料搭配组合描述")
    @ExcelProperty(value = "材料搭配组合描述")
    private String materialCombinationDesc;
    /**
     * 片串 LOV：SYS.CELL_QUANTITY
     */
    @ApiModelProperty("片串 LOV：SYS.CELL_QUANTITY")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_CELL_QUANTITY)
    @NotNull(message = "片串不能为空", groups = ValidGroups.Insert.class)
    private Long cellStringId;

    /**
     * 片串 LOV：SYS.CELL_QUANTITY
     */
    @ApiModelProperty("片串 LOV：SYS.CELL_QUANTITY")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.SYS_CELL_QUANTITY + "'")
    private String cellStringIdName;
    /**
     * 功率落档基本表版本
     */
    @ApiModelProperty("功率落档基本表版本")
    @ExcelProperty(value = "功率落档基本表版本")
    @NotBlank(message = "功率落档基本表版本不能为空", groups = ValidGroups.Insert.class)
    private String powerDeratingVersion;
    /**
     * 使用优先级
     */
    @ApiModelProperty("使用优先级")
    @ExcelProperty(value = "使用优先级")
    @NotNull(message = "使用优先级不能为空", groups = ValidGroups.Insert.class)
    private Integer usagePriority;
    /**
     * 基准效率
     */
    @ApiModelProperty("基准效率")
    @ExcelProperty(value = "基准效率")
    @NotNull(message = "基准效率不能为空", groups = ValidGroups.Insert.class)
    private String baseEfficiency;
    /**
     * 基准效率value
     */
    @ApiModelProperty("基准效率value")
    private String baseEfficiencyValue;
    /**
     * 扩展效率个数
     */
    @ApiModelProperty("扩展效率个数")
    @ExcelProperty(value = "扩展效率个数")
    @NotNull(message = "扩展效率个数不能为空", groups = ValidGroups.Insert.class)
    private Integer efficiencyNum;
    /**
     * 面积
     */
    @ApiModelProperty("面积")
    @ExcelProperty(value = "面积")
    @NotNull(message = "面积不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal area;
    /**
     * 基准CTM值
     */
    @ApiModelProperty("基准CTM值")
    @ExcelProperty(value = "基准CTM值")
    @NotNull(message = "基准CTM值不能为空", groups = ValidGroups.Insert.class)
    private String baseCtm;
    /**
     * 基准效率值
     */
    @ApiModelProperty("基准CTM值value")
    private String baseCtmValue;
    /**
     * CTM值系数
     */
    @ApiModelProperty("CTM值系数")
    @ExcelProperty(value = "CTM值系数")
    @NotNull(message = "CTM值系数不能为空", groups = ValidGroups.Insert.class)
    private String ctmCoefficient;
    /**
     * CTM值系数value
     */
    @ApiModelProperty("CTM值系数value")
    private String ctmCoefficientValue;
    /**
     * 归档标识
     */
    @ApiModelProperty("归档标识值")
    @ExcelProperty(value = "归档标识值")
    private String archiveFlag;
    /**
     * 归档标识
     */
    @ApiModelProperty("归档标识")
    @ExcelProperty(value = "归档标识")
    private String archiveFlagName;

    /**
     * PMCODE LOV：SYS.PM_CODE
     */
    @ApiModelProperty("PMCODE LOV：SYS.PM_CODE")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_PM_CODE)
    @NotNull(message = "PMCODE不能为空", groups = ValidGroups.Insert.class)
    private Long pmcodeId;
    /**
     * PMCODE LOV：SYS.PM_CODE
     */
    @ApiModelProperty("PMCODE LOV：SYS.PM_CODE")
    @ExcelProperty(value = "PMCODE")
    @ImportExConvert(sql = ExLovTransConstant.PM_CODE_SQL)
    private String pmcodeIdName;
    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    @ExcelProperty(value = "备注信息")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id", notes = "")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;
}
