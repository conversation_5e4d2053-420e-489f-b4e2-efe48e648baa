package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("产品功率预测基准明细数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionBaseDetailDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 电池片系列代码
     */
    @ApiModelProperty("电池片系列代码")
    @ExcelProperty("电池片系列代码")
    private String cellSeriesCode;
    /**
     * 电池片系列代码
     */
    @ApiModelProperty("电池片系列代码")
    @ExcelProperty("电池片系列描述")
    private String cellSeriesDesc;
    /**
     * 材料搭配组合代码
     */
    @ApiModelProperty("材料搭配组合代码")
    @ExcelProperty(value = "材料搭配组合代码")
    private String materialCombinationCode;
    /**
     * 材料搭配组合描述
     */
    @ApiModelProperty("材料搭配组合描述")
    @ExcelProperty(value = "材料搭配组合描述")
    private String materialCombinationDesc;
    /**
     * 产品功率预测基准表id
     */
    @ApiModelProperty("产品功率预测基准表id")
    private Long predictionBaseId;
    /**
     * 片串 LOV：SYS.CELL_QUANTITY
     */
    @ApiModelProperty("片串id")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_CELL_QUANTITY)
    private Long cellStringId;
    /**
     * 片串 LOV：SYS.CELL_QUANTITY
     */
    @ApiModelProperty("片串 LOV：SYS.CELL_QUANTITY")
    @ExcelProperty(value = "片串")
    private String cellStringIdName;
    /**
     * 功率落档基本表版本
     */
    @ApiModelProperty("功率落档基本表版本")
    @ExcelProperty(value = "功率落档基本表版本")
    private String powerDeratingVersion;
    /**
     * 使用优先级
     */
    @ApiModelProperty("使用优先级")
    @ExcelProperty(value = "使用优先级")
    private Integer usagePriority;
    /**
     * 基准效率
     */
    @ApiModelProperty("基准效率")
    @ExcelProperty(value = "基准效率")
    private String baseEfficiency;
    /**
     * 扩展效率个数
     */
    @ApiModelProperty("扩展效率个数")
    @ExcelProperty(value = "扩展效率个数")
    private Integer efficiencyNum;
    /**
     * 面积
     */
    @ApiModelProperty("面积")
    @ExcelProperty(value = "面积")
    private BigDecimal area;
    /**
     * 基准CTM值
     */
    @ApiModelProperty("基准CTM值")
    @ExcelProperty(value = "基准CTM值")
    private String baseCtm;
    /**
     * CTM值系数
     */
    @ApiModelProperty("CTM值系数")
    @ExcelProperty(value = "CTM值系数")
    private String ctmCoefficient;
    /**
     * 效率
     */
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private String efficiency;
    /**
     * 序号 0：基准行
     */
    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号")
    private String serialNo;
    /**
     * CTM值
     */
    @ApiModelProperty("CTM值")
    @ExcelProperty(value = "CTM值")
    private String ctmValue;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")
    private BigDecimal power;
    /**
     * 落档1
     */
    @ApiModelProperty("落档1")
    @ExcelProperty(value = "落档1")
    private String derating1;
    /**
     * 落档1比率
     */
    @ApiModelProperty("落档1比率")
    @ExcelProperty(value = "落档1比率")
    private String derating1Ratio;
    /**
     * 落档2
     */
    @ApiModelProperty("落档2")
    @ExcelProperty(value = "落档2")
    private String derating2;
    /**
     * 落档2比率
     */
    @ApiModelProperty("落档2比率")
    @ExcelProperty(value = "落档2比率")
    private String derating2Ratio;

    /**
     * PMCODE LOV：SYS.PM_CODE
     */
    @ApiModelProperty("PMCODE id")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_PM_CODE)
    private Long pmcodeId;
    /**
     * PMCODE LOV：SYS.PM_CODE
     */
    @ApiModelProperty("PMCODE LOV：SYS.PM_CODE")
    @ExcelProperty(value = "PMCODE")
    private String pmcodeIdName;
    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    @ExcelProperty(value = "备注信息")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id", notes = "")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;

    /**
     * 单双玻 SYS.REAR_COVER_TYPE
     */
    @ApiModelProperty(value = "单双玻 SYS.REAR_COVER_TYPE")
    @Dict(headerCode = "SYS.REAR_COVER_TYPE")
    private Long sinDouId;
    /**
     * 单双玻描述
     */
    @ApiModelProperty(value = "单双玻描述")
    @ExcelProperty(value = "单双玻")
    private String sinDouIdName;
    /**
     * 主栅分类
     */
    @ApiModelProperty("主栅分类")
    @ExcelProperty(value = "主栅分类")
    private String gridLineType;
}
