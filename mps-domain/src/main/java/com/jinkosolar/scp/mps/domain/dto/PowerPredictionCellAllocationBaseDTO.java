package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionCellAllocation;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemandDay;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;


@ApiModel("功率预测电池分配表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionCellAllocationBaseDTO {

    private List<PowerPredictionCellAllocationDTO> cellAllocationList;

    private List<LocalDate> dayList;

}