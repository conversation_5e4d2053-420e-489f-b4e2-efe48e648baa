package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionCellAllocation;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.util.List;


@ApiModel("功率预测电池分配表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionCellAllocationDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * APS订单代码
     */
    @ApiModelProperty("APS订单代码")
    @ExcelProperty(value = "APS订单代码")
    private String apsOrderCode;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshopsCode;
    /**
     * 车间描述
     */
    @ApiModelProperty("车间描述")
    @ExcelProperty(value = "车间描述")
    private String workshopsDesc;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNo;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")
    private String sapLineId;
    /**
     * 电池产品编码
     */
    @ApiModelProperty("电池产品编码")
    @ExcelProperty(value = "电池产品编码")
    private String cellProductCode;
    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数")
    @ExcelProperty(value = "主栅数")
    private String gridNum;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产开始时间")
    private LocalDateTime scheduleStartDate;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产结束时间")
    private LocalDateTime scheduleEndDate;
    /**
     * 是否5W分档
     */
    @ApiModelProperty("是否5W分档")
    @ExcelProperty(value = "是否5W分档")
    private String fiveWBinnedFlag;
    /**
     * 5W分档组
     */
    @ApiModelProperty("5W分档组")
    @ExcelProperty(value = "5W分档组")
    private String fiveWBinned;
    /**
     * 赠送功率
     */
    @ApiModelProperty("赠送功率")
    @ExcelProperty(value = "赠送功率")
    private String giftsPower;
    /**
     * 高档比例
     */
    @ApiModelProperty("高档比例")
    @ExcelProperty(value = "高档比例")
    private String highGrade;
    /**
     * 低档比例
     */
    @ApiModelProperty("低档比例")
    @ExcelProperty(value = "低档比例")
    private String lowGrade;
    /**
     * 高档瓦数
     */
    @ApiModelProperty("高档瓦数")
    @ExcelProperty(value = "高档瓦数")
    private String highWattage;
    /**
     * 低档瓦数
     */
    @ApiModelProperty("低档瓦数")
    @ExcelProperty(value = "低档瓦数")
    private String lowWattage;
    /**
     * 组件颜色
     */
    @ApiModelProperty("组件颜色")
    @ExcelProperty(value = "组件颜色")
    private String moduleColor;
    /**
     * 片串
     */
    @ApiModelProperty("片串")
    @ExcelProperty(value = "片串")
    private String cellString;
    /**
     * 使用电池片系列描述
     */
    @ApiModelProperty("使用电池片系列描述")
    @ExcelProperty(value = "使用电池片系列描述")
    private String applyCellSeriesDesc;
    /**
     * 使用功率预测版本
     */
    @ApiModelProperty("使用功率预测版本")
    @ExcelProperty(value = "使用功率预测版本")
    private String applyPowerVersion;
    /**
     * 使用材料搭配组合描述
     */
    @ApiModelProperty("使用材料搭配组合描述")
    @ExcelProperty(value = "使用材料搭配组合描述")
    private String applyMaterialCombinationDesc;
    /**
     * 标准电池效率
     */
    @ApiModelProperty("标准电池效率")
    @ExcelProperty(value = "标准电池效率")
    private BigDecimal cellEfficiency;
    /**
     * APS排产日期
     */
    @ApiModelProperty("APS排产日期")
    @ExcelProperty(value = "APS排产日期")
    private LocalDate apsPlanDate;
    /**
     * APS排产年月
     */
    @ApiModelProperty("APS排产年月")
    @ExcelProperty(value = "APS排产年月")
    private String apsMonth;
    /**
     * 排产区域id
     */
    @ApiModelProperty("排产区域id")
    @ExcelProperty(value = "排产区域id")
    private Long areaId;
    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称")
    private String areaDesc;
    /**
     * 可用日期
     */
    @ApiModelProperty("可用日期")
    @ExcelProperty(value = "可用日期")
    private LocalDate invDate;
    /**
     * 可用日期所在年月
     */
    @ApiModelProperty("可用日期所在年月")
    @ExcelProperty(value = "可用日期所在年月")
    private String invDateMonth;
    /**
     * 供应优先级
     */
    @ApiModelProperty("供应优先级")
    @ExcelProperty(value = "供应优先级")
    private String supplyPriority;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")
    private String dataType;
    /**
     * 需求分类
     */
    @ApiModelProperty("需求分类")
    @ExcelProperty(value = "需求分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DEMAND_TYPE, queryColumns = {"lovValue"}, from = {"lovName"}, to = {"demandTypeName"})
    private String demandType;
    /**
     * 需求分类
     */
    @ApiModelProperty("需求分类")
    @ExcelProperty(value = "需求分类")
    private String demandTypeName;
    /**
     * 特性分类
     */
    @ApiModelProperty("特性分类")
    @ExcelProperty(value = "特性分类")
    private String specialType;
    /**
     * 组件工厂代码
     */
    @ApiModelProperty("组件工厂代码")
    @ExcelProperty(value = "组件工厂代码")
    private String factoryCode;
    /**
     * 组件工厂代码
     */
    @ApiModelProperty("组件工厂描述")
    @ExcelProperty(value = "组件工厂描述")
    private String factoryDesc;
    /**
     * 生产工厂代码
     */
    @ApiModelProperty("生产工厂代码")
    @ExcelProperty(value = "生产工厂代码")
    private String productionFactoryCode;
    /**
     * 生产工厂描述
     */
    @ApiModelProperty("生产工厂描述")
    @ExcelProperty(value = "生产工厂描述")
    private String productionFactoryDesc;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    private String isDirectional;
    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    @ExcelProperty(value = "是否监造")
    private String supervisionFlag;
    /**
     * 是否验货
     */
    @ApiModelProperty("是否验货")
    @ExcelProperty(value = "是否验货")
    private String customerInspectionFlag;
    /**
     * 指定硅料厂家
     */
    @ApiModelProperty("指定硅料厂家")
    @ExcelProperty(value = "指定硅料厂家")
    private String specifyFactory;
    /**
     * 法碳标识
     */
    @ApiModelProperty("法碳标识")
    @ExcelProperty(value = "法碳标识")
    private String frenchCarbonLabel;
    /**
     * 零碳标识
     */
    @ApiModelProperty("零碳标识")
    @ExcelProperty(value = "零碳标识")
    private String zeroCarbonLabel;
    /**
     * 特殊法碳标识
     */
    @ApiModelProperty("特殊法碳标识")
    @ExcelProperty(value = "特殊法碳标识")
    private String specialFrenchCarbonLabel;
    /**
     * 特殊零碳标识
     */
    @ApiModelProperty("特殊零碳标识")
    @ExcelProperty(value = "特殊零碳标识")
    private String specialZeroCarbonLabel;
    /**
     * 直流电池需求工厂
     */
    @ApiModelProperty("直流电池需求工厂")
    @ExcelProperty(value = "直流电池需求工厂")
    private String galvanicCellFactory;
    /**
     * 效率
     */
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private BigDecimal efficiencyValue;
    /**
     * 需求效率
     */
    @ApiModelProperty("需求效率")
    @ExcelProperty(value = "需求效率")
    private BigDecimal demandEfficiency;
    /**
     * 电池片需求数量
     */
    @ApiModelProperty("电池片需求数量")
    @ExcelProperty(value = "电池片需求数量")
    private BigDecimal cellDemandQty;
    /**
     * 供应数量
     */
    @ApiModelProperty("供应数量")
    @ExcelProperty(value = "供应数量")
    private BigDecimal quantity;
    /**
     * 分解后的数量
     */
    @ApiModelProperty("分解后的数量")
    @ExcelProperty(value = "分解后的数量")
    private BigDecimal splitQuantity;
    /**
     * 本次分配数量
     */
    @ApiModelProperty("本次分配数量")
    @ExcelProperty(value = "本次分配数量")
    private BigDecimal allocationQuantity;

    /**
     * 特性分类名称
     */
    @ApiModelProperty("特性分类名称")
    private String specialTypeName;

    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    private BigDecimal planQty;

    /**
     * 供应类型
     */
    @ApiModelProperty("供应类型")
    private String dataTypeName;

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String itemCode;

    /**
     * 供应ID
     */
    @ApiModelProperty("供应ID")
    private Long supplyId;

    /**
     * 按天汇总需求ID
     */
    @ApiModelProperty("需求ID")
    private Long demandDayId;

    /**
     * 各效率总分配数量
     */
    @ApiModelProperty("各效率总分配数量")
    private BigDecimal totalSplitQuantity;

    /**
     * 抹平后的数量差异
     */
    @ApiModelProperty("抹平前的数量差异")
    private BigDecimal totalSplitDiffQuantityBefor;

    /**
     * 抹平后的数量差异
     */
    @ApiModelProperty("抹平后的数量差异")
    private BigDecimal totalSplitDiffQuantity;


    @ApiModelProperty("是否监造/验货通用标识")
    @Transient
    private String commonFlag;

    /**
     * 动态列数据
     */
    @Transient
    private List<PowerPredictionCellAllocationDTO> dataList;

    /**
     * 组件颜色名称
     */
    @ApiModelProperty("组件颜色名称")
    @Transient
    private String moduleColorName;

    /**
     * 分配类型
     */
    @ApiModelProperty("分配类型")
    @Translate(DictType = LovHeaderCodeConstant.MPS_ALLOCATION_TYPE, queryColumns = {"lovValue"}, from = {"lovName"}, to = {"typeName"})
    private String type;

    /**
     * 分配类型名称
     */
    @ApiModelProperty("分配类型名称")
    private String typeName;

    /**
     * 单玻/双玻
     */
    @ApiModelProperty("单玻/双玻")
    private String oddEven;

    /**
     * 指定硅料供应商厂家
     */
    @ApiModelProperty("指定硅料供应商厂家")
    private String specifySupplier;
}