package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;  


@ApiModel("功率预测&电池分配_需求整合表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionDemandDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * APS订单代码
     */
    @ApiModelProperty("APS订单代码")
    @ExcelProperty(value = "APS订单代码")  
    private String apsOrderCode;
    /**
     * 工厂lovId
     */
    @ApiModelProperty("工厂lovId")
    @ExcelProperty(value = "工厂lovId")  
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")  
    private String factoryDesc;
    /**
     * 车间id
     */
    @ApiModelProperty("车间id")
    @ExcelProperty(value = "车间id")  
    private Long workshopId;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshopsCode;
    /**
     * 车间描述
     */
    @ApiModelProperty("车间描述")
    @ExcelProperty(value = "车间描述")  
    private String workshopsDesc;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")  
    private Long workCenterId;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")  
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")  
    private String workCenterDesc;
    /**
     * 排产区域id
     */
    @ApiModelProperty("排产区域id")
    @ExcelProperty(value = "排产区域id")  
    private Long areaId;
    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称")  
    private String areaDesc;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")  
    private String sapOrderNo;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")  
    private String sapLineId;
    /**
     * 电池产品id
     */
    @ApiModelProperty("电池产品id")
    @ExcelProperty(value = "电池产品id")  
    private Long cellProductId;
    /**
     * 电池产品编码
     */
    @ApiModelProperty("电池产品编码")
    @ExcelProperty(value = "电池产品编码")  
    private String cellProductCode;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产开始时间")  
    private LocalDateTime scheduleStartDate;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产结束时间")  
    private LocalDateTime scheduleEndDate;
    /**
     * APS排产日期
     */
    @ApiModelProperty("APS排产日期")
    @ExcelProperty(value = "APS排产日期")  
    private LocalDate apsPlanDate;
    /**
     * APS排产年月
     */
    @ApiModelProperty("APS排产年月")
    @ExcelProperty(value = "APS排产年月")  
    private String apsMonth;
    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @ExcelProperty(value = "排产数量")  
    private BigDecimal planQty;
    /**
     * 是否5W分档
     */
    @ApiModelProperty("是否5W分档")
    @ExcelProperty(value = "是否5W分档")  
    private String fiveWBinnedFlag;
    /**
     * 5W分档组
     */
    @ApiModelProperty("5W分档组")
    @ExcelProperty(value = "5W分档组")  
    private String fiveWBinned;
    /**
     * 赠送功率
     */
    @ApiModelProperty("赠送功率")
    @ExcelProperty(value = "赠送功率")  
    private String giftsPower;
    /**
     * 高档比例
     */
    @ApiModelProperty("高档比例")
    @ExcelProperty(value = "高档比例")  
    private String highGrade;
    /**
     * 低档比例
     */
    @ApiModelProperty("低档比例")
    @ExcelProperty(value = "低档比例")  
    private String lowGrade;
    /**
     * 高档瓦数
     */
    @ApiModelProperty("高档瓦数")
    @ExcelProperty(value = "高档瓦数")  
    private String highWattage;
    /**
     * 低档瓦数
     */
    @ApiModelProperty("低档瓦数")
    @ExcelProperty(value = "低档瓦数")  
    private String lowWattage;
    /**
     * 组件颜色
     */
    @ApiModelProperty("组件颜色")
    @ExcelProperty(value = "组件颜色")  
    private String moduleColor;
    /**
     * 片串
     */
    @ApiModelProperty("片串")
    @ExcelProperty(value = "片串")  
    private String cellString;
    /**
     * 使用电池片系列描述
     */
    @ApiModelProperty("使用电池片系列描述")
    @ExcelProperty(value = "使用电池片系列描述")  
    private String applyCellSeriesDesc;
    /**
     * 使用功率预测版本
     */
    @ApiModelProperty("使用功率预测版本")
    @ExcelProperty(value = "使用功率预测版本")  
    private String applyPowerVersion;
    /**
     * 使用材料搭配组合描述
     */
    @ApiModelProperty("使用材料搭配组合描述")
    @ExcelProperty(value = "使用材料搭配组合描述")  
    private String applyMaterialCombinationDesc;
    /**
     * 标准电池效率
     */
    @ApiModelProperty("标准电池效率")
    @ExcelProperty(value = "标准电池效率")
    private BigDecimal cellEfficiency;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")  
    private String isDirectional;
    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    @ExcelProperty(value = "是否监造")  
    private String supervisionFlag;
    /**
     * 是否验货
     */
    @ApiModelProperty("是否验货")
    @ExcelProperty(value = "是否验货")  
    private String customerInspectionFlag;
    /**
     * 指定硅料厂家
     */
    @ApiModelProperty("指定硅料供应商品牌")
    @ExcelProperty(value = "指定硅料供应商品牌")
    private String specifyFactory;
    /**
     * 法碳标识
     */
    @ApiModelProperty("法碳标识")
    @ExcelProperty(value = "法碳标识")  
    private String frenchCarbonLabel;
    /**
     * 零碳标识
     */
    @ApiModelProperty("零碳标识")
    @ExcelProperty(value = "零碳标识")  
    private String zeroCarbonLabel;
    /**
     * 特殊法碳标识
     */
    @ApiModelProperty("特殊法碳标识")
    @ExcelProperty(value = "特殊法碳标识")  
    private String specialFrenchCarbonLabel;
    /**
     * 特殊零碳标识
     */
    @ApiModelProperty("特殊零碳标识")
    @ExcelProperty(value = "特殊零碳标识")  
    private String specialZeroCarbonLabel;
    /**
     * 直流电池需求工厂
     */
    @ApiModelProperty("直流电池需求工厂")
    @ExcelProperty(value = "直流电池需求工厂")  
    private String galvanicCellFactory;
    /**
     * 需求分类
     */
    @ApiModelProperty("需求分类")
    @ExcelProperty(value = "需求分类")  
    private String demandType;
    /**
     * 特性分类
     */
    @ApiModelProperty("特性分类")
    @ExcelProperty(value = "特性分类")  
    private String specialType;
    /**
     * 电池片需求数量
     */
    @ApiModelProperty("电池片需求数量")
    @ExcelProperty(value = "电池片需求数量")  
    private BigDecimal cellDemandQty;
    /**
     * 高档需求数量
     */
    @ApiModelProperty("高档需求数量")
    @ExcelProperty(value = "高档需求数量")  
    private BigDecimal highDemandQty;
    /**
     * 高档需求数量
     */
    @ApiModelProperty("高档需求数量")
    @ExcelProperty(value = "高档需求数量")  
    private BigDecimal lowDemandQty;

    /**
     * 最低可投入电池效率
     */
    @ApiModelProperty("最低可投入电池效率")
    @ExcelProperty(value = "最低可投入电池效率")
    private BigDecimal lowCellEfficiency;

    /**
     * 最高可投入电池效率
     */
    @ApiModelProperty("最高可投入电池效率")
    @ExcelProperty(value = "最高可投入电池效率")
    private BigDecimal highCellEfficiency;

    /**
     * 功率落档基表版本
     */
    @ApiModelProperty("功率落档基表版本")
    @ExcelProperty(value = "功率落档基表版本")  
    private String powerDeratingVersion;
    /**
     * 效率
     */
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private BigDecimal efficiencyValue;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")  
    private BigDecimal powerValue;
    /**
     * 重算后功率
     */
    @ApiModelProperty("重算后功率")
    @ExcelProperty(value = "重算后功率")  
    private BigDecimal resetPower;
    /**
     * 落档1
     */
    @ApiModelProperty("落档1")
    @ExcelProperty(value = "落档1")  
    private String derating1;
    /**
     * 落档1比率
     */
    @ApiModelProperty("落档1比率")
    @ExcelProperty(value = "落档1比率")  
    private String derating1Ratio;
    /**
     * 落档2
     */
    @ApiModelProperty("落档2")
    @ExcelProperty(value = "落档2")  
    private String derating2;
    /**
     * 落档2比率
     */
    @ApiModelProperty("落档2比率")
    @ExcelProperty(value = "落档2比率")  
    private String derating2Ratio;

    /**
     * 单玻标识
     */
    @ApiModelProperty("单玻标识id")
    private Long singleGlassIdentificationId;
    /**
     * 单玻标识
     */
    @ApiModelProperty("单玻标识")
    @ExcelProperty(value = "单玻标识")
    private String singleGlassIdentification;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商厂家")
    private Long specifySupplier;

    /**
     * 指定硅料供应商厂家名称
     */
    @ApiModelProperty("指定硅料供应商厂家名称")
    @ExcelProperty(value = "指定硅料供应商厂家名称")
    private String specifySupplierName;
}