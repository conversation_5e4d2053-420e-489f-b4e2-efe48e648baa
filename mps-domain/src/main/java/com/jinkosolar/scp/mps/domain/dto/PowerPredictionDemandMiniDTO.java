package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("功率预测&电池分配_需求整合表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionDemandMiniDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;

    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNo;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")
    private String sapLineId;

    /**
     * 效率
     */
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private BigDecimal efficiencyValue;

    /**
     * 分配值
     */
    @ApiModelProperty("分配值")
    @ExcelProperty(value = "分配值")
    private BigDecimal totalOccupancyQuantity;

}