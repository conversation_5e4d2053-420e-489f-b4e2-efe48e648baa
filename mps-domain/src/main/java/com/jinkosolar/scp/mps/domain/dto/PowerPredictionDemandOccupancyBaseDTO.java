package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemand;
import com.jinkosolar.scp.mps.domain.entity.PowerPredictionDemandOccupancy;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;


@ApiModel("功率预测&电池分配_需求整合表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionDemandOccupancyBaseDTO {

    List<PowerPredictionDemandOccupancyDTO> occupancyList;

    List<LocalDate> dayList;

    List<String> monthList;
}