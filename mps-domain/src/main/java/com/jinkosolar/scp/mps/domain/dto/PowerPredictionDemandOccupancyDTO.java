package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


@ApiModel("功率预测需求占用表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionDemandOccupancyDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * APS订单代码
     */
    @ApiModelProperty("APS订单代码")
    @ExcelProperty(value = "APS订单代码")
    private String apsOrderCode;
    /**
     * 电池产品编码
     */
    @ApiModelProperty("电池产品编码")
    @ExcelProperty(value = "电池产品编码")
    private String cellProductCode;
    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数")
    @ExcelProperty(value = "主栅数")
    private String gridNum;

    /**
     * 排产区域id
     */
    @ApiModelProperty("排产区域id")
    //@ExcelProperty(value = "排产区域id")
    private Long areaId;

    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域")
    private String areaDesc;

    /**
     * 需求分类
     */
    @ApiModelProperty("需求分类")
    @ExcelProperty(value = "需求分类")
    private String demandDataType;

    /**
     * 需求分类
     */
    @ApiModelProperty("需求分类描述")
    @ExcelProperty(value = "需求分类描述")
    private String demandDataTypeName;
    /**
     * 效率
     */
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private BigDecimal efficiencyValue;

    /**
     * 期初库存
     */
    @ApiModelProperty("期初库存")
    @ExcelProperty(value = "期初库存")
    private BigDecimal initInventoryQuantity;


    /**
     * 落档1
     */
    @ApiModelProperty("落档1")
    //@ExcelProperty(value = "落档1")
    private String derating1;
    /**
     * 落档1比率
     */
    @ApiModelProperty("落档1比率")
    //@ExcelProperty(value = "落档1比率")
    private String derating1Ratio;
    /**
     * 落档2
     */
    @ApiModelProperty("落档2")
    //@ExcelProperty(value = "落档2")
    private String derating2;
    /**
     * 落档2比率
     */
    @ApiModelProperty("落档2比率")
    //@ExcelProperty(value = "落档2比率")
    private String derating2Ratio;
    /**
     * 高出订单瓦数标识
     */
    @ApiModelProperty("高出订单瓦数标识")
    //@ExcelProperty(value = "高出订单瓦数标识")
    private String highOrderWattageFlag;
    /**
     * 标准电池效率行标识
     */
    @ApiModelProperty("标准电池效率行标识")
    //@ExcelProperty(value = "标准电池效率行标识")
    private String cellEfficiencyFlag;
    /**
     * 剩余库存可用总量
     */
    @ApiModelProperty("剩余库存可用总量")
    //@ExcelProperty(value = "剩余库存可用总量")
    private BigDecimal totalInventoryQuantity;
    /**
     * 库存占用数量
     */
    @ApiModelProperty("库存占用数量")
    //@ExcelProperty(value = "库存占用数量")
    private BigDecimal totalOccupancyQuantity;

    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    private String workshopsCode;
    /**
     * 车间描述
     */
    @ApiModelProperty("车间描述")
    private String workshopsDesc;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    private String workCenterDesc;
    /**
     * 电池分配到货区域id
     */
    @ApiModelProperty("电池分配到货区域id")
    private Long deliveryAreaId;
    /**
     * 电池分配到货区域描述
     */
    @ApiModelProperty("电池分配到货区域描述")
    private String deliveryAreaDesc;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    private String sapOrderNo;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    private String sapLineId;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    private LocalDateTime scheduleStartDate;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    private LocalDateTime scheduleEndDate;
    /**
     * 是否5W分档
     */
    @ApiModelProperty("是否5W分档")
    private String fiveWBinnedFlag;
    /**
     * 5W分档组
     */
    @ApiModelProperty("5W分档组")
    private String fiveWBinned;
    /**
     * 赠送功率
     */
    @ApiModelProperty("赠送功率")
    private String giftsPower;
    /**
     * 高档比例
     */
    @ApiModelProperty("高档比例")
    private String highGrade;
    /**
     * 低档比例
     */
    @ApiModelProperty("低档比例")
    private String lowGrade;
    /**
     * 高档瓦数
     */
    @ApiModelProperty("高档瓦数")
    private String highWattage;
    /**
     * 低档瓦数
     */
    @ApiModelProperty("低档瓦数")
    private String lowWattage;
    /**
     * 组件颜色
     */
    @ApiModelProperty("组件颜色")
    private String moduleColor;
    /**
     * 片串
     */
    @ApiModelProperty("片串")
    private String cellString;
    /**
     * 使用电池片系列描述
     */
    @ApiModelProperty("使用电池片系列描述")
    private String applyCellSeriesDesc;
    /**
     * 使用功率预测版本
     */
    @ApiModelProperty("使用功率预测版本")
    private String applyPowerVersion;
    /**
     * 使用材料搭配组合描述
     */
    @ApiModelProperty("使用材料搭配组合描述")
    private String applyMaterialCombinationDesc;
    /**
     * 标准电池效率
     */
    @ApiModelProperty("标准电池效率")
    private BigDecimal cellEfficiency;
    /**
     * 指定硅料厂家
     */
    @ApiModelProperty("指定硅料厂家")
    private String specifyFactory;

    //排产总量
    private BigDecimal planQuantitySummary;

    // 原始分解排产数量
    private BigDecimal originalSplitQuantity;
    // 分解排产数量
    private BigDecimal splitQuantity;

    // 需求数量
    private BigDecimal demandQuantity;

    // 供应数量
    private BigDecimal supplyQuantity;
    //电池片需求数量
    private BigDecimal cellDemandQty;
    //总库存占用量
    private BigDecimal occupancyQuantitySummary;

    // 剩余数量
    private BigDecimal balanceQuantity;

    /**
     * APS排产日期
     */
    private LocalDate apsPlanDate;

    /**
     * 排产数量
     */
    private BigDecimal planQuantity;

    /**
     * 排产区域集合
     */
    private List<Long> areaIdList;

    /**
     * 排产区域名称集合
     */
    private List<String> areaDescList;

    /**
     * 需求分类
     */
    @ApiModelProperty("需求分类")
    private String demandType;
    /**
     * 特性分类
     */
    @ApiModelProperty("特性分类")
    private String specialType;

    /**
     * 特性分类
     */
    @ApiModelProperty("特性分类名称")
    private String specialTypeName;

    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    private String isDirectional;
    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    private String supervisionFlag;
    /**
     * 是否验货
     */
    @ApiModelProperty("是否验货")
    private String customerInspectionFlag;
    /**
     * 法碳标识
     */
    @ApiModelProperty("法碳标识")
    private String frenchCarbonLabel;
    /**
     * 零碳标识
     */
    @ApiModelProperty("零碳标识")
    private String zeroCarbonLabel;
    /**
     * 特殊法碳标识
     */
    @ApiModelProperty("特殊法碳标识")
    private String specialFrenchCarbonLabel;
    /**
     * 特殊零碳标识
     */
    @ApiModelProperty("特殊零碳标识")
    private String specialZeroCarbonLabel;
    /**
     * 直流电池需求工厂
     */
    @ApiModelProperty("直流电池需求工厂")
    private String galvanicCellFactory;

    /**
     * 组件工厂代码
     */
    @ApiModelProperty("组件工厂代码")
    private String factoryCode;

    /**
     * 组件工厂描述
     */
    @ApiModelProperty("组件工厂描述")
    private String factoryDesc;

    /**
     * 分类数据排序
     */
    private String demandOrder;

    /**
     * 分配优先级
     */
    private String basePriorityOrder;

    /**
     * 按天汇总需求ID
     */
    private Long demandDayId;

    /**
     * APS排产日期年月
     */
    private String planMonth;

    // 自产数量
    private BigDecimal productQuantity;

    // 自产调拨计划
    private BigDecimal productTransferPlanQuantity;

    // 外购数量
    private BigDecimal outsourceQuantity;

    // 差异数量
    private BigDecimal diffQuantity;

    // 累计结存数量
    private BigDecimal initSummaryQuantity;

    // 分解后的瓦片数量
    private BigDecimal planWattageQty;

    private List<PowerPredictionDemandOccupancyDTO> dataList;

    public PowerPredictionDemandOccupancyDTO group() {
        PowerPredictionDemandOccupancyDTO bean = new PowerPredictionDemandOccupancyDTO();
        bean.setCellProductCode(this.cellProductCode);
        bean.setEfficiencyValue(this.efficiencyValue);
        bean.setGridNum(this.gridNum);
        return bean;
    }

    public String yearMonth(){
        return DateUtil.getMonth(this.apsPlanDate);
    }

    public String sortFieldsDesc(){
        return "SAP订单号:["+this.apsOrderCode+"],SAP订单号行号:["+this.sapLineId+"],oaps排产日期:["+this.getApsPlanDate()+"],需求分类:["+this.getDemandType()+"],特性分类:["+this.getSpecialType()+"],排产开始时间:["+this.getScheduleStartDate()+"],电池效率:["+this.getCellEfficiency() +"]";
    }
}