package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.dto.system.FileParam;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@ApiModel("产品功率预测版本数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionReleaseDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 产品功率预测基准表id
     */
    @ApiModelProperty(value = "产品功率预测基准表id")
    @Column(name = "prediction_base_id")
    private Long predictionBaseId;

    /**
     * 产品功率预测基准表id
     */
    @ApiModelProperty(value = "产品功率预测基准表明细id")
    @Column(name = "prediction_base_detail_id")
    private Long predictionBaseDetailId;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String version;
    /**
     * 电池片系列代码
     */
    @ApiModelProperty("电池片系列代码")
    @ExcelProperty(value = "电池片系列代码")
    private String cellSeriesCode;
    /**
     * 电池片系列描述
     */
    @ApiModelProperty("电池片系列描述")
    @ExcelProperty(value = "电池片系列描述")
    private String cellSeriesDesc;
    /**
     * 晶体类型
     */
    @ApiModelProperty("晶体类型")
    @ExcelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * 电池尺寸
     */
    @ApiModelProperty("电池尺寸")
    @ExcelProperty(value = "电池尺寸")
    private String cellSize;
    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数")
    @ExcelProperty(value = "主栅数")
    private String mainGridNum;
    /**
     * 单双封
     */
    @ApiModelProperty("单双封")
    @ExcelProperty(value = "单双封")
    private String sealType;
    /**
     * 材料搭配组合代码
     */
    @ApiModelProperty("材料搭配组合代码")
    @ExcelProperty(value = "材料搭配组合代码")
    private String materialCombinationCode;
    /**
     * 材料搭配组合描述
     */
    @ApiModelProperty("材料搭配组合描述")
    @ExcelProperty(value = "材料搭配组合描述")
    private String materialCombinationDesc;
    /**
     * 电池工艺
     */
    @ApiModelProperty("电池工艺")
    @ExcelProperty(value = "电池工艺")
    private String cellProcess;
    /**
     * 胶膜颜色
     */
    @ApiModelProperty("胶膜颜色")
    @ExcelProperty(value = "胶膜颜色")
    private String filmColor;
    /**
     * 线缆长度
     */
    @ApiModelProperty("线缆长度")
    @ExcelProperty(value = "线缆长度")
    private String cableLength;
    /**
     * 膜袋-是否贴膜
     */
    @ApiModelProperty("膜袋-是否贴膜")
    @ExcelProperty(value = "膜袋-是否贴膜")
    private String filmBagCoated;
    /**
     * 背板颜色
     */
    @ApiModelProperty("背板颜色")
    @ExcelProperty(value = "背板颜色")
    private String backsheetColor;
    /**
     * 正面玻璃-镀膜
     */
    @ApiModelProperty("正面玻璃-镀膜")
    @ExcelProperty(value = "正面玻璃-镀膜")
    private String frontGlassCoated;
    /**
     * 背面玻璃-是否网格
     */
    @ApiModelProperty("背面玻璃-是否网格")
    @ExcelProperty(value = "背面玻璃-是否网格")
    private String backGlassGrid;
    /**
     * 汇流条-是否反光
     */
    @ApiModelProperty("汇流条-是否反光")
    @ExcelProperty(value = "汇流条-是否反光")
    private String busbarReflective;
    /**
     * 互联条-直径
     */
    @ApiModelProperty("互联条-直径")
    @ExcelProperty(value = "互联条-直径")
    private String interconnectDiameter;
    /**
     * 片串
     */
    @ApiModelProperty("片串")
    @ExcelProperty(value = "片串")
    private String cellString;
    /**
     * 功率落档基本表版本
     */
    @ApiModelProperty("功率落档基本表版本")
    @ExcelProperty(value = "功率落档基本表版本")
    private String powerDeratingVersion;
    /**
     * 使用优先级
     */
    @ApiModelProperty("使用优先级")
    @ExcelProperty(value = "使用优先级")
    private Integer usagePriority;
    /**
     * 基准效率
     */
    @ApiModelProperty("基准效率")
    @ExcelProperty(value = "基准效率")
    private String baseEfficiency;
    /**
     * 扩展效率个数
     */
    @ApiModelProperty("扩展效率个数")
    @ExcelProperty(value = "扩展效率个数")
    private Integer efficiencyNum;
    /**
     * 面积
     */
    @ApiModelProperty("面积")
    @ExcelProperty(value = "面积")
    private BigDecimal area;
    /**
     * 基准CTM值
     */
    @ApiModelProperty("基准CTM值")
    @ExcelProperty(value = "基准CTM值")
    private String baseCtm;
    /**
     * CTM值系数
     */
    @ApiModelProperty("CTM值系数")
    @ExcelProperty(value = "CTM值系数")
    private String ctmCoefficient;
    /**
     * 效率
     */
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private String efficiency;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    @ExcelProperty(value = "序号")
    private String serialNo;
    /**
     * CTM值
     */
    @ApiModelProperty("CTM值")
    @ExcelProperty(value = "CTM值")
    private String ctmValue;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")
    private BigDecimal power;
    /**
     * 落档1
     */
    @ApiModelProperty("落档1")
    @ExcelProperty(value = "落档1")
    private String derating1;
    /**
     * 落档1比率
     */
    @ApiModelProperty("落档1比率")
    @ExcelProperty(value = "落档1比率")
    private String derating1Ratio;
    /**
     * 落档2
     */
    @ApiModelProperty("落档2")
    @ExcelProperty(value = "落档2")
    private String derating2;
    /**
     * 落档2比率
     */
    @ApiModelProperty("落档2比率")
    @ExcelProperty(value = "落档2比率")
    private String derating2Ratio;
    /**
     * PMCODE
     */
    @ApiModelProperty("PMCODE")
    @ExcelProperty(value = "PMCODE")
    private String pmcode;
    /**
     * 备注信息
     */
    @ApiModelProperty("备注信息")
    @ExcelProperty(value = "备注信息")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id", notes = "")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "邮件内容")
    private String mailContent;

    @ApiModelProperty(value = "接收人邮箱地址信息,默认按照,分隔")
    private String recipientNo;

    @ApiModelProperty(value = "抄送人邮箱地址信息,默认按照,分隔")
    private String copyTo;

    @ApiModelProperty(value = "主题")
    private String title;

    @ApiModelProperty(value = "附件列表")
    List<MultipartFile> multipartFiles;
    /**
     * 单双玻 SYS.REAR_COVER_TYPE
     */
    @ApiModelProperty(value = "单双玻 SYS.REAR_COVER_TYPE")
    @Dict(headerCode = "SYS.REAR_COVER_TYPE")
    private Long sinDouId;
    /**
     * 单双玻描述
     */
    @ApiModelProperty(value = "单双玻描述")
    @ExcelProperty(value = "单双玻")
    private String sinDouIdName;
    /**
     * 主栅分类
     */
    @ApiModelProperty("主栅分类")
    @ExcelProperty(value = "主栅分类")
    private String gridLineType;
    /**
     * 围字贴：MPS.WEIZITIEMO
     */
    @ApiModelProperty(value = "围字贴：MPS.WEIZITIEMO")
    @Translate(DictType = LovHeaderCodeConstant.MPS_WEIZITIEMO, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"surroundingFilmName"})
    private Long surroundingFilmId;
    /**
     * 围字贴：MPS.WEIZITIEMO
     */
    @ApiModelProperty(value = "围字贴：MPS.WEIZITIEMO")
    @ExcelProperty(value = "围字贴")
    private String surroundingFilmName;
    /**
     * 膜带性能：ATTR_TYPE_015_ATTR_1900
     */
    @ApiModelProperty(value = "膜带性能：ATTR_TYPE_015_ATTR_1900")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_015_ATTR_1900, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"filmBagPerfName"})
    private Long filmBagPerfId;
    /**
     * 膜带性能：ATTR_TYPE_015_ATTR_1900
     */
    @ApiModelProperty(value = "膜带性能：ATTR_TYPE_015_ATTR_1900")
    @ExcelProperty(value = "膜带性能")
    private String filmBagPerfName;
    /**
     * 正玻厚度：ATTR_TYPE_005_ATTR_1500
     */
    @ApiModelProperty(value = "正玻厚度：ATTR_TYPE_005_ATTR_1500")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_005_ATTR_1500, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"frontGlassThicknessName"})
    private Long frontGlassThicknessId;
    /**
     * 正玻厚度：ATTR_TYPE_005_ATTR_1500
     */
    @ApiModelProperty(value = "正玻厚度：ATTR_TYPE_005_ATTR_1500")
    @ExcelProperty(value = "正玻厚度")
    private String frontGlassThicknessName;
    /**
     * 线缆长度 LOV：MPS.CABLE_LENGTH
     */
    @ApiModelProperty(value = "线缆长度 LOV：MPS.CABLE_LENGTH")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CABLE_LENGTH, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"cableLengthName"})
    private Long cableLengthId;
    /**
     * 线缆长度 LOV：MPS.CABLE_LENGTH
     */
    @ApiModelProperty(value = "线缆长度 LOV：MPS.CABLE_LENGTH")
    @ExcelProperty(value = "线缆长度")
    private String cableLengthName;
    /**
     * 汇流条规格：ATTR_TYPE_009_ATTR_2000
     */
    @ApiModelProperty(value = "汇流条规格：ATTR_TYPE_009_ATTR_2000")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_009_ATTR_2000, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"busBarSpecName"})
    private Long busBarSpecId;
    /**
     * 汇流条规格：ATTR_TYPE_009_ATTR_2000
     */
    @ApiModelProperty(value = "汇流条规格：ATTR_TYPE_009_ATTR_2000")
    @ExcelProperty(value = "汇流条规格")
    private String busBarSpecName;
    /**
     * 功率落档基本表
     */
    @ApiModelProperty("功率落档基本表")
    private List<PowerDeratingDTO> powerDeratingList;

    /**
     * 用户主动上传的文件转换后对象
     */
    private List<FileParam> customFileParams;
}
