package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@ApiModel("产品功率预测版本数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class PowerPredictionReleaseMESDTO extends JipRequestData implements Serializable {

    @JSONField(
            name = "IT_DATA"
    )
    private ReleaseHeader releaseHeader = new ReleaseHeader();

    @Getter
    @Setter
    public static class ReleaseHeader  {
        /**
         * 版本号
         */
        @ApiModelProperty("版本号")
        private String version;

        @ApiModelProperty(value = "页码,从1开始")
        private Integer pageNumber = 1;

        @ApiModelProperty(value = "每页条数")
        private Integer pageSize = 100;
    }
}
