package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.data.domain.Page;

import java.io.Serializable;

@Setter
@Getter
@ApiModel("产品功率预测")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class PowerPredictionReleaseMESResponseDTO extends JipResponseData implements Serializable {

    /**
     * 返回数据
     */
    @JSONField(name = "ET_DATA")
    private Page<PowerPredictionReleaseDTO> releasePageList;
}
