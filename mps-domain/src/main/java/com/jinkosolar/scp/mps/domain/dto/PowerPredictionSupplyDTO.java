package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("功率预测&电池分配_供应数据表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PowerPredictionSupplyDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")  
    private String dataType;
    /**
     * 存储工厂lovId
     */
    @ApiModelProperty("存储工厂lovId")
    @ExcelProperty(value = "存储工厂lovId")  
    private Long factoryId;
    /**
     * 存储工厂代码
     */
    @ApiModelProperty("存储工厂代码")
    @ExcelProperty(value = "存储工厂代码")  
    private String factoryCode;
    /**
     * 存储工厂描述
     */
    @ApiModelProperty("存储工厂描述")
    @ExcelProperty(value = "存储工厂描述")  
    private String factoryDesc;
    /**
     * 库存地点编码
     */
    @ApiModelProperty("库存地点编码")
    @ExcelProperty(value = "库存地点编码")  
    private String invSiteCode;
    /**
     * 库存地点描述
     */
    @ApiModelProperty("库存地点描述")
    @ExcelProperty(value = "库存地点描述")  
    private String invSiteName;
    /**
     * 物料表itemId
     */
    @ApiModelProperty("物料表itemId")
    @ExcelProperty(value = "物料表itemId")  
    private Long itemId;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")  
    private String itemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String itemDesc;
    /**
     * 基本计量单位
     */
    @ApiModelProperty("基本计量单位")
    @ExcelProperty(value = "基本计量单位")  
    private String uom;
    /**
     * 可用日期
     */
    @ApiModelProperty("可用日期")
    @ExcelProperty(value = "可用日期")  
    private LocalDate invDate;
    /**
     * 可用日期所在旬
     */
    @ApiModelProperty("可用日期所在旬")
    @ExcelProperty(value = "可用日期所在旬")  
    private String invDatePeriod;
    /**
     * 可用日期所在年月
     */
    @ApiModelProperty("可用日期所在年月")
    @ExcelProperty(value = "可用日期所在年月")  
    private String invDateMonth;
    /**
     * 供应优先级
     */
    @ApiModelProperty("供应优先级")
    @ExcelProperty(value = "供应优先级")  
    private String supplyPriority;
    /**
     * 供应效率
     */
    @ApiModelProperty("供应效率")
    @ExcelProperty(value = "供应效率")  
    private String supplyEfficiency;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")  
    private String batchNo;
    /**
     * 特殊库存标记
     */
    @ApiModelProperty("特殊库存标记")
    @ExcelProperty(value = "特殊库存标记")  
    private String specialStockFlag;
    /**
     * 生产工厂代码
     */
    @ApiModelProperty("生产工厂代码")
    @ExcelProperty(value = "生产工厂代码")  
    private String productionFactoryCode;
    /**
     * 生产工厂名称
     */
    @ApiModelProperty("生产工厂名称")
    @ExcelProperty(value = "生产工厂名称")  
    private String productionFactoryDesc;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 占用数量
     */
    @ApiModelProperty("占用数量")
    @ExcelProperty(value = "占用数量")
    private BigDecimal occupancyQuantity;
    /**
     * 电池分配数量
     */
    @ApiModelProperty("电池分配数量")
    @ExcelProperty(value = "电池分配数量")
    private BigDecimal allocationQuantity;
    /**
     * 远期电池分配数量
     */
    @ApiModelProperty("远期电池分配数量")
    @ExcelProperty(value = "远期电池分配数量")
    private BigDecimal forwardAllocationQuantity;
    /**
     * 远期电池抹平数量
     */
    @ApiModelProperty("远期电池抹平数量")
    @ExcelProperty(value = "远期电池抹平数量")
    private BigDecimal forwardSmoothQuantity;
    /**
     * 兆瓦数量
     */
    @ApiModelProperty("兆瓦数量")
    @ExcelProperty(value = "兆瓦数量")
    private BigDecimal quantityMw;
    /**
     * 远期兆瓦量
     */
    @ApiModelProperty("远期兆瓦量")
    @ExcelProperty(value = "远期兆瓦量")
    private BigDecimal forwardQuantityMw;
    /**
     * 电池颜色
     */
    @ApiModelProperty("电池颜色")
    @ExcelProperty(value = "电池颜色")  
    private String cellColor;
    /**
     * 外购库存供应商编码
     */
    @ApiModelProperty("外购库存供应商编码")
    @ExcelProperty(value = "外购库存供应商编码")  
    private String outVendorCode;
    /**
     * 外购库存供应商名称
     */
    @ApiModelProperty("外购库存供应商名称")
    @ExcelProperty(value = "外购库存供应商名称")  
    private String outVendorName;
    /**
     * 包装方式
     */
    @ApiModelProperty("包装方式")
    @ExcelProperty(value = "包装方式")  
    private String packageType;
    /**
     * 电池产品id
     */
    @ApiModelProperty("电池产品id")
    @ExcelProperty(value = "电池产品id")  
    private Long cellProductId;
    /**
     * 电池产品编码
     */
    @ApiModelProperty("电池产品编码")
    @ExcelProperty(value = "电池产品编码")  
    private String cellProductCode;
    /**
     * 电池等级
     */
    @ApiModelProperty("电池等级")
    @ExcelProperty(value = "电池等级")  
    private String cellLevel;
    /**
     * 电池效率
     */
    @ApiModelProperty("电池效率")
    @ExcelProperty(value = "电池效率")  
    private String cellEfficiency;
    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数")
    @ExcelProperty(value = "主栅数")  
    private String gridNum;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")  
    private String isDirectional;
    /**
     * 料号供应商编码
     */
    @ApiModelProperty("料号供应商编码")
    @ExcelProperty(value = "料号供应商编码")  
    private String itemVendorCode;
    /**
     * 料号供应商名称
     */
    @ApiModelProperty("料号供应商名称")
    @ExcelProperty(value = "料号供应商名称")  
    private String itemVendorDesc;
    /**
     * 硅料供应商品牌
     */
    @ApiModelProperty("硅料供应商品牌")
    @ExcelProperty(value = "硅料供应商品牌")  
    private String siliconVendorBrand;
    /**
     * 虚拟料号
     */
    @ApiModelProperty("虚拟料号")
    @ExcelProperty(value = "虚拟料号")  
    private String assemblyItemCode;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")  
    private String thickness;
    /**
     * 供应商品牌
     */
    @ApiModelProperty("供应商品牌")
    @ExcelProperty(value = "供应商品牌")  
    private String vendorBrand;
    /**
     * 晶科效率标准
     */
    @ApiModelProperty("晶科效率标准")
    @ExcelProperty(value = "晶科效率标准")  
    private BigDecimal jkEfficiency;
    /**
     * 电池分配到货区域id
     */
    @ApiModelProperty("电池分配到货区域id")
    @ExcelProperty(value = "电池分配到货区域id")  
    private Long deliveryAreaId;
    /**
     * 电池分配到货区域描述
     */
    @ApiModelProperty("电池分配到货区域描述")
    @ExcelProperty(value = "电池分配到货区域描述")  
    private String deliveryAreaDesc;
    /**
     * 排产区域id
     */
    @Translate(DictType = MpsLovConstant.LOCATION, queryColumns = {"lovLineId"}, from = {"lovValue", "lovName"}, to = {"areaCode", "areaDesc"})
    @ApiModelProperty("排产区域id")
    private Long areaId;
    /**
     * 排产区域code
     */
    @ApiModelProperty("排产区域code")
    private String areaCode;
    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    private String areaDesc;
    /**
     * 法碳标识
     */
    @ApiModelProperty("法碳标识")
    @ExcelProperty(value = "法碳标识")  
    private String frenchCarbonLabel;
    /**
     * 零碳标识
     */
    @ApiModelProperty("零碳标识")
    @ExcelProperty(value = "零碳标识")  
    private String zeroCarbonLabel;
    /**
     * 特殊法碳标识
     */
    @ApiModelProperty("特殊法碳标识")
    @ExcelProperty(value = "特殊法碳标识")  
    private String specialFrenchCarbonLabel;
    /**
     * 特殊零碳标识
     */
    @ApiModelProperty("特殊零碳标识")
    @ExcelProperty(value = "特殊零碳标识")  
    private String specialZeroCarbonLabel;
    /**
     * 比例
     */
    @ApiModelProperty("比例")
    @ExcelProperty(value = "比例")
    private BigDecimal proportion;

    /**
     * 单玻标识
     */
    @ApiModelProperty("电性能id")
    private Long singleGlassIdentificationId;
    /**
     * 电性能
     */
    @ApiModelProperty("电性能")
    @ExcelProperty(value = "单玻标识")
    private String singleGlassIdentification;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商厂家id")
    private Long specifySupplier;
    /**
     * 剩余需求数量
     */
    @ApiModelProperty("剩余需求数量")
    private BigDecimal surplusDemandQty;
    /**
     * 指定硅料供应商厂家
     */
    @ApiModelProperty("指定硅料供应商厂家")
    @ExcelProperty(value = "指定硅料供应商厂家名称")
    private String specifySupplierName;
    public String sign() {
        return StringUtils.join(this.areaCode, "-", this.cellProductCode, "-", this.invDate.getYear(), "-", this.invDate.getMonthValue());
    }
}