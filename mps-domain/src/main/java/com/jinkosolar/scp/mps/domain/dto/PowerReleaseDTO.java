package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: PowerReleaseDTO
 * @date 2024/9/2 11:07
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class PowerReleaseDTO {
    /**
     * 功率落档基本表版本
     */
    @ApiModelProperty("功率落档基本表版本")
    private String powerDeratingVersion;

    /**
     * 效率分布正式表
     */
    @ApiModelProperty("效率分布正式表")
    private List<PowerPredictionReleaseDTO> powerPredictionReleaseList;

    /**
     * 功率落档基本表
     */
    @ApiModelProperty("功率落档基本表")
    private List<PowerDeratingDTO> powerDeratingList;

    public PowerReleaseDTO(String powerDeratingVersion, List<PowerPredictionReleaseDTO> powerPredictionReleaseList) {
        this.powerDeratingVersion = powerDeratingVersion;
        this.powerPredictionReleaseList = powerPredictionReleaseList;
    }

    public static PowerReleaseDTO build(List<PowerPredictionReleaseDTO> powerPredictionReleaseList) {
        String version = powerPredictionReleaseList.stream().findAny().map(PowerPredictionReleaseDTO::getPowerDeratingVersion).get();
        return new PowerReleaseDTO(version, powerPredictionReleaseList);
    }
}
