package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;


/**
 * PowerResultLongGap
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 11:31:01
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PowerResultLongGap {

    private List<String> monthList;

    private List<PowerResultLongGapDTO> gapList;
}
