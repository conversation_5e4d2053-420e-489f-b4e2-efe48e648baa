package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.annotation.ExportConvert;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Map;


/**
 * PowerResultLongGapDTO
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-12-19 11:31:01
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PowerResultLongGapDTO {

    /**
     * 国内/海外
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.IS_OVERSEA)
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 产品族
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.FAMILY_CODE)
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamilyName;
    /**
     * 电池类型
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.CELL_TYPE)
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellTypeName;
    /**
     * 横竖装
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.CROSS_VERTICAL)
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installTypeName;
    /**
     * 功率版本
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.POWER_VERSION)
    @ApiModelProperty(value = "功率版本")
    private String versions;
    /**
     * 功率版本
     */
    @ApiModelProperty(value = "功率版本")
    private String versionsName;
    /**
     * 材料组合
     */
    @ApiModelProperty(value = "材料组合")
    private String materialCombination;
    /**
     * 动态列
     */
    @ApiModelProperty(value = "动态列")
    private Map subMap;


    public PowerResultLongGapDTO(String isOversea, String productFamily, String cellType, String installType, String versions) {
        this.isOversea = isOversea;
        this.productFamily = productFamily;
        this.cellType = cellType;
        this.installType = installType;
        this.versions = versions;
    }

    public PowerResultLongGapDTO(String isOversea, String productFamily, String cellType, String installType, String versions, String materialCombination) {
        this.isOversea = isOversea;
        this.productFamily = productFamily;
        this.cellType = cellType;
        this.installType = installType;
        this.versions = versions;
        this.materialCombination = materialCombination;
    }
}
