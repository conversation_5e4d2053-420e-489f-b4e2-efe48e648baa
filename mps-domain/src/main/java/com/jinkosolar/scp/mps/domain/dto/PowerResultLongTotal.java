package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 长期功率预测结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-29 04:31:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PowerResultLongTotal {

    @ApiModelProperty(value = "动态月份集合")
    private List<String> monthList;

    @ApiModelProperty(value = "结果集合")
    private List<PowerResultLongTotalDTO> totalList;

}
