package com.jinkosolar.scp.mps.domain.dto;

import com.google.common.collect.Maps;
import com.ibm.scp.common.api.annotation.ExportConvert;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 长期功率预测结果
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-29 04:31:01
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "长期功率预测结果DTO对象", description = "DTO对象")
public class PowerResultLongTotalDTO {

    /**
     * 国内/海外
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.IS_OVERSEA)
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 产品族
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.FAMILY_CODE)
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamilyName;
    /**
     * 横竖装
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.CROSS_VERTICAL)
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installTypeName;
    /**
     * 功率版本
     */
    @ExportConvert(lovCode = LovHeaderCodeConstant.POWER_VERSION)
    @ApiModelProperty(value = "功率版本")
    private String versions;
    /**
     * 功率版本
     */
    @ApiModelProperty(value = "功率版本")
    private String versionsName;
    /**
     * 材料组合
     */
    @ApiModelProperty(value = "材料组合")
    private String materialCombination;

    /**
     * 动态列
     */
    private Map subMap;


    public PowerResultLongTotalDTO group() {
       return new PowerResultLongTotalDTO();
    }

    public PowerResultLongTotalDTO(String isOversea, String productFamily, String installType, String versions) {
        this.isOversea = isOversea;
        this.productFamily = productFamily;
        this.installType = installType;
        this.versions = versions;
    }

    public PowerResultLongTotalDTO(String isOversea, String productFamily, String installType, String versions, String materialCombination) {
        this.isOversea = isOversea;
        this.productFamily = productFamily;
        this.installType = installType;
        this.versions = versions;
        this.materialCombination = materialCombination;
    }
}
