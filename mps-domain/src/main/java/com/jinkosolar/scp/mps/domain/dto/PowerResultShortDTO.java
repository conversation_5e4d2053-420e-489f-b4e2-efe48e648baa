package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 基础档位表（月度）查询
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:33:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerResultShortDTO对象", description = "DTO对象")
public class PowerResultShortDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    private List<Map<String, String>> dataStructures;

    private List<Map<String, String>> efficiencyStructures;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 横竖装名称
     */
    @ApiModelProperty(value = "横竖装名称")
    private String installTypeName;
    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    private String projectPlace;

    /**
     * 项目地名称
     */
    @ApiModelProperty(value = "项目地名称")
    private String projectPlaceName;
    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度")
    private String cableLength;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String dataVersion;
    /**
     * 版本号名称
     */
    @ApiModelProperty(value = "版本号名称")
    private String dataVersionName;
    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String moduleSize;

    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String moduleSizeName;
    /**
     * 功率
     */
    @ApiModelProperty(value = "功率")
    private BigDecimal power;

    private String powerName;

    private String resultShortCode;

    /**
     * 是否是功率行
     */
    @ApiModelProperty(value = "是否是功率行")
    private Integer isPowerLine;

    /**
     * 结果数据需要更新
     */
    @ApiModelProperty(value = "结果数据需要更新")
    private Integer isRecalcResultData;

    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1")
    private BigDecimal efficiency1;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency2;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency3;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency4;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency5;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency6;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency7;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency8;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency9;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency10;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency11;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency12;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency13;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency14;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2")
    private BigDecimal efficiency15;


    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16")
    private BigDecimal efficiency16;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17")
    private BigDecimal efficiency17;
    /**
     * 效率18
     */
    @ApiModelProperty(value = "效率18")
    private BigDecimal efficiency18;
    /**
     * 效率19
     */
    @ApiModelProperty(value = "效率19")
    private BigDecimal efficiency19;
    /**
     * 效率20
     */
    @ApiModelProperty(value = "效率20")
    private BigDecimal efficiency20;
    /**
     * 效率21
     */
    @ApiModelProperty(value = "效率21")
    private BigDecimal efficiency21;
    /**
     * 效率22
     */
    @ApiModelProperty(value = "效率22")
    private BigDecimal efficiency22;
    /**
     * 效率23
     */
    @ApiModelProperty(value = "效率23")
    private BigDecimal efficiency23;
    /**
     * 效率24
     */
    @ApiModelProperty(value = "效率24")
    private BigDecimal efficiency24;
    /**
     * 效率25
     */
    @ApiModelProperty(value = "效率25")
    private BigDecimal efficiency25;
    /**
     * 效率26
     */
    @ApiModelProperty(value = "效率26")
    private BigDecimal efficiency26;
    /**
     * 效率27
     */
    @ApiModelProperty(value = "效率27")
    private BigDecimal efficiency27;
    /**
     * 效率28
     */
    @ApiModelProperty(value = "效率28")
    private BigDecimal efficiency28;
    /**
     * 效率29
     */
    @ApiModelProperty(value = "效率29")
    private BigDecimal efficiency29;
    /**
     * 效率30
     */
    @ApiModelProperty(value = "效率30")
    private BigDecimal efficiency30;

    /**
     * 效率31
     */
    @ApiModelProperty(value = "效率31")
    private BigDecimal efficiency31;
    /**
     * 效率32
     */
    @ApiModelProperty(value = "效率32")
    private BigDecimal efficiency32;
    /**
     * 效率33
     */
    @ApiModelProperty(value = "效率33")
    private BigDecimal efficiency33;
    /**
     * 效率34
     */
    @ApiModelProperty(value = "效率34")
    private BigDecimal efficiency34;
    /**
     * 效率35
     */
    @ApiModelProperty(value = "效率35")
    private BigDecimal efficiency35;
    /**
     * 效率36
     */
    @ApiModelProperty(value = "效率36")
    private BigDecimal efficiency36;
    /**
     * 效率37
     */
    @ApiModelProperty(value = "效率37")
    private BigDecimal efficiency37;
    /**
     * 效率38
     */
    @ApiModelProperty(value = "效率38")
    private BigDecimal efficiency38;
    /**
     * 效率39
     */
    @ApiModelProperty(value = "效率39")
    private BigDecimal efficiency39;
    /**
     * 效率40
     */
    @ApiModelProperty(value = "效率40")
    private BigDecimal efficiency40;


    /**
     * 副标题1
     */
    @ApiModelProperty(value = "副标题1")
    private String subTitle1;
    /**
     * 副标题2
     */
    @ApiModelProperty(value = "副标题2")
    private String subTitle2;
    /**
     * 副标题3
     */
    @ApiModelProperty(value = "副标题3")
    private String subTitle3;
    /**
     * 副标题4
     */
    @ApiModelProperty(value = "副标题4")
    private String subTitle4;
    /**
     * 副标题5
     */
    @ApiModelProperty(value = "副标题5")
    private String subTitle5;
    /**
     * 副标题6
     */
    @ApiModelProperty(value = "副标题6")
    private String subTitle6;
    /**
     * 副标题7
     */
    @ApiModelProperty(value = "副标题7")
    private String subTitle7;
    /**
     * 副标题8
     */
    @ApiModelProperty(value = "副标题8")
    private String subTitle8;
    /**
     * 副标题9
     */
    @ApiModelProperty(value = "副标题9")
    private String subTitle9;
    /**
     * 副标题10
     */
    @ApiModelProperty(value = "副标题10")
    private String subTitle10;
    /**
     * 副标题11
     */
    @ApiModelProperty(value = "副标题11")
    private String subTitle11;
    /**
     * 副标题12
     */
    @ApiModelProperty(value = "副标题12")
    private String subTitle12;
    /**
     * 副标题13
     */
    @ApiModelProperty(value = "副标题13")
    private String subTitle13;
    /**
     * 副标题14
     */
    @ApiModelProperty(value = "副标题14")
    private String subTitle14;
    /**
     * 副标题15
     */
    @ApiModelProperty(value = "副标题15")
    private String subTitle15;

    /**
     * 副标题16
     */
    @ApiModelProperty(value = "副标题16")
    private String subTitle16;
    /**
     * 副标题17
     */
    @ApiModelProperty(value = "副标题17")
    private String subTitle17;
    /**
     * 副标题18
     */
    @ApiModelProperty(value = "副标题18")
    private String subTitle18;
    /**
     * 副标题19
     */
    @ApiModelProperty(value = "副标题19")
    private String subTitle19;
    /**
     * 副标题20
     */
    @ApiModelProperty(value = "副标题20")
    private String subTitle20;
    /**
     * 副标题21
     */
    @ApiModelProperty(value = "副标题21")
    private String subTitle21;
    /**
     * 副标题22
     */
    @ApiModelProperty(value = "副标题22")
    private String subTitle22;
    /**
     * 副标题23
     */
    @ApiModelProperty(value = "副标题23")
    private String subTitle23;
    /**
     * 副标题24
     */
    @ApiModelProperty(value = "副标题24")
    private String subTitle24;
    /**
     * 副标题25
     */
    @ApiModelProperty(value = "副标题25")
    private String subTitle25;
    /**
     * 副标题26
     */
    @ApiModelProperty(value = "副标题26")
    private String subTitle26;
    /**
     * 副标题27
     */
    @ApiModelProperty(value = "副标题27")
    private String subTitle27;
    /**
     * 副标题28
     */
    @ApiModelProperty(value = "副标题28")
    private String subTitle28;
    /**
     * 副标题29
     */
    @ApiModelProperty(value = "副标题29")
    private String subTitle29;
    /**
     * 副标题30
     */
    @ApiModelProperty(value = "副标题30")
    private String subTitle30;

    /**
     * 副标题31
     */
    @ApiModelProperty(value = "副标题31")
    private String subTitle31;
    /**
     * 副标题32
     */
    @ApiModelProperty(value = "副标题32")
    private String subTitle32;
    /**
     * 副标题33
     */
    @ApiModelProperty(value = "副标题33")
    private String subTitle33;
    /**
     * 副标题34
     */
    @ApiModelProperty(value = "副标题34")
    private String subTitle34;
    /**
     * 副标题35
     */
    @ApiModelProperty(value = "副标题35")
    private String subTitle35;
    /**
     * 副标题36
     */
    @ApiModelProperty(value = "副标题36")
    private String subTitle36;
    /**
     * 副标题37
     */
    @ApiModelProperty(value = "副标题37")
    private String subTitle37;
    /**
     * 副标题38
     */
    @ApiModelProperty(value = "副标题38")
    private String subTitle38;
    /**
     * 副标题39
     */
    @ApiModelProperty(value = "副标题39")
    private String subTitle39;
    /**
     * 副标题40
     */
    @ApiModelProperty(value = "副标题40")
    private String subTitle40;



    /**
     * 效率1
     */
    @ApiModelProperty(value = "效率1名称")
    private String efficiency1Name;
    /**
     * 效率2
     */
    @ApiModelProperty(value = "效率2名称")
    private String efficiency2Name;
    /**
     * 效率3名称
     */
    @ApiModelProperty(value = "效率3名称")
    private String efficiency3Name;
    /**
     * 效率4名称
     */
    @ApiModelProperty(value = "效率4名称")
    private String efficiency4Name;
    /**
     * 效率5名称
     */
    @ApiModelProperty(value = "效率5名称")
    private String efficiency5Name;
    /**
     * 效率6名称
     */
    @ApiModelProperty(value = "效率6名称")
    private String efficiency6Name;
    /**
     * 效率7名称
     */
    @ApiModelProperty(value = "效率7名称")
    private String efficiency7Name;
    /**
     * 效率8名称
     */
    @ApiModelProperty(value = "效率8名称")
    private String efficiency8Name;
    /**
     * 效率9名称
     */
    @ApiModelProperty(value = "效率9名称")
    private String efficiency9Name;
    /**
     * 效率10名称
     */
    @ApiModelProperty(value = "效率10名称")
    private String efficiency10Name;
    /**
     * 效率11名称
     */
    @ApiModelProperty(value = "效率11名称")
    private String efficiency11Name;
    /**
     * 效率12名称
     */
    @ApiModelProperty(value = "效率12名称")
    private String efficiency12Name;
    /**
     * 效率13名称
     */
    @ApiModelProperty(value = "效率13名称")
    private String efficiency13Name;
    /**
     * 效率14名称
     */
    @ApiModelProperty(value = "效率14名称")
    private String efficiency14Name;
    /**
     * 效率15名称
     */
    @ApiModelProperty(value = "效率15名称")
    private String efficiency15Name;

    /**
     * 效率16
     */
    @ApiModelProperty(value = "效率16名称")
    private String efficiency16Name;
    /**
     * 效率17
     */
    @ApiModelProperty(value = "效率17名称")
    private String efficiency17Name;
    /**
     * 效率18名称
     */
    @ApiModelProperty(value = "效率18名称")
    private String efficiency18Name;
    /**
     * 效率19名称
     */
    @ApiModelProperty(value = "效率19名称")
    private String efficiency19Name;
    /**
     * 效率20名称
     */
    @ApiModelProperty(value = "效率20名称")
    private String efficiency20Name;
    /**
     * 效率21名称
     */
    @ApiModelProperty(value = "效率21名称")
    private String efficiency21Name;
    /**
     * 效率22名称
     */
    @ApiModelProperty(value = "效率22名称")
    private String efficiency22Name;
    /**
     * 效率23名称
     */
    @ApiModelProperty(value = "效率23名称")
    private String efficiency23Name;
    /**
     * 效率24名称
     */
    @ApiModelProperty(value = "效率24名称")
    private String efficiency24Name;
    /**
     * 效率25名称
     */
    @ApiModelProperty(value = "效率25名称")
    private String efficiency25Name;
    /**
     * 效率26名称
     */
    @ApiModelProperty(value = "效率26名称")
    private String efficiency26Name;
    /**
     * 效率27名称
     */
    @ApiModelProperty(value = "效率27名称")
    private String efficiency27Name;
    /**
     * 效率28名称
     */
    @ApiModelProperty(value = "效率28名称")
    private String efficiency28Name;
    /**
     * 效率29名称
     */
    @ApiModelProperty(value = "效率29名称")
    private String efficiency29Name;
    /**
     * 效率30名称
     */
    @ApiModelProperty(value = "效率30名称")
    private String efficiency30Name;

    /**
     * 效率31名称
     */
    @ApiModelProperty(value = "效率31名称")
    private String efficiency31Name;
    /**
     * 效率32名称
     */
    @ApiModelProperty(value = "效率32名称")
    private String efficiency32Name;
    /**
     * 效率33名称
     */
    @ApiModelProperty(value = "效率33名称")
    private String efficiency33Name;
    /**
     * 效率34名称
     */
    @ApiModelProperty(value = "效率34名称")
    private String efficiency34Name;
    /**
     * 效率35名称
     */
    @ApiModelProperty(value = "效率35名称")
    private String efficiency35Name;
    /**
     * 效率36名称
     */
    @ApiModelProperty(value = "效率36名称")
    private String efficiency36Name;
    /**
     * 效率37名称
     */
    @ApiModelProperty(value = "效率37名称")
    private String efficiency37Name;
    /**
     * 效率38名称
     */
    @ApiModelProperty(value = "效率38名称")
    private String efficiency38Name;
    /**
     * 效率39名称
     */
    @ApiModelProperty(value = "效率39名称")
    private String efficiency39Name;
    /**
     * 效率40名称
     */
    @ApiModelProperty(value = "效率40名称")
    private String efficiency40Name;
    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20;


    /**
     * 预留1
     */
    @ApiModelProperty(value = "预留1")
    private String itemAttribute1Name;
    /**
     * 预留2
     */
    @ApiModelProperty(value = "预留2")
    private String itemAttribute2Name;
    /**
     * 预留3
     */
    @ApiModelProperty(value = "预留3")
    private String itemAttribute3Name;
    /**
     * 预留4
     */
    @ApiModelProperty(value = "预留4")
    private String itemAttribute4Name;
    /**
     * 预留5
     */
    @ApiModelProperty(value = "预留5")
    private String itemAttribute5Name;
    /**
     * 预留6
     */
    @ApiModelProperty(value = "预留6")
    private String itemAttribute6Name;
    /**
     * 预留7
     */
    @ApiModelProperty(value = "预留7")
    private String itemAttribute7Name;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8Name;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9Name;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10Name;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11Name;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12Name;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13Name;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14Name;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15Name;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16Name;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17Name;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18Name;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19Name;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20Name;
}
