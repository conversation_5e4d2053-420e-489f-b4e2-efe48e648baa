package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 功率预测预警清单
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-07 09:32:50
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerWarningDTO对象", description = "DTO对象")
public class PowerWarningDTO extends BaseDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 步骤主键
     */
    @ApiModelProperty(value = "步骤主键")
    private Long stepId;
    /**
     * DP-ID
     */
    @ApiModelProperty(value = "DP-ID")
    private String dpId;
    /**
     * dpGroupId
     */
    @ApiModelProperty(value = "dpGroupId")
    private String dpGroupId;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String installType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String dataVersion;
    /**
     * 符合率
     */
    @ApiModelProperty(value = "符合率")
    private BigDecimal passPercent;
    /**
     * 副产物比例
     */
    @ApiModelProperty(value = "副产物比例")
    private BigDecimal byProductPercent;
    /**
     * 降档比例
     */
    @ApiModelProperty(value = "降档比例")
    private BigDecimal downshiftPercent;

    /**
     * 符合率名称
     */
    @ApiModelProperty(value = "符合率名称")
    private String passPercentName;
    /**
     * 副产物比例名称
     */
    @ApiModelProperty(value = "副产物比例名称")
    private String byProductPercentName;
    /**
     * 降档比例名称
     */
    @ApiModelProperty(value = "降档比例名称")
    private String downshiftPercentName;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;

    /**
     * 横竖装名称
     */
    @ApiModelProperty(value = "横竖装名称")
    private String installTypeName;
    /**
     * 版本号名称
     */
    @ApiModelProperty(value = "版本号名称")
    private String dataVersionName;

    /**
     * 预警类型
     */
    @ApiModelProperty(value = "预警类型")
    private Integer warningType;

    /**
     * 预警类型名称
     */
    @ApiModelProperty(value = "预警类型名称")
    private String warningTypeName;

    private List<Map<String, String>> dataStructures;


    /**
     * dp渠道
     */
    @ApiModelProperty(value = "dp渠道")
    private String dpChannel;
    /**
     * dp渠道
     */
    @ApiModelProperty(value = "dp渠道")
    private String dpChannelName;

    /**
     * dp特殊需求单号
     */
    @ApiModelProperty(value = "dp特殊需求单号")
    private String dpSpecialSn;


    /**
     * dp客户id
     */
    @ApiModelProperty(value = "dp客户id")
    private Long dpCustomerId;

    /**
     * dp客户名称
     */
    @ApiModelProperty(value = "dp客户名称")
    private String dpCustomerName;

    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20;

    /**
     * 焊带
     */
    @ApiModelProperty(value = "焊带")
    private String itemAttribute1Name;
    /**
     * 前玻璃
     */
    @ApiModelProperty(value = "前玻璃")
    private String itemAttribute2Name;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute3Name;
    /**
     * EVA
     */
    @ApiModelProperty(value = "EVA")
    private String itemAttribute4Name;
    /**
     * 后玻璃
     */
    @ApiModelProperty(value = "后玻璃")
    private String itemAttribute5Name;
    /**
     * 反光汇流条
     */
    @ApiModelProperty(value = "反光汇流条")
    private String itemAttribute6Name;
    /**
     * 汇流条厚度
     */
    @ApiModelProperty(value = "汇流条厚度")
    private String itemAttribute7Name;
    /**
     * 预留8
     */
    @ApiModelProperty(value = "预留8")
    private String itemAttribute8Name;
    /**
     * 预留9
     */
    @ApiModelProperty(value = "预留9")
    private String itemAttribute9Name;
    /**
     * 预留10
     */
    @ApiModelProperty(value = "预留10")
    private String itemAttribute10Name;
    /**
     * 预留11
     */
    @ApiModelProperty(value = "预留11")
    private String itemAttribute11Name;
    /**
     * 预留12
     */
    @ApiModelProperty(value = "预留12")
    private String itemAttribute12Name;
    /**
     * 预留13
     */
    @ApiModelProperty(value = "预留13")
    private String itemAttribute13Name;
    /**
     * 预留14
     */
    @ApiModelProperty(value = "预留14")
    private String itemAttribute14Name;
    /**
     * 预留15
     */
    @ApiModelProperty(value = "预留15")
    private String itemAttribute15Name;
    /**
     * 预留16
     */
    @ApiModelProperty(value = "预留16")
    private String itemAttribute16Name;
    /**
     * 预留17
     */
    @ApiModelProperty(value = "预留17")
    private String itemAttribute17Name;
    /**
     * 预留18
     */
    @ApiModelProperty(value = "预留18")
    private String itemAttribute18Name;
    /**
     * 预留19
     */
    @ApiModelProperty(value = "预留19")
    private String itemAttribute19Name;
    /**
     * 预留20
     */
    @ApiModelProperty(value = "预留20")
    private String itemAttribute20Name;
}
