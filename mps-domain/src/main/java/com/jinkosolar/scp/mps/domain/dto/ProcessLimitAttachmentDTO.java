package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ApiModel("工艺限制规则表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitAttachmentDTO implements Serializable {


    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    @ExcelProperty(value = "附件名称")
    private String fileName;

    /**
     * 附件base64编码
     */
    @ApiModelProperty("附件base64编码")
    @ExcelProperty(value = "附件base64编码")
    private String fileBase64Str;

}