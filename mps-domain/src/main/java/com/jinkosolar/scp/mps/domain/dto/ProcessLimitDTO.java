package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.TranslateCustomBean;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;

@ApiModel("工艺限制主表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitDTO extends BaseDTO implements Serializable {

    /**
     * 限制编号
     */
    @ApiModelProperty("限制编号")
    @ExcelProperty(value = "限制编号")
    private Long id;
    /**
     * 限制范围,10:物料/20:产品
     */
    @ApiModelProperty("限制范围,10:物料/20:产品")
    @ExcelProperty(value = "限制范围,10:物料/20:产品")
    private String limitScopeCode;
    /**
     * 限制范围名称
     */
    @ApiModelProperty("限制范围名称")
    @ExcelProperty(value = "限制范围名称")
    private String limitScopeName;
    /**
     * 限制范围id
     */
    @ApiModelProperty("限制范围id")
    @ExcelProperty(value = "限制范围id")
    private Long limitScope;
    /**
     * 限定范围类型,10:所有,20:工厂30:车间,40:工作中心
     */
    @ApiModelProperty("限定范围类型,10:所有,20:工厂30:车间,40:工作中心")
    @ExcelProperty(value = "限定范围类型,10:所有,20:工厂30:车间,40:工作中心")
    private String limitScopeTypeCode;
    /**
     * 限定范围类型id
     */
    @ApiModelProperty("限定范围类型id")
    @ExcelProperty(value = "限定范围类型id")
    private Long limitScopeType;
    /**
     * 限定范围类型名称
     */
    @ApiModelProperty("限定范围类型名称")
    @ExcelProperty(value = "限定范围类型名称")
    private String limitScopeTypeName;
    /**
     * 申请类型,10:永久限制/20:临时限制
     */
    @ApiModelProperty("申请类型,10:永久限制/20:临时限制")
    @ExcelProperty(value = "申请类型,10:永久限制/20:临时限制")
    private String requestTypeCode;
    /**
     * 申请类型id
     */
    @ApiModelProperty("申请类型id")
    @ExcelProperty(value = "申请类型id")
    private Long requestType;
    /**
     * 申请类型名称
     */
    @ApiModelProperty("申请类型名称")
    @ExcelProperty(value = "申请类型名称")
    private String requestTypeName;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    @ExcelProperty(value = "完成时间")
    private LocalDate endDate;

    /**
     * OA单据号
     */
    @ApiModelProperty("OA单据号")
    @Column(name = "oa_receipt_no")
    private String oaReceiptNo;

    @Translate(DictType = LovHeaderCodeConstant.MRP_SEND_STATUS, from = {"lovValue", "lovName"}, to = {"approveStatusCode", "approveStatusName"})
    @ApiModelProperty("状态")
    @ExcelProperty(value = "状态")
    @ExportExConvert(tableName = "sys_lov_lines", fkColumnName = "lov_line_id", valueColumnName = "lov_name", targetFieldName = "approveStatusName")
    private Long approveStatus;
    @ApiModelProperty("状态编码")
    @ExcelProperty(value = "状态编码")
    private String approveStatusCode;
    @ApiModelProperty("状态名称")
    @ExcelProperty(value = "状态名称")
    private String approveStatusName;

    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicant;

    /**
     * 部门会签人员
     */
    @ApiModelProperty("部门会签人员")
    private String counterSignUsers;

    /**
     * 部门会签人员Id
     */
    @ApiModelProperty("部门会签人员Id")
    private String counterSignUserIds;

    /**
     * 可选部门会签人员
     */
    @ApiModelProperty("可选部门会签人员")
    private String optionalCounterSignUsers;
    /**
     * 可选部门会签人员Id
     */
    @ApiModelProperty("可选部门会签人员Id")
    private String optionalCounterSignUserIds;

    /**
     * 限制/解限责任人
     */
    @Translate(
            customBean = TranslateCustomBean.class,
            customMethod = "getUserNamesByIds",
            from = {"userId"},
            to = {"responsibleName"}
    )
    @ApiModelProperty("限制/解限责任人")
    private String responsible;

    /**
     * 限制/解限责任人名称
     */
    @ApiModelProperty("限制/解限责任人名称")
    private String responsibleName;

    /**
     * 限制/解限责任人
     */
    @ApiModelProperty("限制/解限责任人部门")
    private String responsibleDept;

    /**
     * 限制/解限责任人部门名称
     */
    @ApiModelProperty("限制/解限责任人部门名称")
    private String responsibleDeptName;

    /**
     * 限制/解限部门负责人
     */
    @Translate(
            customBean = TranslateCustomBean.class,
            customMethod = "getUserNamesByIds",
            from = {"userId"},
            to = {"responsibleDeptUserName"}
    )
    @ApiModelProperty("限制/解限部门负责人")
    private String responsibleDeptUser;

    /**
     * 限制/解限部门负责人名称
     */
    @ApiModelProperty("限制/解限部门负责人名称")
    private String responsibleDeptUserName;

    /**
     * 限制/解限原因
     */
    @ApiModelProperty("限制/解限原因")
    private String reason;

    /**
     * 解限方案
     */
    @ApiModelProperty("解限方案")
    private String programme;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 工作流ID
     */
    @ApiModelProperty("工作流ID")
    private String workflowId;

    /**
     * 解限单据号
     */
    @ApiModelProperty("解限单据号")
    private String releaseReceiptNo;
}