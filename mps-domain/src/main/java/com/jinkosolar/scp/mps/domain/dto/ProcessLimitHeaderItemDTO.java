package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "物料工艺限制明细保存参数", description = "保存参数")
public class ProcessLimitHeaderItemDTO implements Serializable {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("头表ID")
    private Long headerId;

    /**
     * 工厂
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;

    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 工作中心
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKCENTER)
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenter;
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    private String workCenterName;
    /**
     * 车间
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKSHOP)
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workShop;
    @ApiModelProperty("车间编码")
    @ExcelProperty(value = "车间编码")
    private String workShopCode;
    /**
     * 车间名称
     */
    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    private String workShopName;
}
