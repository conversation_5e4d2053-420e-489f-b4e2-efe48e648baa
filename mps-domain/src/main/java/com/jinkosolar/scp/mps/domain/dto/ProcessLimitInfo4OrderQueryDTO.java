package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;


@ApiModel("工艺限制主表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitInfo4OrderQueryDTO extends BaseDTO implements Serializable {

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenter;

    /**
     * 限制字段lovCode
     */
    @ApiModelProperty("限制字段lovCode")
    @ExcelProperty(value = "限制字段lovCode")
    private String limitFieldCode;

    /**
     * 限制字段lov名称
     */
    @ApiModelProperty("限制字段lov名称")
    @ExcelProperty(value = "限制字段lov名称")
    private String limitFieldName;

    /**
     * 申请类型id
     */
    @ApiModelProperty("申请类型id")
    @ExcelProperty(value = "申请类型id")
    private String requestTypeCode;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")
    private LocalDate startDate;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("结束时间")
    @ExcelProperty(value = "完成时间")
    private LocalDate endDate;
    /**
     * 限制字段lov值
     */
    @ApiModelProperty("限制字段值")
    private String limitAttrValue;

}