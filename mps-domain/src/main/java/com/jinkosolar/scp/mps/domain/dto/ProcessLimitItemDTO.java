package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("工艺限制明细表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitItemDTO extends BaseDTO implements Serializable {
    /**
     * 主表id
     */
    @ApiModelProperty("主表id")
    @ExcelProperty(value = "主表id")  
    private Long headerId;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 限制字段lov编码
     */
    @ApiModelProperty("限制字段lov编码")
    @ExcelProperty(value = "限制字段lov编码")  
    private String limitFieldCode;
    /**
     * 限制字段lovId
     */
    @ApiModelProperty("限制字段lovId")
    @ExcelProperty(value = "限制字段lovId")  
    private Long limitFieldId;
    /**
     * 限制字段lov名称
     */
    @ApiModelProperty("限制字段lov名称")
    @ExcelProperty(value = "限制字段lov名称")  
    private String limitFieldName;
    /**
     * 限制字段值
     */
    @ApiModelProperty("限制字段值")
    @ExcelProperty(value = "限制字段值")  
    private String limitFieldValue;
    /**
     * 限制明细行id
     */
    @ApiModelProperty("限制明细行id")
    @ExcelProperty(value = "限制明细行id")  
    private Long lineId;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factoryId;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenterId;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workShopId;
    /**
     * 限制字段lov名称
     */
    @ApiModelProperty("限制字段lov名称")
    private String limitAttrValue;
}