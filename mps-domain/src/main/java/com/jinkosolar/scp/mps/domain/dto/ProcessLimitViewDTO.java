package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@ApiModel("工艺限制列表显示对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitViewDTO extends ProcessLimitDTO implements Serializable {

    @ApiModelProperty("头表ID")
    private Long headerId;

    @ApiModelProperty("明细行ID")
    private Long lineId;

    @ApiModelProperty("电池类型名称")
    private String batteryTypeNames;

    @ApiModelProperty("计划版型名称")
    private String planVersionNames;

    @ApiModelProperty("申请人名称")
    private String applicantName;

    @ApiModelProperty("解限责任人名称")
    private String responsibleName;

    @ApiModelProperty("解限责任人部门名称")
    private String responsibleDeptName;

    @ApiModelProperty("解限/限制原因")
    private String reasonName;

    private String limitDetailInfo;

    /**
     * 工厂集合
     */
    @ApiModelProperty("工厂集合")
    @ExcelProperty(value = "工厂集合")
    private List<Long> factoryList;
    /**
     * 工厂名称集合
     */
    @ApiModelProperty("工厂名称集合")
    @ExcelProperty(value = "工厂名称集合")
    private String factoryNames;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workShop;
    /**
     * 车间名称
     */
    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    private String workShopName;
    /**
     * 车间集合
     */
    @ApiModelProperty("车间集合")
    @ExcelProperty(value = "车间集合")
    private List<Long> workShops;
    /**
     * 车间名称集合
     */
    @ApiModelProperty("车间名称集合")
    @ExcelProperty(value = "车间名称集合")
    private String workShopNames;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private List<Long> workCenters;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    private String workCenterNames;
}