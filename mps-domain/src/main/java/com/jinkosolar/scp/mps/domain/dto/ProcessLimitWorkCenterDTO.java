package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@ApiModel("工艺限制按工作中心维度显示对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitWorkCenterDTO extends ProcessLimitDTO implements Serializable {

    @ApiModelProperty("头表ID")
    private Long headerId;

    @ApiModelProperty("明细行ID")
    private Long lineId;

    @ApiModelProperty("电池类型名称")
    private List<String> batteryTypeNames;

    @ApiModelProperty("计划版型名称")
    private List<String> moduleTypeNames;

    @ApiModelProperty("申请人名称")
    private String applicantName;

    @ApiModelProperty("解限责任人名称")
    private String responsibleName;

    @ApiModelProperty("解限责任人部门名称")
    private String responsibleDeptName;

    @ApiModelProperty("解限/限制原因")
    private String reasonName;

    private String limitDetailInfo;


    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factory;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workShop;
    /**
     * 车间名称
     */
    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    private String workShopName;

    /**
     * 车间编码
     */
    @ApiModelProperty("车间编码")
    @ExcelProperty(value = "车间编码")
    private String workShopCode;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenter;
    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    private String workCenterName;
}