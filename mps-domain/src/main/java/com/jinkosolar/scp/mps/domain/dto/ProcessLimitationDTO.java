package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ImportConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


@ApiModel("工艺限制数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProcessLimitationDTO extends BaseDTO implements Serializable {
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")
    private String area;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @NotNull(message = "工厂代码不能为空", groups = {ValidGroups.Insert.class, ValidGroups.Update.class})
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;
    /**
     * 限制条件1
     */
    @ApiModelProperty("限制条件1")
    @ExcelProperty(value = "限制条件1")
    private String limitCondition1;
    /**
     * 限制条件2
     */
    @ApiModelProperty("限制条件2")
    @ExcelProperty(value = "限制条件2")
    private String limitCondition2;
    /**
     * 限制条件3
     */
    @ApiModelProperty("限制条件3")
    @ExcelProperty(value = "限制条件3")
    private String limitCondition3;
    /**
     * 限制条件4
     */
    @ApiModelProperty("限制条件4")
    @ExcelProperty(value = "限制条件4")
    private String limitCondition4;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")
    private Long workCenterId;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @NotNull(message = "工作中心不能为空", groups = {ValidGroups.Insert.class, ValidGroups.Update.class})
    @ExcelProperty(value = "工作中心代码")
    @ImportExConvert(sql = "select t.id from mps_work_center t where t.is_deleted = 0 and t.work_center_code = ?1", targetFieldName = "workCenterId")
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;
}