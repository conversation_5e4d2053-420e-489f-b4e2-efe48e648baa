package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 生产日历 DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "生产日历DTO对象", description = "DTO对象")
public class ProductCalendarDTO extends BaseDTO {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty(value = "工作中心描述")
    @ExcelProperty(value = "工作中心")
    @ImportExConvert(tableName = "mps_work_center", fkColumnName = "work_center_desc", valueColumnName = "work_center_code", targetFieldName = "workCenterCode")
    private String workCenterDesc;
    /**
     * 工作时间起
     */
    @ApiModelProperty(value = "工作时间起")
    @ExcelProperty(value = "日期起")
    private LocalDateTime workTimeFrom;
    /**
     * 工作时间止
     */
    @ApiModelProperty(value = "工作时间止")
    @ExcelProperty(value = "日期止")
    private LocalDateTime workTimeTo;
    /**
     * 出勤模式
     */
    @ApiModelProperty(value = "出勤模式")
    @ExcelProperty(value = "出勤模式")
    private String attendanceModel;

    /**
     * 出勤模式名称
     */
    @ApiModelProperty(value = "出勤模式名称")
    private String attendanceModelIdName;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @ExcelProperty(value = "优先级")
    private Long priority;
    /**
     * 产线数量
     */
    @ApiModelProperty(value = "产线数量")
    @ExcelProperty(value = "产线数量")
    private Integer productionLineQuantity;

}
