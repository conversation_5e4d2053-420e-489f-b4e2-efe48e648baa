package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel("投产方案对应的材料搭配对照关系数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductDescDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * 投产方案描述
     */
    @ApiModelProperty("投产方案描述")
    @ExcelProperty(value = "投产方案描述")
    private String productDesc;
}
