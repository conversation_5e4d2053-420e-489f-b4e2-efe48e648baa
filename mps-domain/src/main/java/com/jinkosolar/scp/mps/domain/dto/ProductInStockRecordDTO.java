package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("生产入库记录表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductInStockRecordDTO extends BaseDTO implements Serializable {
    /**
     * 过账日期
     */
    @ApiModelProperty("过账日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "过账日期")  
    private LocalDateTime budgeDate;
    /**
     * 移动类型(库存管理)
     */
    @ApiModelProperty("移动类型(库存管理)")
    @ExcelProperty(value = "移动类型(库存管理)")  
    private String bwart;
    /**
     * 凭证日期
     */
    @ApiModelProperty("凭证日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "凭证日期")  
    private LocalDateTime certDate;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")  
    private String factoryDesc;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")  
    private Long id;
    /**
     * 库存地点
     */
    @ApiModelProperty("库存地点")
    @ExcelProperty(value = "库存地点")  
    private String invSiteCode;
    /**
     * 库存地点描述
     */
    @ApiModelProperty("库存地点描述")
    @ExcelProperty(value = "库存地点描述")  
    private String invSiteName;
    /**
     * 入库日期
     */
    @ApiModelProperty("入库日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "入库日期")  
    private LocalDateTime inventoryDate;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")  
    private String inventoryLot;
    /**
     * 物料凭证编码
     */
    @ApiModelProperty("物料凭证编码")
    @ExcelProperty(value = "物料凭证编码")  
    private String itemCertCode;
    /**
     * 物料凭证项目
     */
    @ApiModelProperty("物料凭证项目")
    @ExcelProperty(value = "物料凭证项目")  
    private String itemCertProject;
    /**
     * 物料凭证年份
     */
    @ApiModelProperty("物料凭证年份")
    @ExcelProperty(value = "物料凭证年份")  
    private String itemCertYear;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")  
    private String itemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String itemDesc;
    /**
     * 入库日期
     */
    @ApiModelProperty("入库日期")
    @ExcelProperty(value = "入库日期")  
    private BigDecimal quantity;
    /**
     * 来源
     */
    @ApiModelProperty("来源")
    @ExcelProperty(value = "来源")  
    private String sourceType;
    /**
     * 基本计量单位
     */
    @ApiModelProperty("基本计量单位")
    @ExcelProperty(value = "基本计量单位")  
    private String uom;

    /**
     * 车间
     */
    @ApiModelProperty("车间")
    private String workShop;
    /**
     * 车间描述
     */
    @ApiModelProperty("车间描述")
    private String workShopDesc;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    private String workCenter;

    /**
     * segment6
     */
    @ApiModelProperty("segment6")
    private String segment6;

    /**
     * categorySegment3
     */
    @ApiModelProperty("categorySegment3")
    private String categorySegment3;

    /**
     * categorySegment3Name
     */
    @ApiModelProperty("categorySegment3Name")
    private String categorySegment3Name;

    /**
     * categorySegment4
     */
    @ApiModelProperty("categorySegment4")
    private String categorySegment4;

    /**
     * categorySegment5
     */
    @ApiModelProperty("categorySegment5")
    private String categorySegment5;
}