package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;                 
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("投产方案对应的材料搭配对照关系数据转换对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductMaterialCombinationMappingDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 材料搭配组合_id
     */
    @ApiModelProperty("材料搭配组合_id")
    @ExcelProperty(value = "材料搭配组合_id")
    private Long materialCombinationId;
    /**
     * 材料搭配组合描述
     */
    @ApiModelProperty("材料搭配组合描述")
    @ExcelProperty(value = "材料搭配组合描述")
    @ImportExConvert(sql = "select id from mps_power_material_combination where material_combination_desc = ?1 and is_deleted = 0", targetFieldName = "materialCombinationId")
    private String materialCombinationDesc;
    /**
     * 投产方案编码
     */
    @ApiModelProperty("投产方案编码")
    @ExcelProperty(value = "投产方案编码")  
    private String productCode;
    /**
     * 投产方案描述
     */
    @ApiModelProperty("投产方案描述")
    @ExcelProperty(value = "投产方案描述")  
    private String productDesc;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdByName;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedByName;
}