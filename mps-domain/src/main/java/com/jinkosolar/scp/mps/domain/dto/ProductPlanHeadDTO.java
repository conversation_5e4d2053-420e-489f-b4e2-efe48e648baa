package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("APS排产计划头数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPlanHeadDTO extends BaseDTO implements Serializable {
    /** 主键 */
    @ApiModelProperty(name = "主键",notes = "")
    private Long id ;
    /**
     * 指令代码
     */
    @ApiModelProperty("指令代码")
    @ExcelProperty(value = "指令代码")  
    private String instructionCode;
    /**
     * 指令种类
     */
    @ApiModelProperty("指令种类")
    @ExcelProperty(value = "指令种类")  
    private String instructionType;
    /**
     * 版型
     */
    @ApiModelProperty("版型")
    @ExcelProperty(value = "版型")  
    private String moduleType;
    /**
     * 工序代码
     */
    @ApiModelProperty("工序代码")
    @ExcelProperty(value = "工序代码")  
    private String procedureCode;
    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @ExcelProperty(value = "工序编号")  
    private String procedureNo;

}