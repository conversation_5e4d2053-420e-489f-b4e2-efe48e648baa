package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("APS排产计划行数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPlanLineDTO extends BaseDTO implements Serializable {
    /** 主键 */
    @ApiModelProperty(name = "主键",notes = "")
    private Long id ;
    /**
     * 制造产能
     */
    @ApiModelProperty("制造产能")
    @ExcelProperty(value = "制造产能")
    private BigDecimal capacity;
    /**
     * 制造产能
     */
    @ApiModelProperty("制造产能")
    @ExcelProperty(value = "制造产能")
    private BigDecimal hourCapacity;
    /**
     * 制造产能单位
     */
    @ApiModelProperty("制造产能单位")
    @ExcelProperty(value = "制造产能单位")
    private String capacityUnit;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开始日期")  
    private LocalDateTime fromDate;

    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @ExcelProperty(value = "结束日期")  
    private LocalDateTime toDate;
    /**
     * 利用率(带损失比例)
     */
    @ApiModelProperty("带损失比例")
    @ExcelProperty(value = "带损失比例")
    private BigDecimal useRatio;
    /**
     * 原始利用率(不带损失比例)
     */
    @ApiModelProperty("原始利用率(不带损失比例)")
    @ExcelProperty(value = "原始利用率(不带损失比例)")
    private BigDecimal oriUseRatio;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private Long workCenterId;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    private Long workshopId;

    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    private String workCenterCode;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshopCode;

    @ApiModelProperty("临时限制类型")
    private String limitType;

    @ApiModelProperty("产能替代类型:1.爬坡2.实验")
    private Integer type;

    /**
     * 版型
     */
    @ApiModelProperty("版型")
    private String moduleType;

    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    private Long dataType;

    /**
     * 指令代码
     */
    @ApiModelProperty("指令代码")
    @ExcelProperty(value = "指令代码")
    private String instructionCode;
    /**
     * 指令种类
     */
    @ApiModelProperty("指令种类")
    @ExcelProperty(value = "指令种类")
    private String instructionType;
    /**
     * 工序代码
     */
    @ApiModelProperty("工序代码")
    @ExcelProperty(value = "工序代码")
    private String procedureCode;
    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @ExcelProperty(value = "工序编号")
    private String procedureNo;
}