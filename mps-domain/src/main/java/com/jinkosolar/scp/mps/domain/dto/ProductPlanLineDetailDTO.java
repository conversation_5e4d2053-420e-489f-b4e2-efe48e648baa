package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@ApiModel("生产计划明细行(到天)数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductPlanLineDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版型
     */
    @ApiModelProperty("版型")
    @ExcelProperty(value = "版型")
    private Long moduleTypeId;
    /**
     * 版型
     */
    @ApiModelProperty("版型")
    @ExcelProperty(value = "版型")  
    private String moduleType;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂ID")
    @ExcelProperty(value = "工厂ID")  
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")  
    private Long workshopId;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshopCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private Long workCenterId;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")  
    private String workCenterCode;
    /**
     * 制造/产能
     */
    @ApiModelProperty("制造/产能")
    @ExcelProperty(value = "制造/产能")  
    private BigDecimal capacity;
    /**
     * 每小时产能
     */
    @ApiModelProperty("每小时产能")
    @ExcelProperty(value = "每小时产能")  
    private BigDecimal hourCapacity;
    /**
     * 产能单位
     */
    @ApiModelProperty("产能单位")
    @ExcelProperty(value = "产能单位")  
    private String capacityUnit;
    /**
     * 产能标识
     */
    @ApiModelProperty("产能标识")
    @ExcelProperty(value = "产能标识")  
    private Integer type;

    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开始日期")  
    private LocalDateTime fromDate;
    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "结束日期")  
    private LocalDateTime toDate;
    /**
     * 利用率
     */
    @ApiModelProperty("利用率")
    @ExcelProperty(value = "利用率")  
    private BigDecimal useRatio;

    @ApiModelProperty("产能年月")
    @ExcelProperty(value = "产能年月")
    private String capacityYearMonth;

    /**
     * 利用率
     */
    @ApiModelProperty("利用率")
    @ExcelProperty(value = "利用率")
    private LocalDate capacityDate;

    /**
     * 数据类型
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")
    private Long dataType;
    /**
     * 产能标识
     */
    @ApiModelProperty("区域名称")
    @ExcelProperty(value = "区域名称")
    private String dataTypeName;

    /**
     * 数据类型
     */
    @ApiModelProperty("工作车间描述")
    @ExcelProperty(value = "工作车间描述")
    private String workCenterDesc;


    /**
     * 数据类型
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")
    private String factoryDesc;


    private BigDecimal utilizeCapacity;

    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new HashMap<>();

}