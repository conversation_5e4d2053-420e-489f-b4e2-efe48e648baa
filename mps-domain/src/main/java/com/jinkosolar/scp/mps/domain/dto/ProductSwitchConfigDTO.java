package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;


@ApiModel("产品切换计划维护数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductSwitchConfigDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 排产区域
     */
    @NotNull(message = "排产区域不能为空", groups = {ValidGroups.Insert.class, ValidGroups.Update.class})
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA)
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")
    private Long domesticOversea;
    @ApiModelProperty("排产区域描述")
    @ExcelProperty(value = "排产区域描述")
    private String domesticOverseaName;
    /**
     * 产品类型
     */
    @NotNull(message = "产品类型不能为空", groups = {ValidGroups.Insert.class, ValidGroups.Update.class})
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1000)
    @ApiModelProperty("产品类型")
    @ExcelProperty(value = "产品类型")
    private Long productType;
    @ApiModelProperty("产品类型编码")
    @ExcelProperty(value = "产品类型编码")
    private String productTypeCode;
    @ApiModelProperty("产品类型描述")
    @ExcelProperty(value = "产品类型描述")
    private String productTypeName;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")
    private LocalDate startDate;
    /**
     * 尺寸(切换前)
     */
    @ApiModelProperty("尺寸(切换前)")
    @ExcelProperty(value = "尺寸(切换前)")
    private String beforeSize;
    /**
     * 尺寸(切换后)
     */
    @ApiModelProperty("尺寸(切换后)")
    @ExcelProperty(value = "尺寸(切换后)")
    private String afterSize;
    /**
     * 厚度(切换前)
     */
    @ApiModelProperty("厚度(切换前)")
    @ExcelProperty(value = "厚度(切换前)")
    private String beforeThickness;
    /**
     * 厚度(切换后)
     */
    @ApiModelProperty("厚度(切换后)")
    @ExcelProperty(value = "厚度(切换后)")
    private String afterThickness;
    /**
     * 倒角(切换前)
     */
    @ApiModelProperty("倒角(切换前)")
    @ExcelProperty(value = "倒角(切换前)")
    private String beforeChamfer;
    /**
     * 倒角(切换后)
     */
    @ApiModelProperty("倒角(切换后)")
    @ExcelProperty(value = "倒角(切换后)")
    private String afterChamfer;
    /**
     * 高低阻(切换前)
     */
    @ApiModelProperty("高低阻(切换前)")
    @ExcelProperty(value = "高低阻(切换前)")
    private String beforeCrystalType;
    /**
     * 高低阻(切换后)
     */
    @ApiModelProperty("高低阻(切换后)")
    @ExcelProperty(value = "高低阻(切换后)")
    private String afterCrystalType;
    /**
     * 扩展属性1
     */
    @ApiModelProperty("扩展属性1")
    @ExcelProperty(value = "扩展属性1")
    private String attribute1;
    /**
     * 扩展属性2
     */
    @ApiModelProperty("扩展属性2")
    @ExcelProperty(value = "扩展属性2")
    private String attribute2;
}