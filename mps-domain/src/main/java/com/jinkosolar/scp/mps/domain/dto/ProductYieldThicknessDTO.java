package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.LinkedHashMap;


@ApiModel("产品良率厚度信息表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductYieldThicknessDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;

    @ApiModelProperty("排产区域ID")
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"domesticOverseaName"})
    private Long domesticOverseaId;

    @ApiModelProperty("排产区域ID")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"domesticOverseaName"},
            from = {"lovLineId"}, to = {"domesticOverseaId"}, required = true)
    private String domesticOverseaName;
    /**
     * 产品ID
     */
    @ApiModelProperty("产品ID")
    @ExcelProperty(value = "产品ID")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"productTypeName"})
    private Long productTypeId;

    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400, queryColumns = {"productTypeName"},
            from = {"lovLineId"}, to = {"productTypeId"}, required = true)
    private String productTypeName;

    /**
     * 类型ID
     */
    @ApiModelProperty("类型ID")
    @ExcelProperty(value = "类型ID")
    @Translate(DictType = LovHeaderCodeConstant.MPS_Product_Information_Type, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"typeName"})
    private Long typeId;

    /**
     * 类型
     */
    @ApiModelProperty("类型")
    @ExcelProperty(value = "类型")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_Product_Information_Type, queryColumns = {"typeName"},
            from = {"lovLineId"}, to = {"typeId"}, required = true)
    private String typeName;

    /**
     * 尺寸ID
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1200, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sizeName"})
    private Long size;

    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1200, queryColumns = {"sizeName"},
            from = {"lovLineId"}, to = {"size"}, required = true)
    private String sizeName;

    /**
     * 分组 区分同区域同产品同尺寸下的类别值不同
     */
    @ApiModelProperty("分组")
    @Translate(required = true)
    private String groupFlag;

    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    private LocalDate month;
    /**
     * 月份值
     */
    @ApiModelProperty("月份值")
    @ExcelProperty(value = "月份值")  
    private String monthValue;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;

    /**
     * ID集合
     */
    @ApiModelProperty("ID集合")
    @ExcelProperty(value = "ID集合")
    private String ids;

    // 使用HashMap来存储ID和值
    private LinkedHashMap<String, String> monthIdMap = new LinkedHashMap<>();


    private Integer excelIndex;

    //计算错误标识
    private boolean isError;
}