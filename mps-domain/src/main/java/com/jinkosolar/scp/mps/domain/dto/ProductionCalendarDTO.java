package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.math.BigDecimal;


@ApiModel("生产日历数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductionCalendarDTO extends BaseDTO implements Serializable {
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private Long id;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")
    private Long workCenterId;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterIdCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + LovHeaderCodeConstant.SYS_WORKCENTER + "'"
            , targetFieldName = "workCenterId")
    private String workCenterIdName;
    /**
     * 日期/星期
     */
    @ApiModelProperty("日期/星期")
    @ExcelProperty(value = "日期/星期")  
    private String productionDate;
    /**
     * 出勤模式id
     */
    @ApiModelProperty("出勤模式id")
    private Long attendanceId;

    @ApiModelProperty("出勤模式")
    @ExcelProperty(value = "出勤模式")
    @ImportExConvert(sql = "SELECT id FROM mps_attendance t1 WHERE t1.is_deleted = 0 AND t1.attendance_mode = ?1 ",targetFieldName="attendanceId")
    private String attendanceIdName;
    /**
     * 优先级
     */
    @ApiModelProperty("优先级")
    @ExcelProperty(value = "优先级")  
    private String priority;
    /**
     * 资源量
     */
    @ApiModelProperty("资源量")
    @ExcelProperty(value = "资源量")  
    private BigDecimal resourceQuantity;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")  
    private String remark;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    private Long modelTypeId;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + LovHeaderCodeConstant.MPS_MODELTYPE + "'"
            , targetFieldName = "modelTypeId")
    private String modelTypeIdName;
}