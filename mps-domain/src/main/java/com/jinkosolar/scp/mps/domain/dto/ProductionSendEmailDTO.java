package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.dto.message.ModuleMsgReceiverDto;
import com.jinkosolar.scp.mps.domain.dto.message.MsgReceiverDto;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("组件排产计划发送邮件入参对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductionSendEmailDTO extends BaseDTO implements Serializable {
    /**
     * 定线规划
     */
    @ApiModelProperty("组件排产计划查询对象")
    private ModuleProductionPlanQuery query;
    /**
     * 工作中心
     */
    @ApiModelProperty("邮件入参对象")
    private ModuleMsgReceiverDto msgReceiverDto;

    @ApiModelProperty(value = "应用ID")
    private String applicationId;

    @ApiModelProperty(value = "基地ID")
    private Long baseId;

    @ApiModelProperty(value = "基地名称")
    private String baseName;
}