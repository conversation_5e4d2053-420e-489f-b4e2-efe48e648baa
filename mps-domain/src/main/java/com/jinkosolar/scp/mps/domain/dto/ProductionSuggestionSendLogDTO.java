package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产建议推送SAP日志
 *
 * <AUTHOR> chenc
 * @date : 2024-12-27
 */
@ApiModel(value = "生产建议推送SAP日志")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProductionSuggestionSendLogDTO extends PageDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;
    /**
     * 生产ID
     */
    @ApiModelProperty("生产ID")
    private Long productionId;
    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private Long batchId;
    /**
     * 类型
     */
    @ApiModelProperty("类型")
    private String type;
    /**
     * R:研发 P:大货
     */
    @ApiModelProperty("R:研发 P:大货")
    private String categoryType;
    /**
     * oem
     */
    @ApiModelProperty("oem")
    private String oem;
    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    private String directionalName;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String itemCode;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    private String factoryCode;
    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    private BigDecimal schedulingQty;
    /**
     *
     */
    @ApiModelProperty("")
    private String productType;
    /**
     * 生产车间编码
     */
    @ApiModelProperty("生产车间编码")
    private String workShopCode;
    /**
     * 生产车间描述
     */
    @ApiModelProperty("生产车间描述")
    private String workShopDesc;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    private String workCenterCode;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    private LocalDateTime schedulingStartTime;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    private LocalDateTime schedulingEndTime;
    /**
     * 推送SAP消息
     */
    @ApiModelProperty("推送SAP消息")
    private String sapResultMsg;
    /**
     * 消息类型: S 成功,E 错误,W 警告,I 信息,A 中断
     */
    @ApiModelProperty("消息类型: S 成功,E 错误,W 警告,I 信息,A 中断")
    private String sapResultType;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createdTime;

}