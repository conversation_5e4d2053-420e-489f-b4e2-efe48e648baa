package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;


@ApiModel("拉晶A级良率数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PullingYieldDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE,unTranslate = false)
    private String modelClassification;


    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"modelClassificationName"},
            from = {"lovLineId"}, to = {"modelClassification"})
    @ExcelProperty(value = "模型分类")
    private String modelClassificationName;



    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String version;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE,unTranslate = false)
    @ExcelProperty(value = "工厂")
    private Long factoryId;

    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"factoryCode"},
            from = {"lovLineId"}, to = {"factoryId"})
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")

    @ExcelProperty(value = "工厂描述")  
    private String factoryDesc;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryIdName;

    /**
     * 生产车间Id
     */
    @ApiModelProperty("生产车间Id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP,unTranslate = false)
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;


    /**
     * 生产车间名称
     */
    @ApiModelProperty("生产车间名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"workshopIdName"},
            from = {"lovLineId"}, to = {"workshopId"})
    @ExcelProperty(value = "生产车间名称")
    private String workshopIdName;


    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000,unTranslate = false)
    private Long productId;


    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"product"},
            from = {"lovLineId"}, to = {"productId"})
    @ExcelProperty(value = "产品")  
    private String product;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productIdName;



    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100,unTranslate = false)
    @ExcelProperty(value = "热场")  
    private String thermalField;


    /**
     * 热场名称
     */
    @ApiModelProperty("热场名称")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100, queryColumns = {"thermalFieldName"},
            from = {"lovLineId"}, to = {"thermalField"})
    @ExcelProperty(value = "热场名称")
    private String thermalFieldName;

    /**
     * 年
     */
    @ApiModelProperty("年")
    @ExcelProperty(value = "年")
    private Integer year;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")  
    private LocalDate month;
    /**
     * 月份良率
     */
    @ApiModelProperty("月份良率")
    @ExcelProperty(value = "月份良率")  
    private BigDecimal monthValue;

    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directionalId;

    /**
     * 是否定向名称
     */
    @ApiModelProperty("是否定向名称")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"directionalName"},
            from = {"lovLineId"}, to = {"directionalId"})
    private String directionalName;

    /**
     * 高低阻
     */
    @ApiModelProperty("高低阻")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"crystalTypeName"})
    private Long crystalTypeId;

    /**
     * 高低阻名称
     */
    @ApiModelProperty("高低阻名称")
    @Translate(unTranslate = true,required = true,DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"crystalTypeName"},
            from = {"lovLineId"}, to = {"crystalTypeId"})
    private String crystalTypeName;

    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new LinkedHashMap<>();
}