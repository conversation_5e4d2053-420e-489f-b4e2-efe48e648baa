package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 预测结果实际用料规则
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RealityMaterialRuleDTO对象", description = "DTO对象")
public class RealityMaterialRuleDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
    private String value;

    /**
     * 是否匹配预测
     */
    @ApiModelProperty(value = "是否匹配预测")
    private String isMatchPredict;

    private String isMatchPredictName;

    /**
     * 订单名称
     */
    @ApiModelProperty(value = "订单名称")
    private String orderName;

    /**
     * 是否监造
     */
    @ApiModelProperty(value = "是否监造")
    private String isSupervisor;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
