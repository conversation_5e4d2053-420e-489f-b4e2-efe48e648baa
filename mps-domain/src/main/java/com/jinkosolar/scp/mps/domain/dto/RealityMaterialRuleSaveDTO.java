package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 预测结果实际用料规则
 * <AUTHOR>
 * @date 2022-9-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RealityMaterialRule保存参数", description = "保存参数")
public class RealityMaterialRuleSaveDTO extends TokenDTO implements Serializable {
    /**
     *主键
     */
    @ApiModelProperty(value = "主键")
    private Long id ;

    /**
     *类型
     */
    @ApiModelProperty(value = "类型")
    private String type ;

    /**
     *值
     */
    @ApiModelProperty(value = "值")
    private String value ;

    /**
     *是否匹配预测
     */
    @ApiModelProperty(value = "是否匹配预测")
    private String isMatchPredict ;

    /**
     *订单名称
     */
    @ApiModelProperty(value = "订单名称")
    private String orderName ;

    /**
     *是否监造
     */
    @ApiModelProperty(value = "是否监造")
    private String isSupervisor ;

    /**
     *产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily ;

    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    private String remark ;

}
