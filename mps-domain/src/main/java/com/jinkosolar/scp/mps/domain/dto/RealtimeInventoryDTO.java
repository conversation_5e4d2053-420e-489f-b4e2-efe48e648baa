package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("实时库存表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RealtimeInventoryDTO extends PageDTO implements Serializable {
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")
    private String factoryDecs;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 库存地点
     */
    @ApiModelProperty("库存地点")
    @ExcelProperty(value = "库存地点")
    private String invSiteCode;
    /**
     * 库存地点描述
     */
    @ApiModelProperty("库存地点描述")
    @ExcelProperty(value = "库存地点描述")
    private String invSiteName;
    /**
     * 入库日期
     */
    @ApiModelProperty("入库日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "入库日期")
    private LocalDateTime inventoryDate;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")
    private String inventoryLot;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")
    private String itemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")
    private String itemDesc;
    /**
     * 外购供应商编码
     */
    @ApiModelProperty("外购供应商编码")
    @ExcelProperty(value = "外购供应商编码")
    private String poVendorCode;
    /**
     * 外购供应商名称
     */
    @ApiModelProperty("外购供应商名称")
    @ExcelProperty(value = "外购供应商名称")
    private String poVendorName;
    /**
     * 数量-非限制
     */
    @ApiModelProperty("数量-非限制")
    @ExcelProperty(value = "数量-非限制")
    private BigDecimal quantity;
    /**
     * 数量-冻结
     */
    @ApiModelProperty("数量-冻结")
    @ExcelProperty(value = "数量-冻结")
    private BigDecimal quantityS;
    /**
     * 数量-质检
     */
    @ApiModelProperty("数量-质检")
    @ExcelProperty(value = "数量-质检")
    private BigDecimal quantityX;
    /**
     * 销售订单号 E-销售特有
     */
    @ApiModelProperty("销售订单号 E-销售特有")
    @ExcelProperty(value = "销售订单号 E-销售特有")
    private String soHeaderNum;
    /**
     * 销售订单行号 E-销售特有
     */
    @ApiModelProperty("销售订单行号 E-销售特有")
    @ExcelProperty(value = "销售订单行号 E-销售特有")
    private String soLineNum;
    /**
     * 来源
     */
    @ApiModelProperty("来源")
    @ExcelProperty(value = "来源")
    private String sourceType;
    /**
     * 特殊库存标记 O-分包商，K- VMI，E-销售
     */
    @ApiModelProperty("特殊库存标记 O-分包商，K- VMI，E-销售")
    @ExcelProperty(value = "特殊库存标记 O-分包商，K- VMI，E-销售")
    private String specialStockFlag;
    /**
     * 基本计量单位
     */
    @ApiModelProperty("基本计量单位")
    @ExcelProperty(value = "基本计量单位")
    private String uom;
    /**
     * 供应商编号
     */
    @ApiModelProperty("供应商编号")
    @ExcelProperty(value = "供应商编号")
    private String vendorCode;
    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    @ExcelProperty(value = "供应商名称")
    private String vendorName;
}
