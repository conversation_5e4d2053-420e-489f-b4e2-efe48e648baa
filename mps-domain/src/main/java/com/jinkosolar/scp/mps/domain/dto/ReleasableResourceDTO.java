package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;


@ApiModel("可排资源数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReleasableResourceDTO extends BaseDTO implements Serializable {
    /**
     * 
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    @ExcelIgnore
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    @ExcelIgnore
    private String versionNumber;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"productTypeId"}, required = true)
    private String productTypeCode;

    @ExcelIgnore
    @ApiModelProperty("产品")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"productTypeCode","productTypeName"})
    private Long productTypeId;

    @ExcelIgnore
    @ApiModelProperty("产品名称")
    private String productTypeName;
    /**
     * 可排资源代码
     */
    @ApiModelProperty("可排资源代码")
    @ExcelProperty(value = "可排资源代码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workCenterId"})
    private String workCenterCode;

    @ExcelIgnore
    @ApiModelProperty("可排资源代码")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workCenterCode","workCenterName"})
    private Long workCenterId;

    @ExcelIgnore
    @ApiModelProperty("可排资源代码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workCenterId"}, required = true)
    private String workCenterName;
    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    @ExcelProperty(value = "开始日期")  
    private LocalDate startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    @ExcelProperty(value = "结束日期")  
    private LocalDate endDate;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"dataTypeId","dataTypeCode"}, required = true)
    private String dataTypeName;

    @ExcelIgnore
    @ApiModelProperty("数据分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataTypeId;

    @ExcelIgnore
    @ApiModelProperty("数据分类Code")
    private String dataTypeCode;
}