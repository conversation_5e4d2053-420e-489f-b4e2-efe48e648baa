package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("释放功率表Aps数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReleasePowerApsDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 销售订单号
     */
    @ApiModelProperty("销售订单号")
    @ExcelProperty(value = "销售订单号",index = 0)
    @ImportExConvert()
    private String sapOrderNo;
    /**
     * 行号
     */
    @ApiModelProperty("行号")
    @ExcelProperty(value = "行号",index = 1)
    @ImportExConvert()
    private String sapLineId;

    /**
     * 工厂_lovId
     */
    @ApiModelProperty("工厂_lovId")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},from={"lovName"}, to = {"factoryName"})
    @ExcelProperty(value = "工厂",index = 4)
    @ExportExConvert(tableName = "sys_lov_lines", fkColumnName = "lov_line_id", valueColumnName = "lov_name")
    private Long factoryId;
    /**
     * 排产工厂代码
     */
    @ApiModelProperty("排产工厂代码")
    @ExcelProperty(value = "排产工厂代码",index = 2)
    @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + LovHeaderCodeConstant.MPS_FACTORY + "'", targetFieldName = "factoryId")
    private String factoryCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;

    /**
     * 赠送功率
     */
    @ApiModelProperty("技术释放瓦数")
    @ExcelProperty(value = "释放瓦数",index = 3)
    @ImportExConvert()
    private String technologyReleasePower;

    /**
     * 国内/海外/山西
     */
    @ApiModelProperty("国内/海外/山西")
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA,queryColumns = {"lovLineId"},from={"lovValue","lovName"}, to = {"domesticOverseaCode","domesticOverseaName"})
    private String domesticOversea;
    /**
     * 国内/海外/山西
     */
    @ApiModelProperty("排产区域编码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"lovValue"},from={"lovLineId"}, to = {"domesticOversea"})
    private String domesticOverseaCode;
    /**
     * 国内/海外/山西
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称",index = 5)
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"lovName"},from={"lovLineId"}, to = {"domesticOversea"})
    private String domesticOverseaName;
}