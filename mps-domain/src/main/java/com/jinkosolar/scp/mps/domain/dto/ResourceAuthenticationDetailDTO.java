package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel("资源认证明细数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceAuthenticationDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")  
    private String factoryCode;
    /**
     * 车间编码
     */
    @ApiModelProperty("车间编码")
    @ExcelProperty(value = "车间编码")  
    private String workShopCode;
    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")  
    private String workCenterCode;
    /**
     * 产地认证
     */
    @ApiModelProperty("产地认证")
    @ExcelProperty(value = "产地认证")  
    private String certcatName;

    /**
     * 单玻 双玻
     */
    @ApiModelProperty("单玻 双玻")
    @ExcelProperty(value = "单玻 双玻")
    private String prodAttrName;

    /**
     * IOS分类
     */
    @ApiModelProperty("IOS分类")
    @ExcelProperty(value = "IOS分类")  
    private String iosName;
    /**
     * 是否CRS认证  Y/N
     */
    @ApiModelProperty("是否CRS认证  Y/N")
    @ExcelProperty(value = "是否CRS认证  Y/N")  
    private String isCrs;

    @ApiModelProperty(value = "生效时间")
    private LocalDate effectiveStartDate;

    @ApiModelProperty(value = "失效时间")
    private LocalDate effectiveEndDate;
}