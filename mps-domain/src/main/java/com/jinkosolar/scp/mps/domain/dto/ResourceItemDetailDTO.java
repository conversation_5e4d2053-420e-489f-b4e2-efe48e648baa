package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.time.LocalDate;

@ApiModel("资源物料限制明细表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceItemDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")  
    private String sapOrderNum;
    /**
     * SAP行号
     */
    @ApiModelProperty("SAP行号")
    @ExcelProperty(value = "SAP行号")  
    private Integer sapLineNum;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")  
    private String productType;
    /**
     * 合同编码
     */
    @ApiModelProperty("合同编码")
    @ExcelProperty(value = "合同编码")  
    private String contractNum;
    /**
     * 限定厂家
     */
    @ApiModelProperty("限定厂家")
    @ExcelProperty(value = "限定厂家")  
    private Long mdaFactory;
    /**
     * 物料结构
     */
    @ApiModelProperty("物料结构")
    @ExcelProperty(value = "物料结构")  
    private String itemName;
    /**
     * 有效开始
     */
    @ApiModelProperty("有效开始")
    @ExcelProperty(value = "有效开始")  
    private LocalDate effectiveStartDate;
    /**
     * 有效结束
     */
    @ApiModelProperty("有效结束")
    @ExcelProperty(value = "有效结束")  
    private LocalDate effectiveEndDate;
}