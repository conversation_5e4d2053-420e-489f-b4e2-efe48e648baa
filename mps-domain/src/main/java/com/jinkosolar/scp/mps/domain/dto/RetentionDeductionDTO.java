package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@ApiModel("留埚率扣减数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RetentionDeductionDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE,unTranslate = false)
    private Long dataTypeId;
    @ApiModelProperty("数据分类名称")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE,unTranslate = true)
    @ExcelProperty(index = 0)
    private String dataTypeIdName;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    @ExcelProperty(value = "版本")
    private String versionNumber;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CELL_PRODUCT)
    private Long cellProductId;

    @ApiModelProperty("产品")
    @ExcelProperty(index = 1)
    @Translate(DictType = LovHeaderCodeConstant.MPS_CELL_PRODUCT,unTranslate = true)
    private String cellProductIdName;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP,unTranslate = false)
    private Long workShopId;

    @ApiModelProperty("车间")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP,unTranslate = true)
    @ExcelProperty(index = 2)
    private String workShopIdName;
    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @Column(name = "thermal_field_id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100)
    private Long thermalFieldId;

    @ApiModelProperty("热场")
    @ExcelProperty(index = 3)
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100,unTranslate = true)
    private String thermalFieldIdName;
    /**
     * 留埚率
     */
    @ApiModelProperty("留埚率")
    @ExcelProperty(index = 4)
    @Translate(required = true)
    private BigDecimal retentionRate;

    @ApiModelProperty("年份")
    @ExcelProperty(index = 5)
    @Translate(required = true)
    private String retentionYear;

    @ApiModelProperty("月份")
    private String deductionMonth;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal deductionNum;

    @ApiModelProperty("月份数据")
    private Map<String,BigDecimal> monthMap = new HashMap<>();

    private List<RetentionDeductionDetailDTO> monthList;;

}
