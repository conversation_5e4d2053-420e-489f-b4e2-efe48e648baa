package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;


@ApiModel("留埚率扣减详情数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RetentionDeductionDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 留埚率扣减id
     */
    @ApiModelProperty("留埚率扣减id")
    @ExcelProperty(value = "留埚率扣减id")
    private Long retentionDeductionId;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    private String deductionMonth;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @ExcelProperty(value = "数量")
    private BigDecimal deductionNum;

    public RetentionDeductionDetailDTO(String deductionMonth, BigDecimal deductionNum) {
        this.deductionMonth = deductionMonth;
        this.deductionNum = deductionNum;
    }
}
