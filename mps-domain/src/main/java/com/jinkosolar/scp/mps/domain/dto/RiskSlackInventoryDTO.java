package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "RiskSlackInventoryDTO对象", description = "DTO对象")
public class RiskSlackInventoryDTO {


    /**
     * id
     */
    private Long id;
    /**
     * 排产月份
     */
    private String scheduleMonth;

    /**
     * 功率影响属性
     */
    private String effectAttribute;

    /**
     * 标准
     */
    private String standard;

    /**
     * 库存量
     */
    private String amount;


    /**
     * 过期月份
     */
    private String expiredMonth;

    /**
     * 来源
     */
    private Integer source;

    /**
     * 导入操作人名字
     */
    private String name;

    /**
     * 最新更新人
     */
    private String updatedBy;

    public static RiskSlackInventoryDTO build(RiskSlackInventoryDTO riskSlackInventoryDTO) {
        RiskSlackInventoryDTO builder = new RiskSlackInventoryDTO();
        builder.setScheduleMonth(riskSlackInventoryDTO.getScheduleMonth());
        builder.setExpiredMonth(riskSlackInventoryDTO.getExpiredMonth());
        builder.setEffectAttribute(riskSlackInventoryDTO.getEffectAttribute());
        builder.setStandard(riskSlackInventoryDTO.getStandard());
        return builder;
    }
}
