package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("销售预测报表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SalesForecastReportBaseDTO implements Serializable {

    public static final String TYPE1 = "销售需求预测";
    public static final String TYPE2 = "组件产量规划";
    public static final String TYPE3 = "匹配差异";

    @ApiModelProperty("类型")
    private String type;
    @ApiModelProperty("年")
    private String year;
    @ApiModelProperty("中长期统计区域编码")
    private String areaName;
    @ApiModelProperty("处理后电池尺寸")
    private String cellProductCode;
    /**
     * 版型
     */
    @ApiModelProperty("版型")
    private String cellModuleType;

    /**
     * 一月
     */
    @ApiModelProperty("一月")
    private BigDecimal m1 = BigDecimal.ZERO;
    /**
     * 二月
     */
    @ApiModelProperty("二月")
    private BigDecimal m2 = BigDecimal.ZERO;
    /**
     * 三月
     */
    @ApiModelProperty("三月")
    private BigDecimal m3 = BigDecimal.ZERO;
    /**
     * 四月
     */
    @ApiModelProperty("四月")
    private BigDecimal m4 = BigDecimal.ZERO;
    /**
     * 五月
     */
    @ApiModelProperty("五月")
    private BigDecimal m5 = BigDecimal.ZERO;
    /**
     * 六月
     */
    @ApiModelProperty("六月")
    private BigDecimal m6 = BigDecimal.ZERO;
    /**
     * 七月
     */
    @ApiModelProperty("七月")
    private BigDecimal m7 = BigDecimal.ZERO;
    /**
     * 八月
     */
    @ApiModelProperty("八月")
    private BigDecimal m8 = BigDecimal.ZERO;
    /**
     * 九月
     */
    @ApiModelProperty("九月")
    private BigDecimal m9 = BigDecimal.ZERO;
    /**
     * 十月
     */
    @ApiModelProperty("十月")
    private BigDecimal m10 = BigDecimal.ZERO;
    /**
     * 十一月
     */
    @ApiModelProperty("十一月")
    private BigDecimal m11 = BigDecimal.ZERO;
    /**
     * 十二月
     */
    @ApiModelProperty("十二月")
    private BigDecimal m12 = BigDecimal.ZERO;

    @ApiModelProperty("1季度")
    private BigDecimal q1;

    /**
     * 定线规划版本
     */
    @ApiModelProperty("定线规划版本")
    private String programVersion;

    /**
     * 汇总单号
     */
    @ApiModelProperty("汇总单号")
    private String summaryNo;

    public BigDecimal getQ1() {
        this.q1 = this.m1;
        if (m2 != null) {
            this.q1 = this.q1.add(this.m2);
        }
        if (m3 != null) {
            this.q1 = this.q1.add(this.m3);
        }
        return this.q1;
    }

    @ApiModelProperty("2季度")
    private BigDecimal q2;

    public BigDecimal getQ2() {
        this.q2 = this.m4;
        if (m5 != null) {
            this.q2 = this.q2.add(this.m5);
        }
        if (m6 != null) {
            this.q2 = this.q2.add(this.m6);
        }
        return this.q2;
    }

    @ApiModelProperty("3季度")
    private BigDecimal q3;

    public BigDecimal getQ3() {
        this.q3 = this.m7;
        if (m8 != null) {
            this.q3 = this.q3.add(this.m8);
        }
        if (m9 != null) {
            this.q3 = this.q3.add(this.m9);
        }
        return this.q3;
    }

    @ApiModelProperty("4季度")
    private BigDecimal q4;

    public BigDecimal getQ4() {
        this.q4 = this.m10;
        if (m11 != null) {
            this.q4 = this.q4.add(this.m11);
        }
        if (m12 != null) {
            this.q4 = this.q4.add(this.m12);
        }
        return this.q4;
    }

    @ApiModelProperty("年度汇总")
    private BigDecimal year0;

    public BigDecimal getYear0() {
        this.year0 = this.getQ1();
        if (this.getQ2() != null) {
            this.year0 = this.year0.add(this.getQ2());
        }
        if (this.getQ3() != null) {
            this.year0 = this.year0.add(this.getQ3());
        }
        if (this.getQ4() != null) {
            this.year0 = this.year0.add(this.getQ4());
        }
        return this.year0;
    }


    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GroupDTO {
        @ApiModelProperty("年")
        private String year;
        @ApiModelProperty("中长期统计区域编码")
        private String areaName;
        @ApiModelProperty("处理后电池尺寸")
        private String cellProductCode;
        @ApiModelProperty("处理后电池尺寸")
        private String cellModuleType;
    }


}