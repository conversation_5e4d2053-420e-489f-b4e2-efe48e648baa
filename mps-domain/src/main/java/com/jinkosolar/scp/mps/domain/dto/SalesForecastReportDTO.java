package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@ApiModel("销售预测报表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SalesForecastReportDTO extends BaseDTO implements Serializable {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 区分、区域名称
     */
    @ApiModelProperty("区分名称")
    private String areaName;

    /**
     * 区分、区域编码
     */
    @ApiModelProperty("区域编码")
    private String areaCode;

    /**
     * 类型编码
     */
    @ApiModelProperty("类型编码")
    private String typeCode;

    /**
     * 类型名称
     */
    @ApiModelProperty("类型名称")
    private String typeName;

    /**
     * 尺寸_产品类型
     */
    @ApiModelProperty("尺寸_产品类型")
    private String productType;

    /**
     * 版型
     */
    @ApiModelProperty("版型")
    private String moduleType;

    /**
     * 年月日期
     */
    @ApiModelProperty("年月日期")
    private String yearMonthStr;

    /**
     * 年份
     */
    @ApiModelProperty("年份")
    private String yearStr;

    /**
     * 年Q1
     */
    @ApiModelProperty("年季度")
    private BigDecimal yearQOneQty;

    /**
     * 年Q2
     */
    @ApiModelProperty("年Q2")
    private BigDecimal yearQTwoQty;

    /**
     * 年Q3
     */
    @ApiModelProperty("年Q3")
    private BigDecimal yearQThreeQty;

    /**
     * 年Q4
     */
    @ApiModelProperty("年Q4")
    private BigDecimal yearQFourQty;

    /**
     * 全年总计
     */
    @ApiModelProperty("全年总计")
    private BigDecimal yearTotalQty;

    /**
     * 销售预测数量
     */
    @ApiModelProperty("销售预测数量")
    private BigDecimal salesPlanQty;

    /**
     * 动态列行值
     */
    List<SalesForecastReportDTO> detailList;
}