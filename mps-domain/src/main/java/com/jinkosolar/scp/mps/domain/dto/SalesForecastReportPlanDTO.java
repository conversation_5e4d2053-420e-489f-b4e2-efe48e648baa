package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@ApiModel("中长期_销售预测需求匹配_定线规划表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)  
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SalesForecastReportPlanDTO extends PageDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 定线规划版本
     */
    @ApiModelProperty("定线规划版本")
    @ExcelProperty(value = "定线规划版本")  
    private String programVersion;
    /**
     * 计划版型_带主栅
     */
    @ApiModelProperty("计划版型_带主栅")
    @ExcelProperty(value = "计划版型_带主栅")  
    private String planLayout;
    /**
     * 车间ID
     */
    @Translate(DictType = MpsLovConstant.WORKSHOP, from = {"lovName","lovValue"}, to = {"workshopIdName","workshopCode"})
    @ApiModelProperty("车间ID")
    @ExcelProperty(value = "车间ID")  
    private Long workshopId;
    private String workshopIdName;
    private String workshopCode;

    /**
     * 工作中心ID
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, from = {"lovName","lovValue"}, to = {"workCenterIdName","workCenterCode"})
    @ApiModelProperty("工作中心ID")
    @ExcelProperty(value = "工作中心ID")  
    private Long workCenterId;
    private String workCenterIdName;
    private String workCenterCode;

    @ApiModelProperty("年")
    private String year;
    /**
     * 年月日期
     */
    @ApiModelProperty("年月日期")
    @ExcelProperty(value = "年月日期")  
    private String yearMonthStr;
    /**
     * 年季度
     */
    @ApiModelProperty("年季度")
    @ExcelProperty(value = "年季度")  
    private String yearQStr;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @ExcelProperty(value = "数量")  
    private BigDecimal planQty;
    /**
     * 电池产品
     */
    @ApiModelProperty("电池产品")
    @ExcelProperty(value = "电池产品")  
    private String spec;
    /**
     * 片串值
     */
    @ApiModelProperty("片串值")
    @ExcelProperty(value = "片串值")  
    private String piece;
    /**
     * 单玻/双玻
     */
    @ApiModelProperty("单玻/双玻")
    @ExcelProperty(value = "单玻/双玻")  
    private String oddEven;
    /**
     * 工艺
     */
    @ApiModelProperty("工艺")
    @ExcelProperty(value = "工艺")  
    private String technique;
    /**
     * 处理后电池尺寸
     */
    @ApiModelProperty("处理后电池尺寸")
    @ExcelProperty(value = "处理后电池尺寸")  
    private String cellProductCode;
    /**
     * 处理后版型
     */
    @ApiModelProperty("处理后版型")
    @ExcelProperty(value = "处理后版型")  
    private String cellModuleType;

    public String getCellModuleType() {
        if (StringUtils.isNotBlank(this.cellModuleType)) {
            return this.cellModuleType;
        }
        this.cellModuleType = StringUtils.join(Arrays.asList(this.piece, this.technique, this.oddEven), "-");
        return this.cellModuleType;
    }

    /**
     * 中长期统计区域编码
     */
    @Translate(DictType = MpsLovConstant.MPS_SESTEM_AREA, from = {"lovName"}, to = {"areaName"})
    @ApiModelProperty("中长期统计区域编码")
    @ExcelProperty(value = "中长期统计区域编码")  
    private Long areaId;
    private String areaName;

    @Data
    public static class MonthDTO extends SalesForecastReportPlanDTO {
        @ApiModelProperty("年")
        private String year;
        /**
         * 一月
         */
        @ApiModelProperty("一月")
        private BigDecimal m1 = BigDecimal.ZERO;
        /**
         * 二月
         */
        @ApiModelProperty("二月")
        private BigDecimal m2 = BigDecimal.ZERO;
        /**
         * 三月
         */
        @ApiModelProperty("三月")
        private BigDecimal m3 = BigDecimal.ZERO;
        /**
         * 四月
         */
        @ApiModelProperty("四月")
        private BigDecimal m4 = BigDecimal.ZERO;
        /**
         * 五月
         */
        @ApiModelProperty("五月")
        private BigDecimal m5 = BigDecimal.ZERO;
        /**
         * 六月
         */
        @ApiModelProperty("六月")
        private BigDecimal m6 = BigDecimal.ZERO;
        /**
         * 七月
         */
        @ApiModelProperty("七月")
        private BigDecimal m7 = BigDecimal.ZERO;
        /**
         * 八月
         */
        @ApiModelProperty("八月")
        private BigDecimal m8 = BigDecimal.ZERO;
        /**
         * 九月
         */
        @ApiModelProperty("九月")
        private BigDecimal m9 = BigDecimal.ZERO;
        /**
         * 十月
         */
        @ApiModelProperty("十月")
        private BigDecimal m10 = BigDecimal.ZERO;
        /**
         * 十一月
         */
        @ApiModelProperty("十一月")
        private BigDecimal m11 = BigDecimal.ZERO;
        /**
         * 十二月
         */
        @ApiModelProperty("十二月")
        private BigDecimal m12 = BigDecimal.ZERO;

        @ApiModelProperty("1季度")
        private BigDecimal q1;

        public BigDecimal getQ1() {
            this.q1 = this.m1;
            if (m2 != null) {
                this.q1 = this.q1.add(this.m2);
            }
            if (m3 != null) {
                this.q1 = this.q1.add(this.m3);
            }
            return this.q1;
        }

        @ApiModelProperty("2季度")
        private BigDecimal q2;

        public BigDecimal getQ2() {
            this.q2 = this.m4;
            if (m5 != null) {
                this.q2 = this.q2.add(this.m5);
            }
            if (m6 != null) {
                this.q2 = this.q2.add(this.m6);
            }
            return this.q2;
        }

        @ApiModelProperty("3季度")
        private BigDecimal q3;

        public BigDecimal getQ3() {
            this.q3 = this.m7;
            if (m8 != null) {
                this.q3 = this.q3.add(this.m8);
            }
            if (m9 != null) {
                this.q3 = this.q3.add(this.m9);
            }
            return this.q3;
        }

        @ApiModelProperty("4季度")
        private BigDecimal q4;

        public BigDecimal getQ4() {
            this.q4 = this.m10;
            if (m11 != null) {
                this.q4 = this.q4.add(this.m11);
            }
            if (m12 != null) {
                this.q4 = this.q4.add(this.m12);
            }
            return this.q4;
        }

        @ApiModelProperty("年度汇总")
        private BigDecimal year0;

        public BigDecimal getYear0() {
            this.year0 = this.getQ1();
            if (this.getQ2() != null) {
                this.year0 = this.year0.add(this.getQ2());
            }
            if (this.getQ3() != null) {
                this.year0 = this.year0.add(this.getQ3());
            }
            if (this.getQ4() != null) {
                this.year0 = this.year0.add(this.getQ4());
            }
            return this.year0;
        }

        @JSONField(serialize = false)
        public List<SalesForecastReportPlanDTO> getSalesForecastReportPlanDTOs() {
            List<SalesForecastReportPlanDTO> list = new ArrayList<>();
            buildDTO(list, this.m1, "01");
            buildDTO(list, this.m2, "02");
            buildDTO(list, this.m3, "03");
            buildDTO(list, this.m4, "04");
            buildDTO(list, this.m5, "05");
            buildDTO(list, this.m6, "06");
            buildDTO(list, this.m7, "07");
            buildDTO(list, this.m8, "08");
            buildDTO(list, this.m9, "09");
            buildDTO(list, this.m10, "10");
            buildDTO(list, this.m11, "11");
            buildDTO(list, this.m12, "12");
            return list;
        }

        private void buildDTO(List<SalesForecastReportPlanDTO> list, BigDecimal planQuantity, String month) {
            if (planQuantity != null) {
                SalesForecastReportPlanDTO dto = new SalesForecastReportPlanDTO();
                BeanUtils.copyProperties(this, dto);
                dto.setPlanQty(planQuantity);
                dto.setYearMonthStr(this.year+"-"+month);
                list.add(dto);
            }
        }

    }

    @Data
    public static class ProductGroup {

        @ApiModelProperty(value = "型号")
        private String codeName;

        @ApiModelProperty(value = "电池型号")
        private String cellTypeCode;

        @ApiModelProperty(value = "电池代码")
        private String cellQuantityCode;

        @ApiModelProperty(value = "电池片数")
        private String cellQuantity;

        @ApiModelProperty(value = "电池尺寸")
        private String cellDimension;

        @ApiModelProperty(value = "PMCode")
        private String pmCode;

        @ApiModelProperty(value = "工艺")
        private String cellQuantityTech;

        @ApiModelProperty(value = "单双玻")
        private String backsheetType;

        @ApiModelProperty(value = "电池尺寸代码")
        private String cellDimensionCode;

        @ApiModelProperty(value = "转换后的电池产品类型")
        private String cellProductCode;

        private List<String> CellList;

    }

}