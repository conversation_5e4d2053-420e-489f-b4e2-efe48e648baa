package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDate;
import java.math.BigDecimal;  


@ApiModel("中长期_销售预测需求匹配_销售预测数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)  
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SalesForecastReportSummaryDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 年
     */
    @ApiModelProperty("年")
    private String year;

    /**
     * 年月日期
     */
    @ApiModelProperty("年月日期")
    @ExcelProperty(value = "年月日期")  
    private String yearMonthStr;
    /**
     * 年季度
     */
    @ApiModelProperty("年季度")
    @ExcelProperty(value = "年季度")  
    private String yearQStr;
    /**
     * 汇总单号
     */
    @ApiModelProperty("汇总单号")
    @ExcelProperty(value = "汇总单号")  
    private String summaryNo;
    /**
     * 汇总类型
     */
    @ApiModelProperty("汇总类型")
    @ExcelProperty(value = "汇总类型")  
    private String summaryType;
    /**
     * 状态(未发布、已发布、已升版)
     */
    @ApiModelProperty("状态(未发布、已发布、已升版)")
    @ExcelProperty(value = "状态(未发布、已发布、已升版)")  
    private String summaryStatus;
    /**
     * 是否分发
     */
    @ApiModelProperty("是否分发")
    @ExcelProperty(value = "是否分发")  
    private String distributeFlag;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")  
    private String salProduct;
    /**
     * 是否追溯相关
     */
    @ApiModelProperty("是否追溯相关")
    @ExcelProperty(value = "是否追溯相关")  
    private String tracedBackFlag;
    /**
     * 总量
     */
    @ApiModelProperty("总量")
    @ExcelProperty(value = "总量")  
    private BigDecimal totalQty;
    /**
     * 库存/新产
     */
    @ApiModelProperty("库存/新产")
    @ExcelProperty(value = "库存/新产")  
    private String placeOrderFlag;
    /**
     * 国内/外产能
     */
    @ApiModelProperty("国内/外产能")
    @ExcelProperty(value = "国内/外产能")  
    private String basePlaceCode;
    /**
     * 产品大类
     */
    @ApiModelProperty("产品大类")
    @ExcelProperty(value = "产品大类")  
    private String productType;
    /**
     * 要求货好时间(确认)
     */
    @ApiModelProperty("要求货好时间(确认)")
    @ExcelProperty(value = "要求货好时间(确认)")  
    private LocalDate confirmDeliveryTime;
    /**
     * Neo_Green
     */
    @ApiModelProperty("Neo_Green")
    @ExcelProperty(value = "Neo_Green")  
    private String neoGreen;
    /**
     * 片串值
     */
    @ApiModelProperty("片串值")
    @ExcelProperty(value = "片串值")  
    private String piece;
    /**
     * 单玻/双玻
     */
    @ApiModelProperty("单玻/双玻")
    @ExcelProperty(value = "单玻/双玻")  
    private String oddEven;
    /**
     * 工艺
     */
    @ApiModelProperty("工艺")
    @ExcelProperty(value = "工艺")  
    private String technique;
    /**
     * 处理后电池尺寸
     */
    @ApiModelProperty("处理后电池尺寸")
    @ExcelProperty(value = "处理后电池尺寸")  
    private String cellProductCode;
    /**
     * 处理后版型
     */
    @ApiModelProperty("处理后版型")
    @ExcelProperty(value = "处理后版型")  
    private String cellModuleType;

    public String getCellModuleType() {
        if (StringUtils.isNotBlank(this.cellModuleType)) {
            return this.cellModuleType;
        }
        return StringUtils.joinWith("-", this.piece, this.technique, this.oddEven);
    }

    /**
     * 中长期统计区域编码
     */
    @ApiModelProperty("中长期统计区域编码")
    @ExcelProperty(value = "中长期统计区域编码")  
    private String areaCode;
    private String areaName;
}