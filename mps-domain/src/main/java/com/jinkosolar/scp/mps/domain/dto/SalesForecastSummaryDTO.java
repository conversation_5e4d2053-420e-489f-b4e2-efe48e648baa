package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("销售预测数据汇总头对象")
@Data
public class SalesForecastSummaryDTO extends PageDTO implements Serializable {
    /**
     * 汇总单号
     */
    @ApiModelProperty("汇总单号")
    private String summaryNo;
    /**
     * 汇总类型(lov)
     */
    @ApiModelProperty("汇总类型(lov)")
    private String summaryType;
    /**
     * 状态(未发布、已发布、已升版)
     */
    @ApiModelProperty("状态(未发布、已发布、已升版)")
    private String summaryStatus;
    /**
     * 是否分发
     */
    @ApiModelProperty("是否分发")
    private String distributeFlag;

    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String salProduct;

    /**
     * 是否追溯相关
     */
    @ApiModelProperty("是否追溯相关")
    private String tracedBackFlag;

    /**
     * 总量
     */
    @ApiModelProperty("总量")
    private BigDecimal totalQty;

    /**
     * 【是否下单】都改为【库存/新产】
     */
    @ApiModelProperty("库存/新产")
    private String placeOrderFlag;

    /**
     * 基地
     */
    @ApiModelProperty("基地")
    private String baseplace;

    /**
     * 国内/外产能
     */
    @ApiModelProperty("国内/外产能")
    private String basePlaceCode;

    /**
     * 产品大类
     */
    @ApiModelProperty("产品大类")
    private String productType;

    /**
     * 要求货好时间（确认）
     */
    @ApiModelProperty("要求货好时间（确认）")
    private LocalDate confirmDeliveryTime;

    /**
     * Neo Green
     */
    @ApiModelProperty("Neo Green")
    private String neoGreen;

    @ApiModelProperty(value = "转换后的电池产品类型")
    private String cellProductCode;

    @ApiModelProperty("年月日期")
    private String yearMonthStr;

    @ApiModelProperty("年份")
    private String yearStr;

    @ApiModelProperty("年季度")
    private String yearQStr;

    @ApiModelProperty("片串值")
    private String piece;

    @ApiModelProperty("单玻/双玻")
    private String oddEven;

    @ApiModelProperty("工艺")
    private String technique;

    @ApiModelProperty("中长期统计区域编码")
    private String areaCode;

    @ApiModelProperty("中长期统计区域名称")
    private String areaName;

    @ApiModelProperty("处理后版型")
    private String cellModuleType;

    public String getCellModuleType() {
        if (StringUtils.isNotBlank(this.cellModuleType)) {
            return this.cellModuleType;
        }
        return StringUtils.joinWith("-", this.piece, this.technique, this.oddEven);
    }

}