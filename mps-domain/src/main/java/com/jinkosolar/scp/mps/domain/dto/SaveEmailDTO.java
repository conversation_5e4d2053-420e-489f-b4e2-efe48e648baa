package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.dto.message.MsgReceiverDto;
import com.jinkosolar.scp.mps.domain.query.ModuleProductionPlanQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@ApiModel("组件排产计划邮件保存邮箱信息入参对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SaveEmailDTO extends BaseDTO implements Serializable {
    /**
     * 定线规划
     */
    @ApiModelProperty("排产区域id")
    private Long domesticOversea;

    /**
     * 基地id
     */
    @ApiModelProperty("基地id")
    private Long baseId;

    /**
     * 抄送人用户id列表
     */
    @ApiModelProperty("抄送人用户id列表")
    private List<String> copyToIds;
    /**
     * 类型 1、汇总版 2、分基地
     */
    @ApiModelProperty("类型")
    private int type;
}