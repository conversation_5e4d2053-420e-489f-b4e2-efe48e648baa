package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2023/10/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProductPlanDigitalRiskControlReqDto对象", description = "DTO对象")
public class ScheduleDigitalRiskControlReqDto implements Serializable {
    private static final long serialVersionUID = -6697585684342260419L;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "页码,从1开始")
    private Integer pageNo = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;
}
