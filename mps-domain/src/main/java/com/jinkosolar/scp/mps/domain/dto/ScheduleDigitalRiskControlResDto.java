package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/10/18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProductPlanDigitalRiskControlResDto对象", description = "DTO对象")
public class ScheduleDigitalRiskControlResDto implements Serializable {
    private static final long serialVersionUID = -8117907600681745398L;

    @ApiModelProperty(value = "组件入库计划时间")
    private String plannedCompletedDate;

    @ApiModelProperty(value = "生产基地")
    private String productionBase;

    @ApiModelProperty(value = "车间")
    private String workShop;

    @ApiModelProperty(value = "计划入库数量")
    private BigDecimal scheduleCount;

    @ApiModelProperty(value = "月份")
    private String month;
}
