package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * DP实际排产
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-01 10:14:28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScheduleLinesDTO对象", description = "DTO对象")
public class ScheduleLinesApiDTO extends BaseDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 排产行ID
     */
    @ApiModelProperty(value = "APS排产行ID")
    private String apsScheduleId;
    /**
     * DP行ID
     */
    @ApiModelProperty(value = "DP行ID")
    private Long dpLinesId;
    /**
     * DP分组ID
     */
    @ApiModelProperty(value = "DP分组ID")
    private Long dpGroupId;
    /**
     * 确认需求状态
     */
    @ApiModelProperty(value = "确认需求状态")
    private String scheduleStatus;
    /**
     * 项目地国家
     */
    @ApiModelProperty(value = "项目地国家")
    private String country;
    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    private String channel;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 库存组织
     */
    @ApiModelProperty(value = "库存组织")
    private Long organizationId;
    /**
     * 销售料号
     */
    @ApiModelProperty(value = "销售料号")
    private String productItemNo;
    /**
     * 需求功率
     */
    @ApiModelProperty(value = "需求功率")
    private String productPower;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 线缆长度（mm）
     */
    @ApiModelProperty(value = "线缆长度（mm）")
    private String itemAttribute7;
    /**
     * 端子
     */
    @ApiModelProperty(value = "端子")
    private String itemAttribute8;
    /**
     * 接线盒型号
     */
    @ApiModelProperty(value = "接线盒型号")
    private String itemAttribute9;
    /**
     * 接线盒电流（a）
     */
    @ApiModelProperty(value = "接线盒电流（a）")
    private String itemAttribute10;
    /**
     * 边框类型;边框类型
     */
    @ApiModelProperty(value = "边框类型;边框类型")
    private String itemAttribute6;
    /**
     * 气候性策略
     */
    @ApiModelProperty(value = "气候性策略")
    private String itemAttribute14;
    /**
     * 电池工艺(null)
     */
    @ApiModelProperty(value = "电池工艺(null)")
    private String itemAttribute15;
    /**
     * 玻璃种类(后)
     */
    @ApiModelProperty(value = "玻璃种类(后)")
    private String itemAttribute27;
    /**
     * 片源种类
     */
    @ApiModelProperty(value = "片源种类")
    private String itemAttribute16;
    /**
     * H追溯
     */
    @ApiModelProperty(value = "H追溯")
    private String itemAttribute18;
    /**
     * eva属性（后）
     */
    @ApiModelProperty(value = "eva属性（后）")
    private String itemAttribute28;
    /**
     * 背板颜色
     */
    @ApiModelProperty(value = "背板颜色")
    private String itemAttribute19;
    /**
     * 背板结构
     */
    @ApiModelProperty(value = "背板结构")
    private String itemAttribute20;
    /**
     * 组件厚度
     */
    @ApiModelProperty(value = "组件厚度")
    private String itemAttribute24;
    /**
     * 边框颜色
     */
    @ApiModelProperty(value = "边框颜色")
    private String itemAttribute25;
    /**
     * 电池尺寸
     */
    @ApiModelProperty(value = "电池尺寸")
    private String itemAttribute26;
    /**
     * el等级
     */
    @ApiModelProperty(value = "el等级")
    private String itemAttribute31;
    /**
     * 网版图型-分类
     */
    @ApiModelProperty(value = "网版图型-分类")
    private String itemAttribute32;
    /**
     * 焊带(null)
     */
    @ApiModelProperty(value = "焊带(null)")
    private String itemAttribute33;
    /**
     * 包装片数
     */
    @ApiModelProperty(value = "包装片数")
    private String itemAttribute21;
    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    private String productFactory;
    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String productPlace;
    /**
     * 排产单元
     */
    @ApiModelProperty(value = "排产单元")
    private String scheduleUnit;
    /**
     * 计划排产数量
     */
    @ApiModelProperty(value = "计划排产数量")
    private BigDecimal planQty;
    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String uom;
    /**
     * 功率符合率
     */
    @ApiModelProperty(value = "功率符合率")
    private BigDecimal powerCoincidenceRate;
    /**
     * 组件良率
     */
    @ApiModelProperty(value = "组件良率")
    private BigDecimal productYield;
    /**
     * 计划开始日期
     */
    @ApiModelProperty(value = "计划开始日期")
    private LocalDate plannedStartDate;
    /**
     * 计划完成日期
     */
    @ApiModelProperty(value = "计划完成日期")
    private LocalDate plannedCompleteDate;
    /**
     * 切换时间
     */
    @ApiModelProperty(value = "切换时间")
    private BigDecimal changeDate;
    /**
     * 切换开始时间
     */
    @ApiModelProperty(value = "切换开始时间")
    private LocalDate changeStartDate;
    /**
     * 切换结束时间
     */
    @ApiModelProperty(value = "切换结束时间")
    private LocalDate changeCompleteDate;
    /**
     * 更新材料
     */
    @ApiModelProperty(value = "更新材料")
    private String changFlag;
    /**
     * 组件ID
     */
    @ApiModelProperty(value = "组件ID")
    private String inventoryItemId;
    /**
     * 降档要求
     */
    @ApiModelProperty(value = "降档要求")
    private String powerChangeRemark;
    /**
     * erp工单号
     */
    @ApiModelProperty(value = "erp工单号")
    private String attribute1;
    /**
     * scp工单号
     */
    @ApiModelProperty(value = "scp工单号")
    private String attribute2;
    /**
     * 功率需求
     */
    @ApiModelProperty(value = "功率需求")
    private String attribute3;
    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    private String attribute4;
    /**
     * 是否最新版本; Y/N
     */
    @ApiModelProperty(value = "是否最新版本; Y/N")
    private String attribute5;
    /**
     * 弹性域6
     */
    @ApiModelProperty(value = "弹性域6")
    private String attribute6;
    /**
     * 弹性域7
     */
    @ApiModelProperty(value = "弹性域7")
    private String attribute7;
    /**
     * 弹性域8
     */
    @ApiModelProperty(value = "弹性域8")
    private String attribute8;
    /**
     * 弹性域9
     */
    @ApiModelProperty(value = "弹性域9")
    private String attribute9;
    /**
     * 弹性域10
     */
    @ApiModelProperty(value = "弹性域10")
    private String attribute10;
    /**
     * 弹性域11
     */
    @ApiModelProperty(value = "弹性域11")
    private String attribute11;
    /**
     * 弹性域12
     */
    @ApiModelProperty(value = "弹性域12")
    private String attribute12;
    /**
     * 弹性域13
     */
    @ApiModelProperty(value = "弹性域13")
    private String attribute13;
    /**
     * DP_ID
     */
    @ApiModelProperty(value = "DP_ID")
    private String dpId;
    /**
     * 排产行Code
     */
    @ApiModelProperty(value = "排产行Code")
    private String scheduleCode;
    /**
     * DP明细行ID
     */
    // @ApiModelProperty(value = "DP明细行ID")
    // private Long dpDetailId;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 组件料号
     */
    @ApiModelProperty(value = "组件料号")
    private String inventoryItemNo;
    /**
     * 配料版本
     */
    @ApiModelProperty(value = "配料版本")
    private String ingredientsVersion;
    /**
     * 出线方式
     */
    @ApiModelProperty(value = "出线方式")
    private String itemAttribute11;
    /**
     * 玻璃厚度（mm）
     */
    @ApiModelProperty(value = "玻璃厚度（mm）")
    private String itemAttribute17;
    /**
     * eva属性（前）
     */
    @ApiModelProperty(value = "eva属性（前）")
    private String itemAttribute3;
    /**
     * LRF
     */
    @ApiModelProperty(value = "LRF")
    private String itemAttribute22;
    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String itemAttribute4;
    /**
     * 护角
     */
    @ApiModelProperty(value = "护角")
    private String itemAttribute36;
    /**
     * 封边
     */
    @ApiModelProperty(value = "封边")
    private String itemAttribute37;
    /**
     * 硅料供应商
     */
    @ApiModelProperty(value = "硅料供应商")
    private String itemAttribute42;
    /**
     * 电池厂家
     */
    @ApiModelProperty(value = "电池厂家")
    private String itemAttribute43;
    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String area;
    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private Long customer;
    /**
     * 是否监造
     */
    @ApiModelProperty(value = "是否监造")
    private String isSupervision;

    /**
     * 特殊单号
     */
    @ApiModelProperty(value = "特殊单号")
    private String specialNo;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "月份")
    private String exeMonth;

    @ApiModelProperty(value = "平均功率")
    private BigDecimal avePower;

    @ApiModelProperty(value = "横竖安装")
    private String itemAttribute5;

    @ApiModelProperty(value = "监造")
    private String itemAttribute13;

    @ApiModelProperty(value = "是否研发标识")
    private String developmentFlag;

    @ApiModelProperty(value = "是否研发标识")
    private String oemAccount;

    @ApiModelProperty(value = "是否确认排产  Y/N null为审核")
    private String confirmFlag;

    @ApiModelProperty(value = "DP_ID加计划排产序号组合")
    private String scheduleSimpleNo;

    @ApiModelProperty(value = "是否还存在DPDetail")
    private String hasDpDetail;

    @ApiModelProperty(value = "批次")
    private String batchNo;

    @ApiModelProperty(value = "模型编号")
    private String moduleNo;

    @ApiModelProperty(value = "确认时间")
    private String confirmDatetime;
    /**
     * 补充要求
     */
    @ApiModelProperty(value = "补充要求")
    private String appendRemark;

    public ScheduleLinesApiDTO group() {
        return ScheduleLinesApiDTO
                .builder()
                .month(this.getMonth())
                .dpId(this.getDpId())
                .dpLinesId(this.getDpLinesId())
                .dpGroupId(this.getDpGroupId())
                .isOversea(this.getIsOversea())
                .workshop(this.getWorkshop())
                .productFamily(this.getProductFamily())
                .confirmDatetime(this.getConfirmDatetime())
                .scheduleSimpleNo(this.getScheduleSimpleNo())
                .itemAttribute13(this.getItemAttribute13())
                .customer(this.getCustomer())
                .area(this.getArea())
                .specialNo(this.getSpecialNo())
                .itemAttribute5(this.getItemAttribute5())
                .itemAttribute8(this.getItemAttribute8())
                .itemAttribute14(this.getItemAttribute14())
                .productPower(this.getProductPower())
                .channel(this.getChannel())
                .inventoryItemNo(this.getInventoryItemNo())
                .country(this.getCountry())
                .exeMonth(DateUtil.getMonth(this.getPlannedStartDate()))
                .appendRemark(this.getAppendRemark())
                .productYield(this.getProductYield())
                .powerCoincidenceRate(this.getPowerCoincidenceRate())
                .build();
    }

    public ScheduleLinesApiDTO summary() {
        return ScheduleLinesApiDTO
                .builder()
                .dpId(this.dpId)
                .dpLinesId(this.dpLinesId)
                .dpGroupId(this.dpGroupId)
                .scheduleSimpleNo(this.scheduleSimpleNo)
                .confirmDatetime(this.confirmDatetime)
                .isOversea(this.isOversea)
                .area(this.area)
                .customer(this.customer)
                .productFamily(this.productFamily)
                .itemAttribute13(this.itemAttribute13)
                .productPower(this.productPower)
                .itemAttribute5(this.itemAttribute5)
                .itemAttribute8(this.itemAttribute8)
                .specialNo(this.specialNo)
                .workshop(this.workshop)
                .month(this.month)
                .plannedStartDate(this.plannedStartDate)
                .channel(this.channel)
                .itemAttribute14(this.itemAttribute14)
                .build();
    }
}
