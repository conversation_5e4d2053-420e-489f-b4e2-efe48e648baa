package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * 排产表数据 修改历史;
 * @author:ma<PERSON><PERSON>
 * @since:2023/7/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScheduleLinesChangeLog保存变更参数", description = "保存变更参数")
public class ScheduleLinesChangeLogSaveChangeDTO implements Serializable, Cloneable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单头或订单行")
    private String fieldTable;

    @ApiModelProperty(value = "需求计划id")
    private Long dpLinesId;

    @ApiModelProperty(value = "订单头id(排产表数据 修改历史主键id)")
    private Long scheduleLinesId;

    @ApiModelProperty(value = "订单id")
    private String dpId;

    @ApiModelProperty(value = "新值")
    private Object newObj;

    @ApiModelProperty(value = "旧值")
    private Object oldObj;

    @ApiModelProperty(value = "操作类型（规则生成，手动修改）")
    private String operationType;

    @ApiModelProperty(value = "客户id")
    private String customerId;

    @ApiModelProperty(value = "车间")
    private String workshop;


}
