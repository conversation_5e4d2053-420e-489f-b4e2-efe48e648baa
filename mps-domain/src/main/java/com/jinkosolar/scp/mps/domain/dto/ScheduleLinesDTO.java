package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.LovLineDTO;

import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * DP实际排产
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-30 11:07:20
 */
@Data
@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScheduleLinesDTO对象", description = "DTO对象")
@ExcelIgnoreUnannotated
public class ScheduleLinesDTO extends BaseDTO {
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @ExcelProperty(value = "车间")
    private String workshop;
    /**
     * DPID
     */
    @ApiModelProperty(value = "DPID")
    @ExcelProperty(value = "DPID")
    private String dpId;
    /**
     * 项目地国家
     */
    @ApiModelProperty(value = "项目地国家")
    @ExcelProperty(value = "项目地国家")
    private String country;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @ExcelProperty(value = "区域")
    private String areaNo;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称")
    private String customerName;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @ExcelProperty(value = "产品族")
    private String productFamily;
    /**
     * 是否监造
     */
    @ApiModelProperty(value = "是否监造")
    @ExcelProperty(value = "是否监造")
    private String itemAttribute13;
    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度")
    @ExcelProperty(value = "线缆长度")
    private String itemAttribute7;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    @ExcelProperty(value = "横竖装")
    private String itemAttribute5;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @ExcelProperty(value = "版本号")
    private String versionNo;
    /**
     * 铭牌  / 图纸号
     */
    @ApiModelProperty(value = "铭牌  / 图纸号")
    @ExcelProperty(value = "图纸号")
    private String itemAttribute23;
    /**
     * 配料版本
     */
    @ApiModelProperty(value = "配料版本")
    @ExcelProperty(value = "配料版本")
    private String ingredientsVersion;
    /**
     * 线盒端子
     */
    @ApiModelProperty(value = "线盒端子")
    @ExcelProperty(value = "线盒端子")
    private String itemAttribute8;
    /**
     * 特殊单编号
     */
    @ApiModelProperty(value = "特殊单编号")
    @ExcelProperty(value = "特殊单编号")
    private String specialSn;
    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    @ExcelProperty(value = "组件尺寸")
    private String itemAttribute4;
    /**
     * 平均功率
     */
    @ApiModelProperty(value = "平均功率")
    @ExcelProperty(value = "平均功率")
    private BigDecimal averagePower;
    /**
     * 功率符合率
     */
    @ApiModelProperty(value = "功率符合率")
    @ExcelProperty(value = "功率符合率")
    private BigDecimal powerCoincidenceRate;
    /**
     * 组件良率
     */
    @ApiModelProperty(value = "组件良率")
    @ExcelProperty(value = "组件良率")
    private BigDecimal productYield;

    /**
     * 组件料号
     */
    @ApiModelProperty(value = "组件料号")
    @ExcelProperty(value = "组件料号")
    private String inventoryItemNo;

    /**
     * 计划开始日期
     */
    @ApiModelProperty(value = "计划开始日期")
    private Date plannedStartDate;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 生产单元
     */
    @ApiModelProperty(value = "生产单元")
    private String scheduleUnit;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String productPlace;
    /**
     * 计划排产日期
     */
    @ApiModelProperty(value = "计划排产日期")
//    @ExcelProperty(value = "交期", converter = LocalDateConverter.class)
    private LocalDate plannedWipDate;
    /**
     * scp工单号
     */
    @ApiModelProperty(value = "scp工单号")
    private String attribute2;
    /**
     * 功率需求
     */
    @ApiModelProperty(value = "功率需求")
    @ExcelProperty(value = "功率需求")
    private String attribute3;
    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String attribute6;
    /**
     * total
     */
    @ExcelProperty(value = "total")
    @ApiModelProperty(value = "total")
    private BigDecimal total;
    /**
     * total(MW)
     */
    @ApiModelProperty(value = "total(MW)")
    @ExcelProperty(value = "total(MW)")
    private BigDecimal totalMW;
    /**
     * 月份数据
     */
    @ExcelIgnore
    private List<MothDataDTO> mothData;

    //数据库月份数据字段
    private String mothDataStr;

    //id串
    private String idStr;

    //预警信息
    private String warningInfo;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 研发/量产
     */
    @ApiModelProperty(value = "研发/量产")
    private String segment43;


    /**
     * 1号
     */
    @ApiModelProperty(value = "1号")
    @ExcelProperty(value = "1号")
    private BigDecimal day1;
    /**
     * 2号
     */
    @ApiModelProperty(value = "2号")
    @ExcelProperty(value = "2号")
    private BigDecimal day2;
    /**
     * 3号
     */
    @ApiModelProperty(value = "3号")
    @ExcelProperty(value = "3号")
    private BigDecimal day3;
    /**
     * 4号
     */
    @ApiModelProperty(value = "4号")
    @ExcelProperty(value = "4号")
    private BigDecimal day4;
    /**
     * 5号
     */
    @ApiModelProperty(value = "5号")
    @ExcelProperty(value = "5号")
    private BigDecimal day5;
    /**
     * 6号
     */
    @ApiModelProperty(value = "6号")
    @ExcelProperty(value = "6号")
    private BigDecimal day6;
    /**
     * 7号
     */
    @ApiModelProperty(value = "7号")
    @ExcelProperty(value = "7号")
    private BigDecimal day7;
    /**
     * 8号
     */
    @ApiModelProperty(value = "8号")
    @ExcelProperty(value = "8号")
    private BigDecimal day8;
    /**
     * 9号
     */
    @ApiModelProperty(value = "9号")
    @ExcelProperty(value = "9号")
    private BigDecimal day9;
    /**
     * 10号
     */
    @ApiModelProperty(value = "10号")
    @ExcelProperty(value = "10号")
    private BigDecimal day10;
    /**
     * 11号
     */
    @ApiModelProperty(value = "11号")
    @ExcelProperty(value = "11号")
    private BigDecimal day11;
    /**
     * 12号
     */
    @ApiModelProperty(value = "12号")
    @ExcelProperty(value = "12号")
    private BigDecimal day12;
    /**
     * 13号
     */
    @ApiModelProperty(value = "13号")
    @ExcelProperty(value = "13号")
    private BigDecimal day13;
    /**
     * 14号
     */
    @ApiModelProperty(value = "14号")
    @ExcelProperty(value = "14号")
    private BigDecimal day14;
    /**
     * 15号
     */
    @ApiModelProperty(value = "15号")
    @ExcelProperty(value = "15号")
    private BigDecimal day15;
    /**
     * 16号
     */
    @ApiModelProperty(value = "16号")
    @ExcelProperty(value = "16号")
    private BigDecimal day16;
    /**
     * 17号
     */
    @ApiModelProperty(value = "17号")
    @ExcelProperty(value = "17号")
    private BigDecimal day17;
    /**
     * 18号
     */
    @ApiModelProperty(value = "18号")
    @ExcelProperty(value = "18号")
    private BigDecimal day18;
    /**
     * 19号
     */
    @ApiModelProperty(value = "19号")
    @ExcelProperty(value = "19号")
    private BigDecimal day19;
    /**
     * 20号
     */
    @ApiModelProperty(value = "20号")
    @ExcelProperty(value = "20号")
    private BigDecimal day20;
    /**
     * 21号
     */
    @ApiModelProperty(value = "21号")
    @ExcelProperty(value = "21号")
    private BigDecimal day21;
    /**
     * 22号
     */
    @ApiModelProperty(value = "22号")
    @ExcelProperty(value = "22号")
    private BigDecimal day22;
    /**
     * 23号
     */
    @ApiModelProperty(value = "23号")
    @ExcelProperty(value = "23号")
    private BigDecimal day23;
    /**
     * 24号
     */
    @ApiModelProperty(value = "24号")
    @ExcelProperty(value = "24号")
    private BigDecimal day24;
    /**
     * 25号
     */
    @ApiModelProperty(value = "25号")
    @ExcelProperty(value = "25号")
    private BigDecimal day25;
    /**
     * 26号
     */
    @ApiModelProperty(value = "26号")
    @ExcelProperty(value = "26号")
    private BigDecimal day26;
    /**
     * 27号
     */
    @ApiModelProperty(value = "27号")
    @ExcelProperty(value = "27号")
    private BigDecimal day27;
    /**
     * 28号
     */
    @ApiModelProperty(value = "28号")
    @ExcelProperty(value = "28号")
    private BigDecimal day28;
    /**
     * 29号
     */
    @ApiModelProperty(value = "29号")
    @ExcelProperty(value = "29号")
    private BigDecimal day29;
    /**
     * 30号
     */
    @ApiModelProperty(value = "30号")
    @ExcelProperty(value = "30号")
    private BigDecimal day30;
    /**
     * 31号
     */
    @ApiModelProperty(value = "31号")
    @ExcelProperty(value = "31号")
    private BigDecimal day31;

    //用于设置值集
    public void setLovLineValue(Map<String, LovLineDTO> lovMap) {
        //横竖装
//        if (lovMap.get(LovHeaderCodeConstant.CROSS_VERTICAL + getItemAttribute5()) != null) {
//            setItemAttribute5(lovMap.get(LovHeaderCodeConstant.CROSS_VERTICAL + getItemAttribute5()).getLovName());
//        }
        //车间
        if (lovMap.get(LovHeaderCodeConstant.WORK_SHOP + getWorkshop()) != null) {
            setWorkshop(lovMap.get(LovHeaderCodeConstant.WORK_SHOP + getWorkshop()).getLovName());
        }
        // 销售区域
        if (lovMap.get(LovHeaderCodeConstant.COUNTRY + getCountry()) != null) {
            setCountry(lovMap.get(LovHeaderCodeConstant.COUNTRY + getCountry()).getLovName());
        }
        //客户
        if (lovMap.get(LovHeaderCodeConstant.CUSTOMER + getCustomerName()) != null) {
            setCustomerName(lovMap.get(LovHeaderCodeConstant.CUSTOMER + getCustomerName()).getLovName());
        }
        //产品族
        if (lovMap.get(LovHeaderCodeConstant.FAMILY_CODE + getProductFamily()) != null) {
            setProductFamily(lovMap.get(LovHeaderCodeConstant.FAMILY_CODE + getProductFamily()).getLovName());
        }
//
//        //是否监造
//        if(lovMap.get(LovHeaderCodeConstant.YES_OR_NO+getItemAttribute13()) != null){
//            setItemAttribute13(lovMap.get(LovHeaderCodeConstant.YES_OR_NO+getItemAttribute13()).getLovName());
//        }
//        //线缆长度
//        if(lovMap.get(LovHeaderCodeConstant.LENGTH_CABLE+getItemAttribute7()) != null){
//            setItemAttribute7(lovMap.get(LovHeaderCodeConstant.LENGTH_CABLE+getItemAttribute7()).getLovName());
//        }
//        //线盒端子
//        if(lovMap.get(LovHeaderCodeConstant.PLUG_CONNECTOR+getItemAttribute8()) != null){
//            setItemAttribute8(lovMap.get(LovHeaderCodeConstant.PLUG_CONNECTOR+getItemAttribute8()).getLovName());
//        }
//        //组件尺寸
//        if(lovMap.get(LovHeaderCodeConstant.COMPONENT_SIZE+getItemAttribute4()) != null){
//            setItemAttribute4(lovMap.get(LovHeaderCodeConstant.COMPONENT_SIZE+getItemAttribute4()).getLovName());
//        }
        //生产单元
        if (lovMap.get(LovHeaderCodeConstant.WORK_UNIT + getScheduleUnit()) != null) {
            setItemAttribute8(lovMap.get(LovHeaderCodeConstant.WORK_UNIT + getScheduleUnit()).getLovName());
        }
        //生产基地
        if (lovMap.get(LovHeaderCodeConstant.BASE_PLACE + getProductPlace()) != null) {
            setProductPlace(lovMap.get(LovHeaderCodeConstant.BASE_PLACE + getProductPlace()).getLovName());
        }
        //国内海外
        if (lovMap.get(LovHeaderCodeConstant.IS_OVERSEA + getIsOversea()) != null) {
            setIsOversea(lovMap.get(LovHeaderCodeConstant.IS_OVERSEA + getIsOversea()).getLovName());
        }
    }
}