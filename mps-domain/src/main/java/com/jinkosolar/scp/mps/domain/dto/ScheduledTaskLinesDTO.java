package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.dpf.common.domain.entity.User;
import com.ibm.scp.common.api.util.UserUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel(value = "DTO对象", description = "DTO对象")
public class ScheduledTaskLinesDTO {

    @ApiModelProperty(value = "主键")
    private Long taskLineId;

    @ApiModelProperty(value = "任务编号")
    private String taskNumber;

    @ApiModelProperty(value = "任务名称")
    private String taskName;

    @ApiModelProperty(value = "任务描述")
    private String  taskDesc;

    @ApiModelProperty(value = "版本号")
    private String versions;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "提交时间")
    private LocalDateTime requestDate;

    @ApiModelProperty(value = "完成时间")
    private LocalDateTime completeDate;

    @ApiModelProperty(value = "提交人")
    private String requestBy;

    @ApiModelProperty(value = "运行日志")
    private String log;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    public static ScheduledTaskLinesDTO init() {
        User user = UserUtil.getUser();
        String name="";
        if (Objects.nonNull(user)){
            name=user.getName();
        }
        return ScheduledTaskLinesDTO.builder().status(ScheduleTaskStatusEnum.RUNNING.getDesc()).requestDate(LocalDateTime.now()).requestBy(name).build();
    }
}
