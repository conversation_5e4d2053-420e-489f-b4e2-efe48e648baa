package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApiModel("爬坡需求表")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SectionOrderDTO {

    /**
     * 订单编码
     */
    @ApiModelProperty("编码")
    private String code;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    private String productType;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("厚度")
    private String thickness;
    /**
     * 交货期
     */
    @ApiModelProperty("交货期")
    private LocalDateTime let;
    /**
     * 需求数
     */
    @ApiModelProperty("需求数")
    private BigDecimal qty;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    private String workshop;
    @ApiModelProperty("模型分类")
    private String dataType;

    @ApiModelProperty("地区")
    private String isOverseaName;
    private Long isOversea;

    @ApiModelProperty("模型分类")
    private String modelType;

    @ApiModelProperty("定向/非定向")
    private String directional;

    @ApiModelProperty("倒角")
    private String chamfer;

    @ApiModelProperty("尺寸")
    private String size;

    @ApiModelProperty("配方")
    private String formula;

    @ApiModelProperty("高低阻")
    private String hlImpedance;

    @ApiModelProperty("组合字段")
    private String groupColumn;
}
