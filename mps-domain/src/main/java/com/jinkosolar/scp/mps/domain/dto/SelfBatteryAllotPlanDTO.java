package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.annotation.TranslateSpecifyFactory;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.serializer.BigDecimalSerializer;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;


@ApiModel("自产电池调拨计划数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SelfBatteryAllotPlanDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 排产区域
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA)
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")  
    private Long domesticOversea;
    /**
     * 排产区域名称
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产区域名称")
    private String domesticOverseaName;
    /**
     * 电池产品类型
     */
    @Translate(DictType = LovHeaderCodeConstant.AESTHETICS_PRODUCT_TYPE)
    @ApiModelProperty("电池产品类型")
    @ExcelProperty(value = "电池产品类型")  
    private Long cellType;
    /**
     * 电池产品类型代码
     */
    @ApiModelProperty("电池产品类型代码")
    @ExcelProperty(value = "电池产品类型代码")  
    private String cellTypeCode;
    @ApiModelProperty("电池产品类型描述")
    @ExcelProperty(value = "电池产品类型描述")
    private String cellTypeName;
    /**
     * 主栅数
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_MAIN_GRID)
    @ApiModelProperty("主栅数")
    @ExcelProperty(value = "主栅数")  
    private Long primaryGateNumber;
    @ApiModelProperty("主栅数描述")
    @ExcelProperty(value = "主栅数描述")
    private String primaryGateNumberName;
    /**
     * 效率
     */
    @JSONField(serializeUsing = BigDecimalSerializer.class, deserializeUsing = BigDecimalSerializer.class)
    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")  
    private BigDecimal efficiency;

    @ApiModelProperty("效率")
    @ExcelProperty(value = "效率")
    private String efficiencyStr;
    /**
     * 厚度
     */
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_024_ATTR_2300)
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")  
    private Long thickness;
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    private String thicknessName;
    /**
     * 定向/非定向
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL)
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")  
    private Long direction;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    private String directionName;
    /**
     * 发货工厂
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    @ApiModelProperty("发货工厂")
    @ExcelProperty(value = "发货工厂")  
    private Long sendFactory;
    /**
     * 发货工厂代码
     */
    @ApiModelProperty("发货工厂代码")
    @ExcelProperty(value = "发货工厂代码")  
    private String sendFactoryCode;
    /**
     * 发货工厂名称
     */
    @ApiModelProperty("发货工厂名称")
    @ExcelProperty(value = "发货工厂名称")
    private String sendFactoryName;
    /**
     * 收货工厂
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    @ApiModelProperty("收货工厂")
    @ExcelProperty(value = "收货工厂")  
    private Long receiveFactory;
    /**
     * 收货工厂代码
     */
    @ApiModelProperty("收货工厂代码")
    @ExcelProperty(value = "收货工厂代码")  
    private String receiveFactoryCode;
    /**
     * 收货工厂名称
     */
    @ApiModelProperty("收货工厂名称")
    @ExcelProperty(value = "收货工厂名称")
    private String receiveFactoryName;
    /**
     * 调拨计划日期
     */
    @ApiModelProperty("调拨计划日期")
    @ExcelProperty(value = "调拨计划日期")  
    private LocalDate planDate;
    /**
     * 调拨计划数量
     */
    @ApiModelProperty("调拨计划数量")
    @ExcelProperty(value = "调拨计划数量")  
    private BigDecimal planQty;

    /**
     * 调拨计划明细日期集合
     */
    @ApiModelProperty("调拨计划明细日期集合")
    @ExcelProperty(value = "调拨计划明细日期集合")
    private List<String> allotPlanDayColumnMap;
    /**
     * 调拨计划明细数量集合
     */
    @ApiModelProperty("调拨计划明细数量集合")
    @ExcelProperty(value = "调拨计划明细数量集合")
    private LinkedHashMap<String, BigDecimal> allotPlanDayQtyMap;


    @ApiModelProperty("电性能")
    @Translate(DictType = LovHeaderCodeConstant.MPS_ELECTRICAL_PERFORMANCE)
    private Long electricalPerformance;


    @ApiModelProperty(value = "电性能")
    private String electricalPerformanceName;



    @ApiModelProperty("指定硅料供应商")
    @TranslateSpecifyFactory(to = "specifySupplierName")
    private Long specifySupplier;


    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商名称")
    @ExcelProperty(value = "指定硅料供应商名称")
    @TranslateSpecifyFactory(dictHeaderCode = LovHeaderCodeConstant.SPECIFY_SILICON_SUPPLIER,to = "specifySupplier",translateType = false)
    private String specifySupplierName;
}