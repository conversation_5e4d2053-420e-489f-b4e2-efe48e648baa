package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;


@ApiModel("硅料月投产比例数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SiliconProductionRatioDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    @ExcelProperty(value = "版本")
    private String versionNumber;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")
//    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, queryColumns = {"lovLineId"},
//            from = {"lovName"}, to = {"dataTypeName"})
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataTypeId;

    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类名称Code")
    @ExcelProperty(value = "数据名称Code")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, queryColumns = {"dataTypeName"},
            from = {"lovLineId"}, to = {"dataTypeId"}, required = true)
    private String dataTypeName;

    /**
     * 数据分类
     */
/*    @ApiModelProperty("数据分类名称")
    @ExcelProperty(value = "数据名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, queryColumns = {"isOverseaName"},
            from = {"lovLineId","lovValue"}, to = {"dataTypeId","dataTypeName"}, required = true)
    private String dataTypeName;*/
    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    @ExcelProperty(value = "产品Id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"productTypeName"})
    private Long productTypeId;
    /**
     * 产品Name
     */
    @ApiModelProperty("产品Name")
    @ExcelProperty(value = "产品Name")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"productTypeName"},
            from = {"lovLineId"}, to = {"productTypeId"}, required = true)
    private String productTypeName;
    /**
     * 定向非定向ID
     */
    @ApiModelProperty("定向非定向ID")
    @ExcelProperty(value = "定向非定向ID")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directionalId;
    /**
     * 定向非定向Name
     */
    @ApiModelProperty("定向非定向Name")
    @ExcelProperty(value = "定向非定向Name")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"directionalName"},
            from = {"lovLineId"}, to = {"directionalId"})
    private String directionalName;
    /**
     * 尺寸Id
     */
    @ApiModelProperty("尺寸Id")
    @ExcelProperty(value = "尺寸Id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sizeName"})
    private Long sizeId;
    /**
     * 尺寸Name
     */
    @ApiModelProperty("尺寸Name")
    @ExcelProperty(value = "尺寸Name")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"sizeName"},
            from = {"lovLineId"}, to = {"sizeId"})
    private String sizeName;
    /**
     * 高低阻Id
     */
    @ApiModelProperty("高低阻Id")
    @ExcelProperty(value = "高低阻Id")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"crystalTypeName"})
    private Long crystalTypeId;
    /**
     * 高低阻Name
     */
    @ApiModelProperty("高低阻Name")
    @ExcelProperty(value = "高低阻Name")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"crystalTypeName"},
            from = {"lovLineId"}, to = {"crystalTypeId"})
    private String crystalTypeName;

    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"formulaName"})
    private Long formulaId;

    /**
     * 配方名称
     */
    @ApiModelProperty("配方名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"formulaName"},
            from = {"lovLineId"}, to = {"formulaId"})
    private String formulaName;
    /**
     * 硅料供应商Id
     */
    @ApiModelProperty("硅料供应商Id")
    @ExcelProperty(value = "硅料供应商Id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"siliconSupplierName"})
    private Long siliconSupplierId;
    /**
     * 硅料供应商Name
     */
    @ApiModelProperty("硅料供应商Name")
    @ExcelProperty(value = "硅料供应商Name")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"siliconSupplierName"},
            from = {"lovLineId"}, to = {"siliconSupplierId"}, required = true)
    private String siliconSupplierName;
    /**
     * 月份  20240101
     */
    @ApiModelProperty("月份  20240101")
    @ExcelProperty(value = "月份  20240101")
    private LocalDate month;
    /**
     * 月份 值
     */
    @ApiModelProperty("月份 值")
    @ExcelProperty(value = "月份 值")
    private BigDecimal monthValue;
    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 冗余字段1
     */
    @ApiModelProperty("冗余字段1")
    @ExcelProperty(value = "冗余字段1")
    private String attribute1;
    /**
     * 冗余字段2
     */
    @ApiModelProperty("冗余字段2")
    @ExcelProperty(value = "冗余字段2")
    private String attribute2;
    /**
     * 冗余字段3
     */
    @ApiModelProperty("冗余字段3")
    @ExcelProperty(value = "冗余字段3")
    private String attribute3;
    /**
     * 冗余字段4
     */
    @ApiModelProperty("冗余字段4")
    @ExcelProperty(value = "冗余字段4")
    private String attribute4;
    /**
     * 冗余字段5
     */
    @ApiModelProperty("冗余字段5")
    @ExcelProperty(value = "冗余字段5")
    private String attribute5;


    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new HashMap<>();
}