package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.ConvertType;
import com.ibm.scp.common.api.annotation.ImportConvert;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;

@ApiModel("硅胶颜色连续性表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SiliconeColorContinuityDTO extends BaseDTO implements Serializable {
    /**
     * 车间
     */
    @ApiModelProperty("产品型号id")
//    @Translate(DictType = LovHeaderCodeConstant.SYS_PRODUCT_MODEL, queryColumns = {"lovLineId"},
//            from = {"lovName"}, to = {"productTypeName"})
    private Long productType;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")
    private String productTypeName;

    @ApiModelProperty("产品型号编码")
    private String productTypeCode;
    /**
     * 车间
     */
    @ApiModelProperty("边框颜色id")
//    @Translate(DictType = "ATTR_TYPE_001_ATTR_1700", queryColumns = {"lovLineId"},
//            from = {"lovName"}, to = {"borderColorName"})
    private Long borderColor;

    /**
     * 车间
     */
    @ApiModelProperty("边框颜色")
    @ExcelProperty(value = "边框颜色")
    @ImportConvert(lovCode = "ATTR_TYPE_001_ATTR_1700", convertType = ConvertType.ID, required = false)
    private String borderColorName;

    /**
     * 车间
     */
    @ApiModelProperty("硅胶颜色id")
    private Long siliconeColor;

    /**
     * 车间
     */
    @ApiModelProperty("硅胶颜色")
    @ExcelProperty(value = "硅胶颜色")
    @ImportConvert(lovCode = "SYS.SILICONE_COLOR", convertType = ConvertType.ID, required = false)
    private String siliconeColorName;

    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty("产品代码")
    @ExcelProperty(value = "产品代码")
    private String pmCode;
    @ApiModelProperty("计算类型")
    @ExcelProperty(value = "计算类型")
    private String calculationType;
}
