package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;


@ApiModel("组件单块瓦数表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SingleModuleWattageDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")
    @Translate(DictType = MpsLovConstant.LOCATION)
    private Long area;

    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域名称")
    @ExcelProperty(value = "排产名称")
    private String areaName;
    /**
     * 计划版型(不带主栅)
     */
    @ApiModelProperty("计划版型(不带主栅)")
    @ExcelProperty(value = "计划版型(不带主栅)")  
    private String planVersionModule;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")  
    private Long planVersion;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    private String planVersionName;


    /**
     * 计划版型名称
     */
    @ApiModelProperty("计划版型名称")
    @ExcelProperty(value = "计划版型名称")
    private String moduleName;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String version;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")  
    private String remark;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    private String month;
    /**
     * 瓦块数
     */
    @ApiModelProperty("瓦块数")
    @ExcelProperty(value = "瓦块数")
    private BigDecimal monthValue;
    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "瓦块数")
    private String year;


    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new LinkedHashMap<>();
}