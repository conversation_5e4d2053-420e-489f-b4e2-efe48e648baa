package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;                 
import java.time.LocalDate;


@ApiModel("拉晶倒角切换计划数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SliceChamferSwitchPlanDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")  
    private Long id;
    /**
     * 计划版本号
     */
    @ApiModelProperty("计划版本号")
    @ExcelProperty(value = "计划版本号")  
    private String planVersion;
    /**
     * 产品id
     */
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000)
    @ApiModelProperty("产品id")
    @ExcelProperty(value = "产品id")  
    private Long productType;
    /**
     * 产品code
     */
    @ApiModelProperty("产品code")
    @ExcelProperty(value = "工厂code")  
    private String productTypeCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    @ExcelProperty(value = "产品名称")
    private String productTypeName;
    /**
     * 工作中心
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER)
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private Long workCenter;
    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    private String workCenterName;
    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    @ExcelProperty(value = "是否定向")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL)
    private Long directional;
    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    @ExcelProperty(value = "是否定向")
    private String directionalName;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300)
    private Long size;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    private String sizeName;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100)
    private Long formula;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    private String formulaName;
    /**
     * 高低阻
     */
    @ApiModelProperty("高低阻")
    @ExcelProperty(value = "高低阻")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE)
    private Long crystalType;
    /**
     * 高低阻
     */
    @ApiModelProperty("高低阻")
    @ExcelProperty(value = "高低阻")
    private String crystalTypeName;
    /**
     * 倒角类型
     */
    @ApiModelProperty("倒角类型")
    @ExcelProperty(value = "倒角类型")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CHAMFER_TYPE)
    private Long chamferType;
    /**
     * 倒角类型编码
     */
    @ApiModelProperty("倒角类型编码")
    @ExcelProperty(value = "倒角类型编码")  
    private String chamferTypeCode;
    /**
     * 倒角类型名称
     */
    @ApiModelProperty("倒角类型名称")
    @ExcelProperty(value = "倒角类型名称")
    private String chamferTypeName;
    /**
     * 数据分类
     */
    @Translate(DictType = LovHeaderCodeConstant.DATA_TYPE)
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")  
    private Long dataType;
    /**
     * 数据分类编码
     */
    @ApiModelProperty("数据分类编码")
    @ExcelProperty(value = "数据分类编码")  
    private String dataTypeCode;
    /**
     * 数据分类名称
     */
    @ApiModelProperty("数据分类名称")
    @ExcelProperty(value = "数据分类名称")
    private String dataTypeName;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")  
    private LocalDate startDate;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @ExcelProperty(value = "开始时间")  
    private LocalDate endDate;
}