package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("切片海外期初库存数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SliceOpeningInventoryOverseaDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;
    /**
     * 数据类型:1.切片 2.方棒
     */
    @ApiModelProperty(" 数据类型:1.切片 2.方棒")
    private String dataType;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂id")
    @ExcelProperty(value = "工厂id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE,to = {"factoryName"})
    private Long factoryId;
    /**
     * 工厂code
     */
    @ApiModelProperty("工厂code")
    @ExcelProperty(value = "工厂code")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 原料供应商
     */
    @ApiModelProperty("原料供应商")
    @ExcelProperty(value = "原料供应商")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1900 )
    private Long materialVendor;
    /**
     * 原料供应商编码
     */
    @ApiModelProperty("原料供应商编码")
    @ExcelProperty(value = "原料供应商编码")
    private String materialVendorCode;

    /**
     * 原料供应商名称
     */
    @ApiModelProperty("原料供应商名称")
    @ExcelProperty(value = "原料供应商名称")
    private String materialVendorName;
    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    @ExcelProperty(value = "产品类型")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400 )
    private Long productType;
    /**
     * 产品类型code
     */
    @ApiModelProperty("产品类型code")
    @ExcelProperty(value = "产品类型code")
    private String productTypeCode;
    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    @ExcelProperty(value = "产品类型名称")
    private String productTypeName;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1300 )
    private Long thickness;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    private String thicknessName;
    /**
     * 是否供美
     */
    @Translate(DictType="x")
    @ApiModelProperty("是否供美")
    private Long supplyUs;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private String supplyUsName;
    /**
     * 定向非定向
     */
    @Translate(DictType="x")
    @ApiModelProperty("定向非定向")
    private Long directional;
    /**
     * 定向非定向
     */
    @ApiModelProperty("定向非定向")
    private String directionalName;
    /**
     * 尺寸
     */
    @Translate(DictType="x")
    @ApiModelProperty("尺寸")
    private Long size;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    private String sizeName;
    /**
     * 硅料厂商
     */
    @Translate(DictType="x")
    @ApiModelProperty("硅料厂商")
    private String siliconSupplier;
    @ApiModelProperty("硅料厂商")
    private String siliconSupplierName;
    /**
     * 单晶产品类型
     */
    @Translate(DictType="x")
    @ApiModelProperty("单晶产品类型")
    private Long crystalType1;
    /**
     * 单晶产品类型
     */
    @ApiModelProperty("单晶产品类型")
    private String crystalType1Name;
    /**
     * 倒角 有Lov
     */
    @Translate(DictType = "x",from = {"lovName"}, to = {"chamferName"})
    @ApiModelProperty(value = "倒角")
    private Long chamferId;
    /**
     * 倒角名称
     */
    @ApiModelProperty(value = "倒角名称")
    private String chamferName;
    /**
     * 等级
     */
    @ApiModelProperty("等级")
    private String grade;
    /**
     * 销售区域
     */
    @ApiModelProperty("销售区域")
    private String invSiteCode;
    /**
     * 计量单位
     */
    @ApiModelProperty("计量单位")
    private String unit;
    /**
     * 库存日期
     */
    @ApiModelProperty("库存日期")
    private LocalDate inventoryDate;
    /**
     * 期初库存
     */
    @ApiModelProperty("期初库存")
    private BigDecimal openingInventory;
    /**
     * 转圆棒重量
     */
    @ApiModelProperty("转圆棒重量")
    private BigDecimal openingInventoryKg;
    /**
     * 转圆棒长度
     */
    @ApiModelProperty("转圆棒长度")
    private BigDecimal openingInventoryMm;
    /**
     * 转方棒刀数
     */
    @ApiModelProperty("转方棒刀数")
    private BigDecimal openingInventoryCut;
}
