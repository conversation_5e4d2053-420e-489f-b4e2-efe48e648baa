package com.jinkosolar.scp.mps.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.annotation.TranslateSpecifyFactory;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: WeeklyPlanReportDTO
 * @date 2024/8/12 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SlicePlanReportOverseaDTO implements Serializable {

    /**
     * 工厂id
     */
    @Translate(DictType = LovHeaderCodeConstant.MPS_FACTORY, queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"factoryCode", "factoryName"})
    @ApiModelProperty("工厂id")
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    private String factoryName;
    /**
     * 工作中心
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"workCenterName"})
    @ApiModelProperty("工作中心")
    private Long workCenterId;
    /**
     * 车间
     */
    @ApiModelProperty("工作中心名称")
    private String workCenterName;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    private String workCenterCode;

    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    private String productType;
    /**
     * 尺寸
     */
    @Translate(DictType = "x")
    @ApiModelProperty("尺寸")
    private Long size;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    private String sizeName;
    /**
     * 定向/非定向
     */
    @Translate(DictType = "x")
    @ApiModelProperty("定向/非定向")
    private Long directional;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    private String directionalName;
    /**
     * 是否供美
     */
    @Translate(DictType = "x")
    @ApiModelProperty("是否供美")
    private Long supplyUs;
    /**
     * 供美/非供美
     */
    @ApiModelProperty("供美/非供美")
    private String supplyUsName;
    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @ExcelProperty(value = "硅料供应商")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_024_ATTR_1400, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"siliconSupplierName"}, splitChar = ",")
    private String siliconSupplier;
    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    private String siliconSupplierName;
    /**
     * 厚度
     */
    @Translate(DictType = "x")
    @ApiModelProperty("厚度")
    private Long thickness;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private String thicknessName;
    /**
     * 晶棒类型1
     */
    @ApiModelProperty("晶棒类型1")
    @ExcelProperty(value = "晶棒类型1")
    @Translate(DictType = "x")
    private Long crystalType1;
    private String crystalType1Name;
    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    private Integer machineQty;
    /**
     * 产出计划
     */
    @ApiModelProperty("产出计划")
    private BigDecimal schedulingQty;

    /**
     * 单产
     */
    @ApiModelProperty("单产")
    private BigDecimal singleProduction;

    /**
     * 产出单位
     */
    @ApiModelProperty("产出单位")
    private String schedulingUom;

    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String attribute19;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDateTime schedulingStartTime;
    /**
     * 日期集合
     */
    @ApiModelProperty("日期集合")
    private List<String> dateList;
    /**
     * 项目
     */
    @ApiModelProperty("项目")
    private String dataItemCode;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String dataItemName;
    /**
     * 汇总
     */
    @ApiModelProperty("汇总")
    private BigDecimal totalValue;

    @ApiModelProperty(value = "期初库存")
    private BigDecimal openingInventory;

    /**
     * 倒角 有Lov
     */
    @Translate(DictType = "x",from = {"lovName"}, to = {"chamferName"})
    @ApiModelProperty(value = "倒角")
    private Long chamferId;
    /**
     * 倒角名称
     */
    @ApiModelProperty(value = "倒角名称")
    private String chamferName;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商厂家id")
//    @Translate(DictType = "BOM.Designated supplier", queryColumns = {"lovLineId"},
//            from = {"lovName"}, to = {"specifySupplierName"})
    @TranslateSpecifyFactory(to = "specifySupplierName")
    private Long specifySupplier;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商厂家名称")
    @ExcelProperty(value = "指定硅料供应商厂家名称")
    private String specifySupplierName;
    /**
     * 动态列头
     */
    @ApiModelProperty("动态列头")
    private List<String> dynamicColumnList;
    /**
     * 动态列结果
     */
    @ApiModelProperty("动态列结果")
    private Map<String, Object> dynamicColumnMap;


    public LocalDate toLocalDate() {
        LocalDateTime parse = DateUtil.parse(attribute19);
        return parse.toLocalDate();
    }

    public Map<String, Object> convertMap() {
        Map<String, Object> objectMap = BeanUtil.beanToMap(this);
        if (MapUtils.isNotEmpty(this.dynamicColumnMap)) {
            //设置动态列
            objectMap.putAll(this.dynamicColumnMap);
        }
        return objectMap;
    }

}
