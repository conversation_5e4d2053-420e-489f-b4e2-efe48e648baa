package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Translate;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: WeeklyPlanReportDTO
 * @date 2024/8/12 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SlicePlanReportOverseaUpdateDTO implements Serializable {

    /**
     * 工厂id
     */
    @Translate(DictType = LovHeaderCodeConstant.MPS_FACTORY, queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"factoryCode", "factoryName"})
    @ApiModelProperty("工厂id")
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    private String factoryName;
    /**
     * 工作中心
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"workCenterName"})
    @ApiModelProperty("工作中心")
    private Long workCenterId;
    /**
     * 车间
     */
    @ApiModelProperty("工作中心名称")
    private String workCenterName;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    private String workCenterCode;

    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    private String productType;
    /**
     * 尺寸
     */
    @Translate(DictType = "x")
    @ApiModelProperty("尺寸")
    private Long size;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    private String sizeName;
    /**
     * 定向/非定向
     */
    @Translate(DictType = "x")
    @ApiModelProperty("定向/非定向")
    private Long directional;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    private String directionalName;
    /**
     * 是否供美
     */
    @Translate(DictType = "x")
    @ApiModelProperty("是否供美")
    private Long supplyUs;
    /**
     * 供美/非供美
     */
    @ApiModelProperty("供美/非供美")
    private String supplyUsName;
    /**
     * 硅料供应商
     */
    @Translate(DictType = "x")
    @ApiModelProperty("硅料供应商")
    private String siliconSupplier;
    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    private String siliconSupplierName;
    /**
     * 厚度
     */
    @Translate(DictType = "x")
    @ApiModelProperty("厚度")
    private Long thickness;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private String thicknessName;
    /**
     * 厚度
     */
    @Translate(DictType = "x")
    @ApiModelProperty("厚度")
    private Long afterThickness;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private String afterThicknessName;
    /**
     * 晶棒类型1
     */
    @ApiModelProperty("晶棒类型1")
    @ExcelProperty(value = "晶棒类型1")
    @Translate(DictType = "x")
    private Long crystalType1;
    private String crystalType1Name;

    /**
     * 修改日期集合
     */
    @ApiModelProperty("修改日期集合")
    private List<String> updateDateList;
    /**
     * 项目
     */
    @ApiModelProperty("项目")
    private String dataItemCode;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String dataItemName;

    /**
     * 倒角 有Lov
     */
    @Translate(DictType = "x",from = {"lovName"}, to = {"chamferName"})
    @ApiModelProperty(value = "倒角")
    private Long chamferId;
    /**
     * 倒角名称
     */
    @ApiModelProperty(value = "倒角名称")
    private String chamferName;


}
