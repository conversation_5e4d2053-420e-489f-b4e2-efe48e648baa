package com.jinkosolar.scp.mps.domain.dto;

import com.jinkosolar.scp.mps.domain.entity.ProductYieldThickness;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SlicePlanShiftDTO对象", description = "DTO对象")
public class SlicePlanShiftContextDTO {
    List<SlicePlanShiftHeadDTO> groupJbdb1List;

    List<SlicePlanShiftHeadDTO> groupJbdb2List;

    List<SlicePlanShiftHeadDTO> groupJbdb3List;

    List<SlicePlanShiftHeadDTO> groupJbdb4List;

    List<SlicePlanShiftHeadDTO> groupJbdb5List;

    List<SlicePlanShiftHeadDTO> groupJbdb6List;

    List<SlicePlanShiftHeadDTO> groupJbdb7List;

    Map<String, SlicePlanShiftDTO> sliceSlicePlanShiftDTOMap;

    Map<Long, Map<String, ProductYieldThickness>> keyMonthAndYieldThicknessMap;

    Map<Long, Map<String, ProductYieldThickness>> keyProductTypeAndYieldThicknessMap;

    Long jizhunlianglvLovId;

    Long meiKgLovId;

}
