package com.jinkosolar.scp.mps.domain.dto;

import com.google.common.base.Joiner;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SlicePlanShiftDTO对象", description = "DTO对象")
public class SlicePlanShiftDTO extends BaseDTO {
    private static final Joiner groupKeyJoiner = Joiner.on("_").useForNull("null");

    @ApiModelProperty(value = "类别")
    private String dataTitleType;

    @ApiModelProperty(value = "分类")
    private String dataType;

    @ApiModelProperty(value = "时间类型")
    private String dateType;

    @ApiModelProperty(value = "工作中心编码")
    private String workCenterCode;

    @ApiModelProperty(value = "产品")
    private String productType;

    @ApiModelProperty(value = "定向标识")
    private String directional;

    @ApiModelProperty(value = "高低阻")
    private String crystalType1;

    @ApiModelProperty(value = "倒角")
    private String chamfer;

    @ApiModelProperty(value = "配方")
    private String formula;

    @ApiModelProperty(value = "硅料厂家")
    private String siliconSupplier;

    @ApiModelProperty(value = "厚度")
    private String thickness;

    @ApiModelProperty(value = "日期")
    private LocalDate dayDate;

    @ApiModelProperty(value = "推移日期")
    private LocalDate shiftDayDate;

    @ApiModelProperty(value = "圆棒数量")
    private BigDecimal quantityYb;

    @ApiModelProperty(value = "方棒数量")
    private BigDecimal quantityFb;

    @ApiModelProperty(value = "最终展示数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "晶棒消耗")
    private BigDecimal quantityJbxh;

    @ApiModelProperty(value = "单产")
    private BigDecimal singleProduction;

    public String getJoinKeyWithDateType() {
        return groupKeyJoiner.join(dateType, workCenterCode, productType, directional, crystalType1, chamfer, formula, siliconSupplier, thickness, dayDate);
    }

    public String getJoinKey() {
        return groupKeyJoiner.join(workCenterCode, productType, directional, crystalType1, chamfer, formula, siliconSupplier, thickness);
    }
}
