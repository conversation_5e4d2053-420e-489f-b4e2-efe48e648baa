package com.jinkosolar.scp.mps.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Joiner;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SlicePlanShiftDTO对象", description = "DTO对象")
public class SlicePlanShiftHeadDTO extends BaseDTO {
    private static final Joiner groupKeyJoiner = Joiner.on("_").useForNull("null");

    @ApiModelProperty(value = "类别")
    private String dataTitleType;

    @ApiModelProperty(value = "分类")
    private String dataType;

    @ApiModelProperty(value = "产品")
    private String productType;

    @ApiModelProperty(value = "工作中心编码")
    private String workCenterCode;

    @ApiModelProperty(value = "工作中心名称")
    private String workCenterName;

    @ApiModelProperty(value = "定向标识")
    private String directional;

    @ApiModelProperty(value = "高低阻")
    private String crystalType1;

    @ApiModelProperty(value = "倒角")
    private String chamfer;

    @ApiModelProperty(value = "配方")
    private String formula;

    @ApiModelProperty(value = "硅料厂家")
    private String siliconSupplier;

    @ApiModelProperty(value = "厚度")
    private String thickness;

    @JsonIgnore
    private Integer rowNum;

    @ApiModelProperty(value = "圆棒数量")
    @JsonIgnore
    private Map<LocalDate, BigDecimal> dayOfQuantityYb;

    @ApiModelProperty(value = "方棒数量")
    @JsonIgnore
    private Map<LocalDate, BigDecimal> dayOfQuantityFb;

    @ApiModelProperty(value = "最后展示的数量")
    @JsonIgnore
    private Map<LocalDate, BigDecimal> dayOfQuantity;

    @ApiModelProperty(value = "晶棒消耗")
    @JsonIgnore
    private Map<LocalDate, BigDecimal> dayOfQuantityJbxh;

    @JsonIgnore
    private Map<LocalDate, BigDecimal> dayOfSingleProduction;

    @JsonIgnore
    private Map<LocalDate, BigDecimal> dayOfConsumer;

    /**
     * 月份集合结果
     */
    @ApiModelProperty("月份集合结果")
    @JsonIgnore
    private Map<String, BigDecimal> monthValue;

    /**
     * 动态列头
     */
    @ApiModelProperty("动态列头")
    private List<String> subList;

    /**
     * 动态列结果
     */
    @ApiModelProperty("动态列结果")
    private Map<String, Object> subMap;

    @JsonIgnore
    List<LocalDate> allDateList;

    @JsonIgnore
    public String getJoinKey() {
        return groupKeyJoiner.join(productType, directional, crystalType1, chamfer, formula, siliconSupplier, thickness, workCenterCode);
    }

    @JsonIgnore
    public String getJoinKey2() {
        return groupKeyJoiner.join(productType, directional, crystalType1, chamfer, formula, siliconSupplier);
    }

    @JsonIgnore
    public String getJoinKeyWithoutWorkCenterCode() {
        return groupKeyJoiner.join(productType, directional, crystalType1, chamfer, formula, siliconSupplier);
    }

    @JsonIgnore
    public String getJoinKey3() {
        return groupKeyJoiner.join(productType, directional, crystalType1, chamfer, formula, siliconSupplier, thickness);
    }
}
