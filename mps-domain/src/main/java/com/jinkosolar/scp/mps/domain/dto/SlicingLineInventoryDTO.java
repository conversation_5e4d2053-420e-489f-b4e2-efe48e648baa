package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("切片线边在制数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlicingLineInventoryDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID，自增
     */
    @ApiModelProperty("主键ID，自增")
    @ExcelProperty(value = "主键ID，自增")  
    private Long id;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String site;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshop;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")  
    private String area;
    /**
     * 产品物料号
     */
    @ApiModelProperty("产品物料号")
    @ExcelProperty(value = "产品物料号")  
    private String itemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String itemDesc;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")  
    private String productionNo;
    /**
     * 原始重量KG(方棒)
     */
    @ApiModelProperty("原始重量KG(方棒)")
    @ExcelProperty(value = "原始重量KG(方棒)")  
    private String originalWeight;
    /**
     * 拉晶配方工艺代码
     */
    @ApiModelProperty("拉晶配方工艺代码")
    @ExcelProperty(value = "拉晶配方工艺代码")  
    private String pullingRecipe;
    /**
     * 高阻
     */
    @ApiModelProperty("高阻")
    @ExcelProperty(value = "高阻")  
    private String highResistance;
    /**
     * 数量(返工/片)
     */
    @ApiModelProperty("数量(返工/片)")
    @ExcelProperty(value = "数量(返工/片)")  
    private BigDecimal quantity;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")  
    private String directional;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")  
    private LocalDateTime dateTime;

    @ApiModelProperty("倒角")
    private String chamfer;


    @ApiModelProperty("厚度")
    private String thickness;

    @ApiModelProperty("尺寸")
    private String size;

}