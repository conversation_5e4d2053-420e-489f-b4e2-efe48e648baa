package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("切片良率数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SlicingYieldRateDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID，自增
     */
    @ApiModelProperty("主键ID，自增")
    @ExcelProperty(value = "主键ID，自增")
    private Long id;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String site;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    private String workshop;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")
    private String area;
    /**
     * 产品物料号
     */
    @ApiModelProperty("产品物料号")
    @ExcelProperty(value = "产品物料号")
    private String itemCode;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String productionNo;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    private String thcik;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    private String size;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间")
    private LocalDateTime dateTime;
    /**
     * A+良率
     */
    @ApiModelProperty("A+良率")
    @ExcelProperty(value = "A+良率")
    private String yieldAa;
    /**
     * A良率
     */
    @ApiModelProperty("A良率")
    @ExcelProperty(value = "A良率")
    private String yieldA;
    /**
     * 破片率
     */
    @ApiModelProperty("破片率")
    @ExcelProperty(value = "破片率")
    private String fragmentYield;
    /**
     * 总良率
     */
    @ApiModelProperty("总良率")
    @ExcelProperty(value = "总良率")
    private String totalYield;
    /**
     * 实际硅片产出数
     */
    @ApiModelProperty("实际硅片产出数")
    @ExcelProperty(value = "实际硅片产出数")
    private String simplyActualQuantity;
    /**
     * 等级集合
     */
    @ApiModelProperty("等级集合")
    @ExcelProperty(value = "等级集合")
    private String calssList;
    /**
     * A+
     */
    @ApiModelProperty("A+")
    @ExcelProperty(value = "A+")
    private String qtyAa;
    /**
     * A-崩
     */
    @ApiModelProperty("A-崩")
    @ExcelProperty(value = "A-崩")
    private String qtyAb;
    /**
     * A-线
     */
    @ApiModelProperty("A-线")
    @ExcelProperty(value = "A-线")
    private String qtyAx;
    /**
     * A边缘
     */
    @ApiModelProperty("A边缘")
    @ExcelProperty(value = "A边缘")
    private String qtyAby;
    /**
     * A花边
     */
    @ApiModelProperty("A花边")
    @ExcelProperty(value = "A花边")
    private String qtyAhb;
    /**
     * A线痕
     */
    @ApiModelProperty("A线痕")
    @ExcelProperty(value = "A线痕")
    private String qtyAxh;
    /**
     * A污片降级
     */
    @ApiModelProperty("A污片降级")
    @ExcelProperty(value = "A污片降级")
    private String qtyAwp;
    /**
     * A低阻
     */
    @ApiModelProperty("A低阻")
    @ExcelProperty(value = "A低阻")
    private String qtyAdz;
    /**
     * A高阻
     */
    @ApiModelProperty("A高阻")
    @ExcelProperty(value = "A高阻")
    private String qtyAgz;
    /**
     * B崩边
     */
    @ApiModelProperty("B崩边")
    @ExcelProperty(value = "B崩边")
    private String qtyJbbb;
    /**
     * B边缘
     */
    @ApiModelProperty("B边缘")
    @ExcelProperty(value = "B边缘")
    private String qtyJbby;
    /**
     * B尺寸
     */
    @ApiModelProperty("B尺寸")
    @ExcelProperty(value = "B尺寸")
    private String qtyJbcc;
    /**
     * B硅落
     */
    @ApiModelProperty("B硅落")
    @ExcelProperty(value = "B硅落")
    private String qtyJbgl;
    /**
     * B厚薄
     */
    @ApiModelProperty("B厚薄")
    @ExcelProperty(value = "B厚薄")
    private String qtyJbhb;
    /**
     * B花片
     */
    @ApiModelProperty("B花片")
    @ExcelProperty(value = "B花片")
    private String qtyJbhp;
    /**
     * B线痕
     */
    @ApiModelProperty("B线痕")
    @ExcelProperty(value = "B线痕")
    private String qtyJbxh;
    /**
     * B翘边
     */
    @ApiModelProperty("B翘边")
    @ExcelProperty(value = "B翘边")
    private String qtyJbqb;
    /**
     * B电阻
     */
    @ApiModelProperty("B电阻")
    @ExcelProperty(value = "B电阻")
    private String qtyJbrs;
    /**
     * B色差
     */
    @ApiModelProperty("B色差")
    @ExcelProperty(value = "B色差")
    private String qtyJbsc;
    /**
     * B应力
     */
    @ApiModelProperty("B应力")
    @ExcelProperty(value = "B应力")
    private String qtyJbyl;
    /**
     * C崩硅
     */
    @ApiModelProperty("C崩硅")
    @ExcelProperty(value = "C崩硅")
    private String qtyJcbg;
    /**
     * C尺寸
     */
    @ApiModelProperty("C尺寸")
    @ExcelProperty(value = "C尺寸")
    private String qtyJccc;
    /**
     * C倒角
     */
    @ApiModelProperty("C倒角")
    @ExcelProperty(value = "C倒角")
    private String qtyJcdj;
    /**
     * C厚薄
     */
    @ApiModelProperty("C厚薄")
    @ExcelProperty(value = "C厚薄")
    private String qtyJchb;
    /**
     * C线痕
     */
    @ApiModelProperty("C线痕")
    @ExcelProperty(value = "C线痕")
    private String qtyJcxh;
    /**
     * D穿孔
     */
    @ApiModelProperty("D穿孔")
    @ExcelProperty(value = "D穿孔")
    private String qtyJdck;
    /**
     * D废片
     */
    @ApiModelProperty("D废片")
    @ExcelProperty(value = "D废片")
    private String qtyJdfp;
    /**
     * D厚薄
     */
    @ApiModelProperty("D厚薄")
    @ExcelProperty(value = "D厚薄")
    private String qtyJdhp;
    /**
     * D缺角
     */
    @ApiModelProperty("D缺角")
    @ExcelProperty(value = "D缺角")
    private String qtyJdqj;
    /**
     * D线痕
     */
    @ApiModelProperty("D线痕")
    @ExcelProperty(value = "D线痕")
    private String qtyJdxh;
    /**
     * D隐裂
     */
    @ApiModelProperty("D隐裂")
    @ExcelProperty(value = "D隐裂")
    private String qtyJdyl;
    /**
     * D杂质
     */
    @ApiModelProperty("D杂质")
    @ExcelProperty(value = "D杂质")
    private String qtyJdzz;
}