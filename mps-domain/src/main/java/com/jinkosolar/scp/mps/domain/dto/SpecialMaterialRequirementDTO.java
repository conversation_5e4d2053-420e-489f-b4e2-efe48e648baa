package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SpecialMaterialRequirementDTO对象", description = "DTO对象")
public class SpecialMaterialRequirementDTO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "DP_ID")
    private String dpId;

    @ApiModelProperty(value = "产品族")
    private String productFamily;

    @ApiModelProperty(value = "功率")
    private String power;

    @ApiModelProperty(value = "降档要求")
    private String downshiftRequirement;

    @ApiModelProperty(value = "功率预留")
    private String powerReservation;

    @ApiModelProperty(value = "标片校准")
    private String labelCalibration;

    @ApiModelProperty(value = "来源")
    private Integer source;
}
