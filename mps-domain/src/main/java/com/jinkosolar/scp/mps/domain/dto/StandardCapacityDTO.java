package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;


@ApiModel("标准产能数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StandardCapacityDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    private String version;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE,unTranslate = false)
    @ExcelProperty(value = "工厂")
    private Long factoryId;

    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;


    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryIdName;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE,unTranslate = false)
    private String modelClassification;

    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    private String modelClassificationName;

    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")
    private String factoryDesc;


    /**
     * 生产车间Id
     */
    @ApiModelProperty("生产车间Id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP,unTranslate = false)
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;


    /**
     * 生产车间名称
     */
    @ApiModelProperty("生产车间名称")
    @ExcelProperty(value = "生产车间名称")
    private String workshopIdName;


    /**
     * 产品id
     */
    @ApiModelProperty("产品id")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000,unTranslate = false)
    private Long productId;

    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String product;


    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    @ExcelProperty(value = "产品名称")
    private String productIdName;


    /**
     * 热场
     */
    @ApiModelProperty("热场")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100,unTranslate = false)
    @ExcelProperty(value = "热场")
    private String thermalField;


    /**
     * 热场名称
     */
    @ApiModelProperty("热场名称")
    @ExcelProperty(value = "热场名称")
    private String thermalFieldName;
    /**
     * 年度标准计划 ,0=不是，1=是
     */
    @ApiModelProperty("年度标准计划")
    @ExcelProperty(value = "年度标准计划")
    private String annualStandardPlan;
    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    private LocalDate month;
    /**
     * 月份产能
     */
    @ApiModelProperty("月份产能")
    @ExcelProperty(value = "月份产能")
    private BigDecimal monthValue;

    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new LinkedHashMap<>();

    /**
     * 是否定向
     */
    @ApiModelProperty("是否定向")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directionalId;

    /**
     * 是否定向名称
     */
    @ApiModelProperty("是否定向名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"directionalName"},
            from = {"lovLineId"}, to = {"directionalId"})
    private String directionalName;

    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sizeName"})
    private Long sizeId;

    /**
     * 尺寸名称
     */
    @ApiModelProperty("尺寸名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1300, queryColumns = {"sizeName"},
            from = {"lovLineId"}, to = {"sizeId"})
    private String sizeName;

    /**
     * 高低阻
     */
    @ApiModelProperty("高低阻")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"crystalTypeName"})
    private Long crystalTypeId;

    /**
     * 高低阻名称
     */
    @ApiModelProperty("高低阻名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"crystalTypeName"},
            from = {"lovLineId"}, to = {"crystalTypeId"})
    private String crystalTypeName;

    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"formulaName"})
    private Long formulaId;

    /**
     * 配方名称
     */
    @ApiModelProperty("配方名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"formulaName"},
            from = {"lovLineId"}, to = {"formulaId"})
    private String formulaName;

    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"siliconSupplierName"})
    private Long siliconSupplierId;

    /**
     * 硅料供应商名称
     */
    @ApiModelProperty("硅料供应商名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1800, queryColumns = {"siliconSupplierName"},
            from = {"lovLineId"}, to = {"siliconSupplierId"})
    private String siliconSupplierName;

}