package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("标准电池效率待计算表Aps数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StandardCellEfficApsDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 5W分档组
     */
    @ApiModelProperty("5W分档组")
    @ExcelProperty(value = "5W分档组")  
    private String splitGroup;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")  
    private String productModel;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")  
    private String planVersion;
    /**
     * 投产方案
     */
    @ApiModelProperty("投产方案")
    @ExcelProperty(value = "投产方案")  
    private String productPlan;
    /**
     * 指定材料搭配组合描述
     */
    @ApiModelProperty("指定材料搭配组合描述")
    @ExcelProperty(value = "指定材料搭配组合描述")  
    private String materialCombinationDesc;
    /**
     * 功率预测版本
     */
    @ApiModelProperty("功率预测版本")
    @ExcelProperty(value = "功率预测版本")  
    private String powerVersion;
    /**
     * 工厂_lovId
     */
    @ApiModelProperty("工厂_lovId")
    @ExcelProperty(value = "工厂_lovId")  
    private Long factoryId;
    /**
     * 排产工厂代码
     */
    @ApiModelProperty("排产工厂代码")
    @ExcelProperty(value = "排产工厂代码")  
    private String factoryCode;
    /**
     * 工作中心_LovId
     */
    @ApiModelProperty("工作中心_LovId")
    @ExcelProperty(value = "工作中心_LovId")  
    private Long workCenterId;
    /**
     * 排产工作中心代码
     */
    @ApiModelProperty("排产工作中心代码")
    @ExcelProperty(value = "排产工作中心代码")  
    private String workCenterCode;
    /**
     * 电池产品LovId
     */
    @ApiModelProperty("电池产品LovId")
    @ExcelProperty(value = "电池产品LovId")  
    private Long productTypeId;
    /**
     * 电池产品
     */
    @ApiModelProperty("电池产品")
    @ExcelProperty(value = "电池产品")  
    private String cellProductCode;
    /**
     * 电池尺寸_LovId
     */
    @ApiModelProperty("电池尺寸_LovId")
    @ExcelProperty(value = "电池尺寸_LovId")  
    private Long cellSizeId;
    /**
     * 电池尺寸
     */
    @ApiModelProperty("电池尺寸")
    @ExcelProperty(value = "电池尺寸")  
    private String cellSize;
    /**
     * 主栅数_LovId
     */
    @ApiModelProperty("主栅数_LovId")
    @ExcelProperty(value = "主栅数_LovId")  
    private Long mainGridId;
    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数")
    @ExcelProperty(value = "主栅数")  
    private String gridNum;
    /**
     * 晶体类型_LovId
     */
    @ApiModelProperty("晶体类型_LovId")
    @ExcelProperty(value = "晶体类型_LovId")  
    private Long crystalTypeId;
    /**
     * 晶体类型
     */
    @ApiModelProperty("晶体类型")
    @ExcelProperty(value = "晶体类型")  
    private String crystalType;
    /**
     * 单双封_LovId
     */
    @ApiModelProperty("单双封_LovId")
    @ExcelProperty(value = "单双封_LovId")  
    private Long sealTypeId;
    /**
     * 单双封
     */
    @ApiModelProperty("单双封")
    @ExcelProperty(value = "单双封")  
    private String sealType;
    /**
     * APS模型代码
     */
    @ApiModelProperty("APS模型代码")
    @ExcelProperty(value = "APS模型代码")  
    private String modelType;
    /**
     * 片串
     */
    @ApiModelProperty("片串")
    @ExcelProperty(value = "片串")  
    private String cellString;
    /**
     * 赠送功率
     */
    @ApiModelProperty("赠送功率")
    @ExcelProperty(value = "赠送功率")  
    private String giftsPower;
    /**
     * 功率公差从
     */
    @ApiModelProperty("功率公差从")
    @ExcelProperty(value = "功率公差从")  
    private String powerDiffFrom;
    /**
     * 单位(功率公差从)
     */
    @ApiModelProperty("单位(功率公差从)")
    @ExcelProperty(value = "单位(功率公差从)")  
    private String powerUnitDiffFrom;
    /**
     * 需求高档比例
     */
    @ApiModelProperty("需求高档比例")
    @ExcelProperty(value = "需求高档比例")  
    private String demandHighGrade;
    /**
     * 需求低档比例
     */
    @ApiModelProperty("需求低档比例")
    @ExcelProperty(value = "需求低档比例")  
    private String demandLowGrade;
    /**
     * 高档比例
     */
    @ApiModelProperty("高档比例")
    @ExcelProperty(value = "高档比例")  
    private String highGrade;
    /**
     * 低档比例
     */
    @ApiModelProperty("低档比例")
    @ExcelProperty(value = "低档比例")  
    private String lowGrade;
    /**
     * 高档瓦数
     */
    @ApiModelProperty("高档瓦数")
    @ExcelProperty(value = "高档瓦数")  
    private String highWattage;
    /**
     * 低档瓦数
     */
    @ApiModelProperty("低档瓦数")
    @ExcelProperty(value = "低档瓦数")  
    private String lowWattage;
    /**
     * 物控指定焊接_lov_id
     */
    @ApiModelProperty("物控指定焊接_lov_id")
    private Long weldingId;
    /**
     * 物控指定焊接_lov_value
     */
    @ApiModelProperty("物控指定焊接_lov_value")
    @ExcelProperty(value = "物控指定焊接_lov_value")  
    private String weldingCode;
    /**
     * 物控指定汇流条_lov_id
     */
    @ApiModelProperty("物控指定汇流条_lov_id")
    private Long busBarId;
    /**
     * 物控指定汇流条_lov_value
     */
    @ApiModelProperty("物控指定汇流条_lov_value")
    @ExcelProperty(value = "物控指定汇流条_lov_value")  
    private String busBarCode;
    /**
     * 主推焊接_lov_id
     */
    @ApiModelProperty("主推焊接_lov_id")
    private Long mainWeldingId;
    /**
     * 主推焊接_lov_value
     */
    @ApiModelProperty("主推焊接_lov_value")
    @ExcelProperty(value = "主推焊接_lov_value")  
    private String mainWeldingCode;
    /**
     * 主推汇流条_lov_id
     */
    @ApiModelProperty("主推汇流条_lov_id")
    private Long mainBusBarId;
    /**
     * 主推汇流条_lov_value
     */
    @ApiModelProperty("主推汇流条_lov_value")
    @ExcelProperty(value = "主推汇流条_lov_value")  
    private String mainBusBarCode;
    /**
     * 使用电池片系列描述
     */
    @ApiModelProperty("使用电池片系列描述")
    @ExcelProperty(value = "使用电池片系列描述")  
    private String applyCellSeriesDesc;
    /**
     * 使用功率预测版本
     */
    @ApiModelProperty("使用功率预测版本")
    @ExcelProperty(value = "使用功率预测版本")  
    private String applyPowerVersion;
    /**
     * 使用材料搭配组合描述
     */
    @ApiModelProperty("使用材料搭配组合描述")
    @ExcelProperty(value = "使用材料搭配组合描述")  
    private String applyMaterialCombinationDesc;
    /**
     * 效率数值
     */
    @ApiModelProperty("效率数值")
    @ExcelProperty(value = "效率数值")  
    private String efficiencyValue;

    /**
     * 汇流条-是否反光 LOV：ATTR_TYPE_009_ATTR_1800
     */
    @ApiModelProperty(value = "汇流条-是否反光 LOV：ATTR_TYPE_009_ATTR_1800")
    private Long busBarReflectiveId;
    /**
     * 互联条-直径 LOV：ATTR_TYPE_010_ATTR_1000
     */
    @ApiModelProperty(value = "互联条-直径 LOV：ATTR_TYPE_010_ATTR_1000")
    private Long interconnectDiameterId;

    // 功率落档基本表版本 powerDebatingVersion
    private String powerDebatingVersion;

    /**
     * 运算异常提示
     */
    @ApiModelProperty("运算异常提示")
    @ExcelProperty(value = "运算异常提示")
    private String abnormalOperationPrompt;
    // 效率 efficiency
    
    private BigDecimal efficiency;
    // 功率 power
    
    private BigDecimal power;

    // 重算后功率 resetPower
    private BigDecimal resetPower;

    /**
     * 落档1
     */
    @ApiModelProperty(value = "落档1")
    private String derating1;
    /**
     * 落档1比率
     */
    @ApiModelProperty(value = "落档1比率")
    private String derating1Ratio;
    /**
     * 落档2
     */
    @ApiModelProperty(value = "落档2")
    private String derating2;
    /**
     * 落档2比率
     */
    @ApiModelProperty(value = "落档2比率")
    private String derating2Ratio;

    // 标记1
    private String giftsFlag1;

    // 标记2
    private String giftsFlag2;
}