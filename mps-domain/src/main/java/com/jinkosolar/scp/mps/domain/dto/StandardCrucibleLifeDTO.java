package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ApiModel("标准坩埚寿命数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StandardCrucibleLifeDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String versionNumber;
    /**
     * 工厂
     */
    @ExcelIgnore
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"factoryCode","factoryName"})
    private Long factoryId;

    @ExcelIgnore
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private String factoryName;

    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"factoryId"}, required = true)
    private String factoryCode;
    /**
     * N/P型
     */
    @ExcelIgnore
    @ApiModelProperty("N/P型")
    @ExcelProperty(value = "N/P型")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_001_ATTR_1100, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"cellTypeCode","cellTypeName"})
    private Long cellTypeId;

    @ExcelIgnore
    @ApiModelProperty("N/P型")
    @ExcelProperty(value = "N/P型")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_001_ATTR_1100, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"cellTypeId","cellTypeCode"}, required = true)
    private String cellTypeName;

    @ApiModelProperty("N/P型")
    @ExcelProperty(value = "N/P型")
    private String cellTypeCode;

    /**
     * 热场
     */
    @ExcelIgnore
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    @Translate(DictType = LovHeaderCodeConstant.MPS_HOTFIELD, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"thermalFieldCode","thermalFieldName"})
    private Long thermalFieldId;

    @ExcelIgnore
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_HOTFIELD, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"thermalFieldId","thermalFieldCode"}, required = true)
    private String thermalFieldName;

    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    private String thermalFieldCode;

    /**
     * 等级
     */
    @ExcelIgnore
    @ApiModelProperty("等级")
    @ExcelProperty(value = "等级")
    @Translate(DictType = LovHeaderCodeConstant.MPS_CRYSTAL_GGCLASS, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"gradeCode","gradeName"})
    private Long gradeId;

    @ExcelIgnore
    @ApiModelProperty("等级")
    @ExcelProperty(value = "等级")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_CRYSTAL_GGCLASS, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"gradeId","gradeCode"}, required = true)
    private String gradeName;

    @ApiModelProperty("等级")
    @ExcelProperty(value = "等级")
    private String gradeCode;
    /**
     * 厂家id
     */
    @ExcelIgnore
    @ApiModelProperty("厂家id")
    @ExcelProperty(value = "厂家id")
    @Translate(DictType = LovHeaderCodeConstant.CRUCIBLE_PROVIDER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"vendorCode","vendorName"})
    private Long vendorId;

    @ExcelIgnore
    @ApiModelProperty("厂家")
    @ExcelProperty(value = "厂家")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.CRUCIBLE_PROVIDER, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"vendorId"}, required = true)
    private String vendorName;

    @ApiModelProperty("厂家")
    @ExcelProperty(value = "厂家")
    private String vendorCode;
    /**
     * 标准坩埚寿命
     */
    @ApiModelProperty("标准坩埚寿命")
    @ExcelProperty(value = "标准坩埚寿命")
    @Translate( required = true)
    private Integer crucibleLife;

    /**
     * 数据类型
     */
    @ApiModelProperty("数据类型")
    @ExcelProperty(value = "数据类型")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataType;

    @ExcelIgnore
    @ApiModelProperty("数据类型")
    @ExcelProperty(value = "数据类型")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"dataType","dataTypeCode"}, required = true)
    private String dataTypeName;

    @ExcelIgnore
    @ApiModelProperty("数据类型")
    @ExcelProperty(value = "数据类型")
    private String dataTypeCode;


    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"workshopIdName"})
    private Long workshopId;

    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"workshopId"}, required = true)
    private String workshopIdName;
}