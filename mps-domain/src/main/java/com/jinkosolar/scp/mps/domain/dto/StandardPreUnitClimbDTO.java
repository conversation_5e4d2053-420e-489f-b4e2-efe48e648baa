package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("国内老基地标准单产爬坡数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StandardPreUnitClimbDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;
    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心Id")
    @ExcelProperty(value = "工作中心Id")
    private Long workCenterId;
    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterCode;

    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心名换")
    @ExcelProperty(value = "工作中心名换")
    private String workCenterName;
    /**
     * 工厂Id
     */
    @ApiModelProperty("工厂Id")
    @ExcelProperty(value = "工厂Id")
    private Long factoryId;

    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂Id
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;
    /**
     * 排产区域Id
     */
    @ApiModelProperty("排产区域Id")
    @ExcelProperty(value = "排产区域Id")
    private Long schedulingAreaId;

    /**
     * 排产区域Id
     */
    @ApiModelProperty("排产区域Id")
    @ExcelProperty(value = "排产区域Id")
    private String schedulingAreaName;
    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    @ExcelProperty(value = "产品Id")
    private Long productId;

    /**
     * 产品Id
     */
    @ApiModelProperty("产品Code")
    @ExcelProperty(value = "产品Code")
    private String productCode;

    /**
     * 产品Id
     */
    @ApiModelProperty("产品名称")
    @ExcelProperty(value = "产品名称")
    private String productName;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    private Long thicknessId;

    /**
     * 厚度
     */
    @ApiModelProperty("厚度Code")
    @ExcelProperty(value = "厚度Code")
    private String thicknessCode;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度名称")
    @ExcelProperty(value = "厚度名称")
    private String thicknessName;
    /**
     * 机型
     */
    @ApiModelProperty("机型")
    @ExcelProperty(value = "机型")
    private String machineType;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    private Long sizeId;

    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸Code")
    @ExcelProperty(value = "尺寸Code")
    private String sizeCode;

    /**
     * 尺寸
     */
    @ApiModelProperty("名称")
    @ExcelProperty(value = "尺寸")
    private String sizeName;
    /**
     * 单产(万片)
     */
    @ApiModelProperty("单产(万片)")
    @ExcelProperty(value = "单产(万片)")
    private BigDecimal preUnit;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    @ExcelProperty(value = "日期")
    private LocalDate date;
    /**
     * 备用1
     */
    @ApiModelProperty("备用1")
    @ExcelProperty(value = "备用1")
    private String attribute1;
    /**
     * 备用2
     */
    @ApiModelProperty("备用2")
    @ExcelProperty(value = "备用2")
    private String attribute2;
    /**
     * 备用3
     */
    @ApiModelProperty("备用3")
    @ExcelProperty(value = "备用3")
    private String attribute3;
    /**
     * 备用4
     */
    @ApiModelProperty("备用4")
    @ExcelProperty(value = "备用4")
    private String attribute4;
    /**
     * 备用5
     */
    @ApiModelProperty("备用5")
    @ExcelProperty(value = "备用5")
    private String attribute5;
}