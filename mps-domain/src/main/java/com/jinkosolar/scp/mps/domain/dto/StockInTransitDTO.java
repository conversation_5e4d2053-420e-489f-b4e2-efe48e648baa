package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("调拨在途数据数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StockInTransitDTO extends BaseDTO implements Serializable {
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private Long id;
    /**
     * 供货工厂代码
     */
    @ApiModelProperty("供货工厂代码")
    @ExcelProperty(value = "供货工厂代码")  
    private String fromfactoryCode;
    /**
     * 供货工厂描述
     */
    @ApiModelProperty("供货工厂描述")
    @ExcelProperty(value = "供货工厂描述")  
    private String fromfactoryDesc;
    /**
     * 接收工厂代码
     */
    @ApiModelProperty("接收工厂代码")
    @ExcelProperty(value = "接收工厂代码")  
    private String tofactoryCode;
    /**
     * 接收工厂描述
     */
    @ApiModelProperty("接收工厂描述")
    @ExcelProperty(value = "接收工厂描述")  
    private String tofactoryDesc;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")  
    private String itemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String itemDesc;
    /**
     * 基本计量单位
     */
    @ApiModelProperty("基本计量单位")
    @ExcelProperty(value = "基本计量单位")  
    private String uom;
    /**
     * 采购单号
     */
    @ApiModelProperty("采购单号")
    @ExcelProperty(value = "采购单号")  
    private String pono;
    /**
     * 采购行号
     */
    @ApiModelProperty("采购行号")
    @ExcelProperty(value = "采购行号")  
    private String poLineno;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @ExcelProperty(value = "数量")  
    private BigDecimal quantity;
    /**
     * 发出时间
     */
    @ApiModelProperty("发出时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "发出时间")  
    private LocalDateTime sendTime;
    /**
     * 批次号
     */
    @ApiModelProperty("批次号")
    @ExcelProperty(value = "批次号")  
    private String lotNo;
    /**
     * 颜色
     */
    @ApiModelProperty("颜色")
    @ExcelProperty(value = "颜色")  
    private String colorCode;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")  
    private String size;
    /**
     * 定向／非定向
     */
    @ApiModelProperty("定向／非定向")
    @ExcelProperty(value = "定向／非定向")  
    private String direction;
    /**
     * 指定配方
     */
    @ApiModelProperty("指定配方")
    @ExcelProperty(value = "指定配方")  
    private String recipe;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")  
    private String thickness;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")  
    private String remark;
    /**
     * 物料分类5
     */
    @ApiModelProperty("物料分类5")
    @ExcelProperty(value = "物料分类5")  
    private String categorySegment5;
}