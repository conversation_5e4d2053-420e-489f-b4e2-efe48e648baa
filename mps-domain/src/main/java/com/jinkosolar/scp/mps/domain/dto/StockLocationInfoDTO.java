package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("库存地点基本信息维护数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StockLocationInfoDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂ID")
    @ExcelProperty(value = "工厂ID")
    @Translate(DictType = MpsLovConstant.FACTORY, queryColumns = {"lovLineId"}, from = {"lovValue", "lovName"}, to = {"factoryCode", "factoryName"})
    private Long factoryId;
    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;
    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")
    private String factoryName;
    /**
     * ERP库存地点
     */
    @ApiModelProperty("ERP库存地点")
    @ExcelProperty(value = "ERP库存地点")
    private String stockCode;

    /**
     * 电池产品_lov_id
     */
    @ApiModelProperty("电池产品_lov_id")
    @ExcelProperty(value = "电池产品")
    @Translate(DictType = MpsLovConstant.ATTR_TYPE_006_ATTR_1000, queryColumns = {"lovLineId"}, from = {"lovValue", "lovName"}, to = {"productTypeCode", "productTypeName"})
    private Long productTypeId;

    /**
     * 电池产品_编码
     */
    @ApiModelProperty("电池产品_编码")
    @ExcelProperty(value = "电池产品_编码")
    private String productTypeCode;

    /**
     * 电池产品_名称
     */
    @ApiModelProperty("电池产品_名称")
    @ExcelProperty(value = "电池产品_名称")
    private String productTypeName;
}