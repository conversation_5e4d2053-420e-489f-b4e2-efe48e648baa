package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


@ApiModel("MPS自产电池调拨计划定向非定向转换数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StockTransferDirectionalDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 供应商品牌
     */
    @ApiModelProperty("供应商品牌")
    @ExcelProperty(value = "供应商品牌")  
    private String vendorBrand;
    /**
     * 发出工厂_id
     */
    @ApiModelProperty("发出工厂_id")
    @ExcelProperty(value = "发出工厂_id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"fromFactoryName"})
    private Long fromFactoryId;
    /**
     * 发出工厂_编码
     */
    @ApiModelProperty("发出工厂_编码")
    @ExcelProperty(value = "发出工厂_编码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"fromFactoryId"}, required = true)
    private String fromFactoryCode;
    /**
     * 发出工厂_名称
     */
    @ApiModelProperty("发出工厂_名称")
    @ExcelProperty(value = "发出工厂_名称")
    private String fromFactoryName;
    /**
     * 接收工厂_id
     */
    @ApiModelProperty("接收工厂_id")
    @ExcelProperty(value = "接收工厂_id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"toFactoryName"})
    private Long toFactoryId;
    /**
     * 接收工厂_编码
     */
    @ApiModelProperty("接收工厂_编码")
    @ExcelProperty(value = "接收工厂_编码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"toFactoryId"})
    private String toFactoryCode;
    /**
     * 接收工厂_名称
     */
    @ApiModelProperty("接收工厂_名称")
    @ExcelProperty(value = "接收工厂_名称")
    private String toFactoryName;
    /**
     * 接收排产区域id
     */
    @ApiModelProperty("接收排产区域id")
    @ExcelProperty(value = "接收排产区域id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"areaName"})
    private Long areaId;
    /**
     * 接收排产区域编码
     */
    @ApiModelProperty("接收排产区域编码")
    @ExcelProperty(value = "接收排产区域编码")
    private String areaCode;
    /**
     * 接收排产区域名称
     */
    @ApiModelProperty("接收排产区域名称")
    @ExcelProperty(value = "接收排产区域名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"areaId","areaCode"}, required = true)
    private String areaName;
    /**
     * 定向非定向_ID
     */
    @ApiModelProperty("定向非定向_ID")
    @ExcelProperty(value = "定向非定向_ID")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directional;
    /**
     * 定向非定向_编码
     */
    @ApiModelProperty("定向非定向_编码")
    @ExcelProperty(value = "定向非定向_编码")
    private String directionalCode;
    /**
     * 定向非定向_名称
     */
    @ApiModelProperty("定向非定向_名称")
    @ExcelProperty(value = "定向非定向_名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"directional","directionalCode"}, required = true)
    private String directionalName;
}