package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.math.BigDecimal;


@ApiModel("补减投报表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SupplementReduceInvestmentReportDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * SAP订单号
     */
    @ApiModelProperty("SAP订单号")
    @ExcelProperty(value = "SAP订单号")
    private String sapOrderNo;
    /**
     * SAP订单行号
     */
    @ApiModelProperty("SAP订单行号")
    @ExcelProperty(value = "SAP订单行号")
    private String sapLineId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 需求数量
     */
    @ApiModelProperty("需求数量")
    @ExcelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")
    private String planType;
    /**
     * 计划转库存率
     */
    @ApiModelProperty("计划转库存率")
    @ExcelProperty(value = "计划转库存率")
    private BigDecimal convertInventoryRate;
    /**
     * 货好日期
     */
    @ApiModelProperty("货好日期")
    @ExcelProperty(value = "货好日期")
    private LocalDate plannedCompleteDate;
    /**
     * 车间良率
     */
    @ApiModelProperty("车间良率")
    @ExcelProperty(value = "车间良率")
    private String workshopYieldRate;
    /**
     * 实际投入数量
     */
    @ApiModelProperty("实际投入数量")
    @ExcelProperty(value = "实际投入数量")
    private BigDecimal actualQuantity;
    /**
     * 产出数量
     */
    @ApiModelProperty("产出数量")
    @ExcelProperty(value = "产出数量")
    private BigDecimal outPutQuantity;
    /**
     * WIP(在制)数量
     */
    @ApiModelProperty("WIP(在制)数量")
    @ExcelProperty(value = "WIP(在制)数量")
    private BigDecimal wipQuantity;
    /**
     * OK(可以出货)数量
     */
    @ApiModelProperty("OK(可以出货)数量")
    @ExcelProperty(value = "OK(可以出货)数量")
    private BigDecimal okQuantity;
    /**
     * NG(不良品)数量
     */
    @ApiModelProperty("NG(不良品)数量")
    @ExcelProperty(value = "NG(不良品)数量")
    private BigDecimal ngQuantity;
    /**
     * JK(转库存)数量
     */
    @ApiModelProperty("JK(转库存)数量")
    @ExcelProperty(value = "JK(转库存)数量")
    private BigDecimal jkQuantity;
    /**
     * 报废数量
     */
    @ApiModelProperty("报废数量")
    @ExcelProperty(value = "报废数量")
    private BigDecimal bfQuantity;
    /**
     * 订单良率数量
     */
    @ApiModelProperty("订单良率数量")
    @ExcelProperty(value = "订单良率数量")
    private BigDecimal orderYieldRate;
    /**
     * 需求差异数量
     */
    @ApiModelProperty("需求差异数量")
    @ExcelProperty(value = "需求差异数量")
    private BigDecimal demandDiffQty;
    /**
     * 补减投数量
     */
    @ApiModelProperty("补减投数量")
    @ExcelProperty(value = "补减投数量")
    private BigDecimal bjtQuantity;
}