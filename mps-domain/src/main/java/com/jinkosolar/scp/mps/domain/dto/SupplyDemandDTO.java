package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * 供应需求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-01 18:24:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SupplyDemandDTO对象", description = "DTO对象")
public class SupplyDemandDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 特殊单
     */
    @ApiModelProperty(value = "特殊单")
    private String specialSn;
    /**
     * 客户需求
     */
    @ApiModelProperty(value = "客户需求")
    private String customerDemand;
    /**
     * 特殊项属性值名称
     */
    @ApiModelProperty(value = "特殊项属性值名称")
    private String specialitemAttributeValue;
}
