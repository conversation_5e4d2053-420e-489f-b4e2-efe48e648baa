package com.jinkosolar.scp.mps.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDate;
import com.ibm.scp.common.api.base.TokenDTO;


/**
 * 供应需求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-01 18:24:27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "SupplyDemand保存参数", description = "保存参数")
public class SupplyDemandSaveDTO extends TokenDTO implements Serializable {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 特殊单
     */
    @ApiModelProperty(value = "特殊单")
    private String specialSn;
    /**
     * 客户需求
     */
    @ApiModelProperty(value = "客户需求")
    private String customerDemand;
    /**
     * 特殊项属性值名称
     */
    @ApiModelProperty(value = "特殊项属性值名称")
    private String specialitemAttributeValue;
}
