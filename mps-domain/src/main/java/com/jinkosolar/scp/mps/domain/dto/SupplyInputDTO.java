package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("补投数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SupplyInputDTO extends PageDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    @ExcelProperty(value = "销售订单行")
    private String saleOrderLine;
    /**
     * 行号
     */
    @ApiModelProperty("行号")
    @ExcelProperty(value = "行号")
    private String lineNum;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号id")
    private String productType;

    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")
    private String productTypeName;
    /**
     * 计划型号
     */
    @ApiModelProperty("计划型号")
    @ExcelProperty(value = "计划型号")
    private String planVersion;
    /**
     * 补投量
     */
    @ApiModelProperty("补投量")
    @ExcelProperty(value = "补投量")
    private Integer supplyInputNum;
    /**
     * 补投方式
     */
    @ApiModelProperty("补投方式id")
    @Dict(headerCode = MpsLovConstant.REMEDY_WAY)
    private Long supplyInputId;

    /**
     * 补投方式
     */
    @ApiModelProperty("补投方式")
    @ExcelProperty(value = "补投方式")
    @ImportExConvert(sql = ExLovTransConstant.SQL + "'" + MpsLovConstant.REMEDY_WAY + "'", targetFieldName = "supplyInputId")
    private String supplyInputIdName;
    /**
     * 补投工作中心
     */
    @ApiModelProperty("补投工作中心id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workCenterCode","workCenterDesc"})
    private Long workCenterId;

    @ApiModelProperty("补投工作中心id")
    @ExcelProperty(value = "补投工作中心编码")
    @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + MpsLovConstant.WORK_CENTER + "'", targetFieldName = "workCenterId")
    private String workCenterCode;

    /**
     * 补投工作中心
     */
    @ApiModelProperty("补投工作中心")
    @ExcelProperty(value = "补投工作中心描述")
    private String workCenterDesc;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码id")
    private Long workShopId;

    @ApiModelProperty("车间编码")
//    @ImportExConvert(sql = ExLovTransConstant.VALUE_SQL + "'" + MpsLovConstant.WORKSHOP + "'", targetFieldName = "workShopId")
    private String workShopCode;

    /**
     * 车间代码
     */
    @ApiModelProperty("车间描述")
    @ExcelProperty(value = "车间描述")
    private String workShopDesc;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    @ExcelProperty(value = "状态")
    @Dict(headerCode = MpsLovConstant.MPS_BT_STATE, fieldName = "statusName")
    private Long status;
    /**
     * 状态
     */
    @ApiModelProperty("状态名称")
    @ExcelProperty(value = "状态名称")
    private String statusName;
    /**
     * 排产时间
     */
    @ApiModelProperty("排产时间")
    @ExcelProperty(value = "排产时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime productionTime;
    /**
     * 正排倒排
     */
    @ApiModelProperty("正排倒排")
    @ExcelProperty(value = "正排倒排")
    @Dict(headerCode = MpsLovConstant.MPS_BT_SORT)
    private Long sort;
    /**
     * 正排倒排
     */
    @ApiModelProperty("正排倒排名称")
    @ExcelProperty(value = "正排倒排名称")
    private String sortName;


    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人id")
    private String updatedBy;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedByName;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    private LocalDateTime checkTime;


    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    private String checkUserName;


    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;


    /**
     * 操作人
     */
    @ApiModelProperty(value = "审批人")
    private String operationName;

}
