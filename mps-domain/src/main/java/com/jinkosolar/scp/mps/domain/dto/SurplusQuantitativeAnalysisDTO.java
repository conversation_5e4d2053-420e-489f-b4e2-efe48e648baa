package com.jinkosolar.scp.mps.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.base.LovLineDTO;
import com.ibm.scp.common.api.util.LovUtils;
import com.jinkosolar.scp.mps.domain.constant.CommonConstant;
import com.jinkosolar.scp.mps.domain.constant.Constants;
import com.jinkosolar.scp.mps.domain.constant.enums.SurplusQuantitativeType;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MathUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 剩余可接单量分析
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-08-15 08:02:10
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "剩余可接单量分析DTO对象", description = "DTO对象")
public class SurplusQuantitativeAnalysisDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * IDs
     */
    @ApiModelProperty(value = "IDs")
    private String ids;
    /**
     * 排产区域
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_DOMESTIC_OVERSEA)
    @ApiModelProperty(value = "排产区域")
    private Long domesticOversea;
    /**
     * 排产区域
     */
    @ApiModelProperty(value = "排产区域")
    private String domesticOverseaName;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellType;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    private String mainGrid;
    /**
     * 计划版型
     */
    @ApiModelProperty(value = "计划版型")
    private String planLayout;
    /**
     * 是否供美
     */
    @Translate(DictType = LovHeaderCodeConstant.MPS_SUPPLY_USA)
    @ApiModelProperty(value = "是否供美")
    private Long supplyUs;
    /**
     * 是否供美
     */
    @ApiModelProperty(value = "是否供美")
    private String supplyUsName;
    /**
     * 类型
     */
    @Translate(DictType = LovHeaderCodeConstant.MPS_REPORT1_TYPE, queryColumns = {"lovValue"}, from = {"lovName"}, to = {"typeName"})
    @ApiModelProperty(value = "类型")
    private String type;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String typeName;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private Integer month;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDate localDate;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 动态列头
     */
    @ApiModelProperty(value = "动态列头")
    private List<String> subList;
    /**
     * 动态列值
     */
    @ApiModelProperty(value = "动态列值")
    private Map<String, BigDecimal> subMap;


    public SurplusQuantitativeAnalysisDTO(Long domesticOversea, String cellType, String productType, String mainGrid, String planLayout, Long supplyUs, String type, Integer year, Integer month, BigDecimal quantity) {
        this.domesticOversea = domesticOversea;
        this.cellType = cellType;
        this.productType = productType;
        this.mainGrid = mainGrid;
        this.planLayout = planLayout;
        this.supplyUs = supplyUs;
        this.type = type;
        this.year = year;
        this.month = month;
        this.quantity = quantity;
    }

    public SurplusQuantitativeAnalysisDTO(Long domesticOversea, String cellType, String productType, String mainGrid, String planLayout, Long supplyUs, String type, LocalDate localDate, BigDecimal quantity) {
        this.domesticOversea = domesticOversea;
        this.cellType = cellType;
        this.productType = productType;
        this.mainGrid = mainGrid;
        this.planLayout = planLayout;
        this.supplyUs = supplyUs;
        this.type = type;
        this.localDate = localDate;
        this.quantity = quantity;
    }

    public SurplusQuantitativeAnalysisDTO(Long domesticOversea, String cellType, String productType, String mainGrid, String planLayout, Long supplyUs, String type, Integer year, Integer month) {
        this.domesticOversea = domesticOversea;
        this.cellType = cellType;
        this.productType = productType;
        this.mainGrid = mainGrid;
        this.planLayout = planLayout;
        this.supplyUs = supplyUs;
        this.type = type;
        this.year = year;
        this.month = month;
    }

    public SurplusQuantitativeAnalysisDTO(Long domesticOversea, String cellType, String productType, String mainGrid, String planLayout, Long supplyUs, Integer year, Integer month) {
        this.domesticOversea = domesticOversea;
        this.cellType = cellType;
        this.productType = productType;
        this.mainGrid = mainGrid;
        this.planLayout = planLayout;
        this.supplyUs = supplyUs;
        this.year = year;
        this.month = month;
    }

    public SurplusQuantitativeAnalysisDTO(Long domesticOversea, String productType, Long supplyUs, String type, Integer year) {
        this.domesticOversea = domesticOversea;
        this.productType = productType;
        this.supplyUs = supplyUs;
        this.type = type;
        this.year = year;
        this.planLayout = "小计";
    }

    public SurplusQuantitativeAnalysisDTO(Long domesticOversea, Long supplyUs, String type, Integer year) {
        this.domesticOversea = domesticOversea;
        this.productType = "合计";
        this.supplyUs = supplyUs;
        this.type = type;
        this.year = year;
    }

    public static List<SurplusQuantitativeAnalysisDTO> build(ModuleProductionPlanReportDTO productionPlan, String productType, String cellType, String mainGridLine) {
        cellType = Optional.ofNullable(productType).map(s -> s.substring(0, 1)).orElse(null);
        List<SurplusQuantitativeAnalysisDTO> resultList = Lists.newArrayList();
        Long nonAaId = LovUtils.getLineIdByHeaderCodeAndValue(LovHeaderCodeConstant.MPS_SUPPLY_USA, CommonConstant.NON_NA);
        LovLineDTO lov = LovUtils.getByName(LovHeaderCodeConstant.DP_Destination_Region, productionPlan.getDestAreaNo());
        Long supplyUs = Optional.ofNullable(lov).map(l -> StringUtils.isBlank(l.getAttribute2()) ? nonAaId : Long.parseLong(l.getAttribute2())).orElse(nonAaId);
        //总排产数据
        LinkedHashMap<String, String> planDayQtyTotalMap = JSON.parseObject(productionPlan.getPlanDayQtyInfo(), LinkedHashMap.class);
        planDayQtyTotalMap = planDayQtyTotalMap.entrySet().stream().filter(i -> new BigDecimal(i.getValue()).compareTo(BigDecimal.ZERO) != 0)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (k1, k2) -> k2, LinkedHashMap::new));

        BigDecimal power = Optional.ofNullable(productionPlan.getPower()).orElse(BigDecimal.ZERO);
        int currentMonth = LocalDate.now().getMonthValue();
        for (String planDateStr : planDayQtyTotalMap.keySet()) {
            LocalDate planDate = LocalDate.parse(planDateStr, DateTimeFormatter.ofPattern(Constants.DATE_FORMAT));
            if(planDate == null || planDate.getMonthValue() < currentMonth){
                continue;
            }
            if(StringUtils.isEmpty(planDayQtyTotalMap.get(planDateStr))){
                continue;
            }
            //兆瓦数=排产数量*功率要求/1000000
            BigDecimal quantity = new BigDecimal(planDayQtyTotalMap.get(planDateStr)).multiply(power);
            quantity = MathUtils.checkIsZero(quantity) ? BigDecimal.ZERO : quantity.divide(new BigDecimal(1000000), 2, BigDecimal.ROUND_HALF_UP);
            SurplusQuantitativeAnalysisDTO surplusQuantitativeAnalysis = new SurplusQuantitativeAnalysisDTO(Long.valueOf(productionPlan.getDomesticOversea()), cellType, productType, mainGridLine, productionPlan.getPlanLayout(), supplyUs, SurplusQuantitativeType.SUPPLY.name(), planDate, quantity);
            resultList.add(surplusQuantitativeAnalysis);
        }

        return resultList;
    }

    public static List<SurplusQuantitativeAnalysisDTO> build(ModuleActualProductionQuantityDTO actualProductionQuantity, Long domesticOversea, String productType, String cellType, String mainGridLine, String planLayout, String destAreaNoName, String power) {
        cellType = Optional.ofNullable(productType).map(s -> s.substring(0, 1)).orElse(null);
        List<SurplusQuantitativeAnalysisDTO> resultList = Lists.newArrayList();
        Long naId = LovUtils.getLineIdByHeaderCodeAndValue(LovHeaderCodeConstant.MPS_SUPPLY_USA, CommonConstant.NA);
        Long nonAaId = LovUtils.getLineIdByHeaderCodeAndValue(LovHeaderCodeConstant.MPS_SUPPLY_USA, CommonConstant.NON_NA);
        LovLineDTO lov = LovUtils.getByName(LovHeaderCodeConstant.DP_Destination_Region, destAreaNoName);
        Long supplyUs = Optional.ofNullable(lov).map(l -> StringUtils.isBlank(l.getAttribute2()) ? nonAaId : Long.parseLong(l.getAttribute2())).orElse(nonAaId);
        //实投
        LocalDate uploadDate = actualProductionQuantity.getUploadDate();
        BigDecimal actualQuantity = Optional.ofNullable(actualProductionQuantity.getActualProductionQuantity()).orElse(BigDecimal.ZERO);
        if (Objects.nonNull(uploadDate) && Objects.nonNull(actualQuantity)) {
            //实投兆瓦数=【实投/取消数量】*功率/1000000
            BigDecimal quantity = actualQuantity.multiply(MathUtils.toBigDecimalDefault0(power)).divide(new BigDecimal(1000000), 2, BigDecimal.ROUND_HALF_UP);
            resultList.add(new SurplusQuantitativeAnalysisDTO(domesticOversea,  cellType,productType, mainGridLine, planLayout, supplyUs, SurplusQuantitativeType.SUPPLY.name(), uploadDate, quantity));
        }
        //取消
        LocalDate cancelDate = actualProductionQuantity.getActualCancelledQuantityScanDate();
        BigDecimal cancelQuantity = Optional.ofNullable(actualProductionQuantity.getActualCancelledQuantity()).orElse(BigDecimal.ZERO).negate();
        if (Objects.nonNull(cancelDate) && Objects.nonNull(cancelQuantity)) {
            //实投兆瓦数=【实投/取消数量】*功率/1000000
            BigDecimal quantity = cancelQuantity.multiply(MathUtils.toBigDecimalDefault0(power)).divide(new BigDecimal(1000000), 2, BigDecimal.ROUND_HALF_UP);
            resultList.add(new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType,  mainGridLine, planLayout, supplyUs, SurplusQuantitativeType.SUPPLY.name(), cancelDate, quantity));
        }
        return resultList;
    }


    public static List<SurplusQuantitativeAnalysisDTO> build(ComponentAlignmentPlanDTO demand, Long domesticOversea, String productType, String cellType, String planLayout) {
        Integer year = Integer.valueOf(demand.getYear());
        BigDecimal janPlan = demand.getJanPlan();
        BigDecimal febPlan = demand.getFebPlan();
        BigDecimal marPlan = demand.getMarPlan();
        BigDecimal aprPlan = demand.getAprPlan();
        BigDecimal mayPlan = demand.getMayPlan();
        BigDecimal junPlan = demand.getJunPlan();
        BigDecimal julPlan = demand.getJulPlan();
        BigDecimal augPlan = demand.getAugPlan();
        BigDecimal sepPlan = demand.getSepPlan();
        BigDecimal octPlan = demand.getOctPlan();
        BigDecimal novPlan = demand.getNovPlan();
        BigDecimal decPlan = demand.getDecPlan();
        String lovValue = LovUtils.get(demand.getMainGridId()).getLovValue();
        cellType = Optional.ofNullable(productType).map(s -> s.substring(0, 1)).orElse(null);

        return Arrays.asList(
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 1, janPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 2, febPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 3, marPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 4, aprPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 5, mayPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 6, junPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 7, julPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 8, augPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 9, sepPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 10, octPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 11, novPlan),
                new SurplusQuantitativeAnalysisDTO(domesticOversea, cellType, productType, lovValue, planLayout, demand.getSupplyUsaId(), SurplusQuantitativeType.DEMAND.name(), year, 12, decPlan)
        );
    }

    public SurplusQuantitativeAnalysisDTO summary() {
        return new SurplusQuantitativeAnalysisDTO(this.domesticOversea, this.cellType, this.productType, this.mainGrid, this.planLayout, this.supplyUs, this.type, this.localDate.getYear(), this.localDate.getMonthValue());
    }

    public SurplusQuantitativeAnalysisDTO summaryDemand() {
        return new SurplusQuantitativeAnalysisDTO(this.domesticOversea, this.cellType, this.productType, this.mainGrid, this.planLayout, this.supplyUs, this.type, this.year, this.month);
    }

    public SurplusQuantitativeAnalysisDTO computeGroup() {
        return new SurplusQuantitativeAnalysisDTO(this.domesticOversea, this.cellType, this.productType, this.mainGrid, this.planLayout, this.supplyUs, this.year, this.month);
    }

    public static SurplusQuantitativeAnalysisDTO cloneBean(SurplusQuantitativeType type, SurplusQuantitativeAnalysisDTO result) {
        SurplusQuantitativeAnalysisDTO clone = SerializationUtils.clone(result);
        clone.setType(type.name());
        clone.setQuantity(BigDecimal.ZERO);
        return clone;
    }

    public Map<String, Object> convertMap() {
        Map<String, Object> objectMap = BeanUtil.beanToMap(this);
        if(MapUtils.isNotEmpty(this.subMap)){
            objectMap.putAll(this.subMap);
        }
        return objectMap;
    }

    public SurplusQuantitativeAnalysisDTO totalPlanLayout() {
        return new SurplusQuantitativeAnalysisDTO(this.domesticOversea, this.productType, this.supplyUs, this.type, this.year);
    }

    public SurplusQuantitativeAnalysisDTO totalProductType() {
        return new SurplusQuantitativeAnalysisDTO(this.domesticOversea, this.supplyUs, this.type, this.year);
    }
}
