package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("丝网2小时产量表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SwInstockRecordDTO extends BaseDTO implements Serializable {
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 工厂描述
     */
    @ApiModelProperty("工厂描述")
    @ExcelProperty(value = "工厂描述")  
    private String factoryDesc;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")  
    private Long id;
    /**
     * 库存地点
     */
    @ApiModelProperty("库存地点")
    @ExcelProperty(value = "库存地点")  
    private String invSiteCode;
    /**
     * 库存地点描述
     */
    @ApiModelProperty("库存地点描述")
    @ExcelProperty(value = "库存地点描述")  
    private String invSiteName;
    /**
     * 线体
     */
    @ApiModelProperty("线体")
    @ExcelProperty(value = "线体")  
    private String linear;
    /**
     * 工序
     */
    @ApiModelProperty("工序")
    @ExcelProperty(value = "工序")  
    private String operationSeq;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production00000200;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production02000400;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production04000600;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production06000800;
    /**
     * 08:00-10:00 2h产量
     */
    @ApiModelProperty("08:00-10:00 2h产量")
    @ExcelProperty(value = "08:00-10:00 2h产量")  
    private BigDecimal production08001000;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production10001200;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production12001400;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production14001600;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production16001800;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production18002000;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production20002200;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private BigDecimal production22002400;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")  
    private String region;
    /**
     * 班次
     */
    @ApiModelProperty("班次")
    @ExcelProperty(value = "班次")  
    private String shift;
    /**
     * 来源
     */
    @ApiModelProperty("来源")
    @ExcelProperty(value = "来源")  
    private String sourceType;
    /**
     * 统计日期
     */
    @ApiModelProperty("统计日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "统计日期")  
    private LocalDateTime statisicsDate;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")  
    private String workCenterCode;
}