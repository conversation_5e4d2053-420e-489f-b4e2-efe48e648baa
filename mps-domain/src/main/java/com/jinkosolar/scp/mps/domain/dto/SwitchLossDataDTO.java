package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.util.ValidGroups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("电池-切换损失数据表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SwitchLossDataDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;
    /**
     * 电池类型数据值集_产品LOV值集
     */
    @ApiModelProperty("电池类型数据值集_产品LOV值集")
    @Dict(headerCode = "ATTR_TYPE_006_ATTR_1000")
    @ExcelProperty(value = "电池类型数据值集_产品LOV值集")
    @NotBlank(message = "电池类型不能为空")
    private Long productId;
    /**
     * 电池类型数据值集_产品LOV值集
     */
    @ApiModelProperty("产品_名称")
    @ExcelProperty(value = "产品")
    @ImportExConvert(required = true,sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 and t2.lov_code='ATTR_TYPE_006_ATTR_1000'", targetFieldName = "productId")
    private String productIdName;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")
    @NotNull(message = "区域不能为空")
    private String switchArea;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime switchBeginDate;
    /**
     * 切回结束时间
     */
    @ApiModelProperty("切回结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切回结束时间")
    @NotNull(message = "切回时间不能为空")
    private LocalDateTime switchEndDate;

    @ApiModelProperty("切换开始数量")
    @ExcelProperty(value = "切换开始数量")
    @NotNull(message = "切换数量不能为空")
    private BigDecimal switchQty;
    /**
     * 切换类型
     */
    @ApiModelProperty("切换类型")
    @ExcelProperty(value = "切换类型")
    @NotNull(message = "切换类型不能为空")
    private String switchType;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    @ImportExConvert(sql = "select t.id from mps_work_center t where t.is_deleted = 0 and t.work_center_code = ?1", targetFieldName = "workCenterId")
    @Dict(methodName = "getWorkCenterCodeById",fieldKey = "workCenterId",fieldName = "workCenterCode")
    private String workCenterCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    @Dict(methodName = "getWorkCenterNameById",fieldKey = "workCenterId",fieldName = "workCenterDesc")
    private String workCenterDesc;
    /**
     * 工作中心_LOV值集
     */
    @ApiModelProperty("工作中心_LOV值集")
    @ExcelProperty(value = "工作中心_LOV值集")
    private Long workCenterId;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    @Dict(headerCode = "SYS.ORG_ARCHITECTURE")
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    @ImportExConvert(required = true,sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0" +
            " WHERE t1.is_deleted = 0 AND t1.lov_value = ?1 and t2.lov_code='SYS.ORG_ARCHITECTURE'", targetFieldName = "factoryId")
    @Dict(methodName = "getLovValueByLovLineId",fieldKey = "factoryId",fieldName = "factoryCode")
    private String factoryCode;

    @ApiModelProperty("单位")
    private String switchUom;

    @ApiModelProperty("版本")
    private String version;
}
