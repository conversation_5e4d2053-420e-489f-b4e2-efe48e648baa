package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("切换损失数据明细表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SwitchLossDataDetailDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @ExcelProperty(value = "主键ID")  
    private Long id;
    /**
     * 切换损失数据表ID
     */
    @ApiModelProperty("切换损失数据表ID")
    @ExcelProperty(value = "切换损失数据表ID")  
    private Long switchLossDataId;
    /**
     * 切换类型
     */
    @ApiModelProperty("切换类型")
    @ExcelProperty(value = "切换类型")  
    private String switchType;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")  
    private Long workCenterId;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")  
    private String switchArea;
    /**
     * 电池类型数据值集_产品LOV值集
     */
    @ApiModelProperty("电池类型数据值集_产品LOV值集")
    @ExcelProperty(value = "电池类型数据值集_产品LOV值集")  
    private Long productId;
    /**
     * 切换开始数量
     */
    @ApiModelProperty("切换开始数量")
    @ExcelProperty(value = "切换开始数量")  
    private BigDecimal switchQty;
    /**
     * 切换开始时间
     */
    @ApiModelProperty("切换开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "切换开始时间")  
    private LocalDateTime switchDate;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    @ExcelProperty(value = "单位")  
    private String switchUom;
}