package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "炉台坩埚寿命表数据转换对象", description = "DTO对象")
public class SyncEquipmentInventoryLifespanDTO extends JipRequestData {
    @JSONField(
            name = "IT_DATA"
    )
    private EquipmentInventoryLifespan_IT_DATA pg = new EquipmentInventoryLifespan_IT_DATA();
    public EquipmentInventoryLifespan_IT_DATA getEquipmentInventoryLifespan_IT_DATA() {
        return this.pg;
    }

    public void setEquipmentInventoryLifespan_IT_DATA(EquipmentInventoryLifespan_IT_DATA info) {
        this.pg = info;
    }

    @Data
    public static class EquipmentInventoryLifespan_IT_DATA {
        /**
         * 主键ID，自增
         */
        @ApiModelProperty("主键ID，自增")
        @ExcelProperty(value = "主键ID，自增")
        private Long id;
        /**
         * 工厂代码
         */
        @ApiModelProperty("工厂代码")
        @ExcelProperty(value = "工厂代码")
        private String site;
        /**
         * 车间代码
         */
        @ApiModelProperty("车间代码")
        @ExcelProperty(value = "车间代码")
        private String workshop;
        /**
         * 区域
         */
        @ApiModelProperty("区域")
        @ExcelProperty(value = "区域")
        private String area;
        /**
         * 炉台设备号
         */
        @ApiModelProperty("炉台设备号")
        @ExcelProperty(value = "炉台设备号")
        private String equipmentNo;
        /**
         * 炉台型号
         */
        @ApiModelProperty("炉台型号")
        @ExcelProperty(value = "炉台型号")
        private String equipmentType;
        /**
         * 产品（假设N182是示例值，非必填）
         */
        @ApiModelProperty("产品（假设N182是示例值，非必填）")
        @ExcelProperty(value = "产品（假设N182是示例值，非必填）")
        private String productionNo;
        /**
         * 当前产品物料号
         */
        @ApiModelProperty("当前产品物料号")
        @ExcelProperty(value = "当前产品物料号")
        private String itemCode;
        /**
         * 热场尺寸
         */
        @ApiModelProperty("热场尺寸")
        @ExcelProperty(value = "热场尺寸")
        private String furnaceSize;
        /**
         * 初装投炉时间
         */
        @ApiModelProperty("初装投炉时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ExcelProperty(value = "初装投炉时间")
        private LocalDateTime startFeedTime;
        /**
         * 完结时间
         */
        @ApiModelProperty("完结时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ExcelProperty(value = "完结时间")
        private LocalDateTime endFeedTime;
        /**
         * 坩埚厂家
         */
        @ApiModelProperty("坩埚厂家")
        @ExcelProperty(value = "坩埚厂家")
        private String crucibleVendor;
        /**
         * 坩埚编码
         */
        @ApiModelProperty("坩埚编码")
        @ExcelProperty(value = "坩埚编码")
        private String crucibleNo;
        /**
         * 坩埚尺寸
         */
        @ApiModelProperty("坩埚尺寸")
        @ExcelProperty(value = "坩埚尺寸")
        private String crucibleSize;
        /**
         * 坩埚高度
         */
        @ApiModelProperty("坩埚高度")
        @ExcelProperty(value = "坩埚高度")
        private String crucibleHeight;
        /**
         * 坩埚等级
         */
        @ApiModelProperty("坩埚等级")
        @ExcelProperty(value = "坩埚等级")
        private String crucibleLevel;
    }
}