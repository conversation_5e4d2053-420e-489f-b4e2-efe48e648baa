package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;  


@ApiModel("单产提升规则数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnitYieldIncreaseRulesDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ExcelIgnore
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版本号
     */
    @ExcelIgnore
    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")  
    private String versionNumber;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"productCode","productName"})
    private Long productId;

    @ExcelIgnore
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_2000, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"productId","productCode"}, required = true)
    private String productName;

    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String productCode;
    /**
     * 工作中心
     */
    @ExcelIgnore
    @ApiModelProperty("可排资源代码")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workCenterCode","workCenterName"})
    private Long workCenterId;

    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private String workCenterName;

    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workCenterId"}, required = true)
    private String workCenterCode;
    /**
     * 热场
     */
    @ExcelIgnore
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"thermalFieldCode","thermalFieldName"})
    private Long thermalFieldId;

    @ExcelIgnore
    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_050_ATTR_1100, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"thermalFieldId","thermalFieldCode"}, required = true)
    private String thermalFieldName;

    @ApiModelProperty("热场")
    @ExcelProperty(value = "热场")
    private String thermalFieldCode;
    /**
     * 月增量
     */
    @ApiModelProperty("月增量")
    @ExcelProperty(value = "月增量")
    @Translate(unTranslate = true, required = true)
    private BigDecimal monthlyIncrement;
    /**
     * 数据分类
     */
    @ExcelIgnore
    @ApiModelProperty("数据类型")
    @ExcelProperty(value = "数据类型")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataType;


    @ApiModelProperty("数据类型")
    @ExcelProperty(value = "数据类型")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"dataType","dataTypeCode"}, required = true)
    private String dataTypeName;

    @ExcelIgnore
    @ApiModelProperty("数据类型")
    @ExcelProperty(value = "数据类型")
    private String dataTypeCode;
}