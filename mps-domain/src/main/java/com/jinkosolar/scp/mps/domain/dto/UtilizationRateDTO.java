package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("利用率数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UtilizationRateDTO extends PageDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 1月产能
     */
    @ApiModelProperty("1月产能")
    @ExcelProperty(value = "1月")
    private BigDecimal month1;
    /**
     * 10月产能
     */
    @ApiModelProperty("10月产能")
    @ExcelProperty(value = "10月")
    private BigDecimal month10;
    /**
     * 11月产能
     */
    @ApiModelProperty("11月产能")
    @ExcelProperty(value = "11月")
    private BigDecimal month11;
    /**
     * 12月产能
     */
    @ApiModelProperty("12月产能")
    @ExcelProperty(value = "12月")
    private BigDecimal month12;
    /**
     * 2月产能
     */
    @ApiModelProperty("2月产能")
    @ExcelProperty(value = "2月")
    private BigDecimal month2;
    /**
     * 3月产能
     */
    @ApiModelProperty("3月产能")
    @ExcelProperty(value = "3月")
    private BigDecimal month3;
    /**
     * 4月产能
     */
    @ApiModelProperty("4月产能")
    @ExcelProperty(value = "4月")
    private BigDecimal month4;
    /**
     * 5月产能
     */
    @ApiModelProperty("5月产能")
    @ExcelProperty(value = "5月")
    private BigDecimal month5;
    /**
     * 6月产能
     */
    @ApiModelProperty("6月产能")
    @ExcelProperty(value = "6月")
    private BigDecimal month6;
    /**
     * 7月产能
     */
    @ApiModelProperty("7月产能")
    @ExcelProperty(value = "7月")
    private BigDecimal month7;
    /**
     * 8月产能
     */
    @ApiModelProperty("8月产能")
    @ExcelProperty(value = "8月")
    private BigDecimal month8;
    /**
     * 9月产能
     */
    @ApiModelProperty("9月产能")
    @ExcelProperty(value = "9月")
    private BigDecimal month9;
    /**
     * 类型
     */
    @Dict(headerCode = MpsLovConstant.TEXT_TYPE)
    @ApiModelProperty("类型")
    @ExportExConvert(tableName = "sys_lov_lines", fkColumnName = "lov_line_id", valueColumnName = "lov_name")
    private Long rateType;

    /**
     * 类型
     */
    @ApiModelProperty("类型名称")
    @ExcelProperty(value = "类型")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='" + MpsLovConstant.TEXT_TYPE + "'", targetFieldName = "rateType")
    private String rateTypeName;

    /**
     * 大类
     */
    @Dict(headerCode = MpsLovConstant.PARENT_TEXT_TYPE)
    @ApiModelProperty("大类")
    @ExportExConvert(tableName = "sys_lov_lines", fkColumnName = "lov_line_id", valueColumnName = "lov_name")
    private Long parentRateType;

    /**
     * 类型
     */
    @ApiModelProperty("大类名称")
    @ExcelProperty(value = "大类名称")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='" + MpsLovConstant.PARENT_TEXT_TYPE + "'", targetFieldName = "parentRateType")
    private String parentRateTypeName;
    /**
     * 车间
     */
    @Dict(headerCode = LovHeaderCodeConstant.SYS_WORKSHOP,fieldName = "workShopName")
    @ApiModelProperty("车间")
    @ExportExConvert(tableName = "sys_lov_lines", fkColumnName = "lov_line_id", valueColumnName = "lov_name", targetFieldName = "workShopName")
    private Long workShopId;

    /**
     * 车间
     */
    @ApiModelProperty("车间名称")
    @ExcelProperty(value = "车间名称")
    private String workShopName;

    @ApiModelProperty("车间编码")
    @ExcelProperty(value = "车间编码")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_value = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='" + MpsLovConstant.WORKSHOP + "'", targetFieldName = "workShopId")
    private String workShopCode;
    /**
     * 年
     */
    @ApiModelProperty("年")
    @ExcelProperty(value = "年份")
    @JSONField(serializeUsing = ToStringSerializer.class)
    private Integer year;
}
