package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.util.TreeMap;


@ApiModel("厂家档位对照数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VendorEfficiencyDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 版本，-1代表默认
     */
    @ApiModelProperty("版本，-1代表默认")
    @ExcelProperty(value = "版本，-1代表默认")  
    private String version;
    /**
     * 电池产品ID
     */
    @ApiModelProperty("电池产品ID")
    @ExcelProperty(value = "电池产品ID")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1000,  from = {"lovValue"}, to = {"productTypeCode"})
    private Long productTypeId;

    /**
     * 电池产品
     */
    @ApiModelProperty("电池产品Code")
    @ExcelProperty(value = "电池产品Code")
    private String productTypeCode;

    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数ID")
    @ExcelProperty(value = "主栅数ID")
    @Translate(DictType = LovHeaderCodeConstant.SYS_MAIN_GRID,  from = {"lovValue"}, to = {"gridCode"})
    private Long gridNum;

    /**
     * 主栅数
     */
    @ApiModelProperty("主栅数Code")
    @ExcelProperty(value = "主栅数Code")
    private String gridCode;
    /**
    /**
     * 供应商品牌
     */
    @ApiModelProperty("供应商品牌")
    @ExcelProperty(value = "供应商品牌")  
    private String vendorBrand;
    /**
     * 工厂id
     */
    @ApiModelProperty("工厂id")
    @ExcelProperty(value = "工厂id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_MAIN_GRID)
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")  
    private String factoryName;
    /**
     * 晶科效率标准
     */
    @ApiModelProperty("晶科效率标准")
    @ExcelProperty(value = "晶科效率标准")  
    private BigDecimal jinkoEfficiency;
    /**
     * 供应商效率标准
     */
    @ApiModelProperty("供应商效率标准")
    @ExcelProperty(value = "供应商效率标准")  
    private BigDecimal vendorEfficiency;


    // 使用HashMap来存储晶科标准和供应商标准
    private Map<String, String> efficiencyMap = new TreeMap<>();
    /**
     * 新增标识
     */
    @ApiModelProperty("新增标识")
    @ExcelProperty(value = "新增标识")
    private String falg;



}