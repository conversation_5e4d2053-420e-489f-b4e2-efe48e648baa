package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VendorEfficiencyToMesRequest extends JipRequestData {

    @JSONField(name = "IT_DATA")
    private IT_DATA input = new IT_DATA();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IT_DATA {
        // 字段名称
        @JSONField(name = "pageNumber")
        private Integer pageNumber;
        // 字段类型,暂时不用
        @JSONField(name = "pageSize")
        private Integer pageSize;
    }
}
