package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VendorEfficiencyToMesResponse extends JipResponseData {
    @JSONField(name = "ET_DATA")
    private List<VendorEfficiencyToMesDTO> infoList;
    @JSONField(name = "TOTAL_PAGES")
    private int totalPages;
    @JSONField(name = "TOTAL_ELEMENTS")
    private long totalElements;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VendorEfficiencyToMesDTO{

        @JSONField(name = "version")
        private String version;
        /**
         * 电池产品
         */
        @JSONField(name = "product_type_code")
        private String productTypeCode;

        /**
         * 主栅数
         */

        @JSONField(name = "grid_code")
        private String gridCode;
        /**
         /**
         * 供应商品牌
         */

        @JSONField(name = "vendor_brand")
        private String vendorBrand;

        /**
         * 工厂代码
         */

        @JSONField(name = "factory_code")
        private String factoryCode;
        /**
         * 工厂名称
         */

        @JSONField(name = "factory_name")
        private String factoryName;
        /**
         * 晶科效率标准
         */

        @JSONField(name = "jinko_efficiency")
        private BigDecimal jinkoEfficiency;
        /**
         * 供应商效率标准
         */
        @JSONField(name = "vendor_efficiency")
        private BigDecimal vendorEfficiency;

    }

    public static VendorEfficiencyToMesResponse success(List<VendorEfficiencyToMesDTO> infoList,Integer totalPages,Long totalElements) {
        VendorEfficiencyToMesResponse jipResponseData = new VendorEfficiencyToMesResponse();
        jipResponseData.getEtReturn().setMsg("success");
        jipResponseData.getEtReturn().setStatus("S");
        jipResponseData.setInfoList(infoList);
        jipResponseData.setTotalPages(totalPages);
        jipResponseData.setTotalElements(totalElements);
        return jipResponseData;
    }
}
