package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@ApiModel("综合采购招标量和商务外发排程量表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VolumeProcurementSchedulingDTO extends BaseDTO implements Serializable {
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    @ExcelProperty(value = "产品类型")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"productTypeName"})
    private Long productTypeId;

    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    @ExcelProperty(value = "产品类型名称")
    @Translate(unTranslate = true, required = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"productTypeId"})
    private String productTypeName;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1300, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"thicknessName"})
    private Long thicknessId;
    /**
     * 厚度名称
     */
    @ApiModelProperty("厚度名称")
    @ExcelProperty(value = "厚度名称")
    private String thicknessName;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1200, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"sizeName"})
    private Long sizeId;

    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    @Translate(unTranslate = true, required = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1200, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"sizeId"})
    private String sizeName;
    /**
     * 倒角
     */
    @ApiModelProperty("倒角")
    @ExcelProperty(value = "倒角")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CHAMFER_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"chamferName"})
    private Long chamferId;

    /**
     * 倒角
     */
    @ApiModelProperty("倒角")
    @ExcelProperty(value = "倒角")
    private String chamferName;


    /**
     * 等级
     */
    @ApiModelProperty("等级")
    @ExcelProperty(value = "等级")
    private String grade;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    @Translate(DictType = LovHeaderCodeConstant.SYS_IF_DIRECTIONAL, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"directionalName"})
    private Long directionalId;

    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    private String directionalName;
    /**
     * 时间
     */
    @ApiModelProperty("时间")
    @ExcelProperty(value = "时间")
    private LocalDate dayTime;
    /**
     * 产量
     */
    @ApiModelProperty("产量")
    @ExcelProperty(value = "产量")
    private BigDecimal quantity;
    /**
     * 数据类型ID
     */
    @ApiModelProperty("数据类型ID")
    @ExcelProperty(value = "数据类型ID")
    @Translate(DictType = LovHeaderCodeConstant.MPS_KCYC_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"typeName"})
    private Long typeId;
    /**
     * 数据类型ID
     */
    @ApiModelProperty("数据类型Name")
    @ExcelProperty(value = "数据类型Name")
   /* @Translate(unTranslate = true, required = true,DictType = LovHeaderCodeConstant.MPS_KCYC_TYPE, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"typeId"})*/
    private String typeName;
    /**
     * 扩展字段1
     */
    @ApiModelProperty("扩展字段1")
    @ExcelProperty(value = "扩展字段1")
    private String attribute1;
    /**
     * 扩展字段2
     */
    @ApiModelProperty("扩展字段2")
    @ExcelProperty(value = "扩展字段2")
    private String attribute2;
    /**
     * 扩展字段3
     */
    @ApiModelProperty("扩展字段3")
    @ExcelProperty(value = "扩展字段3")
    private String attribute3;
    /**
     * 扩展字段4
     */
    @ApiModelProperty("扩展字段4")
    @ExcelProperty(value = "扩展字段4")
    private String attribute4;
    /**
     * 扩展字段5
     */
    @ApiModelProperty("扩展字段5")
    @ExcelProperty(value = "扩展字段5")
    private String attribute5;
    /**
     * 扩展字段6
     */
    @ApiModelProperty("扩展字段6")
    @ExcelProperty(value = "扩展字段6")
    private String attribute6;
    /**
     * 扩展字段7
     */
    @ApiModelProperty("扩展字段7")
    @ExcelProperty(value = "扩展字段7")
    private String attribute7;
    /**
     * 扩展字段8
     */
    @ApiModelProperty("扩展字段8")
    @ExcelProperty(value = "扩展字段8")
    private String attribute8;
    /**
     * 扩展字段9
     */
    @ApiModelProperty("扩展字段9")
    @ExcelProperty(value = "扩展字段9")
    private String attribute9;
    /**
     * 扩展字段10
     */
    @ApiModelProperty("扩展字段10")
    @ExcelProperty(value = "扩展字段10")
    private String attribute10;

    // 使用HashMap来存储日期和日期值
    private Map<String, BigDecimal> dayValueMap = new HashMap<>();

    // 使用HashMap来存储日期
    private List<String> dayValue = new ArrayList<>();
}