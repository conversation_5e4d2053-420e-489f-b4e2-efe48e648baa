package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.EnumValidator;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.ibm.scp.common.api.base.PageDTO;
import com.jinkosolar.scp.mps.domain.constant.ExLovTransConstant;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.constant.enums.ProcessStepEnum;
import com.jinkosolar.scp.mps.domain.constant.enums.ProductionOrientationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@ApiModel("大基地计划上传数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class
WaferCrystalProductionPlanDTO extends PageDTO implements Serializable {
    /**
     * 国内/海外/山西区分	目前分为：国内、海外、山西
     */
    @ApiModelProperty("国内/海外/山西区分	目前分为：国内、海外、山西")
    @ExcelProperty(value = "国内/海外/山西区分	目前分为：国内、海外、山西")
    private String basePlace;
    /**
     * bbom中对应的Id
     */
    @ApiModelProperty("bbom中对应的Id")
    @ExcelProperty(value = "bbom中对应的Id")
    private Long bbomId;
    /**
     * 电池工厂
     */
    @ApiModelProperty("电池工厂")
    @ExcelProperty(value = "电池工厂")
    private String cellFactory;
    /**
     * 电池生产车间
     */
    @ApiModelProperty("电池生产车间")
    @ExcelProperty(value = "电池生产车间")
    private String cellProductionWorkshop;
    /**
     * 电池类型
     */
    @ApiModelProperty("电池类型")
    @ExcelProperty(value = "电池类型")
    private String cellType;
    /**
     * 电池类型Id
     */
    @ApiModelProperty("电池类型Id")
    @ExcelProperty(value = "电池类型Id")
    private Long cellTypeId;
    /**
     * 列名
     */
    @ApiModelProperty("列名")
    @ExcelProperty(value = "列名")
    private String columnName;
    /**
     * 列值
     */
    @ApiModelProperty("列值")
    @ExcelProperty(value = "列值")
    private String columnValue;
    /**
     * 发布状态
     */
    @ApiModelProperty("发布状态")
    @ExcelProperty(value = "发布状态")
    private String confirmStatus;
    /**
     * 坩埚规格
     */
    @ApiModelProperty("坩埚规格")
    @ExcelProperty(value = "坩埚规格")
    private String crucibleSpecification;
    /**
     * 晶棒类型1
     */
    @ApiModelProperty("晶棒类型1")
    @ExcelProperty(value = "晶棒类型1")
    private String crystalType1;
    /**
     * 晶棒类型2
     */
    @ApiModelProperty("晶棒类型2")
    @ExcelProperty(value = "晶棒类型2")
    private String crystalType2;
    /**
     * 实验/发货类型
     */
    @ApiModelProperty("实验/发货类型")
    @ExcelProperty(value = "实验/发货类型")
    private String deliveryType;
    /**
     * 需求地
     */
    @ApiModelProperty("需求地")
    @ExcelProperty(value = "需求地")
    private String demandBasePlace;
    /**
     * 需求类别（研发、大货）
     */
    @ApiModelProperty("需求类别（研发、大货）")
    @ExcelProperty(value = "需求类别（研发、大货）")
    private String demandCategory;
    /**
     * 需求日期
     */
    @ApiModelProperty("需求日期")
    @ExcelProperty(value = "需求日期")
    private LocalDate demandDate;
    /**
     * 需求数量
     */
    @ApiModelProperty("需求数量")
    @ExcelProperty(value = "需求数量")
    private BigDecimal demandQty;
    /**
     * 需求说明
     */
    @ApiModelProperty("需求说明")
    @ExcelProperty(value = "需求说明")
    private String demandRemark;
    /**
     * 需求版本号
     */
    @ApiModelProperty("需求版本号")
    @ExcelProperty(value = "需求版本号")
    private String demandVersion;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    private String directional;

    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='SYS.If_Directional'", targetFieldName = "directional")
    private String directionalName;

    /**
     * 事业部
     */
    @ApiModelProperty("事业部")
    @ExcelProperty(value = "事业部")
    private String division;
    /**
     * DPID
     */
    @ApiModelProperty("DPID")
    @ExcelProperty(value = "DPID")
    private String dpId;
    /**
     * 电性能
     */
    @ApiModelProperty("电性能")
    @ExcelProperty(value = "电性能")
    private String electricalPerformance;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "结束时间")
    private LocalDateTime endTime;
    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private String factory;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;
    /**
     * 工厂Id
     */
    @ApiModelProperty("工厂Id")
    @ExcelProperty(value = "工厂Id")
    private Long factoryId;
    /**
     * 投产计划汇总版本号
     */
    @ApiModelProperty("投产计划汇总版本号")
    @ExcelProperty(value = "投产计划汇总版本号")
    private String finalVersion;
    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @ExcelProperty(value = "配方")
    private String formula;
    /**
     * 炉型
     */
    @ApiModelProperty("炉型")
    @ExcelProperty(value = "炉型")
    private String furnaceType;
    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    @ExcelProperty(value = "炉台数")
    private String furnacesNum;
    /**
     * 等级
     */
    @ApiModelProperty("等级")
    @ExcelProperty(value = "等级")
    private String grade;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long id;
    /**
     * 结存天数
     */
    @ApiModelProperty("结存天数")
    @ExcelProperty(value = "结存天数")
    private String inventoryDays;
    /**
     * 国内海外
     */
    @ApiModelProperty("国内海外")
    @ExcelProperty(value = "国内海外")
    private String isOversea;
    /**
     * 国内海外Id
     */
    @ApiModelProperty("国内海外Id")
    @ExcelProperty(value = "国内海外Id")
    private Long isOverseaId;
    /**
     * 属性1
     */
    @ApiModelProperty("属性1")
    @ExcelProperty(value = "属性1")
    private String itemAttribute1;
    /**
     * 属性10
     */
    @ApiModelProperty("属性10")
    @ExcelProperty(value = "属性10")
    private String itemAttribute10;
    /**
     * 属性11
     */
    @ApiModelProperty("属性11")
    @ExcelProperty(value = "属性11")
    private String itemAttribute11;
    /**
     * 属性12
     */
    @ApiModelProperty("属性12")
    @ExcelProperty(value = "属性12")
    private String itemAttribute12;
    /**
     * 属性13
     */
    @ApiModelProperty("属性13")
    @ExcelProperty(value = "属性13")
    private String itemAttribute13;
    /**
     * 属性14
     */
    @ApiModelProperty("属性14")
    @ExcelProperty(value = "属性14")
    private String itemAttribute14;
    /**
     * 属性15
     */
    @ApiModelProperty("属性15")
    @ExcelProperty(value = "属性15")
    private String itemAttribute15;
    /**
     * 属性2
     */
    @ApiModelProperty("属性2")
    @ExcelProperty(value = "属性2")
    private String itemAttribute2;
    /**
     * 属性3
     */
    @ApiModelProperty("属性3")
    @ExcelProperty(value = "属性3")
    private String itemAttribute3;
    /**
     * 属性4
     */
    @ApiModelProperty("属性4")
    @ExcelProperty(value = "属性4")
    private String itemAttribute4;
    /**
     * 属性5
     */
    @ApiModelProperty("属性5")
    @ExcelProperty(value = "属性5")
    private String itemAttribute5;
    /**
     * 属性6
     */
    @ApiModelProperty("属性6")
    @ExcelProperty(value = "属性6")
    private String itemAttribute6;
    /**
     * 属性7
     */
    @ApiModelProperty("属性7")
    @ExcelProperty(value = "属性7")
    private String itemAttribute7;
    /**
     * 属性8
     */
    @ApiModelProperty("属性8")
    @ExcelProperty(value = "属性8")
    private String itemAttribute8;
    /**
     * 属性9
     */
    @ApiModelProperty("属性9")
    @ExcelProperty(value = "属性9")
    private String itemAttribute9;
    /**
     * 物料料号
     */
    @ApiModelProperty("物料料号")
    @ExcelProperty(value = "物料料号")
    private String itemCode;
    /**
     * 生产线体
     */
    @ApiModelProperty("生产线体")
    @ExcelProperty(value = "生产线体")
    private String lineName;
    /**
     * 机台数
     */
    @ApiModelProperty("机台数")
    @ExcelProperty(value = "机台数")
    private BigDecimal machineQty;
    /**
     * 匹配信息
     */
    @ApiModelProperty("匹配信息")
    @ExcelProperty(value = "匹配信息")
    private String matchRemark;
    /**
     * 匹配状态
     */
    @ApiModelProperty("匹配状态")
    @ExcelProperty(value = "匹配状态")
    private String matchStatus;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")
    private String materialCode;
    /**
     * 最大分布效率
     */
    @ApiModelProperty("最大分布效率")
    @ExcelProperty(value = "最大分布效率")
    private BigDecimal maxEfficiency;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    private String modelType;
    /**
     * 线体数量
     */
    @ApiModelProperty("线体数量")
    @ExcelProperty(value = "线体数量")
    private BigDecimal numberLine;
    /**
     * 原来排产结束时间
     */
    @ApiModelProperty("原来排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "原来排产结束时间")
    private LocalDateTime oldEndTime;
    /**
     * 原来排产开始时间
     */
    @ApiModelProperty("原来排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "原来排产开始时间")
    private LocalDateTime oldStartTime;
    /**
     * 订单交货期
     */
    @ApiModelProperty("订单交货期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "订单交货期")
    private LocalDateTime orderDeliveryDate;
    /**
     * 单产(KG/台）
     */
    @ApiModelProperty("单产(KG/台）")
    @ExcelProperty(value = "单产(KG/台）")
    private String output;
    /**
     * 机台单产-万片
     */
    @ApiModelProperty("机台单产-万片")
    @ExcelProperty(value = "机台单产-万片")
    private String outputPerTool;
    /**
     * 产出计划
     */
    @ApiModelProperty("产出计划")
    @ExcelProperty(value = "产出计划")
    private String outputPlan;
    /**
     * 出片数(片/KG)
     */
    @ApiModelProperty("出片数(片/KG)")
    @ExcelProperty(value = "出片数(片/KG)")
    private String pieces;
    /**
     * 排产来源Id
     */
    @ApiModelProperty("排产来源Id")
    @ExcelProperty(value = "排产来源Id")
    private Long planLineFromId;
    /**
     * 计划版本号
     */
    @ApiModelProperty("计划版本号")
    @ExcelProperty(value = "计划版本号")
    private String planVersion;
    /**
     * 计划工作中心
     */
    @ApiModelProperty("计划工作中心")
    @ExcelProperty(value = "计划工作中心")
    private String planWorkCenter;
    /**
     * 计划良率
     */
    @ApiModelProperty("计划良率")
    @ExcelProperty(value = "计划良率")
    private BigDecimal planYieldRate;
    /**
     * 晶棒投入量
     */
    @ApiModelProperty("晶棒投入量")
    @ExcelProperty(value = "晶棒投入量")
    private String polysiliconIngotInput;
    /**
     * 工序
     */
    @ApiModelProperty("工序")
    @ExcelProperty(value = "工序")
    private String process;
    /**
     * 加工类型
     */
    @ApiModelProperty("加工类型")
    @ExcelProperty(value = "加工类型")
    private String processCategory;
    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @ExcelProperty(value = "工序编号")
    private String processNo;

    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='MPS.WORK_SHOP'", targetFieldName = "workshopId")
    private String workshopIdName;
    /**
     * 工段 1=拉晶 2-切片
     */
    @ApiModelProperty("工段 1=拉晶 2-切片")
    @ExcelProperty(value = "工段")
    @EnumValidator(enumType = ProcessStepEnum.class, message = "工段不正确")
    private Integer processStep;


    /**
     * 工段
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "工段")
    private Long processId;



    /**
     * 工段
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "工段")
    @ImportExConvert(sql = "SELECT lov_line_id FROM sys_lov_lines t1 " +
            " INNER JOIN sys_lov_header t2 ON t1.lov_header_id = t2.lov_header_id AND t2.is_deleted = 0 AND t2.enable_flag='Y'" +
            " WHERE t1.is_deleted = 0 AND t1.lov_name = ?1 AND t1.enable_flag = 'Y' AND t2.lov_code='MPS.PROCESS_ID'", targetFieldName = "processId")
    private String processIdName;

    /**
     * 工段 1=拉晶 2-切片
     */
    @ApiModelProperty("工段 1=拉晶 2-切片")
    @ExcelProperty(value = "工段")
    private String processStepName;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String product;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    private String productName;
    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    @ExcelProperty(value = "产品类型")
    private String productType;
    /**
     * 生产方向 1=定向 2=非定向
     */
    @ApiModelProperty("生产方向 1=定向 2=非定向")
    @ExcelProperty(value = "生产方向 1=定向 2=非定向")
    @EnumValidator(enumType = ProductionOrientationEnum.class, message = "生产方向不正确")
    private Integer productionOrientation;
    /**
     * 生产方向 1=定向 2=非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")
    private String productionOrientationName;
    /**
     * 产量（T)
     */
    @ApiModelProperty("产量（T)")
    @ExcelProperty(value = "产量（T)")
    private String productionVolume;
    /**
     * 数量
     */
    @ApiModelProperty("数量")
    @ExcelProperty(value = "数量")
    private BigDecimal qtyPc;
    /**
     * 小区域国家
     */
    @ApiModelProperty("小区域国家")
    @ExcelProperty(value = "小区域国家")
    private String regionalCountry;
    /**
     * 结存折万片
     */
    @ApiModelProperty("结存折万片")
    @ExcelProperty(value = "结存折万片")
    private String remainingInventory;
    /**
     * 返棒(T)
     */
    @ApiModelProperty("返棒(T)")
    @ExcelProperty(value = "返棒(T)")
    private String returnIngot;
    /**
     * 圆棒产出-折万片
     */
    @ApiModelProperty("圆棒产出-折万片")
    @ExcelProperty(value = "圆棒产出-折万片")
    private String roundIngotOutput;
    /**
     * 每KG圆棒出片数
     */
    @ApiModelProperty("每KG圆棒出片数")
    @ExcelProperty(value = "每KG圆棒出片数")
    private String roundIngotPieces;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产结束时间")
    private LocalDateTime schedulingEndTime;
    /**
     * 排产数量
     */
    @ApiModelProperty("排产数量")
    @ExcelProperty(value = "排产数量")
    private BigDecimal schedulingQty;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产开始时间")
    private LocalDateTime schedulingStartTime;
    /**
     * 排产时间
     */
    @ApiModelProperty("排产时间")
    @ExcelProperty(value = "排产时间")
    private String schedulingTime;
    /**
     * 可外发量
     */
    @ApiModelProperty("可外发量")
    @ExcelProperty(value = "可外发量")
    private String shippableInventory;
    /**
     * 发货方棒(T)
     */
    @ApiModelProperty("发货方棒(T)")
    @ExcelProperty(value = "发货方棒(T)")
    private String shippedIngot;
    /**
     * 硅块投入-万片
     */
    @ApiModelProperty("硅块投入-万片")
    @ExcelProperty(value = "硅块投入-万片")
    private String siliconIngotInput;
    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @ExcelProperty(value = "硅料供应商")
    private String siliconSupplier;
    /**
     * 单产
     */
    @ApiModelProperty("单产")
    @ExcelProperty(value = "单产")
    private BigDecimal singleProduction;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")
    private String size;
    /**
     * 来源类型
     */
    @ApiModelProperty("来源类型")
    @ExcelProperty(value = "来源类型")
    private String sourceType;
    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商")
    @ExcelProperty(value = "指定硅料供应商")
    private String specifiedSiliconSupplier;
    /**
     * 起始效率
     */
    @ApiModelProperty("起始效率")
    @ExcelProperty(value = "起始效率")
    private BigDecimal startEfficiency;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 供美/非美
     */
    @ApiModelProperty("供美/非美")
    @ExcelProperty(value = "供美/非美")
    private String supplyUs;
    /**
     * 热场尺寸
     */
    @ApiModelProperty("热场尺寸")
    @ExcelProperty(value = "热场尺寸")
    private String thermalFieldSize;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")
    private String thickness;

    @ExcelProperty(value = "项目名称")
    private String projectName;
    /**
     * 机台数
     */
    @ApiModelProperty("机台数")
    @ExcelProperty(value = "机台数")
    private String toolNum;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    @ExcelProperty(value = "版本")
    private String version;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    private String workCenter;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")
    @ImportExConvert()
    private String workCenterCode;
    /**
     * 工作中心描述
     */
    @ApiModelProperty("工作中心描述")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")
    private Long workCenterId;
    /**
     * 工作编码
     */
    @ApiModelProperty("工作编码")
    @ExcelProperty(value = "工作编码")
    private String workCode;
    /**
     * 生产车间
     */
    @ApiModelProperty("生产车间")
    @ExcelProperty(value = "生产车间")
    private String workshop;
    /**
     * 生产车间Id
     */
    @ApiModelProperty("生产车间Id")
    @ExcelProperty(value = "生产车间Id")
    private Long workshopId;
    /**
     * 事业部
     */
    @ApiModelProperty("事业部")
    @ExcelProperty(value = "事业部")
    private String workunit;
    /**
     * 事业部Id
     */
    @ApiModelProperty("事业部Id")
    @ExcelProperty(value = "事业部Id")
    private Long workunitId;
    /**
     * 良率
     */
    @ApiModelProperty("良率")
    @ExcelProperty(value = "良率")
    private String yieldRate;
    /**
     * 动态列数据
     */
    @ExcelProperty(value = "动态列数据")
    private Map<String, Map<Object, Object>> dynamicColumnMap;
    /**
     * 动态列名列表
     */
    @ExcelProperty(value = "动态列名列表")
    private List<String> dynamicColumnList;
    /**
     * 项目列表
     */
    @ExcelProperty(value = "项目列表")
    private List<WaferCrystalProductionPlanDTO> projectList;
}
