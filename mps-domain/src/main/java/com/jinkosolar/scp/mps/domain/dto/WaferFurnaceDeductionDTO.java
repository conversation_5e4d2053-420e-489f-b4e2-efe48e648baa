package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.dpf.base.core.util.StringUtils;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;


@ApiModel("炉型扣减表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WaferFurnaceDeductionDTO extends BaseDTO implements SwitchDeductionInterface,Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    @ExcelProperty(value = "版本")  
    private String versionNumber;
    /**
     * 车间ID
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"workShopName"})
    private Long workShopId;

    /**
     * 车间
     */
    @ApiModelProperty("车间")
    @ExcelProperty(value = "车间")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"workShopId"}, required = true)
    private String workShopName;

    /**
     * 数据分类ID
     */
    @ApiModelProperty("数据分类ID")
    @ExcelProperty(value = "数据分类ID")
    @Translate(DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"isOverseaName"})
    private Long isOversea;

    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_DATA_TYPE, queryColumns = {"isOverseaName"},
            from = {"lovLineId"}, to = {"isOversea"}, required = true)
    private String isOverseaName;



    /**
     * 供应商ID
     */
    @ApiModelProperty("供应商ID")
    @ExcelProperty(value = "供应商ID")
    @Translate(DictType = LovHeaderCodeConstant.FURNACE_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"supplierName"})
    private Long supplierId;


    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    @ExcelProperty(value = "供应商名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.FURNACE_TYPE, queryColumns = {"supplierName"},
            from = {"lovLineId"}, to = {"supplierId"}, required = true)
    private String supplierName;


    /**
     * 炉型
     */
    @ApiModelProperty("炉型")
    @ExcelProperty(value = "炉型")
    @Translate(DictType = LovHeaderCodeConstant.FURNACE_PROFILE, queryColumns = {"lovLineId"},
            from = {"lovValue"}, to = {"furnaceTypeName"})
    private Long furnaceType;

    /**
     * 炉型
     */
    @ApiModelProperty("炉型名称")
    @ExcelProperty(value = "炉型名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.FURNACE_PROFILE, queryColumns = {"furnaceTypeName"},
            from = {"lovLineId"}, to = {"furnaceType"}, required = true)
    private String furnaceTypeName;


    /**
     * 年份
     */
    @ApiModelProperty("年份")
    @ExcelProperty(value = "年份")
    private Integer year;


    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")
    private LocalDate month;


    /**
     * 月份值
     */
    @ApiModelProperty("月份值")
    @ExcelProperty(value = "月份值")  
    private BigDecimal monthValue;


    // 使用HashMap来存储月份和月份值
    private Map<String, String> monthValueMap = new HashMap<>();

    @Override
    public LocalDate getDeductionMonth() {
        return month;
    }

    @Override
    public BigDecimal getDeductionNum() {
        return monthValue;
    }

    @Override
    public String getGroupKey() {
        return StringUtils.join(new Object[]{workShopId,supplierId,furnaceType},":");
    }

    @Override
    public Long getExtraId() {
        return supplierId;
    }
}