package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("产品切片标准单产表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WaferPlanSliceStandardDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")  
    private Long id;
    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心Id")
    @ExcelProperty(value = "工作中心Id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"workCenterCode","workCenterName"})
    private Long workCenterId;

    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovName"},
            from = {"lovLineId"}, to = {"workCenterId"}, required = true)
    private String workCenterName;

    /**
     * 工作中心Code
     */
    @ApiModelProperty("工作中心Code")
    @ExcelProperty(value = "工作中心Code")
    private String workCenterCode;

    /**
     * 排产区域
     */
    @ApiModelProperty("排产区域")
    @ExcelProperty(value = "排产区域")
    private String domesticOversea;


    /**
     * 产品ID
     */
    @ApiModelProperty("产品ID")
    @ExcelProperty(value = "产品ID")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"productTypeName"})
    private Long productTypeId;

    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1400, queryColumns = {"productTypeName"},
            from = {"lovLineId"}, to = {"productTypeId"}, required = true)
    private String productTypeName;

    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    @ExcelProperty(value = "厚度")  
    private String thickness;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    @ExcelProperty(value = "尺寸")  
    private String size;
    /**
     * 机型
     */
    @ApiModelProperty("机型")
    @ExcelProperty(value = "机型")  
    private String craftType;
    /**
     * 有效开始时间
     */
    @ApiModelProperty("有效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "有效开始时间")  
    private LocalDate validStartTime;
    /**
     * 有效结束时间
     */
    @ApiModelProperty("有效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "有效结束时间")  
    private LocalDate validEndTime;
    /**
     * 单产
     */
    @ApiModelProperty("单产")
    @ExcelProperty(value = "单产")  
    private BigDecimal preUnit;
    /**
     * 数据类型 basics 基础数据  climb爬坡数据
     */
    @ApiModelProperty("数据类型 basics 基础数据  climb爬坡数据")
    @ExcelProperty(value = "数据类型 basics 基础数据  climb爬坡数据")  
    private String dataType;
}