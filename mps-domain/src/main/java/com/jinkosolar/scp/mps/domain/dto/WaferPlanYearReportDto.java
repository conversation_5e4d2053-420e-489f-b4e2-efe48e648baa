package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WaferPlanYearReportDto {

    @ApiModelProperty("序号")
    private Integer index;

    @ApiModelProperty("产品")
    private String productType;

    @ApiModelProperty("类型")
    private String dataTypeCode;

    @ApiModelProperty("类型")
    private String dataType;

    @ApiModelProperty("季度1")
    private BigDecimal q1;

    @ApiModelProperty("季度2")
    private BigDecimal q2;

    @ApiModelProperty("季度3")
    private BigDecimal q3;

    @ApiModelProperty("季度4")
    private BigDecimal q4;

    @ApiModelProperty("月份1")
    private BigDecimal month1;

    @ApiModelProperty("月份2")
    private BigDecimal month2;

    @ApiModelProperty("月份3")
    private BigDecimal month3;

    @ApiModelProperty("月份4")
    private BigDecimal month4;

    @ApiModelProperty("月份5")
    private BigDecimal month5;

    @ApiModelProperty("月份6")
    private BigDecimal month6;

    @ApiModelProperty("月份7")
    private BigDecimal month7;

    @ApiModelProperty("月份8")
    private BigDecimal month8;

    @ApiModelProperty("月份9")
    private BigDecimal month9;

    @ApiModelProperty("月份10")
    private BigDecimal month10;

    @ApiModelProperty("月份11")
    private BigDecimal month11;

    @ApiModelProperty("月份12")
    private BigDecimal month12;

    @ApiModelProperty("合计")
    private BigDecimal total;

    public WaferPlanYearReportDto(Integer index, String productType, String dataTypeCode, String dataType) {
        this.index = index;
        this.productType = productType;
        this.dataTypeCode = dataTypeCode;
        this.dataType = dataType;
    }
}
