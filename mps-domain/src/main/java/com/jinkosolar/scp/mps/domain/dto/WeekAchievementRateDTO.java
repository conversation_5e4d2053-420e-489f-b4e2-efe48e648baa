package com.jinkosolar.scp.mps.domain.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "WeekBaseAchievementRateDTO对象", description = "DTO对象")
public class WeekAchievementRateDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 排产基地
     */
    @ApiModelProperty(value = "排产基地")
    private String productPlace;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workShop;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    private String productSeries;

    /**
     * 平均功率
     */
    @ApiModelProperty(value = "平均功率")
    private Decimal avePower;

    /**
     * 计划完成日期
     */
    @ApiModelProperty(value = "计划完成日期")
    private LocalDate plannedCompleteDate;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;

    /**
     * 入库数量
     */
    @ApiModelProperty(value = "入库数量")
    private BigDecimal inInventoryQty;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "制造能力")
    private BigDecimal capacityQuantity;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    /**
     * 周
     */
    @ApiModelProperty(value = "周")
    private String week;

    /**
     * 周开始日期（起点为星期四）
     */
    @ApiModelProperty(value = "周开始日期（起点为星期四）")
    private LocalDate startDay;

    /**
     * 周开始日期（终点为星期四）
     */
    @ApiModelProperty(value = "周结束日期（结束于星期三）")
    private LocalDate endDay;

    /**
     * 周开始日期（终点为星期四）
     */
    @ApiModelProperty(value = "周结束日期（结束于星期三）")
    private String isTotal;

    /**
     * 第1周达成率
     */
    @ApiModelProperty(value = "第1周达成率")
    private BigDecimal achievementRateWeek1;

    /**
     * 第2周达成率
     */
    @ApiModelProperty(value = "第2周达成率")
    private BigDecimal achievementRateWeek2;

    /**
     * 第3周达成率
     */
    @ApiModelProperty(value = "第3周达成率")
    private BigDecimal achievementRateWeek3;

    /**
     * 第4周达成率
     */
    @ApiModelProperty(value = "第4周达成率")
    private BigDecimal achievementRateWeek4;

    /**
     * 第5周达成率
     */
    @ApiModelProperty(value = "第5周达成率")
    private BigDecimal achievementRateWeek5;

    /**
     * 第6周达成率
     */
    @ApiModelProperty(value = "第6周达成率")
    private BigDecimal achievementRateWeek6;

    /**
     * 第7周达成率
     */
    @ApiModelProperty(value = "第7周达成率")
    private BigDecimal achievementRateWeek7;

    /**
     * 第8周达成率
     */
    @ApiModelProperty(value = "第8周达成率")
    private BigDecimal achievementRateWeek8;

    /**
     * 第9周达成率
     */
    @ApiModelProperty(value = "第9周达成率")
    private BigDecimal achievementRateWeek9;

    /**
     * 第10周达成率
     */
    @ApiModelProperty(value = "第10周达成率")
    private BigDecimal achievementRateWeek10;

    /**
     * 第11周达成率
     */
    @ApiModelProperty(value = "第11周达成率")
    private BigDecimal achievementRateWeek11;

    /**
     * 第12周达成率
     */
    @ApiModelProperty(value = "第12周达成率")
    private BigDecimal achievementRateWeek12;

    /**
     * 第13周达成率
     */
    @ApiModelProperty(value = "第13周达成率")
    private BigDecimal achievementRateWeek13;

    /**
     * 第14周达成率
     */
    @ApiModelProperty(value = "第14周达成率")
    private BigDecimal achievementRateWeek14;

    /**
     * 第15周达成率
     */
    @ApiModelProperty(value = "第15周达成率")
    private BigDecimal achievementRateWeek15;

    /**
     * 第16周达成率
     */
    @ApiModelProperty(value = "第16周达成率")
    private BigDecimal achievementRateWeek16;

    /**
     * 第17周达成率
     */
    @ApiModelProperty(value = "第17周达成率")
    private BigDecimal achievementRateWeek17;

    /**
     * 第18周达成率
     */
    @ApiModelProperty(value = "第18周达成率")
    private BigDecimal achievementRateWeek18;

    /**
     * 第19周达成率
     */
    @ApiModelProperty(value = "第19周达成率")
    private BigDecimal achievementRateWeek19;

    /**
     * 第20周达成率
     */
    @ApiModelProperty(value = "第20周达成率")
    private BigDecimal achievementRateWeek20;

    /**
     * 第21周达成率
     */
    @ApiModelProperty(value = "第21周达成率")
    private BigDecimal achievementRateWeek21;

    /**
     * 第22周达成率
     */
    @ApiModelProperty(value = "第22周达成率")
    private BigDecimal achievementRateWeek22;

    /**
     * 第23周达成率
     */
    @ApiModelProperty(value = "第23周达成率")
    private BigDecimal achievementRateWeek23;

    /**
     * 第24周达成率
     */
    @ApiModelProperty(value = "第24周达成率")
    private BigDecimal achievementRateWeek24;

    /**
     * 第25周达成率
     */
    @ApiModelProperty(value = "第25周达成率")
    private BigDecimal achievementRateWeek25;

    /**
     * 第26周达成率
     */
    @ApiModelProperty(value = "第26周达成率")
    private BigDecimal achievementRateWeek26;

    /**
     * 第27周达成率
     */
    @ApiModelProperty(value = "第27周达成率")
    private BigDecimal achievementRateWeek27;

    /**
     * 第28周达成率
     */
    @ApiModelProperty(value = "第28周达成率")
    private BigDecimal achievementRateWeek28;

    /**
     * 第29周达成率
     */
    @ApiModelProperty(value = "第29周达成率")
    private BigDecimal achievementRateWeek29;

    /**
     * 第30周达成率
     */
    @ApiModelProperty(value = "第30周达成率")
    private BigDecimal achievementRateWeek30;

    /**
     * 第31周达成率
     */
    @ApiModelProperty(value = "第31周达成率")
    private BigDecimal achievementRateWeek31;

    /**
     * 第32周达成率
     */
    @ApiModelProperty(value = "第32周达成率")
    private BigDecimal achievementRateWeek32;

    /**
     * 第33周达成率
     */
    @ApiModelProperty(value = "第33周达成率")
    private BigDecimal achievementRateWeek33;

    /**
     * 第34周达成率
     */
    @ApiModelProperty(value = "第34周达成率")
    private BigDecimal achievementRateWeek34;

    /**
     * 第35周达成率
     */
    @ApiModelProperty(value = "第35周达成率")
    private BigDecimal achievementRateWeek35;

    /**
     * 第36周达成率
     */
    @ApiModelProperty(value = "第36周达成率")
    private BigDecimal achievementRateWeek36;

    /**
     * 第37周达成率
     */
    @ApiModelProperty(value = "第37周达成率")
    private BigDecimal achievementRateWeek37;

    /**
     * 第38周达成率
     */
    @ApiModelProperty(value = "第38周达成率")
    private BigDecimal achievementRateWeek38;

    /**
     * 第39周达成率
     */
    @ApiModelProperty(value = "第39周达成率")
    private BigDecimal achievementRateWeek39;

    /**
     * 第40周达成率
     */
    @ApiModelProperty(value = "第40周达成率")
    private BigDecimal achievementRateWeek40;


    /**
     * 第41周达成率
     */
    @ApiModelProperty(value = "第41周达成率")
    private BigDecimal achievementRateWeek41;

    /**
     * 第42周达成率
     */
    @ApiModelProperty(value = "第42周达成率")
    private BigDecimal achievementRateWeek42;

    /**
     * 第43周达成率
     */
    @ApiModelProperty(value = "第43周达成率")
    private BigDecimal achievementRateWeek43;

    /**
     * 第44周达成率
     */
    @ApiModelProperty(value = "第44周达成率")
    private BigDecimal achievementRateWeek44;

    /**
     * 第45周达成率
     */
    @ApiModelProperty(value = "第45周达成率")
    private BigDecimal achievementRateWeek45;

    /**
     * 第46周达成率
     */
    @ApiModelProperty(value = "第46周达成率")
    private BigDecimal achievementRateWeek46;

    /**
     * 第47周达成率
     */
    @ApiModelProperty(value = "第47周达成率")
    private BigDecimal achievementRateWeek47;

    /**
     * 第48周达成率
     */
    @ApiModelProperty(value = "第48周达成率")
    private BigDecimal achievementRateWeek48;

    /**
     * 第49周达成率
     */
    @ApiModelProperty(value = "第49周达成率")
    private BigDecimal achievementRateWeek49;

    /**
     * 第50周达成率
     */
    @ApiModelProperty(value = "第50周达成率")
    private BigDecimal achievementRateWeek50;

    /**
     * 第51周达成率
     */
    @ApiModelProperty(value = "第51周达成率")
    private BigDecimal achievementRateWeek51;

    /**
     * 第52周达成率
     */
    @ApiModelProperty(value = "第52周达成率")
    private BigDecimal achievementRateWeek52;

    /**
     * 第53周达成率
     */
    @ApiModelProperty(value = "第53周达成率")
    private BigDecimal achievementRateWeek53;

    /**
     * 第54周达成率
     */
    @ApiModelProperty(value = "第54周达成率")
    private BigDecimal achievementRateWeek54;


    /**
     * 维度类型：1：IE各基地周度达成率
     * 2：IE各车间周度达成率
     * 3：IE各产品周度达成率
     */
    @ApiModelProperty(value = "维度类型")
    private String type;

    @ApiModelProperty(value = "分类展示头1")
    private String headFirst;

    @ApiModelProperty(value = "分类展示头2")
    private String headSecond;

    @ApiModelProperty(value = "材料大类，分类用")
    private String materialCategory;

}
