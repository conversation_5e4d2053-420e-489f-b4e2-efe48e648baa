package com.jinkosolar.scp.mps.domain.dto;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.annotation.TranslateSpecifyFactory;
import com.jinkosolar.scp.mps.domain.enums.PulledCrystalItem;
import com.jinkosolar.scp.mps.domain.enums.SectionItem;
import com.jinkosolar.scp.mps.domain.enums.WeeklyPlanItem;
import com.jinkosolar.scp.mps.domain.factory.WeeklyPlanReportFactory;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.domain.util.MathUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: WeeklyPlanReportDTO
 * @date 2024/8/12 14:49
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeeklyPlanReportDTO implements Serializable {
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String planVersion;
    /**
     * 工段
     */
    @Translate(DictType = LovHeaderCodeConstant.MPS_PROCESS_ID, queryColumns = {"lovValue"}, from = {"lovName"}, to = {"processNoName"})
    @ApiModelProperty("工段")
    private String processNo;
    /**
     * 工段
     */
    @ApiModelProperty("工段")
    private String processNoName;
    /**
     * 工厂id
     */
    @Translate(DictType = LovHeaderCodeConstant.MPS_FACTORY, queryColumns = {"lovLineId"},
            from = {"lovValue", "lovName"}, to = {"factoryCode", "factoryName"})
    @ApiModelProperty("工厂id")
    private Long factoryId;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    private String factoryCode;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    private String factoryName;
    /**
     * 车间
     */
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKSHOP, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"workshopName"})
    @ApiModelProperty("车间")
    private Long workshopId;
    /**
     * 车间
     */
    @ApiModelProperty("车间")
    private String workshopName;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovName"}, to = {"planWorkCenter"})
    private String workCenterCode;
    /**
     * 工作中心名称
     */
    @ApiModelProperty("工作中心名称")
    private String planWorkCenter;
    /**
     * 产品类型
     */
    @ApiModelProperty("产品类型")
    private String productType;
    /**
     * 尺寸
     */
    @Translate
    @ApiModelProperty("尺寸")
    private Long size;
    /**
     * 尺寸
     */
    @ApiModelProperty("尺寸")
    private String sizeName;
    /**
     * 电池原始尺寸
     */
    @ApiModelProperty("电池原始尺寸")
    private String dcSize;
    /**
     * 厚度
     */
    @Translate
    @ApiModelProperty("厚度")
    private Long thickness;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private String thicknessName;
    /**
     * 定向/非定向
     */
    @Translate
    @ApiModelProperty("定向/非定向")
    private Long directional;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    private String directionalName;
    /**
     * 供美/非供美
     */
    @Translate
    @ApiModelProperty("供美/非供美")
    private Long supplyUs;
    /**
     * 供美/非供美
     */
    @ApiModelProperty("供美/非供美")
    private String supplyUsName;

    /**
     * 倒角 有Lov
     */
    @Translate(DictType = LovHeaderCodeConstant.BOM_CHAMFER_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"chamferName"})
    private Long chamferId;

    /**
     * 倒角名称
     */
    @ApiModelProperty(value = "倒角名称")
    private String chamferName;

    /**
     * 炉台数
     */
    @ApiModelProperty("炉台数")
    private BigDecimal machineQty;
    /**
     * 产出计划
     */
    @ApiModelProperty("产出计划")
    private BigDecimal schedulingQty;
    /**
     * 产出单位
     */
    @ApiModelProperty("产出单位")
    private String schedulingUom;
    /**
     * 单产
     */
    @ApiModelProperty("单产")
    private BigDecimal singleProduction;
    /**
     * 良率
     */
    @ApiModelProperty("良率")
    private BigDecimal planYieldRate;
    /**
     * 硅片投入
     */
    @ApiModelProperty("硅片投入")
    private BigDecimal productionVolume;
    /**
     * attribute1
     */
    @ApiModelProperty("attribute1")
    private String attribute1;
    /**
     * attribute2
     */
    @ApiModelProperty("attribute2")
    private String attribute2;
    /**
     * attribute3
     */
    @ApiModelProperty("attribute3")
    private String attribute3;
    /**
     * attribute4
     */
    @ApiModelProperty("attribute4")
    private String attribute4;
    /**
     * attribute5
     */
    @ApiModelProperty("attribute5")
    private String attribute5;
    /**
     * attribute6
     */
    @ApiModelProperty("attribute6")
    private String attribute6;
    /**
     * attribute7
     */
    @ApiModelProperty("attribute7")
    private String attribute7;
    /**
     * attribute8
     */
    @ApiModelProperty("attribute8")
    private String attribute8;
    /**
     * 出片数/可外发量
     */
    @ApiModelProperty("出片数/可外发量")
    private String attribute10;
    /**
     * 棒长
     */
    @ApiModelProperty("棒长")
    private String attribute13;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private String attribute19;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDateTime schedulingStartTime;
    /**
     * 日期
     */
    @ApiModelProperty("日期")
    private LocalDate date;
    /**
     * 项目
     */
    @ApiModelProperty("项目")
    private WeeklyPlanItem item;
    /**
     * 项目
     */
    @ApiModelProperty("项目")
    private String itemName;

    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")
    private String modelType;
    /**
     * 汇总
     */
    @ApiModelProperty("汇总")
    private BigDecimal totalValue;
    /**
     * 月份集合结果
     */
    @ApiModelProperty("月份集合结果")
    private Map<YearMonth, BigDecimal> monthValue;
    /**
     * 每天结果集合
     */
    @ApiModelProperty("每天结果集合")
    private List<DailyValue> dailyValueList = Lists.newArrayList();
    /**
     * 动态列头
     */
    @ApiModelProperty("动态列头")
    private List<String> subList;
    /**
     * 动态列结果
     */
    @ApiModelProperty("动态列结果")
    private Map<String, Object> subMap;

    @ApiModelProperty(value = "是否追溯")
    @Translate(DictType = LovHeaderCodeConstant.YES_OR_NO, queryColumns = {"lovValue"},
            from = {"lovName"}, to = {"tracedBackFlagName"})
    private String tracedBackFlag;

    @ApiModelProperty(value = "是否追溯")
    private String tracedBackFlagName;

    /**
     * 晶棒类型1
     */
    @ApiModelProperty("晶棒类型1")
    @Translate(DictType = LovHeaderCodeConstant.BOM_CRY_TYPE, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"crystalType1Name"})
    private Long crystalType1;

    @ApiModelProperty("晶棒类型1 Name")
    private String crystalType1Name;

    /**
     * 配方
     */
    @ApiModelProperty("配方")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_027_ATTR_1100, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"formulaName"})
    private Long formula;

    @ApiModelProperty("配方 Name")
    private String formulaName;

    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商")
    @Translate(DictType = LovHeaderCodeConstant.ATTR_TYPE_029_ATTR_1900, queryColumns = {"lovLineId"}, from = {"lovName"}, to = {"siliconSupplierName"})
    private String siliconSupplier;

    /**
     * 硅料供应商
     */
    @ApiModelProperty("硅料供应商品牌 Name")
    private String siliconSupplierName;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商厂家id")
    @TranslateSpecifyFactory(to = "specifySupplierName")
    private Long specifySupplier;

    /**
     * 指定硅料供应商
     */
    @ApiModelProperty("指定硅料供应商厂家名称")
    @ExcelProperty(value = "指定硅料供应商厂家名称")
    private String specifySupplierName;

    @ApiModelProperty("新产品名称")
    private String newProductName;

    public WeeklyPlanReportDTO(String planVersion,
                               String processNo,
                               Long factoryId,
                               Long workshopId,
                               String workCenterCode,
                               String planWorkCenter,
                               String productType,
                               Long size,
                               String sizeName,
                               Long thickness,
                               String thicknessName,
                               String attribute13,
                               Long directional,
                               Long supplyUs,
                               String schedulingUom,
                               Long chamferId,
                               String tracedBackFlag,
                               Long crystalType1,
                               Long formula,
                               String siliconSupplier,
                               Long specifySupplier) {
        this.planVersion = planVersion;
        this.processNo = processNo;
        this.factoryId = factoryId;
        this.workshopId = workshopId;
        this.workCenterCode = workCenterCode;
        this.planWorkCenter = planWorkCenter;
        this.productType = productType;
        this.size = size;
        this.sizeName = sizeName;
        this.thickness = thickness;
        this.thicknessName = thicknessName;
        this.attribute13 = attribute13;
        this.directional = directional;
        this.supplyUs = supplyUs;
        this.schedulingUom = schedulingUom;
        this.chamferId = chamferId;
        this.tracedBackFlag = tracedBackFlag;
        this.crystalType1 = crystalType1;
        this.formula = formula;
        this.siliconSupplier = siliconSupplier;
        this.specifySupplier=specifySupplier;
    }

    public WeeklyPlanReportDTO(String planVersion, String processNo, Long factoryId, Long workshopId, String workCenterCode, String planWorkCenter, String productType, Long size, String sizeName, Long thickness, String thicknessName, Long directional, String schedulingUom, Long chamferId) {
        this.planVersion = planVersion;
        this.processNo = processNo;
        this.factoryId = factoryId;
        this.workshopId = workshopId;
        this.workCenterCode = workCenterCode;
        this.planWorkCenter = planWorkCenter;
        this.productType = productType;
        this.size = size;
        this.sizeName = sizeName;
        this.thickness = thickness;
        this.thicknessName = thicknessName;
        this.directional = directional;
        this.schedulingUom = schedulingUom;
        this.chamferId = chamferId;
        this.tracedBackFlag = tracedBackFlag;

    }

    public static WeeklyPlanReportDTO group(WeeklyPlanReportDTO weeklyPlanReport) {
        return new WeeklyPlanReportDTO(weeklyPlanReport.getPlanVersion(),
                weeklyPlanReport.getProcessNo(),
                weeklyPlanReport.getFactoryId(),
                weeklyPlanReport.getWorkshopId(),
                weeklyPlanReport.getWorkCenterCode(),
                weeklyPlanReport.getPlanWorkCenter(),
                weeklyPlanReport.getProductType(),
                weeklyPlanReport.getSize(),
                weeklyPlanReport.getSizeName(),
                weeklyPlanReport.getThickness(),
                weeklyPlanReport.getThicknessName(),
                weeklyPlanReport.getAttribute13(),
                weeklyPlanReport.getDirectional(),
                weeklyPlanReport.getSupplyUs(),
                weeklyPlanReport.getSchedulingUom(),
                weeklyPlanReport.getChamferId(),
                weeklyPlanReport.getTracedBackFlag(),
                weeklyPlanReport.getCrystalType1(),
                weeklyPlanReport.getFormula(),
                weeklyPlanReport.getSiliconSupplier(),
                weeklyPlanReport.getSpecifySupplier()
        );
    }


    public static WeeklyPlanReportDTO groupNew(WeeklyPlanReportDTO weeklyPlanReport) {
        return new WeeklyPlanReportDTO(weeklyPlanReport.getPlanVersion(),
                weeklyPlanReport.getProcessNo(),
                weeklyPlanReport.getFactoryId(),
                weeklyPlanReport.getWorkshopId(),
                weeklyPlanReport.getWorkCenterCode(),
                weeklyPlanReport.getPlanWorkCenter(),
                weeklyPlanReport.getProductType(),
                weeklyPlanReport.getSize(),
                weeklyPlanReport.getSizeName(),
                weeklyPlanReport.getThickness(),
                weeklyPlanReport.getThicknessName(),
                weeklyPlanReport.getDirectional(),
                weeklyPlanReport.getSchedulingUom(),
                weeklyPlanReport.getChamferId()
        );
    }


    public static WeeklyPlanReportDTO groupWorkCenter(WeeklyPlanReportDTO weeklyPlanReport) {
        return new WeeklyPlanReportDTO(weeklyPlanReport.getPlanVersion(),
                weeklyPlanReport.getProcessNo(),
                weeklyPlanReport.getFactoryId(),
                weeklyPlanReport.getWorkshopId(),
                "合计",
                "合计",
                weeklyPlanReport.getProductType(),
                weeklyPlanReport.getSize(),
                weeklyPlanReport.getSizeName(),
                weeklyPlanReport.getThickness(),
                weeklyPlanReport.getThicknessName(),
                weeklyPlanReport.getAttribute13(),
                weeklyPlanReport.getDirectional(),
                weeklyPlanReport.getSupplyUs(),
                weeklyPlanReport.getSchedulingUom(),
                weeklyPlanReport.getChamferId(),
                weeklyPlanReport.getTracedBackFlag(),
                weeklyPlanReport.getCrystalType1(),
                weeklyPlanReport.getFormula(),
                weeklyPlanReport.getSiliconSupplier(),
                weeklyPlanReport.getSpecifySupplier());
    }

    public LocalDate toLocalDate() {
        LocalDateTime parse = DateUtil.parse(attribute19);
        return parse.toLocalDate();
    }

    public void convertMap(List<String> subList) {
        this.itemName = this.item.getDesc();
        this.subList = subList;
        this.subMap = Maps.newHashMap();
        boolean isPer = SectionItem.YIELD_RATE.equals(this.item) || PulledCrystalItem.YIELD_RATE.equals(this.item);
        if (MapUtils.isNotEmpty(this.monthValue)) {
            this.monthValue.forEach((month, value) -> {
                //良率字段转换百分比
                if (isPer) {
                    String perStr = MathUtils.num2PerStr(value);
                    this.subMap.put(DateUtil.convertYYYMM(month), perStr);
                } else {
                    this.subMap.put(DateUtil.convertYYYMM(month), value);
                }
            });
            //计算汇总量
            computeTotal(isPer);
            this.dailyValueList.forEach(dailyValue -> {
                LocalDate dayDate = dailyValue.getDayDate();
                BigDecimal value = dailyValue.getValue();
                //良率字段转换百分比
                if (isPer) {
                    String perStr = MathUtils.num2PerStr(value);
                    this.subMap.put(DateUtil.convertYYYMMDD(dayDate), perStr);
                } else {
                    this.subMap.put(DateUtil.convertYYYMMDD(dayDate), value);
                }
            });
            //清空数据对象，避免返回前端数据过多
            this.monthValue.clear();
            this.dailyValueList.clear();
        }
    }

    public void recalculateAggregates() {
        if (this.item == null || this.dailyValueList == null) {
            return;
        }

        // 重算月度合计
        if (PulledCrystalItem.lastMonthValueList().contains(this.item)) {
            this.monthValue = WeeklyPlanReportFactory.lastByMonthValue(this.dailyValueList);
        } else if (PulledCrystalItem.summaryMonthValueList().contains(this.item)) {
            this.monthValue = WeeklyPlanReportFactory.sumByMonthValue(this.dailyValueList);
        } else if (SectionItem.summaryMonthValueList().contains(this.item)) {
            this.monthValue = WeeklyPlanReportFactory.sumByMonthValue(this.dailyValueList);
        } else if (SectionItem.maxMonthValueList().contains(this.item)) {
            this.monthValue = WeeklyPlanReportFactory.maxByMonthValue(this.dailyValueList);
        }

        // 重算年度总计
        if (MapUtils.isNotEmpty(this.monthValue)) {
            computeTotal(this.getItem().isPer());
        } else {
            this.totalValue = BigDecimal.ZERO;
        }
    }

    private void computeTotal(boolean isPer) {
        //计算汇总量
        if (this.monthValue == null || this.monthValue.isEmpty()) {
            this.totalValue = BigDecimal.ZERO;
            if (this.subMap != null) {
                if (isPer) {
                    this.subMap.put("TOTAL", MathUtils.num2PerStr(this.totalValue));
                } else {
                    this.subMap.put("TOTAL", this.totalValue);
                }
            }
            return;
        }
        
        if (PulledCrystalItem.lastMonthValueList().contains(this.item)) {
            //取最后一个月份的数据：炉台数，单产，良率，出片数
            Optional<YearMonth> yearMonthOpt = this.monthValue.keySet().stream().sorted(Comparator.reverseOrder()).findFirst();
            if (yearMonthOpt.isPresent()) {
                this.totalValue = this.monthValue.get(yearMonthOpt.get());
            } else {
                this.totalValue = BigDecimal.ZERO;
            }
        } else if (PulledCrystalItem.summaryMonthValueList().contains(this.item) || SectionItem.summaryMonthValueList().contains(this.item)) {
            //取所有月汇总：产量，返棒，出货方棒，圆棒产出-折万片，硅块投入，产出计划，可外发量
            this.totalValue = this.monthValue.values().stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (SectionItem.maxMonthValueList().contains(this.item)) {
            //按月取最大：机台单产，机台数
            this.totalValue = this.monthValue.values().stream().filter(Objects::nonNull).sorted(Comparator.reverseOrder()).findFirst().orElse(null);
        } else if (SectionItem.avgMonthValueList().contains(this.item)) {
            //月平均：良率
            Double avg = this.monthValue.values().stream().filter(Objects::nonNull).collect(Collectors.averagingDouble(BigDecimal::doubleValue));
            this.totalValue = BigDecimal.valueOf(avg);
        } else {
            this.totalValue = this.monthValue.values().stream().filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (this.subMap != null) {
            if (isPer) {
                this.subMap.put("TOTAL", MathUtils.num2PerStr(this.totalValue));
            } else {
                this.subMap.put("TOTAL", this.totalValue);
            }
        }
    }

    public Map<String, Object> convertMap() {
        Map<String, Object> objectMap = BeanUtil.beanToMap(this);
        if (MapUtils.isNotEmpty(this.subMap)) {
            //设置动态列
            objectMap.putAll(this.subMap);
        }
        return objectMap;
    }

    public String sign() {
        return StringUtils.join(this.productType, this.directional, this.sizeName);
    }

    public String cellSign() {
        return StringUtils.join(this.productType, this.directional, this.sizeName, this.thickness, this.planWorkCenter);
    }


    public String getNewProductName(){
        String sb = this.directionalName + this.productType +
                this.chamferName +
                "-" +
                this.thicknessName;
        return sb;
    }
}
