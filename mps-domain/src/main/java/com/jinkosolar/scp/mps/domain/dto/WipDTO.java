package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 工单下发对象
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022年9月10日09:30:38
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "WipDTO对象", description = "DTO对象")
public class WipDTO {
    /**
     * dp_schedule_lines id
     */
    @ApiModelProperty(value = "dp_schedule_lines id")
    private Long id;
    /**
     * dpId
     */
    @ApiModelProperty(value = "dpId")
    private String dpId;
    /**
     * organization_id
     */
    @ApiModelProperty(value = "organization_id")
    private Long organizationId;
    /**
     * 是否海外
     */
    @ApiModelProperty(value = "是否海外")
    private String isOversea;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 生产基地
     */
    private String productPlace;
    /**
     * DP分组ID
     */
    @ApiModelProperty(value = "DP分组ID")
    private Long dpGroupId;
    /**
     * DP分组ID串
     */
    @ApiModelProperty(value = "DP分组ID串")
    private String dpGroupIdStr;
    /**
     * DP行类型
     */
    @ApiModelProperty(value = "DP行类型")
    private String dpLinesType;
    /**
     * dp_lines_id
     */
    @ApiModelProperty(value = "dp_lines_id")
    private Long dpLinesId;
    /**
     * id字符串
     */
    @ApiModelProperty(value = "id字符串")
    private List<Long> ids;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 组件料号
     */
    @ApiModelProperty(value = "组件料号")
    private String inventoryItemNo;
    /**
     * 项目地国家
     */
    @ApiModelProperty(value = "项目地国家")
    private String country;
    /**
     * 客户
     */
    @ApiModelProperty(value = "客户")
    private Long customerId;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 产地信息
     */
    @ApiModelProperty(value = "产地信息")
    private String placeInfo;
    /**
     * 是否监造
     */
    @ApiModelProperty(value = "是否监造")
    private String isSupervision;
    /**
     * 线缆长度
     */
    @ApiModelProperty(value = "线缆长度")
    private String itemAttribute7;
    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    private String itemAttribute5;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String dataVersion;
    /**
     * 铭牌  / 图纸号
     */
    @ApiModelProperty(value = "铭牌/图纸号")
    private String itemAttribute23;
    /**
     * 配料版本
     */
    @ApiModelProperty(value = "配料版本")
    private String ingredientsVersion;
    /**
     * 线盒端子
     */
    @ApiModelProperty(value = "线盒端子")
    private String itemAttribute8;
    /**
     * 特殊单编号
     */
    @ApiModelProperty(value = "特殊单编号")
    private String specialSn;
    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String itemAttribute4;
    /**
     * 平均功率
     */
    @ApiModelProperty(value = "平均功率")
    private BigDecimal averagePower;
    /**
     * 功率符合率
     */
    @ApiModelProperty(value = "功率符合率")
    private BigDecimal powerCoincidenceRate;
    /**
     * 组件良率
     */
    @ApiModelProperty(value = "组件良率")
    private BigDecimal productYield;
    /**
     * 交期
     */
    @ApiModelProperty(value = "交期")
    private LocalDate requiredDate;
    /**
     * 组件ID
     */
    @ApiModelProperty(value = "组件ID")
    private String apsScheduleId;

    /**
     * scp工单号
     */
    @ApiModelProperty(value = "scp工单号")
    private String attribute2;
    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String classCode;
    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String attribute6;
    /**
     * 配件袋
     */
    @ApiModelProperty(value = "配件袋")
    private String itemAttribute41;
    /**
     * 防尘塞
     */
    @ApiModelProperty(value = "防尘塞")
    private String itemAttribute34;
    /**
     * 计划排产数量
     */
    @ApiModelProperty(value = "计划排产数量")
    private BigDecimal planQty;
    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal scheduleQty;
    /**
     * total计划排产数量
     */
    @ApiModelProperty(value = "total计划排产数量")
    private BigDecimal totalPlanQty;
    /**
     * total(MW)
     */
    @ApiModelProperty(value = "total(MW)")
    private BigDecimal totalMW;
    /**
     * 预警信息
     */
    @ApiModelProperty(value = "计划排产数量")
    private String warningInfo;
    /**
     * ERP单号
     */
    @ApiModelProperty(value = "ERP单号")
    private String attribute1;
    /**
     * 计划完成日期
     */
    @ApiModelProperty(value = "计划完成日期")
    private LocalDate plannedCompleteDate;
    /**
     * 计划开始日期
     */
    @ApiModelProperty(value = "计划开始日期")
    private LocalDate plannedStartDate;
    /**
     * 替代项
     */
    @ApiModelProperty(value = "替代项")
    private String replaceItem;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 组件颜色
     */
    @ApiModelProperty(value = "组件颜色")
    private String moduleColor;
}
