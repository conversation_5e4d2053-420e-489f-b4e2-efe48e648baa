package com.jinkosolar.scp.mps.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 工单发放记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-29 14:06:13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "WipRecordDTO对象", description = "DTO对象")
public class WipRecordDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 工单发放编号
     */
    @ApiModelProperty(value = "工单发放编号")
    private String scpNo;
    /**
     * 来源流水号
     */
    @ApiModelProperty(value = "来源流水号")
    private String sourceId;
    /**
     * 来源系统
     */
    @ApiModelProperty(value = "来源系统")
    private String sourceCode;
    /**
     * 执行类型
     */
    @ApiModelProperty(value = "执行类型")
    private String processType;
    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态")
    private String processStatus;
    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private Long organizationId;
    /**
     * 工单弹性域上下文：组织ID
     */
    @ApiModelProperty(value = "工单弹性域上下文：组织ID")
    private String attributeCategory;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusType;
    /**
     * 装配件料号
     */
    @ApiModelProperty(value = "装配件料号")
    private String primaryItemNum;
    /**
     * 订单类型：标准1/非标准3
     */
    @ApiModelProperty(value = "订单类型：标准1/非标准3")
    private String jobType;
    /**
     * 分类
     */
    @ApiModelProperty(value = "分类")
    private String classCode;
    /**
     * 完成日期
     */
    @ApiModelProperty(value = "完成日期")
    private LocalDateTime scheduledCompletionDate;
    /**
     * 起始数量
     */
    @ApiModelProperty(value = "起始数量")
    private BigDecimal startQuantity;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectNo;
    /**
     * dpId
     */
    @ApiModelProperty(value = "dpId")
    private String dpId;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    private String basePlace;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String country;
    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private Long customerId;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建人")
    private String createdByName;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;
    /**
     * 组件料号
     */
    @ApiModelProperty(value = "组件料号")
    private String inventoryItemNo;
    /**
     * 铭牌  / 图纸号
     */
    @ApiModelProperty(value = "铭牌/图纸号")
    private String itemAttribute23;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String dataVersion;
    /**
     * 配件袋
     */
    @ApiModelProperty(value = "配件袋")
    private String itemAttribute41;
    /**
     * 防尘塞
     */
    @ApiModelProperty(value = "防尘塞")
    private String itemAttribute34;
    /**
     * ERP数量
     */
    @ApiModelProperty(value = "ERP数量")
    private BigDecimal erpQuantity;
}
