package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@ApiModel("拉晶工作中心对应关系数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkCenterCorrespondenceDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty("版本号")
    @ExcelProperty(value = "版本号")
    @ExcelIgnore
    private String versionNumber;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"factoryId"}, required = true)
    private String factoryCode;

    @ExcelIgnore
    @ApiModelProperty("工厂")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"factoryCode","factoryName"})
    private Long factoryId;

    @ExcelIgnore
    @ApiModelProperty("工厂名称")
    private String factoryName;
    /**
     * 拉晶工作中心
     */
    @ApiModelProperty("拉晶工作中心")
    @ExcelProperty(value = "拉晶工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"crystalPullingCenterId"})
    private String crystalPullingCenterCode;

    @ExcelIgnore
    @ApiModelProperty("拉晶工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"crystalPullingCenterCode","crystalPullingCenterName"})
    private Long crystalPullingCenterId;

    @ExcelIgnore
    @ApiModelProperty("拉晶工作中心名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"crystalPullingCenterId"}, required = true)
    private String crystalPullingCenterName;
    /**
     * 切方工作中心
     */
    @ApiModelProperty("切方工作中心")
    @ExcelProperty(value = "切方工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"cutSquareCenterId"})
    private String cutSquareCenterCode;

    @ExcelIgnore
    @ApiModelProperty("切方工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"cutSquareCenterCode","cutSquareCenterName"})
    private Long cutSquareCenterId;

    @ExcelIgnore
    @ApiModelProperty("切方工作中心名称")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"cutSquareCenterId"}, required = true)
    private String cutSquareCenterName;
    /**
     * 籽晶工作中心
     */
    @ApiModelProperty("籽晶工作中心")
    @ExcelProperty(value = "籽晶工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"seedCrystalCenterId"})
    private String seedCrystalCenterCode;
    @ExcelIgnore
    @ApiModelProperty("籽晶工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"seedCrystalCenterCode","seedCrystalCenterName"})
    private Long seedCrystalCenterId;

    @ExcelIgnore
    @ApiModelProperty("籽晶工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"seedCrystalCenterId"}, required = true)
    private String seedCrystalCenterName;

    /**
     * 酸洗工作中心
     */
    @ApiModelProperty("酸洗工作中心")
    @ExcelProperty(value = "酸洗工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"picklingCenterId"})
    private String picklingCenterCode;
    @ExcelIgnore
    @ApiModelProperty("酸洗工作中心")
    @Translate(DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"picklingCenterCode","picklingCenterName"})
    private Long picklingCenterId;

    @ExcelIgnore
    @ApiModelProperty("酸洗工作中心")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_WORKCENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"picklingCenterId"}, required = true)
    private String picklingCenterName;
    /**
     * 数据分类
     */
    @ApiModelProperty("数据分类")
    @ExcelProperty(value = "数据分类")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovName"},
            from = {"lovLineId","lovValue"}, to = {"dataType","dataTypeCode"}, required = true)
    private String dataTypeName;
    @ExcelIgnore
    @ApiModelProperty("数据分类")
    @Translate(DictType = LovHeaderCodeConstant.MPS_MODEL_TYPE, queryColumns = {"lovLineId"},
            from = {"lovName"}, to = {"dataTypeName"})
    private Long dataType;

    @ExcelIgnore
    @ApiModelProperty("数据分类Code")
    private String dataTypeCode;
}