package com.jinkosolar.scp.mps.domain.dto;

import com.ibm.scp.common.api.base.BaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * [说明]工作中心调整开线汇总表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-11-12
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "工作中心调整开线汇总表DTO对象", description = "DTO对象")
public class WorkCenterLineAdjustSummaryDTO extends BaseDTO {


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private Long id;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterId;
    
    /**
     * 工作中心编码
     */
    @ApiModelProperty(value = "工作中心编码")
    private String workCenterCode;
    
    /**
     * 工作中心名称
     */
    @ApiModelProperty(value = "工作中心名称")
    private String workCenterName;
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDate attendanceDate;
    
    /**
     * 车间Id
     */
    @ApiModelProperty(value = "车间Id")
    private Long workShopId;
    
    /**
     * 车间代码
     */
    @ApiModelProperty(value = "车间代码")
    private String workShopCode;
    
    /**
     * 车间名称
     */
    @ApiModelProperty(value = "车间名称")
    private String workShopName;
    
    /**
     * 区域id
     */
    @ApiModelProperty(value = "区域id")
    private Long areaId;
    
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    private String areaIdName;
    
    /**
     * 工段id
     */
    @ApiModelProperty(value = "工段id")
    private Long workNumId;
    
    /**
     * 工段名称
     */
    @ApiModelProperty(value = "工段名称")
    private String workNumIdName;
    
    /**
     * 中长期统计区域id
     */
    @ApiModelProperty(value = "中长期统计区域id")
    private Long sestemAreaId;
    
    /**
     * 中长期统计区域名称
     */
    @ApiModelProperty(value = "中长期统计区域名称")
    private String sestemAreaIdName;
    
    /**
     * 开线数
     */
    @ApiModelProperty(value = "开线数")
    private BigDecimal attendanceNum;
    

}
