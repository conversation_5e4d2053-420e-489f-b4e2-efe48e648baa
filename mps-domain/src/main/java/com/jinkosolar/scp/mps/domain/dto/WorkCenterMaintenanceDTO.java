package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.jinkosolar.scp.mps.domain.constant.MpsLovConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("工作中心维护(停机，停电，保养)数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkCenterMaintenanceDTO extends BaseDTO implements Serializable {
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalDateTime fromTime;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")
    @NotNull(message = "区域不能为空")
    private String maintenanceArea;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注")
    private String remark;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalDateTime toTime;
    /**
     * 工作中心id
     */
    @ApiModelProperty("工作中心id")
    @ExcelProperty(value = "工作中心id")
    @NotNull(message = "工作中心不能为空")
    @Translate(DictType = MpsLovConstant.WORK_CENTER, queryColumns = {"lovLineId"},
            from = {"lovName","lovValue"}, to = {"workCenterDesc","workCenterCode"})
    private Long workCenterId;

    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心编码")
    @Translate(unTranslate = true,DictType = MpsLovConstant.WORK_CENTER, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"workCenterId"}, required = true)
    private String workCenterCode;

    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心描述")
    private String workCenterDesc;


    @ApiModelProperty("工作中心id")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovLineId"},
            from = {"lovValue","lovName"}, to = {"factoryCode","factoryName"})
    private Long factoryId;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    @ApiModelProperty("工厂代码")
    @Translate(unTranslate = true,DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE, queryColumns = {"lovValue"},
            from = {"lovLineId"}, to = {"factoryId"}, required = true)
    private String factoryCode;
}
