package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 
import java.time.LocalDateTime;
import java.math.BigDecimal;  


@ApiModel("拉晶线边在制品表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkInProgressDTO extends BaseDTO implements Serializable {
    /**
     * 主键ID，自增
     */
    @ApiModelProperty("主键ID，自增")
    @ExcelProperty(value = "主键ID，自增")  
    private Long id;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")  
    private String factoryCode;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")  
    private String workshop;
    /**
     * 规程代码
     */
    @ApiModelProperty("规程代码")
    @ExcelProperty(value = "规程代码")  
    private String operationNo;
    /**
     * 规程名称
     */
    @ApiModelProperty("规程名称")
    @ExcelProperty(value = "规程名称")  
    private String operationDesc;
    /**
     * 产品
     */
    @ApiModelProperty("产品")
    @ExcelProperty(value = "产品")  
    private String productionNo;
    /**
     * 数量（KG）
     */
    @ApiModelProperty("数量（KG）")
    @ExcelProperty(value = "数量（KG）")  
    private BigDecimal quantity;
    /**
     * 产品物料号
     */
    @ApiModelProperty("产品物料号")
    @ExcelProperty(value = "产品物料号")  
    private String itemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String itemDesc;
    /**
     * 指定硅料供应商代码
     */
    @ApiModelProperty("指定硅料供应商代码")
    @ExcelProperty(value = "指定硅料供应商代码")  
    private String siliconsupplierNo;
    /**
     * 指定硅料供应商名称
     */
    @ApiModelProperty("指定硅料供应商名称")
    @ExcelProperty(value = "指定硅料供应商名称")  
    private String siliconsupplierDesc;
    /**
     * 定向/非定向
     */
    @ApiModelProperty("定向/非定向")
    @ExcelProperty(value = "定向/非定向")  
    private String directional;
    /**
     * 高阻
     */
    @ApiModelProperty("高阻")
    @ExcelProperty(value = "高阻")  
    private String highResistance;
    /**
     * 数据获取时间
     */
    @ApiModelProperty("数据获取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "数据获取时间")  
    private LocalDateTime dateTime;
}