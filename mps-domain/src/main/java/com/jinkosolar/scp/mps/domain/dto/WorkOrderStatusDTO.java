package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.ImportExConvert;                             
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;                 


@ApiModel(" 工单状态表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkOrderStatusDTO extends BaseDTO implements Serializable {
    /**
     * 
     */
    @ApiModelProperty("id")
    @ExcelProperty(value = "")  
    private Long id;
    /**
     * 生产建议
     */
    @ApiModelProperty("生产建议")
    @ExcelProperty(value = "生产建议")
    @JSONField(name = "ZSCJY")
    private String zscjy;
    /**
     * 组合号
     */
    @ApiModelProperty("组合号")
    @ExcelProperty(value = "组合号")
    @JSONField(name = "ZPKGNUM")
    private String zpkgnum;
    /**
     * 组合行
     */
    @ApiModelProperty("组合行")
    @ExcelProperty(value = "组合行")
    @JSONField(name = "ZPKGITM")
    private Long zpkgitm;
    /**
     * 销售订单号
     */
    @ApiModelProperty("销售订单号")
    @ExcelProperty(value = "销售订单号")
    @JSONField(name = "VBELN")
    private String vbeln;
    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    @ExcelProperty(value = "销售订单行")
    @JSONField(name = "POSNR")
    private Long posnr;
    /**
     * 状态标识
     */
    @ApiModelProperty("状态标识")
    @ExcelProperty(value = "状态标识")
    @JSONField(name = "ZBS")
    private String zbs;
}