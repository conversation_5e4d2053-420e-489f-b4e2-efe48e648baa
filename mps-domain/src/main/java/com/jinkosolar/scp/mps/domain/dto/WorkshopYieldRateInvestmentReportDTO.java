package com.jinkosolar.scp.mps.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


@ApiModel("补减投报表车间良率DTO")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WorkshopYieldRateInvestmentReportDTO extends BaseDTO implements Serializable {

    /**
     * 计划转库存率
     */
    @ApiModelProperty("计划转库存率")
    private BigDecimal planToInventoryRate;

    /**
     * 计划降级率
     */
    @ApiModelProperty("计划降级率")
    private BigDecimal planToReduceRate;

    /**
     * 计划版型ID
     */
    @ApiModelProperty("计划版型ID")
    private Long planVersion;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    private String planType;


    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    private String productFactoryCode;

}