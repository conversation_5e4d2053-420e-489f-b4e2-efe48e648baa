package com.jinkosolar.scp.mps.domain.dto.bom;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


@ApiModel("电池产品产品分片数维护数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BomBatteryProductSegmentationDTO extends BaseDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 电池片产品(电池片类型属性【产品类型】)
     */
    @ApiModelProperty("电池片产品(电池片类型属性【产品类型】)")
    private Long cellProductId;

    /**
     * 电池产品
     */
    @ApiModelProperty("电池产品Code")
    @ExcelProperty(value = "电池产品Code")
    private String cellProductCode;


    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    @ExcelProperty(value = "工厂")
    private Long factoryId;

    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryIdName;

    /**
     * 分片数
     */
    @ApiModelProperty("分片数")
    @ExcelProperty(value = "分片数")
    private Integer shardNumber;
}