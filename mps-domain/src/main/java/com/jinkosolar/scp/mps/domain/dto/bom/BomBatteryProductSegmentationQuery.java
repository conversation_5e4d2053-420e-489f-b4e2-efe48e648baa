package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@ApiModel("电池产品产品分片数维护查询条件对象")
@Data
@Accessors(chain = true)
public class BomBatteryProductSegmentationQuery extends PageDTO implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 电池片产品(电池片类型属性【产品类型】)
     */
    @ApiModelProperty("电池片产品(电池片类型属性【产品类型】)")
    private Long cellProductId;

    /**
     * 工厂
     */
    @ApiModelProperty("工厂")
    private Long factoryId;

    /**
     * 电池产品
     */
    @ApiModelProperty("电池产品Code")
    private String cellProductCode;


    /**
     * 分片数
     */
    @ApiModelProperty("分片数")
    private Integer shardNumber;

    /**
     * 电池产品
     */
    @ApiModelProperty("多个电池产品Code")
    private List<String> cellProductCodes;

    @ApiModelProperty("excel参数对象")
    private ExcelPara excelPara;
}