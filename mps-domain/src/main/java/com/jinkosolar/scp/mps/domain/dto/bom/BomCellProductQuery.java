package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@ApiModel("电池产品信息管理查询条件对象")
@Data
public class BomCellProductQuery extends PageDTO implements Serializable {

    @ApiModelProperty("电池片产品")
    private String cellProduct;

    @ApiModelProperty("电池片产品")
    private List<String> cellProducts;

    @ApiModelProperty("电性能(A级组件类型属性【电池类型】)")
    private Long electricalProperty;

    @ApiModelProperty("主栅(DS版本的【Main_Grid_Line 】)")
    private String coralLine;

    @ApiModelProperty("excel参数对象")
    private ExcelPara excelPara;
}
