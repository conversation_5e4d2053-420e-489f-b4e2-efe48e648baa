package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ComponentsDTO对象", description = "DTO对象")
public class ComponentsDTO extends BaseDTO {

    private static final long serialVersionUID = -5793285154076772464L;

    /**
     * bom构件id，自增序列
     */
    @ApiModelProperty(value = "bom构件id，自增序列")
    private Long id;

    /**
     * bom id，从结构上取
     */
    @ApiModelProperty(value = "bom id，从结构上取")
    private Long bomId;

    /**
     * 操作序列号
     */
    @ApiModelProperty(value = "操作序列号")
    private Integer operationSeqNum;

    /**
     * 构件物料id
     */
    @ApiModelProperty(value = "构件物料id")
    private Long componentItemId;

    @ApiModelProperty(value = "bom结构")
    private String bomStructure;

    /**
     * 替代料标识，Y/N
     */
    @ApiModelProperty(value = "替代料标识，Y/N")
    private String substituteFlag;

    /**
     * 最后更新日期
     */
    @ApiModelProperty(value = "最后更新日期")
    private LocalDateTime lastUpdateDate;

    /**
     * 项目序列号
     */
    @ApiModelProperty(value = "项目序列号")
    private Integer itemNum;

    /**
     * 构件数量
     */
    @ApiModelProperty(value = "构件数量")
    private String componentQuantity;

    /**
     * 产出因子
     */
    @ApiModelProperty(value = "产出因子")
    private Integer componentYieldFactor;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String componentRemarks;

    /**
     * 生效日期
     */
    @ApiModelProperty(value = "生效日期")
    private LocalDateTime effectivityDate;

    /**
     * 更改注释eco更改号
     */
    @ApiModelProperty(value = "更改注释eco更改号")
    private String changeNotice;

    /**
     * 实施日期
     */
    @ApiModelProperty(value = "实施日期")
    private LocalDateTime implementationDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private LocalDateTime disableDate;

    @ApiModelProperty(value = "成品品番id")
    private Long productItemId;
    @ApiModelProperty(value = "成品品番编码")
    private String productItemCode;
    @ApiModelProperty(value = "成品品番描述")
    private String productItemDesc;
    @ApiModelProperty(value = "成品品番工厂")
    private Long productItemFactory;

    /**
     * 扩展属性分类
     */
    @ApiModelProperty(value = "扩展属性分类")
    private String attributeCategory;

    /**
     * 扩展属性1
     */
    @ApiModelProperty(value = "扩展属性1")
    private String attribute1;

    /**
     * 扩展属性2
     */
    @ApiModelProperty(value = "扩展属性2")
    private String attribute2;

    /**
     * 扩展属性3
     */
    @ApiModelProperty(value = "扩展属性3")
    private String attribute3;

    /**
     * 扩展属性4
     */
    @ApiModelProperty(value = "扩展属性4")
    private String attribute4;

    /**
     * 扩展属性5
     */
    @ApiModelProperty(value = "扩展属性5")
    private String attribute5;

    /**
     * 扩展属性6
     */
    @ApiModelProperty(value = "扩展属性6")
    private String attribute6;

    /**
     * 扩展属性7
     */
    @ApiModelProperty(value = "扩展属性7")
    private String attribute7;

    /**
     * 扩展属性8
     */
    @ApiModelProperty(value = "扩展属性8")
    private String attribute8;

    /**
     * 扩展属性9
     */
    @ApiModelProperty(value = "扩展属性9")
    private String attribute9;

    /**
     * 扩展属性10
     */
    @ApiModelProperty(value = "扩展属性10")
    private String attribute10;

    /**
     * 扩展属性11
     */
    @ApiModelProperty(value = "扩展属性11")
    private String attribute11;

    /**
     * 扩展属性12
     */
    @ApiModelProperty(value = "扩展属性12")
    private String attribute12;

    /**
     * 扩展属性13
     */
    @ApiModelProperty(value = "扩展属性13")
    private String attribute13;

    /**
     * 扩展属性14
     */
    @ApiModelProperty(value = "扩展属性14")
    private String attribute14;

    /**
     * 扩展属性15
     */
    @ApiModelProperty(value = "扩展属性15")
    private String attribute15;

    /**
     * 计划百分比
     */
    @ApiModelProperty(value = "计划百分比")
    private Integer planningFactor;

    /**
     * 相关数量
     */
    @ApiModelProperty(value = "相关数量")
    private Integer quantityRelated;

    /**
     * acdType
     */
    @ApiModelProperty(value = "acdType")
    private Integer acdType;

    /**
     * 构件序号
     */
    @ApiModelProperty(value = "构件序号")
    private Long componentSequenceId;

    @ApiModelProperty(value = "substituteComponentId")
    private Long substituteComponentId;

    /**
     * 清单序号（关键字）
     */
    @ApiModelProperty(value = "清单序号（关键字）")
    private Long billSequenceId;

    /**
     * 车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟
     */
    @ApiModelProperty(value = "车间供应类型1.推式.装配拉式.操作拉式 4.大量.供应商.虚拟")
    private Integer wipSupplyType;

    /**
     * 供应子库存
     */
    @ApiModelProperty(value = "供应子库存")
    private String supplySubinventory;

    /**
     * 供应货位d
     */
    @ApiModelProperty(value = "供应货位d")
    private Long supplyLocatorId;

    /**
     * 清单项目类型1.模型.选项类.计划中.标准
     */
    @ApiModelProperty(value = "清单项目类型1.模型.选项类.计划中.标准")
    private Integer bomItemType;

    /**
     * basisType
     */
    @ApiModelProperty(value = "basisType")
    private Integer basisType;


    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    private String itemDesc;

    /**
     * 物料单位
     */
    @ApiModelProperty(value = "物料单位")
    private String unit;

    /**
     * dpId
     */
    @ApiModelProperty(value = "dpId")
    private String dpId;

    /**
     * 6A料号
     */
    @ApiModelProperty(value = "6A料号")
    private String parentItemCode;

    @ApiModelProperty("替代项目：组")
    private String alpgr;

    @ApiModelProperty(value = "基本用量")
    private String productQuantity;

    @ApiModelProperty(value = "层次")
    private Integer level;

    @ApiModelProperty(value = "组件集合")
    private List<ComponentsDTO> componentsDTOList = new ArrayList<>();

    @ApiModelProperty(value = "父级id")
    private Long parentId;
}
