/**
 *
 * @Function: ErpAlternateDesignatorQuery.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niu<PERSON><PERSON>
 * @date: 2024年1月3日 10:54:25
 */
package com.jinkosolar.scp.mps.domain.dto.bom;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * @Function: ErpAlternateDesignatorDTO.java
 * @Description: 该函数的功能描述
 * @version: v1.0.0
 * @author: niuwei<PERSON>
 * @date: 2024年1月3日 10:54:25
 */
@Data
public class ErpAlternateDesignatorDTO {

 /**
  * 描述-车间
  */
 @ApiModelProperty(value = "描述-车间")
 private String description;
 /**
  * 库存组织ID
  */
 @ApiModelProperty(value = "库存组织ID")
 private Long organizationId;
 /**
  * 替代项
  */
 @ApiModelProperty(value = "备用代号-替代项")
 private String alternateDesignatorCode;
}
