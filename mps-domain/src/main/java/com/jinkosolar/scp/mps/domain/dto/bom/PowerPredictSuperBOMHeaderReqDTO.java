package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerPredictSuperBOMReqDTO对象", description = "PowerPredictSuperBOMReqDTO对象")
@Builder
public class PowerPredictSuperBOMHeaderReqDTO extends BaseDTO {

    /**
     * dpGroupId
     */
    @ApiModelProperty(value = "dpGroupId")
    private Long dpGroupId;

    /**
     * dp_lines_id
     */
    @ApiModelProperty(value = "dp_lines_id")
    private String dpLinesId;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;

    /**
     * 预测结果版本号
     */
    @ApiModelProperty(value = "预测结果版本号")
    private String powerPredictVersion;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 组件尺寸
     */
    @ApiModelProperty(value = "组件尺寸")
    private String moduleSize;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    private String projectPlace;

    /**
     * 材料列表
     */
    @ApiModelProperty(value = "材料列表")
    private List<PowerPredictSuperBOMMaterialLineReqDTO> powerPredictSuperBOMMaterialLineReqDTOList;


}
