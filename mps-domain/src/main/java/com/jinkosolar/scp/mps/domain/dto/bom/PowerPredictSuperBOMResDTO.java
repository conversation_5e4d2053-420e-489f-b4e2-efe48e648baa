package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PowerPredictSuperBOMResDTO对象", description = "PowerPredictSuperBOMResDTO对象")
public class PowerPredictSuperBOMResDTO extends BaseDTO {

    /**
     * dpGroupId
     */
    @ApiModelProperty(value = "dpGroupId")
    private Long dpGroupId;

    /**
     * dpId
     */
    @ApiModelProperty(value = "dp_id")
    private String dpId;

    /**
     * 预测结果版本号
     */
    @ApiModelProperty(value = "预测结果版本号")
    private String powerPredictVersion;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    /**
     * 是否符合bom
     */
    @ApiModelProperty(value = "是否符合bom")
    private String isBom;

    /**
     * 具体信息
     */
    @ApiModelProperty(value = "具体信息")
    private String detailedInformation;
}
