package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;


/**
 * [说明]BOM单组合号分配表 DTO
 * <AUTHOR>
 * @version 创建时间： 2024-08-16
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BOM单组合号分配表DTO对象", description = "DTO对象")
public class ProductBomDiffGroupDTO extends BaseDTO {


    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    /**
     * 组合号ID
     */
    @ApiModelProperty(value = "组合号ID")
    private Long dpMatchItemId;
    
    /**
     * 差异表指定BOM组
     */
    @ApiModelProperty(value = "差异表指定BOM组")
    private String diffBomGroup;
    
    /**
     * BOM组单组合号
     */
    @ApiModelProperty(value = "BOM组单组合号")
    private String singleBomGroupNum;

    /**
     * BOM组单组合号
     */
    @ApiModelProperty(value = "BOM组单组合号列表")
    private List<String> singleBomGroupNumList;
    
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    

}
