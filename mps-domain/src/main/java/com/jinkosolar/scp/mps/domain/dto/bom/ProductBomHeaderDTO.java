package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * 生产BOM头
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-12 16:20:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProductBomHeaderDTO对象", description = "DTO对象")
public class ProductBomHeaderDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "DP分组匹配料号信息ID")
    private Long dpMatchItemId;
    /**
     * 组件料号
     */
    @ApiModelProperty(value = "组件料号")
    private String itemCode;

    /**
     * BOM类型
     */
    @ApiModelProperty(value = "BOM类型")
    private String bomType;

    /**
     * 记录引用的超级BOM头ID或标准BOM头ID
     */
    @ApiModelProperty(value = "记录引用的超级BOM头ID或标准BOM头ID")
    private Long sourceBomId;

    /**
     * BOM状态
     */
    @ApiModelProperty(value = "BOM状态")
    private String bomStatus;

    /**
     * 基准数量
     */
    @ApiModelProperty(value = "基准数量")
    private BigDecimal moduleQty;

    @ApiModelProperty(value = "认证分类")
    private String certCat;

    /**
     * Q2料号
     */
    @ApiModelProperty(value = "Q2料号")
    private String q2ItemCode;
    /**
     * Q3料号
     */
    @ApiModelProperty(value = "Q3料号")
    private String q3ItemCode;
    /**
     * Qb料号
     */
    @ApiModelProperty(value = "Qb料号")
    private String qbItemCode;
    /**
     * 联产品Q1料号
     */
    @ApiModelProperty(value = "联产品Q1料号")
    private String productQ1ItemCode;
    /**
     * 替代项
     */
    @ApiModelProperty(value = "替代项")
    private String substituteItem;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 销售料号
     */
    @ApiModelProperty(value = "销售料号")
    private String selaItemNo;
    /**
     * BOM部门
     */
    @ApiModelProperty(value = "BOM部门")
    private String bomDept;
    /**
     * 特殊单号
     */
    @ApiModelProperty(value = "特殊单号")
    private String specialSn;

    @ApiModelProperty(value = "是否确认：0未确认，1已确认")
    private Integer isConfirm;

    @ApiModelProperty(value = "来源")
    private String sourceType;

    @ApiModelProperty(value = "生产bom行列表")
    private List<ProductBomLineDTO> productBomLineDTOS;

    @ApiModelProperty(value = "指定bom分配组合号列表")
    private List<ProductBomDiffGroupDTO> productBomDiffGroupDTOS;
}
