package com.jinkosolar.scp.mps.domain.dto.bom;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;


/**
 * 生产BOM行
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-12 16:21:06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProductBomLineDTO对象", description = "DTO对象")
public class ProductBomLineDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "生产bom头Id")
    private Long productBomHeaderId;

    @ApiModelProperty(value = "产品结构")
    private String bomStructure;
    /**
     * 排序字段，从小到大排序
     */
    @ApiModelProperty(value = "排序字段，从小到大排序")
    private Integer ranking;
    /**
     * 1主，0替代
     */
    @ApiModelProperty(value = "1主，0替代")
    private Integer isPrimary;
    /**
     * 子件（物料编码）
     */
    @ApiModelProperty(value = "子件（物料编码）")
    private String itemCode;
    /**
     * 子件描述（物料描述）
     */
    @ApiModelProperty(value = "子件描述（物料描述）")
    private String itemDesc;

    /**
     * 父级物料编码
     */
    @ApiModelProperty(value = "父级物料编码")
    private String parentItemCode;

    /**
     * 父级标识
     */
    @ApiModelProperty(value = "父级标识")
    private String parentItemFlag;
    /**
     * 子件单位（物料单位）
     */
    @ApiModelProperty(value = "子件单位（物料单位）")
    private String priUom;
    /**
     * 单位用量（组件数量）
     */
    @ApiModelProperty(value = "单位用量（组件数量）")
    private String componentQuantity;

    /**
     * 组件数量,字符型或公式
     */
    @ApiModelProperty(value = "组件数量,字符型或公式")
    private String componentQtyOriginal;
    /**
     * 计划%
     */
    @ApiModelProperty(value = "计划%")
    private String plan;
    /**
     * 损耗率
     */
    @ApiModelProperty(value = "损耗率")
    private String outputRate;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    private String supplyType;
    /**
     * 特殊单材料标识
     */
    @ApiModelProperty(value = "特殊单材料标识")
    private String isSpecialMaterial;


    @ApiModelProperty(value = "特殊单材料备注")
    private String specialMaterialRemark;
    /**
     * 异常标识
     */
    @ApiModelProperty(value = "异常标识")
    private Integer isException;
    /**
     * 消息
     */
    @ApiModelProperty(value = "消息")
    private String message;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String sourceType;
    /**
     * 调整标识
     */
    @ApiModelProperty(value = "调整标识")
    private String adjustFlag;

    /**
     * 规则验证状态:0 未验证 1 已验证
     */
    @ApiModelProperty(value = "规则验证状态:0 未验证 1 已验证 2 验证未通过")
    private Integer isRuleVerify;

    @ApiModelProperty(value = "规则验证状态名称:0 未验证 1 已验证 2 验证未通过 ")
    private String isRuleVerifyName;

    @ApiModelProperty(value = "特殊单验证状态:0 未验证 1 已验证 2 验证未通过")
    private Integer isSpecialVerify;

    @ApiModelProperty(value = "特殊单验证状态名称:0 未验证 1 已验证 2 验证未通过")
    private String isSpecialVerifyName;

    /**
     * 子件状态（组件状态）
     */
    @ApiModelProperty(value = "子件状态（组件状态）")
    private String itemStatus;

    @ApiModelProperty(value = "包括在累计成本中")
    private String includedAccumulatedCost;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 差异表指定BOM组
     */
    @ApiModelProperty(value = "差异表指定BOM组")
    private String diffBomGroup;

    /**
     * 差异表指定BOM名称
     */
    @ApiModelProperty(value = "差异表指定BOM名称")
    private String diffBomName;

    @ApiModelProperty(value = "bom结构占比")
    private BigDecimal bomItemUsage;

    @ApiModelProperty(value = "DP分组匹配料号信息ID")
    private Long dpMatchItemId;

    @ApiModelProperty(value = "大类")
    private Long categorySegment1;

    @ApiModelProperty(value = "中类")
    private Long categorySegment2;

    @ApiModelProperty(value = "小类")
    private Long categorySegment3;

    @ApiModelProperty(value = "细类")
    private Long categorySegment4;
}
