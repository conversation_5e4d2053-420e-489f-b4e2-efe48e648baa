package com.jinkosolar.scp.mps.domain.dto.bom;

import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.MyThreadLocal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 产品族基础信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 19:20:45
 */
@Data
@ApiModel(value = "ProductGroup查询条件", description = "查询条件")
@Accessors(chain = true)
public class ProductGroupQuery extends PageDTO implements Serializable {
    /**
     * 属性分类ID
     */
    @ApiModelProperty(value = "属性分类ID")
    private Long moduleStackId;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private List<String> productFamilies;

    private List<Long> idList;

    public ProductGroupQuery() {
    }

    public ProductGroupQuery(List<String> productFamilies, Integer pageNumber, Integer pageSize) {
        this.productFamilies = productFamilies;
        this.setPageNumber(pageNumber);
        this.setPageSize(pageSize);
        this.setToken(MyThreadLocal.get().getToken());
    }

    public ProductGroupQuery(List<String> productFamilies) {
        this.productFamilies = productFamilies;
    }
}
