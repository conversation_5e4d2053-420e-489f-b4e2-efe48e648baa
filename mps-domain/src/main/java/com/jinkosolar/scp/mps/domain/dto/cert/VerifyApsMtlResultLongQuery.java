package com.jinkosolar.scp.mps.domain.dto.cert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
@ApiModel(value = "VerifyApsMtlResultLongQuery", description = "查询条件")
public class VerifyApsMtlResultLongQuery {

    @ApiModelProperty(value = "产品族")
    private String prod;

    @ApiModelProperty(value = "Bom结构的ATTR Code")
    private String bomStructureAttrCode;

    @ApiModelProperty(value = "bom item 的db 字段")
    private String dbField;

    @ApiModelProperty(value = "校验值")
    private String attrValue;
}
