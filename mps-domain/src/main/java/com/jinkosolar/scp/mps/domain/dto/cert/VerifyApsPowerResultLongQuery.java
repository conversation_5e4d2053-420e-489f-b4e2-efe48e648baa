package com.jinkosolar.scp.mps.domain.dto.cert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
@ApiModel(value = "VerifyApsPowerResultLongQuery", description = "查询条件")
public class VerifyApsPowerResultLongQuery {

    @ApiModelProperty(value = "产品族")
    private String prod;

    @ApiModelProperty(value = "功率")
    private String power;

    @ApiModelProperty(value = "功率值止,只有当为范围值时才填此字段")
    private String powerTo;

}
