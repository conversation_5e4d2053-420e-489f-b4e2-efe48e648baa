package com.jinkosolar.scp.mps.domain.dto.cert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/9/2
 */
@Data
@Builder
@ApiModel(value = "VerifyApsPowerResultLongVO", description = "VO对象")
@AllArgsConstructor
@NoArgsConstructor
public class VerifyApsPowerResultLongVO {
    @ApiModelProperty(value = "IEC功率符合结果")
    private Boolean iecResult;

    @ApiModelProperty(value = "IEC功率符合结果描述")
    private String iecResultRemark;

    @ApiModelProperty(value = "UL 功率符合结果")
    private Boolean ulResult;

    @ApiModelProperty(value = "UL 功率符合结果描述")
    private String ulResultRemark;

}
