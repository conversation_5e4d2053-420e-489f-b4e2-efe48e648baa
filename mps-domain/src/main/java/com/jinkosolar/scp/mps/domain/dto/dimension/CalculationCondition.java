package com.jinkosolar.scp.mps.domain.dto.dimension;

import cn.hutool.core.bean.BeanUtil;
import com.jinkosolar.scp.mps.domain.entity.PowerFinance;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CalculationCondition 长期功率计算条件
 * @date 2023/12/1 09:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CalculationCondition {
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 功率版本
     */
    @ApiModelProperty(value = "功率版本")
    private String versions;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;

    public static CalculationCondition build(PowerFinance bean){
        CalculationCondition condition = new CalculationCondition();
        BeanUtil.copyProperties(bean, condition);
        return condition;
    }
}
