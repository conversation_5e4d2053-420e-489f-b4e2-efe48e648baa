package com.jinkosolar.scp.mps.domain.dto.dp;

import com.ibm.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * APS推送DP实际排产中间表-批次
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-12 11:53:25
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ApsScheduleBatchDTO extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 排产行ID
     */
    @ApiModelProperty(value = "排产行ID")
    private String scheduleBatchId;

    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    private String batchNo;

    /**
     * 是否处理
     */
    @ApiModelProperty(value = "是否处理")
    private String flag;

    /**
     * 错误次数
     */
    @ApiModelProperty(value = "错误次数")
    private Integer errorCount;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    private String month;
    /**
     * 模型号
     */
    @ApiModelProperty(value = "模型号")
    private String moduleNo;
    /**
     * 审批状态（Y通过，N拒绝，null未审批）
     */
    @ApiModelProperty(value = "审批状态（Y通过，N拒绝，null未审批）")
    private String status;
}
