package com.jinkosolar.scp.mps.domain.dto.dp;

import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "AttrTypeLineQuery", description = "查询条件")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class AttrTypeLineQuery extends PageDTO {
    /**
     * 属性类型编码
     */
    @ApiModelProperty(value = "Header Code,头的code")
    private String code;

    @ApiModelProperty(value = "AttrTypeHeaderId")
    private Long attrTypeHeaderId;

    @ApiModelProperty(value = "属性行id")
    private Long attrLineId;

    @ApiModelProperty(value = "属性中文名")
    private String attrCnName;

}
