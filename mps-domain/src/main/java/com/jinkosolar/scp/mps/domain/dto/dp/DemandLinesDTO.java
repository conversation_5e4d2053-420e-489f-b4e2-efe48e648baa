package com.jinkosolar.scp.mps.domain.dto.dp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Joiner;
import com.ibm.scp.common.api.annotation.Dict;
import com.jinkosolar.scp.mps.domain.constant.SplitConstant;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * DP需求行
 *
 * <AUTHOR> chenc
 * @date : 2024-5-14
 */
@ApiModel(value = "DP需求行")
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DemandLinesDTO implements Serializable {
    /**
     * 主键
     */
    @NotNull(message = "请选择拆分的行", groups = Split.class)
    @ApiModelProperty("主键")
    private Long dpLinesId;
    /**
     * SAP订单号-CRM接口数据获取
     */
    @ApiModelProperty("SAP订单号-CRM接口数据获取")
    private String sapOrderNo;
    /**
     * 订单类型-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_ORDER_TYPE)
    @ApiModelProperty("订单类型-CRM接口数据获取")
    private String orderTypeId;

    @ApiModelProperty("订单类型名称")
    private String orderTypeIdName;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private String versionNum;
    /**
     * 合同号（PI）-CRM接口数据获取
     */
    @NotBlank(message = "合同号不能为空", groups = W5Group.class)
    @ApiModelProperty("合同号（PI）-CRM接口数据获取")
    private String contractPo;
    /**
     * 项目分类-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_Project_Type)
    @ApiModelProperty("项目分类-CRM接口数据获取")
    private String projectClassification;

    @ApiModelProperty("项目分类-CRM接口数据获取")
    private String projectClassificationName;

    /**
     * 销售员-CRM接口数据获取
     */
    @ApiModelProperty("销售员-CRM接口数据获取")
    private String sellerName;
    /**
     * 销售主体-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_SELLER)
    @ApiModelProperty("销售主体-CRM接口数据获取")
    private String sellingEntities;

    @ApiModelProperty("销售主体-CRM接口数据获取")
    private String sellingEntitiesName;

    /**
     * 目的地区域-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_Destination_Region)
    @ApiModelProperty("目的地区域-CRM接口数据获取")
    private String destAreaNo;

    @ApiModelProperty("目的地区域-CRM接口数据获取")
    private String destAreaNoName;

    /**
     * 目的地国家-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_Destination_Country)
    @ApiModelProperty("目的地国家-CRM接口数据获取")
    private String destCountry;

    @ApiModelProperty("目的地国家-CRM接口数据获取")
    private String destCountryName;

    /**
     * 港口/目的地-CRM接口数据获取
     */
    @ApiModelProperty("港口/目的地-CRM接口数据获取")
    private String destPort;
    /**
     * 客户名称-CRM接口数据获取
     */
    @ApiModelProperty("客户名称-CRM接口数据获取")
    private String customer;
    /**
     * 客户编码(ecc)-CRM接口数据获取
     */
    @ApiModelProperty("客户编码(ecc)-CRM接口数据获取")
    private String customerCodeEcc;
    /**
     * 客户编码(s4)-CRM接口数据获取
     */
    @ApiModelProperty("客户编码(s4)-CRM接口数据获取")
    private String customerCodeS4;
    /**
     * 是否定向-CRM接口数据获取
     */
    @ApiModelProperty("是否定向-CRM接口数据获取")
    private String directional;
    /**
     * 是否追溯-CRM接口数据获取
     */
    @ApiModelProperty("是否追溯-CRM接口数据获取")
    private String tracedBackFlag;

    /**
     * 是否定向-行
     */
    @ApiModelProperty(name = "是否定向-行", notes = "")
    private String lineDirectional;
    /**
     * 是否追溯-行
     */
    @ApiModelProperty(name = "是否追溯-行", notes = "")
    private String lineTracedBackFlag;

    /**
     * 是否监造-行
     */
    @ApiModelProperty("是否监造-行")
    private String supervisionFlag;
    /**
     * 是否验货-行
     */
    @ApiModelProperty("是否验货-行")
    private String inspectionFlag;
    /**
     * 是否特批订单-CRM接口数据获取
     */
    @ApiModelProperty("是否特批订单-CRM接口数据获取")
    private String specialOrderFlag;
    /**
     * 是否变更-CRM接口数据获取
     */
    @ApiModelProperty("是否变更-CRM接口数据获取")
    private String reqChangeFlag;
    /**
     * 追溯条款-CRM接口数据获取
     */
    @ApiModelProperty("追溯条款-CRM接口数据获取")
    private String tracedBackReason;
    /**
     * 验货描述-CRM接口数据获取
     */
    @ApiModelProperty("验货描述-CRM接口数据获取")
    private String inspectionDesc;
    /**
     * 特批原因-CRM接口数据获取
     */
    @ApiModelProperty("特批原因-CRM接口数据获取")
    private String specialOrderDesc;
    /**
     * 贸易方式-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_Trade_Term)
    @ApiModelProperty("贸易方式-CRM接口数据获取")
    private String tradeMode;

    @ApiModelProperty("贸易方式-CRM接口数据获取")
    private String tradeModeName;

    /**
     * 财务评级-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_Gross_Margin_Rating)
    @ApiModelProperty("财务评级-CRM接口数据获取")
    private String financialRatings;

    @ApiModelProperty("财务评级-CRM接口数据获取")
    private String financialRatingsName;

    /**
     * 客户评级-CRM接口数据获取
     */
    @ApiModelProperty("客户评级-CRM接口数据获取")
    private String customerRatings;
    /**
     * 有限保修-CRM接口数据获取
     */
    @ApiModelProperty("有限保修-CRM接口数据获取")
    private String limitedWarranty;
    /**
     * 保险-CRM接口数据获取
     */
    @ApiModelProperty("保险-CRM接口数据获取")
    private String insurance;
    /**
     * 报关主体-CRM接口数据获取
     */
    @ApiModelProperty("报关主体-CRM接口数据获取")
    private String customsMainSubject;
    /**
     * 运输方式-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_Shipping_type)
    @ApiModelProperty("运输方式-CRM接口数据获取")
    private String transports;
    @ApiModelProperty("运输方式-CRM接口数据获取")
    private String transportsName;
    /**
     * 总块数-CRM接口数据获取
     */
    @ApiModelProperty("总块数-CRM接口数据获取")
    private BigDecimal totalBlockQuantity;
    /**
     * 总兆瓦数-CRM接口数据获取
     */
    @ApiModelProperty("总兆瓦数-CRM接口数据获取")
    private BigDecimal mwQuantityTotal;
    /**
     * 千瓦时-CRM接口数据获取
     */
    @ApiModelProperty("千瓦时-CRM接口数据获取")
    private BigDecimal khwQuantity;
    /**
     * 要求供货工厂-CRM接口数据获取
     */
    @ApiModelProperty("要求供货工厂-CRM接口数据获取")
    private String productFactory;
    /**
     * 指定硅料厂家-CRM接口数据获取
     */
    @ApiModelProperty("指定硅料厂家-CRM接口数据获取")
    private String specifyFactory;
    /**
     * 是否派遣-CRM接口数据获取
     */
    @ApiModelProperty("是否派遣-CRM接口数据获取")
    private String dispatchFlag;
    /**
     * 有生产额外成本
     */
    @ApiModelProperty("有生产额外成本")
    private String productionCost;
    /**
     * 需求状态
     */
    @ApiModelProperty("需求状态")
    private String scheduleStatus;

    @ApiModelProperty("需求状态编码")
    @Dict(headerCode = LovHeaderCodeConstant.DP_DEMAND_STATUS, fieldKey = "scheduleStatus", fieldName = "scheduleStatusCode", returnCode = true)
    private String scheduleStatusCode;
    @ApiModelProperty("需求状态")
    private String scheduleStatusName;
    /**
     * 变更说明
     */
    @ApiModelProperty("变更说明")
    private String changeReason;

    @ApiModelProperty(name = "需求变更说明", notes = "")
    private String demandChangeReason;

    /**
     * 生产确认
     */
    @ApiModelProperty("生产确认")
    private String productionReason;
    /**
     * 订单同步日期/签发日期-CRM接口数据获取
     */
    @ApiModelProperty("订单同步日期/签发日期-CRM接口数据获取")
    private LocalDateTime confirmDatetime;
    /**
     * DP_ID
     */
    @ApiModelProperty("DP_ID")
    private String dpId;
    /**
     * 拆分行-主行ID
     */
    @ApiModelProperty("拆分行-主行DPID")
    private String parentId;
    /**
     * CRM头ID
     */
    @ApiModelProperty("CRM头ID")
    private String crmHeadId;
    /**
     * CRM行ID-CRM接口数据获取
     */
    @ApiModelProperty("CRM行ID-CRM接口数据获取")
    private String crmLinesId;

    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料描述", notes = "")
    private String itemDesc;

    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称", notes = "")
    private String productFactoryName;

    /**
     * 主副产品-CRM接口数据获取
     */
    @ApiModelProperty("主副产品-CRM接口数据获取")
    private String primaryProduct;
    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String productCode;

    @NotBlank(message = "产品型号不能为空", groups = W5Group.class)
    @ApiModelProperty(name = "产品型号", notes = "")
    private String productModel;

    /**
     * 功率
     */
    @ApiModelProperty("功率")
    private String power;
    /**
     * 需求数量-CRM接口数据获取
     */

    @NotNull(message = "需求数量不能为空", groups = Split.class)
    @ApiModelProperty("行需求数量-CRM接口数据获取")
    private BigDecimal demandQty;
    /**
     * 订单原数量-CRM接口数据获取
     */
    @ApiModelProperty("订单原数量-CRM接口数据获取")
    private BigDecimal orderQty;
    /**
     * 兆瓦数-CRM接口数据获取
     */
    @ApiModelProperty("兆瓦数-CRM接口数据获取")
    private BigDecimal mwQuantity;
    /**
     * 合同要求货好日期/GRD-CRM接口数据获取
     */
    @NotNull(message = "合同要求货好日期不能为空", groups = W5Group.class)
    @ApiModelProperty("合同要求货好日期/GRD-CRM接口数据获取")
    private LocalDate plannedCompleteDate;
    /**
     * 工厂更新货好时间
     */
    @ApiModelProperty("工厂更新货好时间")
    private LocalDate factoryCompleteDate;
    /**
     * 要求离厂日期/Ex-Work Date-CRM接口数据获取
     */
    @ApiModelProperty("要求离厂日期/Ex-Work Date-CRM接口数据获取")
    private LocalDate scheduleShipDate;
    /**
     * 要求离港日期/ETD-CRM接口数据获取
     */
    @ApiModelProperty("要求离港日期/ETD-CRM接口数据获取")
    private LocalDate etd;
    /**
     * 生产类型-CRM接口数据获取
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_PRD_TYPE)
    @ApiModelProperty("生产类型-CRM接口数据获取")
    private String productionType;
    @ApiModelProperty("生产类型-CRM接口数据获取")
    private String productionTypeName;

    /**
     * 生产基地
     */
    @NotBlank(message = "生产基地不能为空", groups = W5Group.class)
    @Dict(methodName = "getFactoryDesc", fieldName = "specifyBaseDesc")
    @ApiModelProperty("生产基地")
    private String specifyBase;

    @ApiModelProperty("生产基地描述")
    private String specifyBaseDesc;

    /**
     * 投产方案
     */
    @NotBlank(message = "投产方案不能为空", groups = W5Group.class)
    @ApiModelProperty("投产方案")
    private String startUpPlan;

    @ApiModelProperty("投产方案编码")
    private String startUpPlanCode;

    @ApiModelProperty("投产方案描述")
    private String startUpPlanName;

    @Dict(methodName = "getPowerMaterialCombinationDetailDesc")
    @ApiModelProperty("材料搭配")
    private Long materialMatching;

    @ApiModelProperty("材料搭配描述")
    private String materialMatchingName;

    @ApiModelProperty("材料搭配(计划)")
    private String materialMatchingPlan;

    /**
     * 功率预测版本
     */
    @NotBlank(message = "功率预测版本不能为空", groups = W5Group.class)
    @ApiModelProperty("功率预测版本")
    private String powerForecastVersion;

    /**
     * 子母物料
     */
    @ApiModelProperty(value = "子母物料", notes = "")
    private String masterBatch;

    /**
     * 是否定向，批次定向 N：否 Y：是
     */
    @ApiModelProperty("是否定向，批次定向 N：否 Y：是")
    private String batchDirectional;
    /**
     * 是否5W分档
     */
    @ApiModelProperty("是否5W分档")
    private String fiveWBinnedFlag;
    /**
     * 5W分档组
     */
    @ApiModelProperty("5W分档组")
    private String fiveWBinned;
    /**
     * 是否指定实验线
     */
    @ApiModelProperty("是否指定实验线")
    private String experimentalTesting;
    /**
     * 是否标准评审
     */
    @ApiModelProperty("是否标准评审")
    private String reviewFlag;
    /**
     * 建议排产开始日期
     */
    @ApiModelProperty("建议排产开始日期")
    private LocalDate plannedWipDate;
    /**
     * 是否贴牌订单
     */
    @ApiModelProperty("是否贴牌订单")
    private String placeOrderFlag;
    /**
     * 生产功率
     */
    @ApiModelProperty("生产功率")
    private String productionPower;
    /**
     * 高档比例
     */
    @ApiModelProperty("高档比例")
    private BigDecimal highGrade;
    /**
     * 低档比例
     */
    @ApiModelProperty("低档比例")
    private BigDecimal lowGrade;

    @ApiModelProperty("低档比例")
    private BigDecimal lowGradePower;
    /**
     * 合同交期
     */
    @ApiModelProperty("合同交期")
    private LocalDate orderDate;
    /**
     * 产能替换(行号id)-CRM接口数据获取
     */
    @ApiModelProperty("产能替换(行号id)-CRM接口数据获取")
    private String capacityReplacementLineId;
    /**
     * 产能替换DP-id
     */
    @ApiModelProperty("产能替换DP-id")
    private String capacityReplacementDpId;
    /**
     * 生产通知书更新时间
     */
    @ApiModelProperty("生产通知书更新时间")
    private LocalDateTime notificationTime;

    @ApiModelProperty(name = "CSR-头", notes = "")
    private String orderHeadCsr;

    @ApiModelProperty(name = "CSR-行", notes = "")
    private String orderLineCsr;

    @ApiModelProperty(name = "出库存单号", notes = "")
    private String inventoryOrderNo;

    /**
     * SAP行-id
     */
    @ApiModelProperty("SAP行-id")
    private String sapLineId;
    /**
     * MDA ID
     */
    @NotBlank(message = "MDA ID不能为空", groups = W5Group.class)
    @ApiModelProperty("MDA ID")
    private String mdaId;
    /**
     * 是否存在差异表
     */
    @ApiModelProperty("是否存在差异表")
    private String differencesTableFlag;
    /**
     * 是否赠送功率-MDA表获取
     */
    @Dict
    @ApiModelProperty("是否赠送功率-MDA表获取")
    private String itemAttribute1;
    /**
     * 赠送功率（W/块）-MDA表获取
     */
    @ApiModelProperty("赠送功率（W/块）-MDA表获取")
    private String itemAttribute2;
    /**
     * 接线盒型号-MDA表获取
     */
    @Dict
    @ApiModelProperty("接线盒型号-MDA表获取")
    private String itemAttribute3;
    /**
     * 连接头型号-MDA表获取
     */
    @Dict
    @ApiModelProperty("连接头型号-MDA表获取")
    private String itemAttribute4;
    /**
     * 导线长度-MDA表获取
     */
    @Dict
    @ApiModelProperty("导线长度-MDA表获取")
    private String itemAttribute5;
    /**
     * 边框颜色-MDA表获取
     */
    @Dict
    @ApiModelProperty("边框颜色-MDA表获取")
    private String itemAttribute6;
    /**
     * 边框氧化膜厚度-MDA表获取
     */
    @Dict
    @ApiModelProperty("边框氧化膜厚度-MDA表获取")
    private String itemAttribute7;
    /**
     * 背板类型-MDA表获取
     */
    @Dict
    @ApiModelProperty("背板类型-MDA表获取")
    private String itemAttribute8;
    /**
     * 胶膜要求-MDA表获取
     */
    @Dict
    @ApiModelProperty("胶膜要求-MDA表获取")
    private String itemAttribute9;
    /**
     * 生产前验厂-MDA表获取
     */
    @Dict
    @ApiModelProperty("生产前验厂-MDA表获取")
    private String itemAttribute10;
    /**
     * 生产前验厂（备注）-MDA表获取
     */
    @ApiModelProperty("生产前验厂（备注）-MDA表获取")
    private String itemAttribute11;
    /**
     * 客户是否验货或驻厂监造-MDA表获取
     */
    @Dict
    @ApiModelProperty("客户是否验货或驻厂监造-MDA表获取")
    private String itemAttribute12;
    /**
     * 客户是否验货或驻厂监造（备注）-MDA表获取
     */
    @ApiModelProperty("客户是否验货或驻厂监造（备注）-MDA表获取")
    private String itemAttribute13;
    /**
     * 工厂验货-MDA表获取
     */
    @Dict
    @ApiModelProperty("工厂验货-MDA表获取")
    private String itemAttribute14;
    /**
     * 工厂验货（备注）-MDA表获取
     */
    @ApiModelProperty("工厂验货（备注）-MDA表获取")
    private String itemAttribute15;
    /**
     * 实验测试-MDA表获取
     */
    @Dict
    @ApiModelProperty("实验测试-MDA表获取")
    private String itemAttribute16;
    /**
     * 颜色标签-MDA表获取
     */
    @Dict
    @ApiModelProperty("颜色标签-MDA表获取")
    private String itemAttribute17;
    /**
     * 包装-MDA表获取
     */
    @Dict
    @ApiModelProperty("包装-MDA表获取")
    private String itemAttribute18;
    /**
     * 包装（备注）-MDA表获取
     */
    @ApiModelProperty("包装（备注）-MDA表获取")
    private String itemAttribute19;
    /**
     * 铭牌样式-MDA表获取
     */
    @Dict
    @ApiModelProperty("铭牌样式-MDA表获取")
    private String itemAttribute20;
    /**
     * 产品认证-MDA表获取
     */
    @Dict
    @ApiModelProperty("产品认证-MDA表获取")
    private String itemAttribute21;
    /**
     * 区域强制认证-MDA表获取
     */
    @Dict(splitChar = ",")
    @ApiModelProperty("区域强制认证-MDA表获取")
    private String itemAttribute22;
    /**
     * 区域认证：CFP-MDA表获取
     */
    @ApiModelProperty("区域认证：CFP-MDA表获取")
    private String itemAttribute23;
    /**
     * 区域认证：NPB-MDA表获取
     */
    @ApiModelProperty("区域认证：NPB-MDA表获取")
    private String itemAttribute24;
    /**
     * 功率公差从-MDA表获取
     */
    @ApiModelProperty("功率公差从-MDA表获取")
    private String itemAttribute25;
    /**
     * 功率公差至-MDA表获取
     */
    @ApiModelProperty("功率公差至-MDA表获取")
    private String itemAttribute26;
    /**
     * 是否需使用缠绕膜-MDA表获取
     */
    @Dict
    @ApiModelProperty("是否需使用缠绕膜-MDA表获取")
    private String itemAttribute27;
    /**
     * 是否需使用缠绕膜（备注）-MDA表获取
     */
    @ApiModelProperty("是否需使用缠绕膜（备注）-MDA表获取")
    private String itemAttribute28;
    /**
     * 是否需要防尘塞-MDA表获取
     */
    @Dict
    @ApiModelProperty("是否需要防尘塞-MDA表获取")
    private String itemAttribute29;
    /**
     * 是否需要防震标签-MDA表获取
     */
    @Dict
    @ApiModelProperty("是否需要防震标签-MDA表获取")
    private String itemAttribute30;
    /**
     * ISO Achieved Later-MDA表获取
     */
    @Dict
    @ApiModelProperty("ISO Achieved Later-MDA表获取")
    private String itemAttribute31;
    /**
     * ISO Certificate Type-MDA表获取
     */
    @Dict(splitChar = ",")
    @ApiModelProperty("ISO Certificate Type-MDA表获取")
    private String itemAttribute32;
    /**
     * ISO Certificate-MDA表获取
     */
    @Dict
    @ApiModelProperty("ISO Certificate-MDA表获取")
    private String itemAttribute33;
    /**
     * DS版本-MDA表获取
     */
    @ApiModelProperty("DS版本-MDA表获取")
    private String itemAttribute34;
    /**
     * 边框高度(mm)-MDA表获取
     */
    @Dict
    @ApiModelProperty("边框高度(mm)-MDA表获取")
    private String itemAttribute35;
    /**
     * 短边C面宽度(mm)-MDA表获取
     */
    @Dict
    @ApiModelProperty("短边C面宽度(mm)-MDA表获取")
    private String itemAttribute36;
    /**
     * 长边C面宽度(mm)-MDA表获取
     */
    @Dict
    @ApiModelProperty("长边C面宽度(mm)-MDA表获取")
    private String itemAttribute37;
    /**
     * 边框安装孔距-MDA表获取
     */
    @Dict
    @ApiModelProperty("边框安装孔距-MDA表获取")
    private String itemAttribute38;
    /**
     * 组件重量(KG)-MDA表获取
     */
    @ApiModelProperty("组件重量(KG)-MDA表获取")
    private String itemAttribute39;
    /**
     * 主栅-MDA表获取
     */
    @Dict
    @ApiModelProperty("主栅-MDA表获取")
    private String itemAttribute40;
    /**
     * 电池片长度(mm)-MDA表获取
     */
    @ApiModelProperty("电池片长度(mm)-MDA表获取")
    private String itemAttribute41;
    /**
     * 电池片宽度(mm)-MDA表获取
     */
    @ApiModelProperty("电池片宽度(mm)-MDA表获取")
    private String itemAttribute42;
    /**
     * 包装数据(片）-MDA表获取
     */
    @ApiModelProperty("包装数据(片）-MDA表获取")
    private String itemAttribute43;
    /**
     * 转库存率-MDA表获取
     */
    @ApiModelProperty("转库存率-MDA表获取")
    private String itemAttribute44;
    /**
     * 正面胶膜克重-MDA表获取
     */
    @Dict
    @ApiModelProperty("正面胶膜克重-MDA表获取")
    private String itemAttribute45;
    /**
     * 背面胶膜克重-MDA表获取
     */
    @Dict
    @ApiModelProperty("背面胶膜克重-MDA表获取")
    private String itemAttribute46;
    /**
     * 背玻镀膜-MDA表获取
     */
    @Dict
    @ApiModelProperty("背玻镀膜-MDA表获取")
    private String itemAttribute47;
    /**
     * 指定基地-MDA表获取
     */
    @ApiModelProperty("指定基地-MDA表获取")
    private String itemAttribute48;
    /**
     * 指定车间-MDA表获取
     */
    @ApiModelProperty("指定车间-MDA表获取")
    private String itemAttribute49;
    /**
     * 是否接受认证候补-MDA表获取
     */
    @Dict
    @ApiModelProperty("是否接受认证候补-MDA表获取")
    private String itemAttribute50;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute51;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute52;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute53;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute54;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute55;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute56;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute57;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute58;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute59;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute60;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute61;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute62;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute63;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute64;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute65;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    @Dict
    private String itemAttribute66;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute67;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute68;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute69;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute70;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute71;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute72;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute73;
    /**
     * 扩展属性
     */
    @Dict
    @ApiModelProperty("扩展属性")
    private String itemAttribute74;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute75;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute76;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute77;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute78;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute79;
    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String itemAttribute80;
    /**
     * 产品类型
     */
    @Dict(headerCode = LovHeaderCodeConstant.DP_ORDER_LINE_PRODUCTTYPE)
    @ApiModelProperty(value = "主/副产品/补货（0：主产品，1：副产品，2：补投）")
    private String productType;

    @ApiModelProperty(value = "主/副产品/补货（0：主产品，1：副产品，2：补投）")
    private String productTypeName;

    /**
     * 组合号ID-bom
     */
    @ApiModelProperty("组合号ID-bom")
    private Long bomGroupId;
    /**
     * 组合号-bom
     */
    @ApiModelProperty("组合号-bom")
    private String bomGroupNum;
    /**
     * DP更新材料要求标识-bom
     */
    @ApiModelProperty("DP更新材料要求标识-bom")
    private String bomMUpdateFlag;
    /**
     * DP更新消息-bom
     */
    @ApiModelProperty("DP更新消息-bom")
    private String bomMUpdateMessage;
    /**
     * 组合号分配来源-bom
     */
    @ApiModelProperty("组合号分配来源-bom")
    private String bomGroupSource;
    /**
     * 明细行数量-old
     */
    @ApiModelProperty("明细行数量-old")
    private Integer subCount;
    /**
     * dp行类型：1汇总2明细3、独立-old
     */
    @ApiModelProperty("dp行类型：1汇总2明细3、独立-old")
    private Integer dpLinesType;
    /**
     * DP分组ID-old
     */
    @ApiModelProperty("DP分组ID-old")
    private Long dpGroupId;
    /**
     * 来源单号版本
     */
    @ApiModelProperty("来源单号版本")
    private String orderNumber;
    /**
     * 来源类型-old
     */
    @ApiModelProperty("来源类型-old")
    private String dataType;
    /**
     * 材料切换-old
     */
    @ApiModelProperty("材料切换-old")
    private String materialSwitch;
    /**
     * 业务员-old
     */
    @ApiModelProperty("业务员-old")
    private String salesrepId;
    /**
     * 客户id-old
     */
    @ApiModelProperty("客户id-old")
    private Long customerId;
    /**
     * 分组后明细的排序序号-old
     */
    @ApiModelProperty("分组后明细的排序序号-old")
    private Integer groupDetailNumber;
    /**
     * 销售区域-old
     */
    @ApiModelProperty("销售区域-old")
    private String areaNo;
    /**
     * 区域细分-old
     */
    @ApiModelProperty("区域细分-old")
    private String areaSubNo;
    /**
     * 项目地国家-old
     */
    @ApiModelProperty("项目地国家-old")
    private String country;
    /**
     * 项目地省份-old
     */
    @ApiModelProperty("项目地省份-old")
    private String province;
    /**
     * 国内/海外-old
     */
    @ApiModelProperty("国内/海外-old")
    private String isOversea;
    /**
     * 商机编号-old
     */
    @ApiModelProperty("商机编号-old")
    private String chanceNo;
    /**
     * 付款条件-old
     */
    @ApiModelProperty("付款条件-old")
    private String paymentTermsId;
    /**
     * 贸易条款-old
     */
    @ApiModelProperty("贸易条款-old")
    private String tradeTerm;
    /**
     * 库存组织-old
     */
    @ApiModelProperty("库存组织-old")
    private String organizationId;
    /**
     * 来源行号/订单版本号
     */
    @ApiModelProperty("来源行号/订单版本号")
    private String sourceLineNum;
    /**
     * 来源行id
     */
    @ApiModelProperty("来源行id")
    private Long sourceLineId;
    /**
     * 销售料号
     */
    @ApiModelProperty("销售料号")
    private String productItemNo;

    @ApiModelProperty(name = "新料号", notes = "")
    private String productItemNew;

    /**
     * 需求功率
     */
    @ApiModelProperty("需求功率")
    private String productPower;
    /**
     * 产品族
     */
    @ApiModelProperty("产品族")
    private String productFamily;
    /**
     * 产品分类编码
     */
    @ApiModelProperty("产品分类编码")
    private String productCategory;
    /**
     * 片数量
     */
    @ApiModelProperty("片数量")
    private BigDecimal quantity2;
    /**
     * 片数量占总数量的比例
     */
    @ApiModelProperty("片数量占总数量的比例")
    private String quantity2Ratio;
    /**
     * W数量
     */
    @ApiModelProperty("行订单原数量")
    private BigDecimal quantity;
    /**
     * 已计划排产数量
     */
    @ApiModelProperty("已计划排产数量")
    private BigDecimal planQty;
    /**
     * 待排产数量
     */
    @ApiModelProperty("待排产数量")
    private BigDecimal leftPlanQty;
    /**
     * 已实际排产数量
     */
    @ApiModelProperty("已实际排产数量")
    private BigDecimal scheduleQty;
    /**
     * 需交付日期
     */
    @ApiModelProperty("需交付日期")
    private LocalDate requiredDate;
    /**
     * 排产日期
     */
    @ApiModelProperty("排产日期")
    private LocalDate scheduleDate;
    /**
     * 是否特殊单
     */
    @ApiModelProperty("是否特殊单")
    private String specialFlag;
    /**
     * 特殊单号
     */
    @ApiModelProperty("特殊单号")
    private String specialSn;
    /**
     * 降档要求
     */
    @ApiModelProperty("降档要求")
    private String powerChangeRemark;
    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String uom;
    /**
     * 补充要求
     */
    @ApiModelProperty("补充要求")
    private String appendRemark;
    /**
     * APS排产状态
     */
    @ApiModelProperty("APS排产状态")
    private String planedStatus;
    /**
     * 需求总功率
     */
    @ApiModelProperty("需求总功率")
    private BigDecimal collectProductPower;
    /**
     * 需排产瓦数
     */
    @ApiModelProperty("需排产瓦数")
    private BigDecimal needScheduleWattage;
    /**
     * APS实际排产基地
     */
    @ApiModelProperty("APS实际排产基地")
    private String productPlace;
    /**
     * 需排产基地
     */
    @ApiModelProperty("需排产基地")
    private String scheduleProductPlace;
    /**
     * OEM
     */
    @ApiModelProperty("OEM")
    private String oem;
    /**
     * 订单行状态
     */
    @ApiModelProperty("订单行状态")
    private String orderLineStatus;
    /**
     * 产品族动态属性更新时间
     */
    @ApiModelProperty("产品族动态属性更新时间")
    private LocalDateTime attrUpdatedTime;
    /**
     * 特殊评审单号
     */
    @ApiModelProperty("特殊评审单号")
    private String reviewOrderNo;
    /**
     * 产品族组件分类
     */
    @ApiModelProperty("产品族组件分类")
    @Dict(headerCode = LovHeaderCodeConstant.SYS_REAR_COVER_TYPE)
    private Long moduleStackId;

    @ApiModelProperty("产品族组件分类")
    private String moduleStackIdName;
    /**
     * 预估总良损(%)
     */
    @ApiModelProperty("预估总良损(%)")
    private String exceptGoodLoss;
    /**
     * 排产车间
     */
    @ApiModelProperty("排产车间")
    private String workshops;
    /**
     * 数据来源，手工、导入、CRM等
     */
    @ApiModelProperty("数据来源，手工、导入、CRM等")
    private String dataSource;
    /**
     * 备货预测/预订单创建人
     */
    @ApiModelProperty("备货预测/预订单创建人")
    private String orderCreatedBy;
    /**
     * 排产开始日期
     */
    @ApiModelProperty("排产开始日期")
    private LocalDate scheduleStartDate;
    /**
     * 合同要求交货地点
     */
    @ApiModelProperty("合同要求交货地点")
    private String contractRequireDeliveryPlace;
    /**
     * 安装地
     */
    @ApiModelProperty("安装地")
    private String installationPlace;
    /**
     * 电池工厂
     */
    @ApiModelProperty("电池工厂")
    private String cellFactory;

    @ApiModelProperty(value = "电池类型", notes = "")
    private String cellModel;

    @ApiModelProperty(value = "需求类型", notes = "DP/拉晶/切片/电池")
    private String demandType;

    @ApiModelProperty(value = "订单创建时间")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty(value = "订单更新时间")
    private LocalDateTime orderUpdateTime;

    @ApiModelProperty(name = "计划版型", notes = "")
    private String planType;

    @ApiModelProperty(name = "是否主版本", notes = "")
    private String mainFlag;

    private BigDecimal avePower;

    private String customerName;

    private String productItemNoName;
    private BigDecimal scheduleWattage;

    @ApiModelProperty(name = "创建人名称", notes = "")
    private String createdName;

    @ApiModelProperty(name = "修改人名称", notes = "")
    private String updatedName;

    /**
     * 订单行状态
     */
    private String lineStatus;

    /**
     * 合并来源id
     */
    @ApiModelProperty(value = "合并来源id")
    private List<Long> mergeIds;

    /**
     * 总功率默认转MW
     *
     * @return
     */
    public BigDecimal getCollectProductPower() {
        if (collectProductPower == null) {
            return null;
        }
        return collectProductPower.divide(BigDecimal.valueOf(10L).pow(6)).setScale(6, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 5W分档组分组
     *
     * @return
     */
    public String get5wGroup() {
        String group = this.getContractPo() +
                Objects.toString(this.getPlannedCompleteDate(), "") +
                this.getProductModel() +
                this.getSpecifyBase() +
                this.getMaterialMatching() +
                this.getStartUpPlan() +
                this.getItemAttribute1() +
                this.getItemAttribute11() +
                this.getItemAttribute12() +
                this.getItemAttribute13() +
                this.getItemAttribute14() +
                this.getItemAttribute15() +
                this.getItemAttribute16() +
                this.getItemAttribute17() +
                this.getItemAttribute18() +
                this.getItemAttribute19() +
                this.getItemAttribute10() +
                this.getItemAttribute2() +
                this.getItemAttribute21() +
                this.getItemAttribute22() +
                this.getItemAttribute23() +
                this.getItemAttribute24() +
                this.getItemAttribute25() +
                this.getItemAttribute26() +
                this.getItemAttribute27() +
                this.getItemAttribute28() +
                this.getItemAttribute29() +
                this.getItemAttribute20() +
                this.getItemAttribute3() +
                this.getItemAttribute31() +
                this.getItemAttribute32() +
                this.getItemAttribute33() +
                this.getItemAttribute34() +
                this.getItemAttribute35() +
                this.getItemAttribute36() +
                this.getItemAttribute37() +
                this.getItemAttribute38() +
                this.getItemAttribute39() +
                this.getItemAttribute30() +
                this.getItemAttribute4() +
                this.getItemAttribute41() +
                this.getItemAttribute42() +
                this.getItemAttribute43() +
                this.getItemAttribute44() +
                this.getItemAttribute45() +
                this.getItemAttribute46() +
                this.getItemAttribute47() +
                this.getItemAttribute48() +
                this.getItemAttribute49() +
                this.getItemAttribute40() +
                this.getItemAttribute5() +
                this.getItemAttribute50() +
                this.getItemAttribute6() +
                this.getItemAttribute7() +
                this.getItemAttribute8() +
                this.getItemAttribute9();
        return group;
    }

    /**
     * 5W分组
     */
    public interface W5Group {
    }

    /**
     * 拆分
     */
    public interface Split {
    }

    public String groupByOrderAndBaseMps() {
        return Joiner.on(SplitConstant.SPLIT_SEPARATOR).useForNull("null").join(
                this.getSapOrderNo(), this.getSapLineId(), this.getSpecifyBase()
        );

    }

}