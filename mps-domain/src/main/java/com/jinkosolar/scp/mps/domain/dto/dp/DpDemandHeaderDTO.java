package com.jinkosolar.scp.mps.domain.dto.dp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 需求计划头
 *
 * <AUTHOR> chenc
 * @date : 2024-5-16
 */
@ApiModel(value = "需求计划头")
@Getter
@Setter

@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DpDemandHeaderDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 状态
     */
    private Long status;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("状态编码")
    private String statusCode;

    /**
     * 编号
     */
    @ApiModelProperty("编号")
    private String no;

    /**
     * 基地分类
     */
    private Long domesticOversea;

    @ApiModelProperty("基地分类名称")
    private String domesticOverseaName;

    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型", notes = "")
    private String demandType;

    /**
     * 来源
     */
    @ApiModelProperty("来源")
    private Long source;

    /**
     * 来源
     */
    @ApiModelProperty("来源code")
    private String sourceCode;

    @ApiModelProperty("来源名称")
    private String sourceName;

    /**
     * 来源数据编号
     */
    @ApiModelProperty("来源数据编号")
    private String sourceNo;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private LocalDate startDate;

    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private LocalDate endDate;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


    /**
     * 区分电池需求中的组件特殊需求数据
     */
    @ApiModelProperty("区分电池需求中的组件特殊需求数据")
    private String itemAttribute53;

    /**
     * 同步状态Y/N
     */
    @ApiModelProperty(name = "同步状态Y/N", notes = "")
    private String syncStatus;

    /**
     * 确认生成切片独立需求Y/N
     */
    @ApiModelProperty(name = "确认生成切片独立需求Y/N", notes = "")
    private String confirmGenerateStatus;

    /**
     * 切片对切片需求、拉晶所有需求计算标识
     */
    @ApiModelProperty(name = "切片对切片需求、拉晶所有需求计算标识", notes = "")
    private String summaryCalculate;

    /**
     * 切片对拉晶需求计算标识
     */
    @ApiModelProperty(name = "切片对拉晶需求计算标识", notes = "")
    private String slicedPushPcSummaryCalculate;

    /**
     * 切片对切片计算 sliced_push_aps
     * 拉晶对拉晶计算 sliced_push_pc
     */
    @ApiModelProperty(name = "按钮计算标识", notes = "")
    private String calculateFlag;

    /**
     * 切片对切片计算 sliced_push_aps
     * 拉晶对拉晶计算 sliced_push_pc
     */
    @ApiModelProperty(name = "切片需求导入标识", notes = "")
    private String orderLineStatus;
}