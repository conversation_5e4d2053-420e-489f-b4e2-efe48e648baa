package com.jinkosolar.scp.mps.domain.dto.dp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@ApiModel("询单请求信息表查询条件对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderQueryInfoQuery implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;
    /**
     * 询单单号
     */
    @ApiModelProperty("询单单号")
    private String iqHeaderNum;
    /**
     * 询单阶段DP.IQ_Stage
     */
    @ApiModelProperty("询单阶段DP.IQ_Stage")
    private String iqStage;

    @ApiModelProperty("询单阶段号")
    private String iqStageNum;
    /**
     * 询单阶段原始单号
     */
    @ApiModelProperty("询单阶段原始单号")
    private String iqStageNumId;

    @ApiModelProperty("询单阶段原始单号")
    private String inQueryId;
    /**
     * 询单版本号
     */
    @ApiModelProperty("询单版本号")
    private String iqVersion;
    /**
     * 关联合同号
     */
    @ApiModelProperty("关联合同号")
    private String contractNo;
    /**
     * 关联补充协议号
     */
    @ApiModelProperty("关联补充协议号")
    private String amendmentNo;
    /**
     * 关联crmorder
     */
    @ApiModelProperty("关联crmorder")
    private String crmOrder;
    /**
     * 关联crmorder
     */
    @ApiModelProperty("关联SAP订单号")
    private String sapNo;
    /**
     * 客户
     */
    @ApiModelProperty("客户")
    private String customer;
    /**
     * 贸易方式
     */
    @ApiModelProperty("贸易方式")
    private String tradeTerm;
    /**
     * 目的地国家
     */
    @ApiModelProperty("目的地国家")
    private String destCountry;
    /**
     * 目的地区域
     */
    @ApiModelProperty("目的地区域")
    private String destRegion;
    /**
     * 区域认证
     */
    @ApiModelProperty("区域认证")
    private String regionalCertifi;
    /**
     * 指定基地
     */
    @ApiModelProperty("指定基地")
    private String designatedBase;
    /**
     * 监造
     */
    @ApiModelProperty("监造")
    private String isMonitor;
    /**
     * 验货
     */
    @ApiModelProperty("验货")
    private String isInspection;
    /**
     * 要求csr
     */
    @ApiModelProperty("要求csr")
    private String csr;
    /**
     * 是否追溯
     */
    @ApiModelProperty("是否追溯")
    private String isTracibility;
    /**
     * 是否定向SYS.If_Directional
     */
    @ApiModelProperty("是否定向SYS.If_Directional")
    private String isDirectional;
    /**
     * 指定硅料厂家
     */
    @ApiModelProperty("指定硅料厂家")
    private String siliconSup;
    /**
     * 生产基地类型DP.IQ_Production_Base
     */
    @ApiModelProperty("生产基地类型DP.IQ_Production_Base")
    private String type;

    /**
     * 询单状态DP.IQ_Status
     */
    @ApiModelProperty("询单状态DP.IQ_Status")
    private String status;
    /**
     * 询单状态DP.IQ_Status
     */
    @ApiModelProperty("询单状态DP.IQ_Status")
    private List<String> statusList;

    @ApiModelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planDateTimeStart;

    /**
     * 计划结束时间
     */
    @ApiModelProperty("计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime planDateTimeEnd;

    @ApiModelProperty("历史询单号")
    @ExcelProperty(value = "历史询单号")
    private String relatedIqHeaderNum;

    @ApiModelProperty("来源类型")
    @ExcelProperty(value = "来源类型")
    private String sourceType;
}