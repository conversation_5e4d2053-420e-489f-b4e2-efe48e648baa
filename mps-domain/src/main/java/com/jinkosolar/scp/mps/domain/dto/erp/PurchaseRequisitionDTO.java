package com.jinkosolar.scp.mps.domain.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 采购申请
 *
 * <AUTHOR>
 * @date 2022/8/22 16:38
 */
@Data
@Builder
public class PurchaseRequisitionDTO implements Serializable {

    @ApiModelProperty("采购申请头")
    private PurchaseRequisitionHeadDto head;

    @ApiModelProperty("采购申请行")
    private List<PurchaseRequisitionLineDto> lines;

    /**
     * 采购申请头对象
     *
     * <AUTHOR>
     * @date 2022/8/22 16:49
     */
    @Data
    @Builder
    public static class PurchaseRequisitionHeadDto implements Serializable {

        @ApiModelProperty("采购申请头id")
        @JsonProperty("requisition_header_id")
        private Long requisitionHeaderId;

        @ApiModelProperty("业务实体名")
        @JsonProperty("operating_unit")
        private String operatingUnit;

        @ApiModelProperty("帐套id")
        @JsonProperty("org_id")
        private Long orgId;

        @ApiModelProperty("订单类型")
        @JsonProperty("document_type")
        private String documentType;

        @ApiModelProperty("订单类型代码")
        @JsonProperty("type_lookup_code")
        private String typeLookupCode;

        @ApiModelProperty("制单人编号")
        @JsonProperty("preparer_num")
        private String preparerNum;

        @ApiModelProperty("制单人id")
        @JsonProperty("preparer_id")
        private Long preparerId;

        @ApiModelProperty("说明")
        @JsonProperty("description")
        private String description;

        @ApiModelProperty("状态")
        @JsonProperty("authorization_status")
        private String authorizationStatus;

        @ApiModelProperty("币种（本位币可为空）")
        @JsonProperty("currency_code")
        private String currencyCode;

        @ApiModelProperty("批次号")
        @JsonProperty("batch_id")
        private Long batchId;

        @ApiModelProperty("源系统代码，追溯字段，默认=当前node")
        @JsonProperty("source_code")
        private String sourceCode;

        @ApiModelProperty("源系统行id，追溯字段，默认=本表id")
        @JsonProperty("source_id")
        private String sourceId;

        @ApiModelProperty("源系统参考，显示在界面供用户看")
        @JsonProperty("source_reference")
        private String sourceReference;

        @ApiModelProperty("后台处理状态")
        @JsonProperty("process_status")
        private String processStatus;

        @ApiModelProperty("后台处理类型")
        @JsonProperty("process_type")
        private String processType;

        @ApiModelProperty("后台处理组id，供分批、并发控制用")
        @JsonProperty("process_group_id")
        private Long processGroupId;

        @ApiModelProperty("后台处理日期")
        @JsonProperty("process_date")
        private Date processDate;

        @ApiModelProperty("后台处理信息")
        @JsonProperty("process_message")
        private String processMessage;

        @ApiModelProperty("备注，有条件的话做成多行文本")
        @JsonProperty("comments")
        private String comments;

        @ApiModelProperty("行版本号，用来处理锁")
        @JsonProperty("row_version_number")
        private Long rowVersionNumber;

        @ApiModelProperty("")
        @JsonProperty("creation_date")
        private Date creationDate;

        @ApiModelProperty("")
        @JsonProperty("created_by")
        private Long createdBy;

        @ApiModelProperty("")
        @JsonProperty("last_updated_by")
        private Long lastUpdatedBy;

        @ApiModelProperty("")
        @JsonProperty("last_update_date")
        private Date lastUpdateDate;

        @ApiModelProperty("")
        @JsonProperty("last_update_login")
        private Long lastUpdateLogin;

        @ApiModelProperty("")
        @JsonProperty("program_application_id")
        private Long programApplicationId;

        @ApiModelProperty("")
        @JsonProperty("program_id")
        private Long programId;

        @ApiModelProperty("")
        @JsonProperty("program_update_date")
        private Date programUpdateDate;

        @ApiModelProperty("")
        @JsonProperty("request_id")
        private Long requestId;

        @ApiModelProperty("")
        @JsonProperty("attribute_category")
        private String attributeCategory;

        @ApiModelProperty("")
        @JsonProperty("attribute1")
        private String attribute1;

        @ApiModelProperty("")
        @JsonProperty("attribute2")
        private String attribute2;

        @ApiModelProperty("")
        @JsonProperty("attribute3")
        private String attribute3;

        @ApiModelProperty("")
        @JsonProperty("attribute4")
        private String attribute4;

        @ApiModelProperty("")
        @JsonProperty("attribute5")
        private String attribute5;

        @ApiModelProperty("")
        @JsonProperty("attribute6")
        private String attribute6;

        @ApiModelProperty("")
        @JsonProperty("attribute7")
        private String attribute7;

        @ApiModelProperty("")
        @JsonProperty("attribute8")
        private String attribute8;

        @ApiModelProperty("")
        @JsonProperty("attribute9")
        private String attribute9;

        @ApiModelProperty("")
        @JsonProperty("attribute10")
        private String attribute10;

        @ApiModelProperty("")
        @JsonProperty("attribute11")
        private String attribute11;

        @ApiModelProperty("")
        @JsonProperty("attribute12")
        private String attribute12;

        @ApiModelProperty("")
        @JsonProperty("attribute13")
        private String attribute13;

        @ApiModelProperty("")
        @JsonProperty("attribute14")
        private String attribute14;

        @ApiModelProperty("")
        @JsonProperty("attribute15")
        private String attribute15;
    }

    /**
     * 采购申请行对象
     *
     * <AUTHOR>
     * @date 2022/8/22 16:49
     */
    @Data
    @Builder
    public static class PurchaseRequisitionLineDto implements Serializable {

        @ApiModelProperty("采购申请行id")
        @JsonProperty("requisition_line_id")
        private Long requisitionLineId;

        @ApiModelProperty("行号")
        @JsonProperty("line_num")
        private Long lineNum;

        @ApiModelProperty("行类型")
        @JsonProperty("line_type")
        private String lineType;

        @ApiModelProperty("行类型id")
        @JsonProperty("line_type_id")
        private Long lineTypeId;

        @ApiModelProperty("料号")
        @JsonProperty("item_number")
        private String itemNumber;

        @ApiModelProperty("料号id")
        @JsonProperty("item_id")
        private Long itemId;

        @ApiModelProperty("类别")
        @JsonProperty("item_category")
        private String itemCategory;

        @ApiModelProperty("类别id")
        @JsonProperty("item_category_id")
        private Long itemCategoryId;

        @ApiModelProperty("说明")
        @JsonProperty("item_description")
        private String itemDescription;

        @ApiModelProperty("单位")
        @JsonProperty("unit_meas_lookup_code")
        private String unitMeasLookupCode;

        @ApiModelProperty("数量")
        @JsonProperty("quantity")
        private BigDecimal quantity;

        @ApiModelProperty("单价")
        @JsonProperty("unit_price")
        private BigDecimal unitPrice;

        @ApiModelProperty("需求日期")
        @JsonProperty("need_by_date")
        private Date needByDate;

        @ApiModelProperty("借记账户")
        @JsonProperty("charge_account")
        private String chargeAccount;

        @ApiModelProperty("借记账户ccid")
        @JsonProperty("charge_account_id")
        private Long chargeAccountId;

        @ApiModelProperty("目的地类型")
        @JsonProperty("destination_type")
        private String destinationType;

        @ApiModelProperty("目的地类型code")
        @JsonProperty("destination_type_code")
        private String destinationTypeCode;

        @ApiModelProperty("来源")
        @JsonProperty("source_type")
        private String sourceType;

        @ApiModelProperty("来源code")
        @JsonProperty("source_type_code")
        private String sourceTypeCode;

        @ApiModelProperty("申请人工号")
        @JsonProperty("requestor_num")
        private String requestorNum;

        @ApiModelProperty("申请人id")
        @JsonProperty("TO_PERSON_ID")
        private Long toPersonId;

        @ApiModelProperty("组织")
        @JsonProperty("destination_organization_name")
        private String destinationOrganizationName;

        @ApiModelProperty("组织id")
        @JsonProperty("destination_organization_id")
        private Long destinationOrganizationId;

        @ApiModelProperty("地点")
        @JsonProperty("deliver_to_location")
        private String deliverToLocation;

        @ApiModelProperty("地点id")
        @JsonProperty("deliver_to_location_id")
        private Long deliverToLocationId;

        @ApiModelProperty("供应商名")
        @JsonProperty("vendor_name")
        private String vendorName;

        @ApiModelProperty("供应商id")
        @JsonProperty("vendor_id")
        private Long vendorId;

        @ApiModelProperty("供应商地点编号")
        @JsonProperty("vendor_site_code")
        private String vendorSiteCode;

        @ApiModelProperty("供应商地点id")
        @JsonProperty("vendor_site_id")
        private Long vendorSiteId;

        @ApiModelProperty("应计账户")
        @JsonProperty("accrual_account_flex")
        private String accrualAccountFlex;

        @ApiModelProperty("应计账户ccid")
        @JsonProperty("accrual_account_id")
        private Long accrualAccountId;

        @ApiModelProperty("差异账户")
        @JsonProperty("variance_account_flex")
        private String varianceAccountFlex;

        @ApiModelProperty("差异账户ccid")
        @JsonProperty("variance_account_id")
        private Long varianceAccountId;

        @ApiModelProperty("币种")
        @JsonProperty("currency_code")
        private String currencyCode;

        @ApiModelProperty("币种单价")
        @JsonProperty("currency_unit_price")
        private Long currencyUnitPrice;

        @ApiModelProperty("汇率类型")
        @JsonProperty("rate_type")
        private Long rateType;

        @ApiModelProperty("汇率日期")
        @JsonProperty("rate_date")
        private Date rateDate;

        @ApiModelProperty("子库存")
        @JsonProperty("destination_subinventory")
        private String destinationSubinventory;

        @ApiModelProperty("源系统代码，追溯字段，默认=当前node")
        @JsonProperty("source_code")
        private String sourceCode;

        @ApiModelProperty("源系统行，追溯字段，默认=本表id")
        @JsonProperty("source_id")
        private String sourceId;

        @ApiModelProperty("源系统行id，追溯字段，默认=本表id")
        @JsonProperty("source_line_id")
        private String sourceLineId;

        @ApiModelProperty("源系统参考，显示在界面供用户看")
        @JsonProperty("source_reference")
        private String sourceReference;

        @ApiModelProperty("后台处理状态")
        @JsonProperty("process_status")
        private String processStatus;

        @ApiModelProperty("后台处理类型")
        @JsonProperty("process_type")
        private String processType;

        @ApiModelProperty("后台处理组id，供分批、并发控制用")
        @JsonProperty("process_group_id")
        private String processGroupId;

        @ApiModelProperty("后台处理日期")
        @JsonProperty("process_date")
        private Date processDate;

        @ApiModelProperty("后台处理信息")
        @JsonProperty("process_message")
        private String processMessage;

        @ApiModelProperty("备注，有条件的话做成多行文本")
        @JsonProperty("comments")
        private String comments;

        @ApiModelProperty("行版本号，用来处理锁")
        @JsonProperty("row_version_number")
        private Long rowVersionNumber;

        @ApiModelProperty("")
        @JsonProperty("creation_date")
        private Date creationDate;

        @ApiModelProperty("")
        @JsonProperty("created_by")
        private Long createdBy;

        @ApiModelProperty("")
        @JsonProperty("last_updated_by")
        private Long lastUpdatedBy;

        @ApiModelProperty("")
        @JsonProperty("last_update_date")
        private Date lastUpdateDate;

        @ApiModelProperty("")
        @JsonProperty("last_update_login")
        private Long lastUpdateLogin;

        @ApiModelProperty("")
        @JsonProperty("attribute_category")
        private String attributeCategory;

        @ApiModelProperty("")
        @JsonProperty("attribute1")
        private String attribute1;

        @ApiModelProperty("")
        @JsonProperty("attribute2")
        private String attribute2;

        @ApiModelProperty("")
        @JsonProperty("attribute3")
        private String attribute3;

        @ApiModelProperty("")
        @JsonProperty("attribute4")
        private String attribute4;

        @ApiModelProperty("")
        @JsonProperty("attribute5")
        private String attribute5;

        @ApiModelProperty("")
        @JsonProperty("attribute6")
        private String attribute6;

        @ApiModelProperty("")
        @JsonProperty("attribute7")
        private String attribute7;

        @ApiModelProperty("")
        @JsonProperty("attribute8")
        private String attribute8;

        @ApiModelProperty("")
        @JsonProperty("attribute9")
        private String attribute9;

        @ApiModelProperty("")
        @JsonProperty("attribute10")
        private String attribute10;

        @ApiModelProperty("")
        @JsonProperty("attribute11")
        private String attribute11;

        @ApiModelProperty("")
        @JsonProperty("attribute12")
        private String attribute12;

        @ApiModelProperty("")
        @JsonProperty("attribute13")
        private String attribute13;

        @ApiModelProperty("")
        @JsonProperty("attribute14")
        private String attribute14;

        @ApiModelProperty("")
        @JsonProperty("attribute15")
        private String attribute15;

    }
}
