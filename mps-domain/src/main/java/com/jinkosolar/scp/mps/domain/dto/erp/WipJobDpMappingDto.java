package com.jinkosolar.scp.mps.domain.dto.erp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工单DP信息接口入参对象
 *
 * <AUTHOR>
 * @date 2022/9/13 16:28
 */
@Data
@ApiModel(value = "WipJobDpMappingDto")
@Builder
public class WipJobDpMappingDto implements Serializable {

    @JsonProperty(required = true)
    @ApiModelProperty("DPID")
    private String dplinesid;

    @JsonProperty(required = true)
    @ApiModelProperty("DP绑单ID")
    private String dpbatchid;

    @JsonProperty(required = true)
    @ApiModelProperty("需求量")
    private BigDecimal needQuantity;

    @ApiModelProperty("源系统代码，追溯字段，默认=当前Node")
    private String sourceCode;

    @ApiModelProperty("源系统行ID，追溯字段，默认=本表ID")
    private String sourceId;

    @ApiModelProperty("源系统参考，显示在界面供用户看")
    private String sourceReference;

    @ApiModelProperty("后台处理状态")
    private String processStatus;

    @ApiModelProperty("后台处理类型")
    private String processType;

    @ApiModelProperty("后台处理组ID，供分批、并发控制用")
    private BigDecimal processGroupId;

    @ApiModelProperty("后台处理日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processDate;

    @ApiModelProperty("后台处理信息")
    private String processMessage;

    @ApiModelProperty("备注，有条件的话做成多行文本")
    private String comments;

    @ApiModelProperty("行版本号，用来处理锁")
    private BigDecimal rowVersionNumber;

    @JsonProperty(required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date creationDate;

    private BigDecimal createdBy;

    private BigDecimal lastUpdatedBy;

    @JsonProperty(required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;

    private BigDecimal lastUpdateLogin;

    private String attributeCategory;

    private String attribute1;

    private String attribute2;

    private String attribute3;

    private String attribute4;

    private String attribute5;

    private String attribute6;

    private String attribute7;

    private String attribute8;

    private String attribute9;

    private String attribute10;

    private String attribute11;

    private String attribute12;

    private String attribute13;

    private String attribute14;

    private String attribute15;


}
