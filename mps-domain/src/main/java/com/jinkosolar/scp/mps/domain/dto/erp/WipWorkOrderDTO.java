package com.jinkosolar.scp.mps.domain.dto.erp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 工单创建对象
 *
 * <AUTHOR>
 * @date 2022/8/10 8:27
 */
@Data
@ApiModel("WipWorkOrderDto")
@Builder
public class WipWorkOrderDTO implements Serializable {

    @ApiModelProperty("工单ID")
    @JsonProperty("wip_entity_id")
    private String wipEntityId;

    @ApiModelProperty("")
    @JsonProperty("organization_code")
    private String organizationCode;

    @ApiModelProperty("")
    @JsonProperty("organization_name")
    private String organizationName;

    @ApiModelProperty("组织ID")
    @JsonProperty("organization_id")
    private Long organizationId;

    @ApiModelProperty("任务号")
    @JsonProperty("wip_entity_name")
    private String wipEntityName;

    @ApiModelProperty("说明")
    @JsonProperty("description")
    private String description;

    @ApiModelProperty("状态")
    @JsonProperty("status_type")
    private Long statusType;

    @ApiModelProperty("装配件ID")
    @JsonProperty("primary_item_id")
    private Long primaryItemId;

    @ApiModelProperty("装配件料号")
    @JsonProperty("primary_item_num")
    private String primaryItemNum;

    @ApiModelProperty("供应类型：基于物料清单7")
    @JsonProperty("wip_supply_type")
    private Long wipSupplyType;

    @ApiModelProperty("订单类型：标准1/非标准3")
    @JsonProperty("job_type")
    private Long jobType;

    @ApiModelProperty("分类")
    @JsonProperty("class_code")
    private String classCode;

    @ApiModelProperty("起始日期")
    @JsonProperty("scheduled_start_date")
    private Date scheduledStartDate;

    @ApiModelProperty("已发放日期")
    @JsonProperty("date_released")
    private Date dateReleased;

    @ApiModelProperty("完成日期")
    @JsonProperty("scheduled_completion_date")
    private Date scheduledCompletionDate;

    @ApiModelProperty("已完成日期")
    @JsonProperty("date_completed")
    private Date dateCompleted;

    @ApiModelProperty("已关闭日期")
    @JsonProperty("date_closed")
    private Date dateClosed;

    @ApiModelProperty("起始数量")
    @JsonProperty("start_quantity")
    private BigDecimal startQuantity;

    @ApiModelProperty("完工数据")
    @JsonProperty("quantity_completed")
    private Long quantityCompleted;

    @ApiModelProperty("报废数量")
    @JsonProperty("quantity_scrapped")
    private Long quantityScrapped;

    @ApiModelProperty("资源ID")
    @JsonProperty("net_quantity")
    private Long netQuantity;

    @ApiModelProperty("BOM参考")
    @JsonProperty("bom_reference_id")
    private Long bomReferenceId;

    @ApiModelProperty("工艺路线参考")
    @JsonProperty("routing_reference_id")
    private Long routingReferenceId;

    @ApiModelProperty("BOM版本")
    @JsonProperty("bom_revision")
    private String bomRevision;

    @ApiModelProperty("工艺路线版本")
    @JsonProperty("routing_revision")
    private String routingRevision;

    @ApiModelProperty("BOM版本日期")
    @JsonProperty("bom_revision_date")
    private Date bomRevisionDate;

    @ApiModelProperty("工艺路线版本日期")
    @JsonProperty("routing_revision_date")
    private Date routingRevisionDate;

    @ApiModelProperty("BOM替代项")
    @JsonProperty("alternate_bom_designator")
    private String alternateBomDesignator;

    @ApiModelProperty("工艺路线替代项")
    @JsonProperty("alternate_routing_designator")
    private String alternateRoutingDesignator;

    @ApiModelProperty("完成子库")
    @JsonProperty("completion_sub_inventory")
    private String completionSubInventory;

    @ApiModelProperty("完成货位ID")
    @JsonProperty("completion_locator_id")
    private Long completionLocatorId;

    @ApiModelProperty("完成货位")
    @JsonProperty("completion_locator_code")
    private String completionLocatorCode;

    @ApiModelProperty("请求起始日期")
    @JsonProperty("requested_start_date")
    private Date requestedStartDate;

    @ApiModelProperty("请求到期日")
    @JsonProperty("due_date")
    private Date dueDate;

    @ApiModelProperty("计划优先级")
    @JsonProperty("priority")
    private Long priority;

    @ApiModelProperty("每日延迟补偿")
    @JsonProperty("due_date_penalty")
    private Long dueDatePenalty;

    @ApiModelProperty("到日期允差")
    @JsonProperty("due_date_tolerance")
    private Date dueDateTolerance;

    @ApiModelProperty("批号")
    @JsonProperty("lot_number")
    private String lotNumber;

    @ApiModelProperty("需求分类")
    @JsonProperty("demand_class")
    private String demandClass;

    @ApiModelProperty("超量允差类型")
    @JsonProperty("over_completion_tolerance_type")
    private Long overCompletionToleranceType;

    @ApiModelProperty("超量允差值")
    @JsonProperty("over_completion_tolerance_value")
    private Long overCompletionToleranceValue;

    @ApiModelProperty("来源系统代码")
    @JsonProperty("source_code")
    private String sourceCode;

    @ApiModelProperty("唯一性ID")
    @JsonProperty("source_id")
    private String sourceId;

    @ApiModelProperty("源系统参考，显示在界面供用户看")
    @JsonProperty("source_reference")
    private String sourceReference;

    @ApiModelProperty("后台处理状态")
    @JsonProperty("process_status")
    private String processStatus;

    @ApiModelProperty("后台处理类型")
    @JsonProperty("process_type")
    private String processType;

    @ApiModelProperty("后台处理组ID，供分批、并发控制用")
    @JsonProperty("process_group_id")
    private Long processGroupId;

    @ApiModelProperty("后台处理日期")
    @JsonProperty("process_date")
    private Date processDate;

    @ApiModelProperty("后台处理信息")
    @JsonProperty("process_message")
    private String processMessage;

    @ApiModelProperty("备注，有条件的话做成多行文本")
    @JsonProperty("comments")
    private String comments;

    @ApiModelProperty("行版本号，用来处理锁")
    @JsonProperty("row_version_number")
    private Long rowVersionNumber;

    @ApiModelProperty("创建日期")
    @JsonProperty("creation_date")
    private Date creationDate;

    @ApiModelProperty("创建人ID")
    @JsonProperty("created_by")
    private Long createdBy;

    @ApiModelProperty("更新人ID")
    @JsonProperty("last_updated_by")
    private Long lastUpdatedBy;

    @ApiModelProperty("更新日期")
    @JsonProperty("last_update_date")
    private Date lastUpdateDate;

    @ApiModelProperty("更新人登陆")
    @JsonProperty("last_update_login")
    private Long lastUpdateLogin;

    @ApiModelProperty("工单弹性域上下文")
    @JsonProperty("attribute_category")
    private String attributeCategory;

    @ApiModelProperty("工单弹性域1")
    @JsonProperty("attribute1")
    private String attribute1;

    @ApiModelProperty("工单弹性域2")
    @JsonProperty("attribute2")
    private String attribute2;

    @ApiModelProperty("工单弹性域3")
    @JsonProperty("attribute3")
    private String attribute3;

    @ApiModelProperty("工单弹性域4")
    @JsonProperty("attribute4")
    private String attribute4;

    @ApiModelProperty("工单弹性域5")
    @JsonProperty("attribute5")
    private String attribute5;

    @ApiModelProperty("工单弹性域6")
    @JsonProperty("attribute6")
    private String attribute6;

    @ApiModelProperty("工单弹性域7")
    @JsonProperty("attribute7")
    private String attribute7;

    @ApiModelProperty("工单弹性域8")
    @JsonProperty("attribute8")
    private String attribute8;

    @ApiModelProperty("工单弹性域9")
    @JsonProperty("attribute9")
    private String attribute9;

    @ApiModelProperty("工单弹性域10")
    @JsonProperty("attribute10")
    private String attribute10;

    @ApiModelProperty("工单弹性域11")
    @JsonProperty("attribute11")
    private String attribute11;

    @ApiModelProperty("工单弹性域12")
    @JsonProperty("attribute12")
    private String attribute12;

    @ApiModelProperty("工单弹性域13")
    @JsonProperty("attribute13")
    private String attribute13;

    @ApiModelProperty("工单弹性域14")
    @JsonProperty("attribute14")
    private String attribute14;

    @ApiModelProperty("工单弹性域15")
    @JsonProperty("attribute15")
    private String attribute15;

}
