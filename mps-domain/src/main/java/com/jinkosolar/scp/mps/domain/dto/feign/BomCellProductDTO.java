package com.jinkosolar.scp.mps.domain.dto.feign;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.LovCustomBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


@ApiModel("电池产品信息管理数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BomCellProductDTO extends BaseDTO implements Serializable {
    /**
     * 基地
     */
    @ApiModelProperty("基地")
    private String basePlace;
    /**
     * 晶棒类型(LOV：【BOM.CRY_TYPE】)
     */
    @ApiModelProperty("晶棒类型(LOV：【BOM.CRY_TYPE】)")
    @Dict(headerCode = "BOM.CRY_TYPE" )
    private Long cbType;
    @ApiModelProperty("晶棒类型(LOV：【BOM.CRY_TYPE】)")
    private String cbTypeName;
    /**
     * 组件电池尺寸
     */
    @ApiModelProperty("组件电池尺寸Code")
    private String cellDimension;
    @ApiModelProperty("组件电池尺寸")
    private String cellDimensionName;
    @ApiModelProperty("组件电池尺寸id")
    private Long cellDimensionId;

    /**
     * 电池片产品
     */
    @ApiModelProperty("电池片产品")
    private String cellProduct;
    /**
     * 阶段(产品型号信息的类型)
     */
    @ApiModelProperty("阶段(产品型号信息的类型)")
    private String cellStage;
    @ApiModelProperty("阶段(产品型号信息的类型)")
    private String cellStageName;
    /**
     * 厚度
     */
    @ApiModelProperty("厚度")
    private BigDecimal cellThickness;
    /**
     * 类型(LOV：【BOM.M_TYPE】)
     */
    @ApiModelProperty("类型(LOV：【BOM.M_TYPE】)")
    @Dict(headerCode = "BOM.M_TYPE" )
    private Long cellType;
    @ApiModelProperty("类型(LOV：【BOM.M_TYPE】)")
    private String cellTypeName;
    /**
     * 主栅(DS版本的【Main_Grid_Line 】)
     */
    @ApiModelProperty("主栅(DS版本的【Main_Grid_Line 】)")
    private String coralLine;
    /**
     * 切半片阶段
     */
    @ApiModelProperty("切半片阶段")
    private String cutOffBu;
    /**
     * 撤销时间
     */
    @ApiModelProperty("撤销时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime downlineDate;
    /**
     * 电性能(A级组件类型属性【电池类型】)
     */
    @ApiModelProperty("电性能(A级组件类型属性【电池类型】)")
    private Long electricalProperty;
    @ApiModelProperty("电性能(A级组件类型属性【电池类型】)")
    private String electricalPropertyName;
    /**
     * 主键id
     */
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 单双封(LOV：【BOM.SIN_DOU_SEAL】)
     */
    @ApiModelProperty("单双封(LOV：【BOM.SIN_DOU_SEAL】)")
    @Dict(headerCode = "BOM.SIN_DOU_SEAL" )
    private Long sinDouSeal;
    @ApiModelProperty("单双封(LOV：【BOM.SIN_DOU_SEAL】)")
    private String sinDouSealName;
    /**
     * 硅片尺寸_长
     */
    @ApiModelProperty("硅片尺寸_长")
    private BigDecimal waferSizeLength;
    /**
     * 硅片尺寸_宽
     */
    @ApiModelProperty("硅片尺寸_宽")
    private BigDecimal waferSizeWidth;
    /**
     * 硅片尺寸
     */
    @ApiModelProperty("硅片尺寸")
    private Long waferSize;
    /**
     * 硅片尺寸编码
     */
    @ApiModelProperty("硅片尺寸编码")
    private String waferSizeCode;

    @ApiModelProperty("电池尺寸")
    @Translate(customBean = LovCustomBean.class, customMethod = "getLovByIds",
            from = {"lovName"}, to = {"cellSizeIdName"}, fields = {"cellSizeId"}, queryColumns = {"lovLineId"})
    private Long cellSizeId;
    @ApiModelProperty("电池尺寸")
    private String cellSizeIdName;
}
