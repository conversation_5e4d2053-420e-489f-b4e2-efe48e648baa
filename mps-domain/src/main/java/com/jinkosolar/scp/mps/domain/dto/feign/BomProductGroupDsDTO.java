package com.jinkosolar.scp.mps.domain.dto.feign;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("产品族基础信息表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BomProductGroupDsDTO extends PageDTO implements Serializable {
    /**
     * 电池片长度
     */
    @ApiModelProperty("电池片长度")
    @ExcelProperty(value = "电池片长度")
    private BigDecimal cellLength;
    /**
     * 电池片宽度
     */
    @ApiModelProperty("电池片宽度")
    @ExcelProperty(value = "电池片宽度")
    private BigDecimal cellWidth;
    /**
     * 产品型号Code
     */
    @ApiModelProperty("产品型号Code")
    @ExcelProperty(value = "产品型号Code")
    private String codeName;
    /**
     * DS版本号
     */
    @ApiModelProperty("DS版本号")
    @ExcelProperty(value = "DS版本号")
    private String dsVersion;
    /**
     * 边框安装孔距
     */
    @ApiModelProperty("边框安装孔距")
    @ExcelProperty(value = "边框安装孔距")
    private String frameHoleSpacing;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")
    private Long id;
    /**
     * 长边C面宽度
     */
    @ApiModelProperty("长边C面宽度")
    @ExcelProperty(value = "长边C面宽度")
    private BigDecimal longCWidth;
    /**
     * 主栅
     */
    @ApiModelProperty("主栅")
    @ExcelProperty(value = "主栅")
    private String mainGridLine;
    /**
     * 边框高度
     */
    @ApiModelProperty("边框高度")
    @ExcelProperty(value = "边框高度")
    private BigDecimal moduleHeight;
    /**
     * 组件长度
     */
    @ApiModelProperty("组件长度")
    @ExcelProperty(value = "组件长度")
    private BigDecimal moduleLength;
    /**
     * 组件重量
     */
    @ApiModelProperty("组件重量")
    @ExcelProperty(value = "组件重量")
    private BigDecimal moduleWeight;
    /**
     * 组件宽度
     */
    @ApiModelProperty("组件宽度")
    @ExcelProperty(value = "组件宽度")
    private BigDecimal moduleWidth;
    /**
     * 包装数据
     */
    @ApiModelProperty("包装数据")
    @ExcelProperty(value = "包装数据")
    private String packingQuantity;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    private Long prodId;
    /**
     * 短边C面宽度
     */
    @ApiModelProperty("短边C面宽度")
    @ExcelProperty(value = "短边C面宽度")
    private BigDecimal shortCWidth;
}
