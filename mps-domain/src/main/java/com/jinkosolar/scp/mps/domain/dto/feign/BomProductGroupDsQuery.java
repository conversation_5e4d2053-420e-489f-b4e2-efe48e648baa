package com.jinkosolar.scp.mps.domain.dto.feign;

import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@ApiModel("产品族基础信息表查询条件对象")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BomProductGroupDsQuery extends PageDTO implements Serializable {
    @ApiModelProperty("excel参数对象")
    private ExcelPara excelPara;

    @ApiModelProperty("产品型号Code")
    private String codeName;

    @ApiModelProperty("DS版本号")
    private String dsVersion;

    public BomProductGroupDsQuery(String codeName, String dsVersion) {
        this.codeName = codeName;
        this.dsVersion = dsVersion;
    }
}
