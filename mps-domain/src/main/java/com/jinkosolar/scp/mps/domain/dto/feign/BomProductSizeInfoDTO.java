package com.jinkosolar.scp.mps.domain.dto.feign;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;


@ApiModel("产品尺寸信息表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BomProductSizeInfoDTO extends BaseDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    @ExcelProperty(value = "ID主键")
    private Long id;

    /**
     * 工厂Id
     */
    @ApiModelProperty("工厂Id")
    @ExcelProperty(value = "工厂Id")
    private Long factoryId;

    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    @ExcelProperty(value = "工厂编码")
    private String factoryCode;

    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")
    private String factoryName;

    /**
     * 工序Id
     */
    @ApiModelProperty("工序Id")
    @ExcelProperty(value = "工序Id")
    private Long processId;

    /**
     * 工序编码
     */
    @ApiModelProperty("工序编码")
    @ExcelProperty(value = "工序编码")
    private String processCode;

    /**
     * 工序编码
     */
    @ApiModelProperty("工序名称")
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心Id")
    @ExcelProperty(value = "工作中心Id")
    private Long workCenterId;

    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心编码")
    @ExcelProperty(value = "工作中心编码")
    private String workCenterCode;

    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心名称")
    @ExcelProperty(value = "工作中心名称")
    private String workCenterName;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 投产尺寸Id
     */
    @ApiModelProperty("投产尺寸Id")
    @ExcelProperty(value = "投产尺寸Id")
    private Long inputSizeId;

    /**
     * 投产尺寸Id 不同工序lov不同手动翻译
     */
    @ApiModelProperty("投产尺寸Id")
    @ExcelProperty(value = "投产尺寸Id")
    private String inputSizeName;

    /**
     * 产出尺寸Id
     */
    @ApiModelProperty("产出尺寸Id")
    @ExcelProperty(value = "产出尺寸Id")
    private Long outputSizeId;

    /**
     * 产出尺寸Id 不同工序lov不同手动翻译
     */
    @ApiModelProperty("产出尺寸Id")
    @ExcelProperty(value = "产出尺寸Id")
    private String outputSizeName;

    /**
     * 晶棒类型Id
     */
    @ApiModelProperty("晶棒类型Id")
    @ExcelProperty(value = "晶棒类型Id")
    private Long crystalRodTypeId;

    /**
     * 晶棒类型Id 为了一致校验不在翻译里面 手动校验
     */
    @ApiModelProperty("晶棒类型名称")
    @ExcelProperty(value = "晶棒类型名称")
    private String crystalRodTypeName;

    /**
     * 有效期起
     */
    @ApiModelProperty("有效期起")
    @ExcelProperty(value = "有效期起")
    private LocalDate expirationDateStart;

    /**
     * 有效期止
     */
    @ApiModelProperty("有效期止")
    @ExcelProperty(value = "有效期止")
    private LocalDate expirationDateEnd;

    /**
     * 备用1
     */
    @ApiModelProperty("备用1")
    @ExcelProperty(value = "备用1")
    private String attribute1;

    /**
     * 备用2
     */
    @ApiModelProperty("备用2")
    @ExcelProperty(value = "备用2")
    private String attribute2;

    /**
     * 备用3
     */
    @ApiModelProperty("备用3")
    @ExcelProperty(value = "备用3")
    private String attribute3;

    /**
     * 备用4
     */
    @ApiModelProperty("备用4")
    @ExcelProperty(value = "备用4")
    private String attribute4;

    /**
     * 备用5
     */
    @ApiModelProperty("备用5")
    @ExcelProperty(value = "备用5")
    private String attribute5;

    /**
     * 导入行号
     */
    @ApiModelProperty("行号")
    @ExcelProperty(value = "行号")
    private Integer rowNum;

}