package com.jinkosolar.scp.mps.domain.dto.feign;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


@ApiModel("产品尺寸信息表查询条件对象")
@Data
public class BomProductSizeInfoQuery extends PageDTO implements Serializable {
    /**
     * ID主键
     */
    @ApiModelProperty("ID主键")
    private Long id;

    /**
     * 工厂Id
     */
    @ApiModelProperty("工厂Id")
    private Long factoryId;

    /**
     * 工厂编码
     */
    @ApiModelProperty("工厂编码")
    private String factoryCode;

    /**
     * 工序Id
     */
    @ApiModelProperty("工序Id")
    private Long processId;

    /**
     * 工序编码
     */
    @ApiModelProperty("工序编码")
    private String processCode;

    /**
     * 工作中心Id
     */
    @ApiModelProperty("工作中心Id")
    private Long workCenterId;

    /**
     * 工作中心编码
     */
    @ApiModelProperty("工作中心编码")
    private String workCenterCode;

    /**
     * 产品Id
     */
    @ApiModelProperty("产品Id")
    private Long productId;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String productCode;

    /**
     * 投产尺寸Id
     */
    @ApiModelProperty("投产尺寸Id")
    private Long inputSizeId;

    /**
     * 产出尺寸Id
     */
    @ApiModelProperty("产出尺寸Id")
    private Long outputSizeId;

    /**
     * 晶棒类型Id
     */
    @ApiModelProperty("晶棒类型Id")
    private Long crystalRodTypeId;

    /**
     * 有效期起
     */
    @ApiModelProperty("有效期起")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expirationDateStart;

    /**
     * 有效期止
     */
    @ApiModelProperty("有效期止")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expirationDateEnd;

    /**
     * 有效期止
     */
    @ApiModelProperty("排除Id")
    private List<Long> excludeIds;

    @ApiModelProperty("excel参数对象")
    private ExcelPara excelPara;

    //区别是从拉晶/切方1 还是从 切片/电池 2
    @ApiModelProperty("区分入口")
    private String pageType;

    @ApiModelProperty("工序编码集合")
    private List<String> processCodeList;
}