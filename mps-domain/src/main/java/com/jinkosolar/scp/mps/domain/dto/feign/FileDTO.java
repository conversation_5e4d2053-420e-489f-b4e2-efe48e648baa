package com.jinkosolar.scp.mps.domain.dto.feign;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("附件表数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileDTO extends PageDTO implements Serializable {
    /**
     * 业务主键
     */
    @ApiModelProperty("业务主键")
    private String bizKey;
    /**
     * 是否业务附件
     */
    @ApiModelProperty("是否业务附件，是：Y，否：N")
    private String bizFlag;
    /**
     * 业务类型;一般是业务表名
     */
    @ApiModelProperty("业务类型;一般是业务表名")
    private String bizType;
    /**
     * 文件夹名
     */
    @ApiModelProperty("文件夹名")
    private String bucketName;
    /**
     * 目录
     */
    @ApiModelProperty("目录")
    private String directory;
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long fileId;
    /**
     * 文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;
    /**
     * 文件大小
     */
    @ApiModelProperty("文件大小")
    private BigDecimal fileSize;
    /**
     * 文件类型 IMAGE：图片，VIDEO：多媒体文件，OTHER：其它文件
     */
    @ApiModelProperty("文件类型 IMAGE：图片，VIDEO：多媒体文件，OTHER：其它文件")
    private String fileType;
    /**
     * 完整访问地址
     */
    @ApiModelProperty("完整访问地址")
    private String fileUrl;
    /**
     * 原始文件名
     */
    @ApiModelProperty("原始文件名")
    private String originalName;
    /**
     * 序号
     */
    @ApiModelProperty("序号")
    private Integer sortIndex;
    /**
     * 预览地址
     */
    @ApiModelProperty("预览地址")
    private String reviewUrl;
}