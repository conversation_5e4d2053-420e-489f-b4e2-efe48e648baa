package com.jinkosolar.scp.mps.domain.dto.feign;

import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="用户参数(getRoleUserList)")
public class GetRoleUserListDto extends PageDTO {
	@ApiModelProperty(value = "应用ID")
    private String applicationId;
	@ApiModelProperty(value = "角色ID")
    private String roleId;
}
