package com.jinkosolar.scp.mps.domain.dto.feign;

import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="角色参数(list)")
public class ListDto extends PageDTO {
	@ApiModelProperty(value = "应用ID",required=true)
    private String applicationId;
	@ApiModelProperty(value = "分类ID")
    private String categoryId;
	@ApiModelProperty(value = "类型", notes="来源：数据字典")
    private String type;
	@ApiModelProperty(value = "名称", notes="")
    private String name;
	@ApiModelProperty(value = "编码", notes="返回结果按此字段正向排序")
    private String code;
	@ApiModelProperty(value = "删除标识", notes="值：0-正用，1-停用")
    private String delFlag;
}
