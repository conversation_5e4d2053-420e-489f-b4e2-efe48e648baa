package com.jinkosolar.scp.mps.domain.dto.feign;

import com.ibm.scp.common.api.annotation.Translate;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 产品族基础信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-09 19:20:45
 */
@ToString
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProductGroupDTO {

    /**
     * prodId
     */
    @ApiModelProperty(value = "prodId")
    private Long prodId;

    /**
     * codeName
     */
    @ApiModelProperty(value = "codeName")
    private String codeName;

    /**
     * 产品版本
     */
    @ApiModelProperty(value = "产品版本")
    private String productVersion;

    /**
     * specialOrder
     */
    @ApiModelProperty(value = "specialOrder")
    private String specialOrder;

    /**
     * 功率范围
     */
    @ApiModelProperty(value = "功率范围")
    private String powerOutputRange;

    /**
     * 组件转化率
     */
    @ApiModelProperty(value = "组件转化率")
    private String moduleEfficiency;

    /**
     * status
     */
    @ApiModelProperty(value = "status")
    private String status;

    /**
     * productManager
     */
    @ApiModelProperty(value = "productManager")
    private String productManager;

    /**
     * descriptionShort
     */
    @ApiModelProperty(value = "descriptionShort")
    private String descriptionShort;

    /**
     * application
     */
    @ApiModelProperty(value = "application")
    private String application;

    /**
     * 产品家族
     */
    @ApiModelProperty(value = "产品家族")
    private String productFamily;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String regions;

    /**
     * 电池片数
     */
    @ApiModelProperty(value = "电池片数")
    private String cellQuantity;

    /**
     * 电池切片方式
     */
    @ApiModelProperty(value = "电池切片方式")
    private String cellPartition;

    /**
     * 硅片尺寸
     */
    @ApiModelProperty(value = "硅片尺寸")
    private String waferSize;

    /**
     * 单双晶
     */
    @ApiModelProperty(value = "单双晶")
    private String solarCells;

    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    private String cellsType;

    /**
     * 电池工艺
     */
    @ApiModelProperty(value = "电池工艺")
    private String cellCraft;

    /**
     * 属性分类ID
     */
    @ApiModelProperty(value = "属性分类ID")
    private Long moduleStackId;
    /**
     * 单双玻
     */
    @ApiModelProperty(value = "属性分类")
    private String moduleStack;

    /**
     * 组件类型
     */
    @ApiModelProperty(value = "组件类型")
    private String moduleType;

    /**
     * 多阻栅
     */
    @ApiModelProperty(value = "多阻栅")
    private String busBar;

    /**
     * 认证
     */
    @ApiModelProperty(value = "认证")
    private String maxSystemVoltage;

    /**
     * totalNumberOfDiodes
     */
    @ApiModelProperty(value = "totalNumberOfDiodes")
    private String totalNumberOfDiodes;

    /**
     * factory
     */
    @ApiModelProperty(value = "factory")
    private String factory;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String noteForBasicInformation;

    /**
     * 长说明
     */
    @ApiModelProperty(value = "长说明")
    private String descriptionLong;

    /**
     * 工艺名称
     */
    @ApiModelProperty(value = "工艺名称")
    private String manufactureName;

    /**
     * featureGroup
     */
    @ApiModelProperty(value = "featureGroup")
    private String featureGroup;

    /**
     * manufacturePid
     */
    @ApiModelProperty(value = "manufacturePid")
    private String manufacturePid;

    /**
     * moduleWeight
     */
    @ApiModelProperty(value = "moduleWeight")
    private String moduleWeight;

    /**
     * moduleLength
     */
    @ApiModelProperty(value = "moduleLength")
    private String moduleLength;

    /**
     * moduleWidth
     */
    @ApiModelProperty(value = "moduleWidth")
    private String moduleWidth;

    /**
     * moduleHeight
     */
    @ApiModelProperty(value = "moduleHeight")
    private String moduleHeight;

    /**
     * 电池组成
     */
    @ApiModelProperty(value = "电池组成")
    private String cellOrientation;

    /**
     * 前玻璃厚度
     */
    @ApiModelProperty(value = "前玻璃厚度")
    private String frontGlassThickness;

    /**
     * 前玻璃类型
     */
    @ApiModelProperty(value = "前玻璃类型")
    private String frontGlassType;

    /**
     * 后玻璃厚度
     */
    @ApiModelProperty(value = "后玻璃厚度")
    private String backGlassThickness;

    /**
     * 后玻璃类型
     */
    @ApiModelProperty(value = "后玻璃类型")
    private String backGlassType;

    /**
     * 背板
     */
    @ApiModelProperty(value = "背板")
    private String backsheet;

    /**
     * 线盒
     */
    @ApiModelProperty(value = "线盒")
    private String jBox;

    /**
     * 粘接材料
     */
    @ApiModelProperty(value = "粘接材料")
    private String encapsulantMaterial;

    /**
     * 端子
     */
    @ApiModelProperty(value = "端子")
    private String plugConnector;

    /**
     * maxPositiveLoad
     */
    @ApiModelProperty(value = "maxPositiveLoad")
    private String maxPositiveLoad;

    /**
     * maxNegativeLoad
     */
    @ApiModelProperty(value = "maxNegativeLoad")
    private String maxNegativeLoad;

    /**
     * maxTestDynamicPressure
     */
    @ApiModelProperty(value = "maxTestDynamicPressure")
    private String maxTestDynamicPressure;

    /**
     * 组件颜色
     */
    @ApiModelProperty(value = "组件颜色")
    private String moduleColor;

    /**
     * colorRearSide
     */
    @ApiModelProperty(value = "colorRearSide")
    private String colorRearSide;

    /**
     * 边框
     */
    @ApiModelProperty(value = "边框")
    private String frame;

    /**
     * 边框厚
     */
    @ApiModelProperty(value = "边框厚")
    private String frameThickness;

    /**
     * frameMaterial
     */
    @ApiModelProperty(value = "frameMaterial")
    private String frameMaterial;

    /**
     * frameColor
     */
    @ApiModelProperty(value = "frameColor")
    private String frameColor;

    /**
     * cableLengthLandscape
     */
    @ApiModelProperty(value = "cableLengthLandscape")
    private String cableLengthLandscape;

    /**
     * cableLengthPortrait
     */
    @ApiModelProperty(value = "cableLengthPortrait")
    private String cableLengthPortrait;

    /**
     * cableCrossSectionSize
     */
    @ApiModelProperty(value = "cableCrossSectionSize")
    private String cableCrossSectionSize;

    /**
     * cableSpec
     */
    @ApiModelProperty(value = "cableSpec")
    private String cableSpec;

    /**
     * typeOfCable
     */
    @ApiModelProperty(value = "typeOfCable")
    private String typeOfCable;

    /**
     * nmot
     */
    @ApiModelProperty(value = "nmot")
    private String nmot;

    /**
     * temperatureCoefficientPmpp
     */
    @ApiModelProperty(value = "temperatureCoefficientPmpp")
    private String temperatureCoefficientPmpp;

    /**
     * temperatureCoefficientIsc
     */
    @ApiModelProperty(value = "temperatureCoefficientIsc")
    private String temperatureCoefficientIsc;

    /**
     * temperatureCoefficientVoc
     */
    @ApiModelProperty(value = "temperatureCoefficientVoc")
    private String temperatureCoefficientVoc;

    /**
     * workingTemperatureHigh
     */
    @ApiModelProperty(value = "workingTemperatureHigh")
    private String workingTemperatureHigh;

    /**
     * workingTemperatureLow
     */
    @ApiModelProperty(value = "workingTemperatureLow")
    private String workingTemperatureLow;

    /**
     * firstYearDegradation
     */
    @ApiModelProperty(value = "firstYearDegradation")
    private String firstYearDegradation;

    /**
     * lid
     */
    @ApiModelProperty(value = "lid")
    private String lid;

    /**
     * averageAnnualDegradation
     */
    @ApiModelProperty(value = "averageAnnualDegradation")
    private String averageAnnualDegradation;

    /**
     * workmanshipWarranty
     */
    @ApiModelProperty(value = "workmanshipWarranty")
    private String workmanshipWarranty;

    /**
     * powerWarranty
     */
    @ApiModelProperty(value = "powerWarranty")
    private String powerWarranty;

    /**
     * productsPerPallet
     */
    @ApiModelProperty(value = "productsPerPallet")
    private String productsPerPallet;

    /**
     * productsPerContainer
     */
    @ApiModelProperty(value = "productsPerContainer")
    private String productsPerContainer;

    /**
     * productsPerContainerUs
     */
    @ApiModelProperty(value = "productsPerContainerUs")
    private String productsPerContainerUs;

    /**
     * packageSizeLength
     */
    @ApiModelProperty(value = "packageSizeLength")
    private String packageSizeLength;

    /**
     * packageSizeWidth
     */
    @ApiModelProperty(value = "packageSizeWidth")
    private String packageSizeWidth;

    /**
     * packageSizeHeight
     */
    @ApiModelProperty(value = "packageSizeHeight")
    private String packageSizeHeight;

    /**
     * signalPackageSizeWeight
     */
    @ApiModelProperty(value = "signalPackageSizeWeight")
    private String signalPackageSizeWeight;

    /**
     * signalPackageSizeLength
     */
    @ApiModelProperty(value = "signalPackageSizeLength")
    private String signalPackageSizeLength;

    /**
     * signalPackageSizeWidth
     */
    @ApiModelProperty(value = "signalPackageSizeWidth")
    private String signalPackageSizeWidth;

    /**
     * signalPackageSizeHeight
     */
    @ApiModelProperty(value = "signalPackageSizeHeight")
    private String signalPackageSizeHeight;

    /**
     * contentDescription
     */
    @ApiModelProperty(value = "contentDescription")
    private String contentDescription;

    /**
     * contentTypeJpgInRgb
     */
    @ApiModelProperty(value = "contentTypeJpgInRgb")
    private String contentTypeJpgInRgb;

    /**
     * contentSourceUrl
     */
    @ApiModelProperty(value = "contentSourceUrl")
    private String contentSourceUrl;

    /**
     * dataDescription
     */
    @ApiModelProperty(value = "dataDescription")
    private String dataDescription;

    /**
     * dataSource
     */
    @ApiModelProperty(value = "dataSource")
    private String dataSource;

    /**
     * minimumQuantity
     */
    @ApiModelProperty(value = "minimumQuantity")
    private String minimumQuantity;

    /**
     * priceQuantity
     */
    @ApiModelProperty(value = "priceQuantity")
    private String priceQuantity;

    /**
     * productOrderDetails
     */
    @ApiModelProperty(value = "productOrderDetails")
    private String productOrderDetails;

    /**
     * productsPerSalesUnit
     */
    @ApiModelProperty(value = "productsPerSalesUnit")
    private String productsPerSalesUnit;

    /**
     * noteData
     */
    @ApiModelProperty(value = "noteData")
    private String noteData;

    /**
     * pmaxNmot
     */
    @ApiModelProperty(value = "pmaxNmot")
    private String pmaxNmot;

    /**
     * imppNmot
     */
    @ApiModelProperty(value = "imppNmot")
    private String imppNmot;

    /**
     * umppNmot
     */
    @ApiModelProperty(value = "umppNmot")
    private String umppNmot;

    /**
     * openCircuitVoltageNmot
     */
    @ApiModelProperty(value = "openCircuitVoltageNmot")
    private String openCircuitVoltageNmot;

    /**
     * shortCircuitCurrentNmot
     */
    @ApiModelProperty(value = "shortCircuitCurrentNmot")
    private String shortCircuitCurrentNmot;

    /**
     * pmaxStc
     */
    @ApiModelProperty(value = "pmaxStc")
    private String pmaxStc;

    /**
     * moduleEfficiencyFactorStc
     */
    @ApiModelProperty(value = "moduleEfficiencyFactorStc")
    private String moduleEfficiencyFactorStc;

    /**
     * imppStc
     */
    @ApiModelProperty(value = "imppStc")
    private String imppStc;

    /**
     * umppStc
     */
    @ApiModelProperty(value = "umppStc")
    private String umppStc;

    /**
     * powerTolerance
     */
    @ApiModelProperty(value = "powerTolerance")
    private String powerTolerance;

    /**
     * measuringUncertainty
     */
    @ApiModelProperty(value = "measuringUncertainty")
    private String measuringUncertainty;

    /**
     * openCircuitVoltageStc
     */
    @ApiModelProperty(value = "openCircuitVoltageStc")
    private String openCircuitVoltageStc;

    /**
     * shortCircuitCurrentStc
     */
    @ApiModelProperty(value = "shortCircuitCurrentStc")
    private String shortCircuitCurrentStc;

    /**
     * reverseCurrentLoad
     */
    @ApiModelProperty(value = "reverseCurrentLoad")
    private String reverseCurrentLoad;

    /**
     * noteForElectricalDataStc
     */
    @ApiModelProperty(value = "noteForElectricalDataStc")
    private String noteForElectricalDataStc;

    /**
     * noct
     */
    @ApiModelProperty(value = "noct")
    private String noct;

    /**
     * maxSeriesFuseRating
     */
    @ApiModelProperty(value = "maxSeriesFuseRating")
    private String maxSeriesFuseRating;

    @ApiModelProperty(value = "PMCode")
    private String pmCode;

    private String prodSeries;
    private String backsheetType;
    private String cellQuantityTech;


    public String getCellQuantityTech() {
        return cellQuantityTech;
    }

    public void setCellQuantityTech(String cellQuantityTech) {
        this.cellQuantityTech = cellQuantityTech;
    }

    public String getBacksheetType() {
        return backsheetType;
    }

    public void setBacksheetType(String backsheetType) {
        this.backsheetType = backsheetType;
    }
}
