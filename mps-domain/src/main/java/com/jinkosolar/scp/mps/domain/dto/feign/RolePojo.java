package com.jinkosolar.scp.mps.domain.dto.feign;

import com.ibm.scp.common.api.base.TokenDTO;

import java.io.Serializable;
import java.util.List;

@SuppressWarnings("serial")
public class RolePojo extends TokenDTO implements Serializable {
    private String id;
    private String name;
    private String code;
    private boolean selected;
    private String otherApplicationId;
    private String categoryId;
    private String type;
    private String delFlag;
    private String isMerchant;

	private String clientId;
    private String isUser;

    public String getIsMerchant() {
        return isMerchant;
    }

    public void setIsMerchant(String isMerchant) {
        this.isMerchant = isMerchant;
    }

	public String getClientId() {
		return clientId;
	}

	public void setClientId(String clientId) {
		this.clientId = clientId;
	}


    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    private String merchantId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean getSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getOtherApplicationId() {
        return otherApplicationId;
    }

    public void setOtherApplicationId(String otherApplicationId) {
        this.otherApplicationId = otherApplicationId;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getIsUser() {
        return isUser;
    }

    public void setIsUser(String isUser) {
        this.isUser = isUser;
    }
}
