package com.jinkosolar.scp.mps.domain.dto.feign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Data
@ApiModel(value="批量发送参数(send)")
public class SendDto {
	@ApiModelProperty(value = "ID")
    private String id;
	@ApiModelProperty(value = "接收人信息,默认按照,分隔",required=true)
    private String recipientNo;
	@ApiModelProperty(value = "应用编号",required=true)
    private String appCode;
	@ApiModelProperty(value = "发送方式",required=true)
    private String sendMethod;
	@ApiModelProperty(value = "发送通道",required=true)
    private String sendChannel;
	@ApiModelProperty(value = "标题",required=true)
    private String title;
	@ApiModelProperty(value = "发送时间")
	private Timestamp sendTime;
	@ApiModelProperty(value = "发送内容",required=true)
	private String content;
	@ApiModelProperty(value = "备注")
    private String remarks;

	@ApiModelProperty(value = "抄送人")
	private String copyTo;

	@ApiModelProperty(value = "邮件附件json")
	private String fileJson;
	@ApiModelProperty(value = "删除标识", notes="值：0-正用，1-停用",required=true)
    private int isDeleted;
	@ApiModelProperty(value = "创建人")
    private String createdBy;
	@ApiModelProperty(value = "创建时间")
    private Date createdDate;
	@ApiModelProperty(value = "乐观锁")
    private int optCounter;
	@ApiModelProperty(value = "更新人")
    private String updatedBy;
	@ApiModelProperty(value = "更新时间")
    private String updatedDate;
}