package com.jinkosolar.scp.mps.domain.dto.feign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/13
 **/
@ApiModel(value = "表同步参数")
@Data
public class SyncTableDTO {

    @ApiModelProperty("lov编码")
    private List<String> lovCodes;


    public String getLockKey(){
        String key = "syncTables";
        if (CollectionUtils.isNotEmpty(lovCodes)) {
            key = key + lovCodes.toString();
        }
        return key;
    }

}
