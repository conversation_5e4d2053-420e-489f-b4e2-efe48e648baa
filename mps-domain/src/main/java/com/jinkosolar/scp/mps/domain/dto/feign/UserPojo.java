package com.jinkosolar.scp.mps.domain.dto.feign;

import com.ibm.dpf.common.domain.entity.Office;
import com.ibm.scp.common.api.base.TokenDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@SuppressWarnings("serial")
public class UserPojo extends TokenDTO implements Serializable {
    private String id;
    private String userId;
    private String loginName;
    //private String password;
    private String name;
    private String no;
    private String email;
    private String phone;
    private String mobile;
    private String loginIp;
    private String locked;
    private Date loginDate;
    private Date startDate;
    private Date endDate;
    private int errors;
    private int errorsMax;
    private String sex;
    private String officeId;
    private String office2Id;
    private String office2Name;
    private String office2Code;
    private String type;
    private String post;
    private String officeName;
    private String identity;
    private String source;
    private String delFlag;
    private String level;
    private String area;
    private List<Office> listParentOffice;


    public List<Office> getListParentOffice() {
        return listParentOffice;
    }

    public void setListParentOffice(List<Office> listParentOffice) {
        this.listParentOffice = listParentOffice;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    //20191104 营销中心编码
    private String region;

    //20190916 add
    private Integer userFlag;//用户标识


    List<RolePojo> rolePojoList;
    List<String> orgIdList;

    private String wxOpenid;

    public String getWxOpenid() {
        return wxOpenid;
    }

    public void setWxOpenid(String wxOpenid) {
        this.wxOpenid = wxOpenid;
    }

    public List<Long> getOrgFrontIdList() {
        return orgFrontIdList;
    }

    public void setOrgFrontIdList(List<Long> orgFrontIdList) {
        this.orgFrontIdList = orgFrontIdList;
    }

    List<Long> orgFrontIdList;

    public List<Long> getOfficeFrontIdList() {
        return officeFrontIdList;
    }

    public void setOfficeFrontIdList(List<Long> officeFrontIdList) {
        this.officeFrontIdList = officeFrontIdList;
    }

    List<Long> officeFrontIdList;
    List<String> officeIdList;

    public List<RolePojo> getRolePojoList() {
        return rolePojoList;
    }

    public void setRolePojoList(List<RolePojo> rolePojoList) {
        this.rolePojoList = rolePojoList;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getLoginIp() {
        return loginIp;
    }

    public void setLoginIp(String loginIp) {
        this.loginIp = loginIp;
    }

    public String getLocked() {
        return locked;
    }

    public void setLocked(String locked) {
        this.locked = locked;
    }

    public Date getLoginDate() {
        return loginDate;
    }

    public void setLoginDate(Date loginDate) {
        this.loginDate = loginDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public int getErrors() {
        return errors;
    }

    public void setErrors(int errors) {
        this.errors = errors;
    }

    public int getErrorsMax() {
        return errorsMax;
    }

    public void setErrorsMax(int errorsMax) {
        this.errorsMax = errorsMax;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getOffice2Id() {
        return office2Id;
    }

    public void setOffice2Id(String office2Id) {
        this.office2Id = office2Id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getOfficeName() {
        return officeName;
    }

    public void setOfficeName(String officeName) {
        this.officeName = officeName;
    }

    public String getOffice2Name() {
        return office2Name;
    }

    public void setOffice2Name(String office2Name) {
        this.office2Name = office2Name;
    }

    public String getOffice2Code() {
        return office2Code;
    }

    public void setOffice2Code(String office2Code) {
        this.office2Code = office2Code;
    }

    public String getIdentity() {
        return identity;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public List<String> getOrgIdList() {
        return orgIdList;
    }

    public void setOrgIdList(List<String> orgIdList) {
        this.orgIdList = orgIdList;
    }

    public List<String> getOfficeIdList() {
        return officeIdList;
    }

    public void setOfficeIdList(List<String> officeIdList) {
        this.officeIdList = officeIdList;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getUserFlag() {
        return userFlag;
    }

    public void setUserFlag(Integer userFlag) {
        this.userFlag = userFlag;
    }


    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }
}
