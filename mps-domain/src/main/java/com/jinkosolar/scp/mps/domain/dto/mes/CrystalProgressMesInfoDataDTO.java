package com.jinkosolar.scp.mps.domain.dto.mes;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * [说明]籽晶酸洗切方进度（mes接口返回） DTO
 * <AUTHOR>
 * @version 创建时间： 2024-11-11
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "籽晶酸洗切方进度（mes接口返回）DTO对象", description = "DTO对象")
public class CrystalProgressMesInfoDataDTO {
    
    /**
     * mes主键
     */
    @ApiModelProperty(value = "mes主键")
    private String mesId;
    
    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;
    
    /**
     * 车间代码
     */
    @ApiModelProperty(value = "车间代码")
    private String workShopCode;
    
    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private String workCenterCode;

    /**
     * 产品
     */
    @ApiModelProperty(value = "产品")
    private String productCode;
    
    /**
     * 产品物料号
     */
    @ApiModelProperty(value = "产品物料号")
    private String itemCode;
    
    /**
     * 工单数量
     */
    @ApiModelProperty(value = "工单数量")
    private BigDecimal quantity;
    
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private LocalDateTime uploadDate;
    
    /**
     * 工序编号
     */
    @ApiModelProperty(value = "工序编号")
    private String processNo;
    
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    private String workCode;
    

}
