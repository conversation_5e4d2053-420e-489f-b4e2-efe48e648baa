package com.jinkosolar.scp.mps.domain.dto.mes;

import com.alibaba.fastjson.annotation.JSONField;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 籽晶酸洗切方进度 接收DTO
 **/
@Getter
@Setter
public class CrystalProgressMesInfoRequestDTO extends JipRequestData {

    @JSONField(name = "IT_DATA")
    private List<CrystalProgressMesInfoDataDTO> data = new ArrayList<>();

}
