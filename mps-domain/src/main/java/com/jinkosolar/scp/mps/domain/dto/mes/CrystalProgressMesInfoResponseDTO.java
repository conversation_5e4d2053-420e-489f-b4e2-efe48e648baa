package com.jinkosolar.scp.mps.domain.dto.mes;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "StandardBomDTO对象", description = "DTO对象")
public class CrystalProgressMesInfoResponseDTO extends JipResponseData {


        @JSONField(name = "ET_RESULT")
        private List<EtResultDTO> etResultDTOList;

        @Data
        public static class EtResultDTO {

                @JSONField(name = "MES_ID")
                private String mesId;

                @JSONField(name = "ZTYPE")
                private String ztype;

                @JSONField(name = "ZMSG")
                private String zmsg;
        }


}
