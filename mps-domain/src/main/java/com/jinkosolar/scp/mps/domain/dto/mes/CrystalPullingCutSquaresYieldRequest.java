package com.jinkosolar.scp.mps.domain.dto.mes;

import com.alibaba.fastjson.annotation.JSONField;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import com.jinkosolar.scp.jip.api.dto.mes.SyncEquipmentInventoryStatusRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CrystalPullingCutSquaresYieldRequest extends JipRequestData {


    @J<PERSON>NField(name = "IT_PARAM")
    private SyncEquipmentInventoryStatusRequest.IT_PARAM input = new SyncEquipmentInventoryStatusRequest.IT_PARAM();

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IT_PARAM {

        @JSONField(name = "WORKSHOP")
        private String workShop;

        @J<PERSON>NField(name = "AREA")
        private String area;

        @JSONField(name = "SITE")
        private String site;

        @J<PERSON>NField(name = "STANDBY1")
        private String standBy1;

        @JSONField(name = "STANDBY2")
        private String standBy2;

        @JSONField(name = "STANDBY3")
        private String standBy3;

        @JSONField(name = "STANDBY4")
        private String standBy4;

        @JSONField(name = "STANDBY5")
        private String standBy5;

    }
}
