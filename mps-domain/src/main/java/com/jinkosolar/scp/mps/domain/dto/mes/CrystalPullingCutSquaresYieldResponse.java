package com.jinkosolar.scp.mps.domain.dto.mes;

import com.alibaba.fastjson.annotation.JSONField;
import com.jinkosolar.scp.jip.api.dto.base.JipResponseData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrystalPullingCutSquaresYieldResponse extends JipResponseData {


    @JSONField(name = "IT_DATA")
    private List<CrystalPullingCutSquaresYieldResponse.CrystalPullingCutSquaresYieldInfo> infoList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrystalPullingCutSquaresYieldInfo {

        @JSONField(name = "SITE")
        private String site;

        @JSONField(name = "WORKSHOP")
        private String workshop;

        @JSONField(name = "AREA")
        private String area;

        @J<PERSON>NField(name = "FURNACESIZE")
        private String furnaceSize;

        @JSONField(name = "PRODUCTIONNO")
        private String productionNo;

        @JSONField(name = "TOTALLENGTH")
        private BigDecimal totalLength;

        @JSONField(name = "INVERSELENGTH")
        private BigDecimal inverseLength;

        @JSONField(name = "YIELD")
        private String yield;

        @JSONField(name = "DATE")
        private LocalDate date;

        @JSONField(name = "FOURPERFORMANCE")
        private BigDecimal fourPerformance;

        @JSONField(name = "SINGLECRYSTAL")
        private BigDecimal singleCrystal;

        @JSONField(name = "BUTTLOSS")
        private BigDecimal buttLoss;

        @JSONField(name = "SIZE")
        private String size;

        @JSONField(name = "DIRECTIONAL")
        private String directional;

        @JSONField(name = "CHAMFER")
        private String chamfer;

        @JSONField(name = "HLRESISTANCE")
        private String hlresistance;

        @JSONField(name = "FORMULA")
        private String formula;

        @JSONField(name = "SILICONPROVIDER")
        private String siliconprovider;

    }
}
