package com.jinkosolar.scp.mps.domain.dto.message;

import lombok.Data;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.sql.Timestamp;

//组件排产计划发邮件使用
@Data
public class ModuleMsgReceiverDto extends MsgReceiverDto{

//    汇总版收件人邮件组
    private String recipientNosGroup;
    private String copyToGroup;

//    密送人邮件组，共用
    private String blindCarbonGroup;
}
