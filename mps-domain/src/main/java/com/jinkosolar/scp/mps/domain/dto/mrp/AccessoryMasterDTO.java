package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;


@ApiModel("Bom装配数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessoryMasterDTO extends BaseDTO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty("id")
    @ExcelProperty(value = "id")
    private Long id;
    /**
     * DPId
     */
    @ApiModelProperty("DPId")
    @ExcelProperty(value = "DPId")
    private String dpId;
    /**
     * dp_lines_id
     */
    @ApiModelProperty("dp_lines_id")
    @ExcelProperty(value = "dp_lines_id")
    private Long dpLinesId;
    /**
     * 主料号
     */
    @ApiModelProperty("主料号")
    @ExcelProperty(value = "主料号")
    private String mainItemCode;
    /**
     * 主料号描述
     */
    @ApiModelProperty("主料号描述")
    @ExcelProperty(value = "主料号描述")
    private String mainItemName;
    /**
     * APS序号
     */
    @ApiModelProperty("APS序号")
    @ExcelProperty(value = "APS序号")
    private Integer structureNo;
    /**
     * bom结构
     */
    @ApiModelProperty("bom结构")
    @ExcelProperty(value = "bom结构")
    private String bomStructure;
    /**
     * 子料号
     */
    @ApiModelProperty("子料号")
    @ExcelProperty(value = "子料号")
    private String subItemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")
    private String subItemDesc;
    /**
     * 物料中类
     */
    @ApiModelProperty("物料中类")
    @ExcelProperty(value = "物料中类")
    private String subItemCategorySegment2;
    /**
     * 物料单位
     */
    @ApiModelProperty("物料单位")
    @ExcelProperty(value = "物料单位")
    private String subItemPriUom;
    /**
     * 物料厂商
     */
    @ApiModelProperty("物料厂商")
    @ExcelProperty(value = "物料厂商")
    private String subItemVendor;
    /**
     * IEC认证序号
     */
    @ApiModelProperty("IEC认证序号")
    @ExcelProperty(value = "IEC认证序号")
    private String rule1Code;
    /**
     * IEC认证脚本
     */
    @ApiModelProperty("IEC认证脚本")
    @ExcelProperty(value = "IEC认证脚本")
    private String rule1Script;
    /**
     * UL认证序号
     */
    @ApiModelProperty("UL认证序号")
    @ExcelProperty(value = "UL认证序号")
    private String rule2Code;
    /**
     * UL认证脚本
     */
    @ApiModelProperty("UL认证脚本")
    @ExcelProperty(value = "UL认证脚本")
    private String rule2Script;
    /**
     * 技术搭配序号
     */
    @ApiModelProperty("技术搭配序号")
    @ExcelProperty(value = "技术搭配序号")
    private String rule3Code;
    /**
     * 技术搭配脚本
     */
    @ApiModelProperty("技术搭配脚本")
    @ExcelProperty(value = "技术搭配脚本")
    private String rule3Script;
    /**
     * 替代序号
     */
    @ApiModelProperty("替代序号")
    @ExcelProperty(value = "替代序号")
    private Integer replaceNo;
    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    @ExcelProperty(value = "工序编号")
    private String processNo;
    /**
     * 工序代码
     */
    @ApiModelProperty("工序代码")
    @ExcelProperty(value = "工序代码")
    private String processCode;
    /**
     * 指令种类
     */
    @ApiModelProperty("指令种类")
    @ExcelProperty(value = "指令种类")
    private String instructionType;
    /**
     * 指令代码
     */
    @ApiModelProperty("指令代码")
    @ExcelProperty(value = "指令代码")
    private String emInstruction;
    /**
     * 单位用量
     */
    @ApiModelProperty("单位用量")
    @ExcelProperty(value = "单位用量")
    private String componentQuantity;
    /**
     * 主替( 1主 0替)
     */
    @ApiModelProperty("主替( 1主 0替)")
    @ExcelProperty(value = "主替( 1主 0替)")
    private String isPrimary;
    /**
     * 良率
     */
    @ApiModelProperty("良率")
    @ExcelProperty(value = "良率")
    private String yieldRate;
    /**
     * 特殊单厂商
     */
    @ApiModelProperty("特殊单厂商")
    @ExcelProperty(value = "特殊单厂商")
    private String specialSnVendor;
    /**
     * 认证厂商
     */
    @ApiModelProperty("认证厂商")
    @ExcelProperty(value = "认证厂商")
    private String certVendor;
    /**
     * 损耗率
     */
    @ApiModelProperty("损耗率")
    @ExcelProperty(value = "损耗率")
    private String lossRate;
    /**
     * 构件序号
     */
    @ApiModelProperty("构件序号")
    @ExcelProperty(value = "构件序号")
    private Long componentSequenceId;
    /**
     * 子构件序号
     */
    @ApiModelProperty("子构件序号")
    @ExcelProperty(value = "子构件序号")
    private Long substituteComponentId;
    /**
     * 单个厂商
     */
    @ApiModelProperty("单个厂商")
    @ExcelProperty(value = "单个厂商")
    private String singleVendor;
    /**
     * 车间（多个时用,分割）
     */
    @ApiModelProperty("车间（多个时用,分割）")
    @ExcelProperty(value = "车间（多个时用,分割）")
    private String workshop;
    /**
     * 产品族
     */
    @ApiModelProperty("产品族")
    @ExcelProperty(value = "产品族")
    private String productFamily;
    /**
     * 额外属性
     */
    @ApiModelProperty("额外属性")
    @ExcelProperty(value = "额外属性")
    private String attributes;
    /**
     * 冻结标记，Y/N
     */
    @ApiModelProperty("冻结标记，Y/N")
    @ExcelProperty(value = "冻结标记，Y/N")
    private String freezeMark;
    /**
     * 供应商asl状态
     */
    @ApiModelProperty("供应商asl状态")
    @ExcelProperty(value = "供应商asl状态")
    private String vendorAslStatus;
    /**
     * 相关联的结构，JSON数组Structure Lov Id
     */
    @ApiModelProperty("相关联的结构，JSON数组Structure Lov Id")
    @ExcelProperty(value = "相关联的结构，JSON数组Structure Lov Id")
    private String structureRelated;
    /**
     * 传APS按钮增加字段用于区分BOM版本
     */
    @ApiModelProperty("传APS按钮增加字段用于区分BOM版本")
    @ExcelProperty(value = "传APS按钮增加字段用于区分BOM版本")
    private String bomVersion;
    /**
     * 工厂code
     */
    @ApiModelProperty("工厂code")
    @ExcelProperty(value = "工厂code")
    private Long organizationId;
    /**
     * BOM同结构序号
     */
    @ApiModelProperty("BOM同结构序号")
    @ExcelProperty(value = "BOM同结构序号")
    private Integer bomStructureIndex;
    /**
     * 差异表指定BOM名称
     */
    @ApiModelProperty("差异表指定BOM名称")
    @ExcelProperty(value = "差异表指定BOM名称")
    private String diffBomName;
    /**
     * 父级物料编码
     */
    @ApiModelProperty("父级物料编码")
    @ExcelProperty(value = "父级物料编码")
    private String parentItemCode;
    /**
     * 父级标识
     */
    @ApiModelProperty("父级标识")
    @ExcelProperty(value = "父级标识")
    private String parentItemFlag;

    @ApiModelProperty("dp版本")
    private String dpVersion;

    /**
     * 需再调整物料
     */
    @ApiModelProperty(value = "需再调整物料")
    private String needAdjustFlag;

    /**
     * 合格供应商标识
     */
    @ApiModelProperty(value = "合格供应商标识")
    private String qualifiedVendorFlag;

    /**
     * 集中供料标识
     */
    @ApiModelProperty(value = "集中供料标识")
    private String centralizedSupplyFlag;

    /**
     * 零配额采购标识
     */
    @ApiModelProperty(value = "零配额采购标识")
    private String zeroQuotaFlag;

    /**
     * 降本切换数量
     */
    @ApiModelProperty(value = "降本切换数量")
    private BigDecimal switchQty;

    public String sign() {
        return StringUtils.join(this.mainItemCode, this.subItemCode);
    }

    public boolean nonNull() {
        if (StringUtils.isBlank(this.structureRelated)) {
            this.structureRelated = "[]";
        }
        return StringUtils.isNoneBlank(this.mainItemCode, this.subItemCode, this.structureRelated);
    }
}
