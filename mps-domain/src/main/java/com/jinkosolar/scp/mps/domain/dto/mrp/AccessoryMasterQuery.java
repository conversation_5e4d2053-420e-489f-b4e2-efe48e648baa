package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.ibm.scp.common.api.base.PageDTO;
import com.ibm.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@ApiModel("Bom装配查询条件对象")
@Data
@Accessors(chain = true)
public class AccessoryMasterQuery extends PageDTO implements Serializable {
    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * DPId
     */
    @ApiModelProperty("DPId")
    private String dpId;
    /**
     * dp_lines_id
     */
    @ApiModelProperty("dp_lines_id")
    private Long dpLinesId;
    /**
     * 主料号
     */
    @ApiModelProperty("主料号")
    private String mainItemCode;
    /**
     * 主料号
     */
    @ApiModelProperty("主料号")
    private List<String> mainItemCodeList;
    /**
     * 主料号描述
     */
    @ApiModelProperty("主料号描述")
    private String mainItemName;
    /**
     * APS序号
     */
    @ApiModelProperty("APS序号")
    private Integer structureNo;
    /**
     * bom结构
     */
    @ApiModelProperty("bom结构")
    private String bomStructure;
    /**
     * 子料号
     */
    @ApiModelProperty("子料号")
    private String subItemCode;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    private String subItemDesc;
    /**
     * 物料中类
     */
    @ApiModelProperty("物料中类")
    private String subItemCategorySegment2;
    /**
     * 物料单位
     */
    @ApiModelProperty("物料单位")
    private String subItemPriUom;
    /**
     * 物料厂商
     */
    @ApiModelProperty("物料厂商")
    private String subItemVendor;
    /**
     * IEC认证序号
     */
    @ApiModelProperty("IEC认证序号")
    private String rule1Code;
    /**
     * IEC认证脚本
     */
    @ApiModelProperty("IEC认证脚本")
    private String rule1Script;
    /**
     * UL认证序号
     */
    @ApiModelProperty("UL认证序号")
    private String rule2Code;
    /**
     * UL认证脚本
     */
    @ApiModelProperty("UL认证脚本")
    private String rule2Script;
    /**
     * 技术搭配序号
     */
    @ApiModelProperty("技术搭配序号")
    private String rule3Code;
    /**
     * 技术搭配脚本
     */
    @ApiModelProperty("技术搭配脚本")
    private String rule3Script;
    /**
     * 替代序号
     */
    @ApiModelProperty("替代序号")
    private Integer replaceNo;
    /**
     * 工序编号
     */
    @ApiModelProperty("工序编号")
    private String processNo;
    /**
     * 工序代码
     */
    @ApiModelProperty("工序代码")
    private String processCode;
    /**
     * 指令种类
     */
    @ApiModelProperty("指令种类")
    private String instructionType;
    /**
     * 指令代码
     */
    @ApiModelProperty("指令代码")
    private String emInstruction;
    /**
     * 单位用量
     */
    @ApiModelProperty("单位用量")
    private String componentQuantity;
    /**
     * 主替( 1主 0替)
     */
    @ApiModelProperty("主替( 1主 0替)")
    private String isPrimary;
    /**
     * 良率
     */
    @ApiModelProperty("良率")
    private String yieldRate;
    /**
     * 特殊单厂商
     */
    @ApiModelProperty("特殊单厂商")
    private String specialSnVendor;
    /**
     * 认证厂商
     */
    @ApiModelProperty("认证厂商")
    private String certVendor;
    /**
     * 损耗率
     */
    @ApiModelProperty("损耗率")
    private String lossRate;
    /**
     * 构件序号
     */
    @ApiModelProperty("构件序号")
    private Long componentSequenceId;
    /**
     * 子构件序号
     */
    @ApiModelProperty("子构件序号")
    private Long substituteComponentId;
    /**
     * 单个厂商
     */
    @ApiModelProperty("单个厂商")
    private String singleVendor;
    /**
     * 车间（多个时用,分割）
     */
    @ApiModelProperty("车间（多个时用,分割）")
    private String workshop;
    /**
     * 产品族
     */
    @ApiModelProperty("产品族")
    private String productFamily;
    /**
     * 额外属性
     */
    @ApiModelProperty("额外属性")
    private String attributes;
    /**
     * 冻结标记，Y/N
     */
    @ApiModelProperty("冻结标记，Y/N")
    private String freezeMark;
    /**
     * 供应商asl状态
     */
    @ApiModelProperty("供应商asl状态")
    private String vendorAslStatus;
    /**
     * 相关联的结构，JSON数组Structure Lov Id
     */
    @ApiModelProperty("相关联的结构，JSON数组Structure Lov Id")
    private String structureRelated;
    /**
     * 传APS按钮增加字段用于区分BOM版本
     */
    @ApiModelProperty("传APS按钮增加字段用于区分BOM版本")
    private String bomVersion;
    /**
     * 工厂code
     */
    @ApiModelProperty("工厂code")
    private Long organizationId;
    /**
     * BOM同结构序号
     */
    @ApiModelProperty("BOM同结构序号")
    private Integer bomStructureIndex;
    /**
     * 差异表指定BOM名称
     */
    @ApiModelProperty("差异表指定BOM名称")
    private String diffBomName;
    /**
     * 父级物料编码
     */
    @ApiModelProperty("父级物料编码")
    private String parentItemCode;
    /**
     * 父级标识
     */
    @ApiModelProperty("父级标识")
    private String parentItemFlag;

    @ApiModelProperty("excel参数对象")
    private ExcelPara excelPara;

    /**
     * 父级物料编码集合
     */
    @ApiModelProperty("父级物料编码")
    private List<String> parentItemCodes;
}
