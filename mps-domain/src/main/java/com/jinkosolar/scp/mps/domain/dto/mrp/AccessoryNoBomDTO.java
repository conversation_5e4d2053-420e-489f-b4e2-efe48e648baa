package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.ibm.scp.common.api.annotation.Dict;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * [说明]材料搭配无BOM订单 DTO
 *
 * <AUTHOR>
 * @version 创建时间： 2024-06-21
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "材料搭配无BOM订单DTO对象", description = "DTO对象")
public class AccessoryNoBomDTO extends BaseDTO {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 事业部名称
     */
    @ApiModelProperty(value = "事业部名称")
    private String businessDivisionName;

    /**
     * 事业部
     */
    @ApiModelProperty(value = "事业部")
    private Long businessDivisionId;

    /**
     * 生产车间编码
     */
    @ApiModelProperty(value = "生产车间编码")
    private String workshopCode;

    /**
     * 生产车间
     */
    @ApiModelProperty(value = "生产车间")
    private Long workshopId;

    /**
     * 生产车间名称
     */
    @ApiModelProperty(value = "生产车间名称")
    private String workshopName;

    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    /**
     * 工厂ID
     */
    @ApiModelProperty(value = "工厂ID")
    private Long factoryId;

    /**
     * 工厂名称
     */
    @ApiModelProperty(value = "工厂名称")
    private String factoryCode;

    /**
     * 工作中心代码
     */
    @ApiModelProperty(value = "工作中心代码")
    private String workCenterCode;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterId;

    /**
     * 工作中心名称
     */
    @ApiModelProperty(value = "工作中心名称")
    private String workCenterName;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderCode;

    /**
     * 订单行号
     */
    @ApiModelProperty(value = "订单行号")
    private String orderLineId;

    /**
     * 订单数量
     */
    @ApiModelProperty(value = "订单数量")
    private BigDecimal orderQuantity;

    /**
     * 订单行数量
     */
    @ApiModelProperty(value = "订单行数量")
    private BigDecimal orderLineNumber;

    /**
     * 组件编码
     */
    @ApiModelProperty(value = "组件编码")
    private String componentCode;

    /**
     * 货好日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "货好日期")
    private LocalDate planDate;

    /**
     * 排产数量
     */
    @ApiModelProperty(value = "排产数量")
    private BigDecimal planQty;

    /**
     * 投产方案（材料搭配）
     */
    @ApiModelProperty(value = "投产方案（材料搭配）")
    private String productPlan;

    /**
     * 排产开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "排产开始时间")
    private LocalDateTime planStartTime;

    /**
     * 排产结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "排产结束时间")
    private LocalDateTime planEndTime;

    /**
     * 功率
     */
    @ApiModelProperty(value = "功率")
    private BigDecimal power;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 产品型号
     */
    @ApiModelProperty(value = "产品型号")
    private String productType;

    /**
     * 电池片尺寸
     */
    @ApiModelProperty(value = "电池片尺寸")
    private String cellSize;

    /**
     * 销售区域
     */
    @ApiModelProperty(value = "销售区域")
    private String salesArea;

    /**
     * 是否验货
     */
    @ApiModelProperty(value = "是否验货")
    private String customerInspectionFlag;

    /**
     * 是否监造
     */
    @ApiModelProperty(value = "是否监造")
    private String supervisionFlag;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String dataVersion;


    /**
     * 组件生产计划版本号
     */
    @ApiModelProperty(value = "组件生产计划版本号")
    private String planVersion;


    /**
     * 订单BOM版本号
     */
    @ApiModelProperty(value = "订单BOM版本号")
    private String bomVersion;

    /**
     * 组件生产计划DP版本号
     */
    @ApiModelProperty(value = "组件生产计划DP版本号")
    private String planDpVersion;

    /**
     * 模型编码
     */
    @ApiModelProperty(name = "模型编码", notes = "")
    private String modelCode;

    /**
     * 模型名称
     */
    @ApiModelProperty(name = "模型名称", notes = "")
    private String modelName;
}
