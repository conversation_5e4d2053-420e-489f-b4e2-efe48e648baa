package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.TranslateCustomBean;
import com.ibm.scp.common.api.component.VendorCustomBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("材料搭配结果数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessoryResultDTO extends BaseDTO implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 事业部
     */
    private Long businessDivisionId;

    @ApiModelProperty("事业部名称")
    private String businessDivisionName;

    @ApiModelProperty("事业部编码")
    private String businessDivisionCode;

    /**
     * 生产车间
     */
    @ApiModelProperty("生产车间" )
    private Long workshopId;

    @ApiModelProperty("生产车间编码")
    private String workshopCode;

    @ApiModelProperty("生产车间名称")
    private String workshopIdName;

    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂代码")
    private Long factoryId;

    @ApiModelProperty("工厂代码")
    private String factoryCode;

    @ApiModelProperty("工厂代码名称")
    private String factoryIdName;

    @ApiModelProperty("工厂名称")
    private String factoryName;

    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    private Long workCenterId;

    @ApiModelProperty("工作中心代码")
    private String workCenterCode;

    @ApiModelProperty("工作中心代码名称")
    private String workCenterIdName;

    /**
     * 订单代码
     */
    @ApiModelProperty("订单代码")
    private String orderCode;

    /**
     * 订单数量
     */
    @ApiModelProperty("订单数量")
    private BigDecimal orderQuantity;

    /**
     * 组件编码
     */
    @ApiModelProperty("组件编码")
    private String componentCode;

    /**
     * 物料类别
     */
    @ApiModelProperty("物料类别")
    private String materialCategory;

    /**
     * 物料大类
     */
    @ApiModelProperty("物料大类" )
    private Long materialCategory1;

    @ApiModelProperty("物料大类名称")
    private String materialCategory1Name;

    @ApiModelProperty("物料大类名称")
    private String materialCategory1Code;

    /**
     * 物料中类
     */
    @ApiModelProperty("物料中类")
    private Long materialCategory2;

    @ApiModelProperty("物料中类名称")
    private String materialCategory2Name;

    @ApiModelProperty("物料中类名称")
    private String materialCategory2Code;

    /**
     * 物料小类
     */
    @ApiModelProperty("物料小类")
    private Long materialCategory3;

    /**
     * 物料小类
     */
    @ApiModelProperty("物料小类")
    private Long materialCategory4;

    /**
     * 物料ID
     */
    @ApiModelProperty(name = "物料ID", notes = "")
    private Long itemId;

    /**
     * 物料编码
     */
    @ApiModelProperty(name = "物料编码", notes = "")
    private String itemCode;

    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    private String materialDesc;

    /**
     * 旧物料编码
     */
    @ApiModelProperty(name = "旧物料编码", notes = "")
    private String oldItemCode;

    /**
     * 物料需求日期
     */
    @ApiModelProperty("物料需求日期")
    private LocalDate demandDate;

    /**
     * 颜色
     */
    @Translate(DictType = "x")
    @ApiModelProperty("颜色")
    private Long color;

    /**
     * 颜色
     */
    @Translate(DictType = "x")
    @ApiModelProperty("颜色")
    private String colorName;

    /**
     * 材料规格/尺寸
     */
    @ApiModelProperty("材料规格/尺寸")
    private String accessorySpec;

    /**
     * 需求数量
     */
    @ApiModelProperty("需求数量")
    private BigDecimal demandQuantity;

    /**
     * 单位用量
     */
    @ApiModelProperty("单位用量")
    private BigDecimal unitQuantity;

    /**
     * 辅料单位
     */
    @ApiModelProperty("辅料单位" )
    private Long unitId;

    /**
     * 辅料单位名称
     */
    @ApiModelProperty(name = "辅料单位名称", notes = "")
    private String unit;

    @ApiModelProperty("辅料单位名称")
    private String unitIdName;

    /**
     * 供应商
     */
    @Translate(customBean = VendorCustomBean.class, customMethod = "getVendorByVendorId",
            from = {"code", "name"}, to = {"supplierCode", "supplierName"}, fields = {"supplierId"}, queryColumns = {"value"})
    @ApiModelProperty("供应商")
    private String supplierId;

    /**
     * 供应商编码
     */
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    private String supplierName;

    /**
     * dp_id
     */
    @ApiModelProperty("DPID")
    private String dpId;

    /**
     * dp版本号
     */
    @ApiModelProperty("dp版本号")
    private String dpVersion;

    /**
     * 组件计划ID
     */
    @ApiModelProperty("组件计划ID")
    private String moduleId;

    /**
     * 组件料号
     */
    @ApiModelProperty("组件料号")
    private String moduleNo;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;

    /**
     * 销售区域
     */
    @ApiModelProperty("销售区域")
    private String area;

    /**
     * 客户
     */
    @ApiModelProperty("客户")
    private Long customerId;

    /**
     * 产品族
     */
    @ApiModelProperty("产品族")
    private Long productFamilyId;

    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    private String isSupervision;

    /**
     * 是否搭配LRF
     */
    @ApiModelProperty("是否搭配LRF")
    private String lrfFlag;

    /**
     * 横竖装
     */
    @ApiModelProperty("横竖装")
    private String installType;

    /**
     * 线盒端子
     */
    @ApiModelProperty("线盒端子")
    private String boxTerminal;

    /**
     * 特殊单编号
     */
    @ApiModelProperty("特殊单编号")
    private String specialNo;

    /**
     * 组件尺寸
     */
    @ApiModelProperty("组件尺寸")
    private String moduleSize;

    /**
     * 组件计划数
     */
    @ApiModelProperty("组件计划数")
    private BigDecimal modulePlanQuantity;

    /**
     * 辅材类型-禁用
     */
    @ApiModelProperty("辅材类型-禁用")
    private Long accessoryType;

    /**
     * 组合号
     */
    @ApiModelProperty("组合号")
    private String accessoryGroup;

    /**
     * 辅材料号
     */
    @ApiModelProperty("辅材料号")
    private String accessoryNo;

    /**
     * 国内/国外
     */
    @ApiModelProperty("国内/国外")
    private String isOversea;

    /**
     * 辅材类型(BOM结构)
     */
    @ApiModelProperty("辅材类型(BOM结构)")
    private Long bomStructure;

    /**
     * 辅材类型(BOM结构)
     */
    @ApiModelProperty("辅材类型(BOM结构)名称")
    private String bomStructureName;

    /**
     * 毛需求数
     */
    @ApiModelProperty("毛需求数")
    private BigDecimal grossDemandQuantity;

    /**
     * 投产要求
     */
    @ApiModelProperty("投产要求")
    private String productRequire;

    /**
     * DP行ID
     */
    @ApiModelProperty("DP行ID")
    private Long dpLinesId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 新旧订单
     */
    @ApiModelProperty("新旧订单")
    private String newOrderFlag;

    /**
     * 特殊单厂商
     */
    @ApiModelProperty("特殊单厂商")
    private String specialSnVendor;

    /**
     * 认证厂商
     */
    @ApiModelProperty("认证厂商")
    private String certVendor;

    /**
     * 净需求数
     */
    @ApiModelProperty("净需求数")
    private BigDecimal netQuantity;

    /**
     * 功率预测-汇流条
     */
    @ApiModelProperty("功率预测-汇流条")
    private String beltBusBar;

    /**
     * 功率预测-互联条（焊带）
     */
    @ApiModelProperty("功率预测-互联条（焊带）")
    private String weldStrip;

    /**
     * 功率预测-后玻璃属性
     */
    @ApiModelProperty("功率预测-后玻璃属性")
    private String backGlassAttr;

    /**
     * 功率预测-后玻璃镀膜
     */
    @ApiModelProperty("功率预测-后玻璃镀膜")
    private String backGlassFilm;

    /**
     * 功率预测-前玻璃属性
     */
    @ApiModelProperty("功率预测-前玻璃属性")
    private String frontGlassAttr;

    /**
     * 功率预测-前玻璃镀膜
     */
    @ApiModelProperty("功率预测-前玻璃镀膜")
    private String frontGlassFilm;

    /**
     * 功率预测-后EVA属性
     */
    @ApiModelProperty("功率预测-后EVA属性")
    private String backEvaAttr;

    /**
     * 型材信息
     */
    @ApiModelProperty("型材信息")
    private String itemAttribute47;

    /**
     * 认证国家
     */
    @ApiModelProperty("认证国家")
    private String country;

    /**
     * 区域
     */
    @ApiModelProperty("区域")
    private String areaNo;

    /**
     * 气候型策略
     */
    @ApiModelProperty("气候型策略")
    private String itemAttribute14;

    /**
     * 月份
     */
    @ApiModelProperty("月份")
    private String month;

    /**
     * 是否缺结构
     */
    @ApiModelProperty("是否缺结构")
    private String notice;

    /**
     * 是否继承历史搭配
     */
    @ApiModelProperty("是否继承历史搭配")
    private String noticeHistory;

    /**
     * 预测接线盒子型号
     */
    @ApiModelProperty("预测接线盒子型号")
    private String material21;

    /**
     * 接线盒子型号（LOV后的值）
     */
    @ApiModelProperty("接线盒子型号（LOV后的值）")
    private String itemAttribute10;

    /**
     * 线缆长度（LOV前的值）
     */
    @ApiModelProperty("线缆长度（LOV前的值）")
    private String originItemAttribute7;

    /**
     * 订单导入时间
     */
    @ApiModelProperty("订单导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime importTime;

    /**
     * 不同批次的版本时间
     */
    @ApiModelProperty("不同批次的版本时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime batchVersionTime;

    /**
     * UL
     */
    @ApiModelProperty("UL")
    private String ul;

    /**
     * 是否缺结构
     */
    @ApiModelProperty("是否缺结构")
    private String lackOfStructureFlag;

    /**
     * 损耗率
     */
    @ApiModelProperty("损耗率")
    private String lossRate;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute1;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute2;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute3;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute4;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute5;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute6;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute7;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute8;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute9;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute10;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute11;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute12;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute13;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute14;

    /**
     * 扩展属性
     */
    @Translate(DictType = "x")
    @ApiModelProperty("扩展属性")
    private Long attribute15;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute1Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute2Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute3Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute4Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute5Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute6Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute7Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute8Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute9Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute10Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute11Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute12Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute13Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute14Name;

    /**
     * 扩展属性
     */
    @ApiModelProperty("扩展属性")
    private String attribute15Name;

    /**
     * 版本
     */
    @ApiModelProperty("版本")
    private String dataVersion;

    /**
     * 来源类型
     */
    @ApiModelProperty("来源类型")
    private Long sourceType;

    @ApiModelProperty("来源类型名称")
    private String sourceTypeName;

    /**
     * 订单行
     */
    @ApiModelProperty("订单行")
    private Long orderLineId;

    /**
     * 产品结构
     */
    @ApiModelProperty("产品结构")
    private Long structureId;

    @ApiModelProperty("产品结构")
    private String structureIdName;

    @ApiModelProperty("产品结构")
    private String structureName;

    /**
     * 计划开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划开始日期")
    private LocalDateTime planStartTime;

    /**
     * 计划结束日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "计划结束日期")
    private LocalDateTime planEndTime;

    /**
     * 厂家
     */
    @ApiModelProperty(name = "厂家", notes = "")
    private String manufacturer;

    @ApiModelProperty(name = "需求来源", notes = "")
    private String demandSource;

    @ApiModelProperty(name = "产品结构排序号", notes = "")
    private Integer structureSortNum;

    @ApiModelProperty(name = "排产行ID", notes = "")
    private String productLineId;

    @ApiModelProperty(name = "排产版本号", notes = "")
    private String planVersion;

    @ApiModelProperty(name = "是否订单BOM外材料", notes = "")
    private String specifyFlag;

    @ApiModelProperty(name = "是否订单BOM外材料", notes = "")
    private String specifyFlagName;

    @ApiModelProperty("产品型号")
    private String productModel;

    @ApiModelProperty(name = "aps排产日期", notes = "")
    private LocalDate apsPlanDate;

    /**
     * 供应商品牌
     */
    @ApiModelProperty(name = "供应商品牌", notes = "")
    private String supplierBrand;

    /**
     * 模型类型
     */
    @ApiModelProperty(name = "模型类型", notes = "")
    private String modelType;

    /**
     * 认证候补
     */
    @ApiModelProperty(name = "认证候补", notes = "")
    private String attribute50;

    /**
     * 材料搭配BOM组标识
     */
    @ApiModelProperty(name = "材料搭配BOM组标识", notes = "")
    private String diffBomGroup;

    /**
     * 库存版本
     */
    @ApiModelProperty(name = "库存版本", notes = "")
    private String inventoryVersion;


    private String vendorFlag;

    /**
     * 模型编码
     */
    @ApiModelProperty(name = "模型编码", notes = "")
    private String modelCode;

    /**
     * 模型名称
     */
    @ApiModelProperty(name = "模型名称", notes = "")
    private String modelName;

    /**
     * 单双玻
     */
    @ApiModelProperty(name = "单双玻", notes = "")
    private String productSpec;

    /**
     * 确认标识
     */
    @ApiModelProperty(name = "确认标识", notes = "")
    private String confirmFlag;

    /**
     * 来源版本
     */
    @ApiModelProperty(name = "来源版本", notes = "")
    private String sourceVersion;

    /**
     * 片串数（版型）
     */
    @ApiModelProperty(name = "片串数（版型）", notes = "")
    private String filmQuantity;


    /**
     * 投产方案（材料搭配）
     */
    @ApiModelProperty(name = "投产方案（材料搭配）", notes = "")
    private String productplan;

    @ApiModelProperty(name = "订单行数量", notes = "")
    private BigDecimal orderLineNumber;

    @ApiModelProperty(name = "是否验货", notes = "")
    private String isInspectGoods;

    @ApiModelProperty(name = "销售区域", notes = "")
    private String salesArea;

    @ApiModelProperty(name = "电池片尺寸", notes = "")
    private String cellSize;

    @ApiModelProperty(name = "功率", notes = "")
    private BigDecimal power;

    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    private String planLayout;

    /**
     * 区域
     */
    @ApiModelProperty("区域")
    private Long areaId;

    /**
     * 不符合材料搭配标识
     */
    @ApiModelProperty(name = "不符合材料搭配标识", notes = "")
    private String noMatchDesc;

    /**
     * 物料中类权限标识
     */
    @ApiModelProperty(name = "物料中类权限标识", notes = "")
    private String category2Privilege;

    /**
     * 是否历史材料搭配结果
     */
    @ApiModelProperty(name = "是否历史材料搭配结果", notes = "")
    private String historyFlag;

    /**
     * 组件BOM基地分类
     */
    @ApiModelProperty(value = "组件BOM基地分类")
    private Long moduleBaseTypeId;

    /**
     * 五瓦分档
     */
    @ApiModelProperty("五瓦分档")
    private String fiveWattGrade;

    /**
     * 指定料号分组key：如果有五瓦分档则为五瓦分档，否则为订单号+行号
     */
    @ApiModelProperty("指定料号分组key")
    private String appointGroup;

    /**
     * 导线长度
     */
    @Translate(DictType = "x")
    @ApiModelProperty(name = "导线长度", notes = "")
    private String attribute16;

    /**
     * 导线长度
     */
    @ApiModelProperty(name = "导线长度", notes = "")
    private String attribute16Name;

    /**
     * MRB单OA单据号
     */
    @ApiModelProperty(name = "是否是MRP单", notes = "")
    private String mrbFlag;

    /**
     * MRB单OA单据号
     */
    @ApiModelProperty(name = "MRB单OA单据号", notes = "")
    private String mrbOaReceiptNo;

    /**
     * 是否继承
     */
    @ApiModelProperty(name = "是否继承", notes = "")
    private String extendsFlag;


    /**
     * 验证标识 仅当为N时验证不通过
     */
    @ApiModelProperty(name = "验证标识 仅当为N时验证不通过", notes = "")
    private String verifyFlag;

    @ApiModelProperty("验证标识名称")
    private String verifyFlagName;

    /**
     * 验证失败原因
     */
    @ApiModelProperty(name = "验证失败原因 LOV：MRP.COLLOCATION_RULE", notes = "")
    private String reasonForVerifyFailure;

    @ApiModelProperty("验证失败原因名称")
    private String reasonForVerifyFailureName;

    /**
     * 物料编码修改标识 为Y表示有修改
     */
    @ApiModelProperty(name = "物料编码修改标识 为Y表示有修改", notes = "")
    private String modifyFlag;

    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "排产计划版本开始日期")
    private String planVersionStartDate;
    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "排产计划版本结束日期")
    private String planVersionEndDate;
    
    /**
     * 版本开始日期
     */
    @ApiModelProperty(value = "排产计划版本结束日期")
    private String productionType;

    /**
     * 需再调整物料
     */
    @ApiModelProperty(value = "需再调整物料")
    private String needAdjustFlag;

    @ApiModelProperty("创建人")
    @Translate(
            customBean = TranslateCustomBean.class,
            customMethod = "getUserNamesByIds",
            from = {"userId"},
            to = {"createdByUserName"}
    )
    private String createdByUser;
    @ApiModelProperty("创建人名称")
    private String createdByUserName;
    @ApiModelProperty("创建时间")
    private LocalDateTime createdByUserTime;
    @ApiModelProperty("更新人")
    @Translate(
            customBean = TranslateCustomBean.class,
            customMethod = "getUserNamesByIds",
            from = {"userId"},
            to = {"updatedByUserName"}
    )
    private String updatedByUser;
    @ApiModelProperty("更新人名称")
    private String updatedByUserName;
    @ApiModelProperty("更新时间")
    private LocalDateTime updatedByUserTime;

    /**
     * 按订单继承标识
     */
    @ApiModelProperty(name = "按订单继承标识", notes = "")
    private String orderExtendsFlag;


    @ApiModel("选择辅料下拉")
    @Data
    public static class ChangeItemVO {
        @ApiModelProperty(value = "辅材料号")
        private String accessoryNo;

        @ApiModelProperty(value = "辅材料号描述")
        private String accessoryNoDesc;
    }

    @Data
    @ApiModel(value = "修改搭配结果,保存参数", description = "保存参数")
    public static class ChangeItemSaveVO {

        @ApiModelProperty(value = "AccessoryResult的Id")
        @NotNull(message = "必须输入Id")
        private Long id;

        @ApiModelProperty(value = "辅材料号")
        @NotNull(message = "必须选择一个辅材料号")
        private String accessoryNo;

        @ApiModelProperty(value = "开始时间")
        private LocalDate startDate;

        @ApiModelProperty(value = "结束时间")
        private LocalDate endDate;

    }

    /**
     * 3.1没有五瓦分档订单按原逻辑工厂+车间+工作中心+订单号+订单行号进行合并
     * 3.2有五瓦分档订单按工厂+车间+工作中心+五瓦分档+组合号进行合并
     */
    public void updateAppointGroup() {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(this.getFiveWattGrade())) {
            this.setAppointGroup(this.getFiveWattGrade() + "_" + this.getAccessoryGroup());
        } else {
            this.setAppointGroup(this.getOrderCode() + "_" + this.getOrderLineId());
        }
    }

    public String groupedKeyForPush() {
        return org.apache.commons.lang3.StringUtils.join(new Object[]{this.getFactoryId(), this.getWorkshopId(), this.getWorkCenterId(), this.getAppointGroup()}, ":");
    }
}