package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@ApiModel("数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)  
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessoryResultHistoryLatestDTO extends BaseDTO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty("主键")
    @ExcelProperty(value = "主键")  
    private Long id;
    /**
     * 事业部名称
     */
    @ApiModelProperty("事业部名称")
    @ExcelProperty(value = "事业部名称")  
    private String businessDivisionName;
    /**
     * 事业部
     */
    @ApiModelProperty("事业部")
    @ExcelProperty(value = "事业部")  
    private Long businessDivisionId;
    /**
     * 生产车间名称
     */
    @ApiModelProperty("生产车间名称")
    @ExcelProperty(value = "生产车间名称")  
    private String workshopCode;
    /**
     * 生产车间
     */
    @ApiModelProperty("生产车间")
    @ExcelProperty(value = "生产车间")  
    private Long workshopId;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")  
    private String factoryName;
    /**
     * 工厂ID
     */
    @ApiModelProperty("工厂ID")
    @ExcelProperty(value = "工厂ID")  
    private Long factoryId;
    /**
     * 工厂名称
     */
    @ApiModelProperty("工厂名称")
    @ExcelProperty(value = "工厂名称")  
    private String factoryCode;
    /**
     * 工作中心代码
     */
    @ApiModelProperty("工作中心代码")
    @ExcelProperty(value = "工作中心代码")  
    private String workCenterCode;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")  
    private Long workCenterId;
    /**
     * 订单号
     */
    @ApiModelProperty("订单号")
    @ExcelProperty(value = "订单号")  
    private String orderCode;
    /**
     * 订单数量
     */
    @ApiModelProperty("订单数量")
    @ExcelProperty(value = "订单数量")  
    private BigDecimal orderQuantity;
    /**
     * 组件编码
     */
    @ApiModelProperty("组件编码")
    @ExcelProperty(value = "组件编码")  
    private String componentCode;
    /**
     * 物料类别
     */
    @ApiModelProperty("物料类别")
    @ExcelProperty(value = "物料类别")  
    private String materialCategory;
    /**
     * 物料大类
     */
    @ApiModelProperty("物料大类")
    @ExcelProperty(value = "物料大类")  
    private Long materialCategory1;
    /**
     * 物料中类
     */
    @ApiModelProperty("物料中类")
    @ExcelProperty(value = "物料中类")  
    private Long materialCategory2;
    /**
     * 物料小类
     */
    @ApiModelProperty("物料小类")
    @ExcelProperty(value = "物料小类")  
    private Long materialCategory3;
    /**
     * 物料小类
     */
    @ApiModelProperty("物料小类")
    @ExcelProperty(value = "物料小类")  
    private Long materialCategory4;
    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    @ExcelProperty(value = "物料编码")  
    private String itemCode;
    /**
     * 物料ID
     */
    @ApiModelProperty("物料ID")
    @ExcelProperty(value = "物料ID")  
    private Long itemId;
    /**
     * 物料描述
     */
    @ApiModelProperty("物料描述")
    @ExcelProperty(value = "物料描述")  
    private String materialDesc;
    /**
     * 物料需求日期
     */
    @ApiModelProperty("物料需求日期")
    @ExcelProperty(value = "物料需求日期")  
    private LocalDate demandDate;
    /**
     * 颜色
     */
    @ApiModelProperty("颜色")
    @ExcelProperty(value = "颜色")  
    private String color;
    /**
     * 材料规格/尺寸
     */
    @ApiModelProperty("材料规格/尺寸")
    @ExcelProperty(value = "材料规格/尺寸")  
    private String accessorySpec;
    /**
     * 需求数量
     */
    @ApiModelProperty("需求数量")
    @ExcelProperty(value = "需求数量")  
    private BigDecimal demandQuantity;
    /**
     * 单位用量
     */
    @ApiModelProperty("单位用量")
    @ExcelProperty(value = "单位用量")  
    private BigDecimal unitQuantity;
    /**
     * 辅料单位
     */
    @ApiModelProperty("辅料单位")
    @ExcelProperty(value = "辅料单位")  
    private String unit;
    /**
     * 辅料单位ID
     */
    @ApiModelProperty("辅料单位ID")
    @ExcelProperty(value = "辅料单位ID")  
    private Long unitId;
    /**
     * 供应商
     */
    @ApiModelProperty("供应商")
    @ExcelProperty(value = "供应商")  
    private Long supplierId;
    /**
     * 供应商编码
     */
    @ApiModelProperty("供应商编码")
    @ExcelProperty(value = "供应商编码")  
    private String supplierCode;
    /**
     * dp_id
     */
    @ApiModelProperty("dp_id")
    @ExcelProperty(value = "dp_id")  
    private String dpId;
    /**
     * 组件计划ID
     */
    @ApiModelProperty("组件计划ID")
    @ExcelProperty(value = "组件计划ID")  
    private String moduleId;
    /**
     * 组件料号
     */
    @ApiModelProperty("组件料号")
    @ExcelProperty(value = "组件料号")  
    private String moduleNo;
    /**
     * 描述
     */
    @ApiModelProperty("描述")
    @ExcelProperty(value = "描述")  
    private String description;
    /**
     * 销售区域
     */
    @ApiModelProperty("销售区域")
    @ExcelProperty(value = "销售区域")  
    private String area;
    /**
     * 客户
     */
    @ApiModelProperty("客户")
    @ExcelProperty(value = "客户")  
    private Long customerId;
    /**
     * 产品族
     */
    @ApiModelProperty("产品族")
    @ExcelProperty(value = "产品族")  
    private Long productFamilyId;
    /**
     * 是否监造
     */
    @ApiModelProperty("是否监造")
    @ExcelProperty(value = "是否监造")  
    private String isSupervision;
    /**
     * 是否验货
     */
    @ApiModelProperty("是否验货")
    @ExcelProperty(value = "是否验货")  
    private String isInspectGoods;
    /**
     * 销售区域
     */
    @ApiModelProperty("销售区域")
    @ExcelProperty(value = "销售区域")  
    private String salesArea;
    /**
     * 电池片尺寸
     */
    @ApiModelProperty("电池片尺寸")
    @ExcelProperty(value = "电池片尺寸")  
    private String cellSize;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")  
    private String productModel;
    /**
     * 是否搭配LRF
     */
    @ApiModelProperty("是否搭配LRF")
    @ExcelProperty(value = "是否搭配LRF")  
    private String lrfFlag;
    /**
     * 横竖装
     */
    @ApiModelProperty("横竖装")
    @ExcelProperty(value = "横竖装")  
    private String installType;
    /**
     * 线盒端子
     */
    @ApiModelProperty("线盒端子")
    @ExcelProperty(value = "线盒端子")  
    private String boxTerminal;
    /**
     * 特殊单编号
     */
    @ApiModelProperty("特殊单编号")
    @ExcelProperty(value = "特殊单编号")  
    private String specialNo;
    /**
     * 组件尺寸
     */
    @ApiModelProperty("组件尺寸")
    @ExcelProperty(value = "组件尺寸")  
    private String moduleSize;
    /**
     * 组件计划数
     */
    @ApiModelProperty("组件计划数")
    @ExcelProperty(value = "组件计划数")  
    private BigDecimal modulePlanQuantity;
    /**
     * 辅材类型-禁用
     */
    @ApiModelProperty("辅材类型-禁用")
    @ExcelProperty(value = "辅材类型-禁用")  
    private Long accessoryType;
    /**
     * 材料组
     */
    @ApiModelProperty("材料组")
    @ExcelProperty(value = "材料组")  
    private String accessoryGroup;
    /**
     * 辅材料号
     */
    @ApiModelProperty("辅材料号")
    @ExcelProperty(value = "辅材料号")  
    private String accessoryNo;
    /**
     * 国内/国外
     */
    @ApiModelProperty("国内/国外")
    @ExcelProperty(value = "国内/国外")  
    private String isOversea;
    /**
     * 辅材类型(BOM结构)
     */
    @ApiModelProperty("辅材类型(BOM结构)")
    @ExcelProperty(value = "辅材类型(BOM结构)")  
    private Long bomStructure;
    /**
     * 毛需求数
     */
    @ApiModelProperty("毛需求数")
    @ExcelProperty(value = "毛需求数")  
    private BigDecimal grossDemandQuantity;
    /**
     * 投产要求
     */
    @ApiModelProperty("投产要求")
    @ExcelProperty(value = "投产要求")  
    private String productRequire;
    /**
     * DP行ID
     */
    @ApiModelProperty("DP行ID")
    @ExcelProperty(value = "DP行ID")  
    private Long dpLinesId;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称")  
    private String customerName;
    /**
     * 功率
     */
    @ApiModelProperty("功率")
    @ExcelProperty(value = "功率")  
    private BigDecimal power;
    /**
     * 新旧订单
     */
    @ApiModelProperty("新旧订单")
    @ExcelProperty(value = "新旧订单")  
    private String newOrderFlag;
    /**
     * 特殊单厂商
     */
    @ApiModelProperty("特殊单厂商")
    @ExcelProperty(value = "特殊单厂商")  
    private String specialSnVendor;
    /**
     * 认证厂商
     */
    @ApiModelProperty("认证厂商")
    @ExcelProperty(value = "认证厂商")  
    private String certVendor;
    /**
     * 净需求数
     */
    @ApiModelProperty("净需求数")
    @ExcelProperty(value = "净需求数")  
    private BigDecimal netQuantity;
    /**
     * 功率预测-汇流条
     */
    @ApiModelProperty("功率预测-汇流条")
    @ExcelProperty(value = "功率预测-汇流条")  
    private String beltBusBar;
    /**
     * 功率预测-互联条（焊带）
     */
    @ApiModelProperty("功率预测-互联条（焊带）")
    @ExcelProperty(value = "功率预测-互联条（焊带）")  
    private String weldStrip;
    /**
     * 功率预测-后玻璃属性
     */
    @ApiModelProperty("功率预测-后玻璃属性")
    @ExcelProperty(value = "功率预测-后玻璃属性")  
    private String backGlassAttr;
    /**
     * 功率预测-后玻璃镀膜
     */
    @ApiModelProperty("功率预测-后玻璃镀膜")
    @ExcelProperty(value = "功率预测-后玻璃镀膜")  
    private String backGlassFilm;
    /**
     * 功率预测-前玻璃属性
     */
    @ApiModelProperty("功率预测-前玻璃属性")
    @ExcelProperty(value = "功率预测-前玻璃属性")  
    private String frontGlassAttr;
    /**
     * 功率预测-前玻璃镀膜
     */
    @ApiModelProperty("功率预测-前玻璃镀膜")
    @ExcelProperty(value = "功率预测-前玻璃镀膜")  
    private String frontGlassFilm;
    /**
     * 功率预测-后EVA属性
     */
    @ApiModelProperty("功率预测-后EVA属性")
    @ExcelProperty(value = "功率预测-后EVA属性")  
    private String backEvaAttr;
    /**
     * 型材信息
     */
    @ApiModelProperty("型材信息")
    @ExcelProperty(value = "型材信息")  
    private String itemAttribute47;
    /**
     * 认证国家
     */
    @ApiModelProperty("认证国家")
    @ExcelProperty(value = "认证国家")  
    private String country;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")  
    private String areaNo;
    /**
     * 气候型策略
     */
    @ApiModelProperty("气候型策略")
    @ExcelProperty(value = "气候型策略")  
    private String itemAttribute14;
    /**
     * 月份
     */
    @ApiModelProperty("月份")
    @ExcelProperty(value = "月份")  
    private String month;
    /**
     * 是否缺结构
     */
    @ApiModelProperty("是否缺结构")
    @ExcelProperty(value = "是否缺结构")  
    private String notice;
    /**
     * 是否继承历史搭配
     */
    @ApiModelProperty("是否继承历史搭配")
    @ExcelProperty(value = "是否继承历史搭配")  
    private String noticeHistory;
    /**
     * 预测接线盒子型号
     */
    @ApiModelProperty("预测接线盒子型号")
    @ExcelProperty(value = "预测接线盒子型号")  
    private String material21;
    /**
     * 接线盒子型号（LOV后的值）
     */
    @ApiModelProperty("接线盒子型号（LOV后的值）")
    @ExcelProperty(value = "接线盒子型号（LOV后的值）")  
    private String itemAttribute10;
    /**
     * 线缆长度（LOV前的值）
     */
    @ApiModelProperty("线缆长度（LOV前的值）")
    @ExcelProperty(value = "线缆长度（LOV前的值）")  
    private String originItemAttribute7;
    /**
     * 订单导入时间
     */
    @ApiModelProperty("订单导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "订单导入时间")  
    private LocalDateTime importTime;
    /**
     * 不同批次的版本时间
     */
    @ApiModelProperty("不同批次的版本时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "不同批次的版本时间")  
    private LocalDateTime batchVersionTime;
    /**
     * UL
     */
    @ApiModelProperty("UL")
    @ExcelProperty(value = "UL")  
    private String ul;
    /**
     * 是否缺结构
     */
    @ApiModelProperty("是否缺结构")
    @ExcelProperty(value = "是否缺结构")  
    private String lackOfStructureFlag;
    /**
     * 损耗率
     */
    @ApiModelProperty("损耗率")
    @ExcelProperty(value = "损耗率")  
    private String lossRate;
    /**
     * 扩展属性,尺寸
     */
    @ApiModelProperty("扩展属性,尺寸")
    @ExcelProperty(value = "扩展属性,尺寸")  
    private String attribute1;
    /**
     * 扩展属性,材质
     */
    @ApiModelProperty("扩展属性,材质")
    @ExcelProperty(value = "扩展属性,材质")  
    private String attribute2;
    /**
     * 扩展属性,长
     */
    @ApiModelProperty("扩展属性,长")
    @ExcelProperty(value = "扩展属性,长")  
    private String attribute3;
    /**
     * 扩展属性，宽
     */
    @ApiModelProperty("扩展属性，宽")
    @ExcelProperty(value = "扩展属性，宽")  
    private String attribute4;
    /**
     * 扩展属性，厚
     */
    @ApiModelProperty("扩展属性，厚")
    @ExcelProperty(value = "扩展属性，厚")  
    private String attribute5;
    /**
     * 扩展属性,胶膜类型
     */
    @ApiModelProperty("扩展属性,胶膜类型")
    @ExcelProperty(value = "扩展属性,胶膜类型")  
    private String attribute6;
    /**
     * 扩展属性,结构
     */
    @ApiModelProperty("扩展属性,结构")
    @ExcelProperty(value = "扩展属性,结构")  
    private String attribute7;
    /**
     * 扩展属性,卡槽
     */
    @ApiModelProperty("扩展属性,卡槽")
    @ExcelProperty(value = "扩展属性,卡槽")  
    private String attribute8;
    /**
     * 扩展属性,截面B面
     */
    @ApiModelProperty("扩展属性,截面B面")
    @ExcelProperty(value = "扩展属性,截面B面")  
    private String attribute9;
    /**
     * 扩展属性,边框孔距
     */
    @ApiModelProperty("扩展属性,边框孔距")
    @ExcelProperty(value = "扩展属性,边框孔距")  
    private String attribute10;
    /**
     * 扩展属性,膜厚
     */
    @ApiModelProperty("扩展属性,膜厚")
    @ExcelProperty(value = "扩展属性,膜厚")  
    private String attribute11;
    /**
     * 扩展属性,截面C面
     */
    @ApiModelProperty("扩展属性,截面C面")
    @ExcelProperty(value = "扩展属性,截面C面")  
    private String attribute12;
    /**
     * 扩展属性,接线盒型号
     */
    @ApiModelProperty("扩展属性,接线盒型号")
    @ExcelProperty(value = "扩展属性,接线盒型号")  
    private String attribute13;
    /**
     * 扩展属性,连接器型号
     */
    @ApiModelProperty("扩展属性,连接器型号")
    @ExcelProperty(value = "扩展属性,连接器型号")  
    private String attribute14;
    /**
     * 扩展属性,工艺
     */
    @ApiModelProperty("扩展属性,工艺")
    @ExcelProperty(value = "扩展属性,工艺")  
    private String attribute15;
    /**
     * 版本
     */
    @ApiModelProperty("版本")
    @ExcelProperty(value = "版本")  
    private String dataVersion;
    /**
     * 来源类型
     */
    @ApiModelProperty("来源类型")
    @ExcelProperty(value = "来源类型")  
    private Long sourceType;
    /**
     * 订单行
     */
    @ApiModelProperty("订单行")
    @ExcelProperty(value = "订单行")  
    private Long orderLineId;
    /**
     * 产品结构id
     */
    @ApiModelProperty("产品结构id")
    @ExcelProperty(value = "产品结构id")  
    private Long structureId;
    /**
     * 产品结构名称
     */
    @ApiModelProperty("产品结构名称")
    @ExcelProperty(value = "产品结构名称")  
    private String structureName;
    /**
     * 片串数（版型）
     */
    @ApiModelProperty("片串数（版型）")
    @ExcelProperty(value = "片串数（版型）")  
    private String filmQuantity;
    /**
     * 投产方案（材料搭配）
     */
    @ApiModelProperty("投产方案（材料搭配）")
    @ExcelProperty(value = "投产方案（材料搭配）")  
    private String productplan;
    /**
     * 排产开始时间
     */
    @ApiModelProperty("排产开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产开始时间")  
    private LocalDateTime planStartTime;
    /**
     * 排产结束时间
     */
    @ApiModelProperty("排产结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "排产结束时间")  
    private LocalDateTime planEndTime;
    /**
     * 订单行数量
     */
    @ApiModelProperty("订单行数量")
    @ExcelProperty(value = "订单行数量")  
    private BigDecimal orderLineNumber;
    /**
     * 需求来源
     */
    @ApiModelProperty("需求来源")
    @ExcelProperty(value = "需求来源")  
    private String demandSource;
    /**
     * 排产行ID
     */
    @ApiModelProperty("排产行ID")
    @ExcelProperty(value = "排产行ID")  
    private String productLineId;
    /**
     * 排产计划发布版本
     */
    @ApiModelProperty("排产计划发布版本")
    @ExcelProperty(value = "排产计划发布版本")  
    private String planVersion;
    /**
     * APS排产日期
     */
    @ApiModelProperty("APS排产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "APS排产日期")  
    private LocalDateTime apsPlanDate;
    /**
     * DP版本号
     */
    @ApiModelProperty("DP版本号")
    @ExcelProperty(value = "DP版本号")  
    private String dpVersion;
    /**
     * 模型分类
     */
    @ApiModelProperty("模型分类")
    @ExcelProperty(value = "模型分类")  
    private String modelType;
    /**
     * 是否订单BOM外材料
     */
    @ApiModelProperty("是否订单BOM外材料")
    @ExcelProperty(value = "是否订单BOM外材料")  
    private String specifyFlag;
    /**
     * 认证候补标识
     */
    @ApiModelProperty("认证候补标识")
    @ExcelProperty(value = "认证候补标识")  
    private String attribute50;
    /**
     * 材料搭配BOM组标识
     */
    @ApiModelProperty("材料搭配BOM组标识")
    @ExcelProperty(value = "材料搭配BOM组标识")  
    private String diffBomGroup;
    /**
     * 供应商标识 Y是N否
     */
    @ApiModelProperty("供应商标识 Y是N否")
    @ExcelProperty(value = "供应商标识 Y是N否")  
    private String vendorFlag;
    /**
     * 库存版本
     */
    @ApiModelProperty("库存版本")
    @ExcelProperty(value = "库存版本")  
    private String inventoryVersion;
    /**
     * 
     */
    @ApiModelProperty("")
    @ExcelProperty(value = "")  
    private String supplierBrand;
    /**
     * 模型编码
     */
    @ApiModelProperty("模型编码")
    @ExcelProperty(value = "模型编码")  
    private String modelCode;
    /**
     * 模型名称
     */
    @ApiModelProperty("模型名称")
    @ExcelProperty(value = "模型名称")  
    private String modelName;
    /**
     * 单双玻
     */
    @ApiModelProperty("单双玻")
    @ExcelProperty(value = "单双玻")  
    private String productSpec;
    /**
     * 旧料号
     */
    @ApiModelProperty("旧料号")
    @ExcelProperty(value = "旧料号")  
    private String oldItemCode;
    /**
     * 确认标识
     */
    @ApiModelProperty("确认标识")
    @ExcelProperty(value = "确认标识")  
    private String confirmFlag;
    /**
     * 来源版本
     */
    @ApiModelProperty("来源版本")
    @ExcelProperty(value = "来源版本")  
    private String sourceVersion;
    /**
     * 计划版型
     */
    @ApiModelProperty("计划版型")
    @ExcelProperty(value = "计划版型")  
    private String planLayout;
    /**
     * 材料搭配结果不匹配描述
     */
    @ApiModelProperty("材料搭配结果不匹配描述")
    @ExcelProperty(value = "材料搭配结果不匹配描述")  
    private String noMatchDesc;
    /**
     * 区域
     */
    @ApiModelProperty("区域")
    @ExcelProperty(value = "区域")  
    private Long areaId;
    /**
     * 导线长度
     */
    @ApiModelProperty("导线长度")
    @ExcelProperty(value = "导线长度")  
    private String attribute16;
    /**
     * 五瓦分档
     */
    @ApiModelProperty("五瓦分档")
    @ExcelProperty(value = "五瓦分档")  
    private String fiveWattGrade;
    /**
     * 材料搭配是否继承标识
     */
    @ApiModelProperty("材料搭配是否继承标识")
    @ExcelProperty(value = "材料搭配是否继承标识")  
    private String extendsFlag;
    /**
     * MRB单OA单据号
     */
    @ApiModelProperty("MRB单OA单据号")
    @ExcelProperty(value = "MRB单OA单据号")  
    private String mrbOaReceiptNo;
    /**
     * 指定料号分组key：如果有五瓦分档则为五瓦分档，否则为订单号+行号
     */
    @ApiModelProperty("指定料号分组key：如果有五瓦分档则为五瓦分档，否则为订单号+行号")
    @ExcelProperty(value = "指定料号分组key：如果有五瓦分档则为五瓦分档，否则为订单号+行号")  
    private String appointGroup;
    /**
     * 验证标识 仅当为N时验证不通过
     */
    @ApiModelProperty("验证标识 仅当为N时验证不通过")
    @ExcelProperty(value = "验证标识 仅当为N时验证不通过")  
    private String verifyFlag;
    /**
     * 验证失败原因
     */
    @ApiModelProperty("验证失败原因")
    @ExcelProperty(value = "验证失败原因")  
    private String reasonForVerifyFailure;
    /**
     * 物料修改标识 为Y表示有修改
     */
    @ApiModelProperty("物料修改标识 为Y表示有修改")
    @ExcelProperty(value = "物料修改标识 为Y表示有修改")  
    private String modifyFlag;
    /**
     * 创建人(报表统计使用)
     */
    @ApiModelProperty("创建人(报表统计使用)")
    @ExcelProperty(value = "创建人(报表统计使用)")  
    private String createdByUser;
    /**
     * 创建时间(报表统计使用)
     */
    @ApiModelProperty("创建时间(报表统计使用)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "创建时间(报表统计使用)")  
    private LocalDateTime createdByUserTime;
    /**
     * 更新人(报表统计使用)
     */
    @ApiModelProperty("更新人(报表统计使用)")
    @ExcelProperty(value = "更新人(报表统计使用)")  
    private String updatedByUser;
    /**
     * 更新时间(报表统计使用)
     */
    @ApiModelProperty("更新时间(报表统计使用)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "更新时间(报表统计使用)")  
    private LocalDateTime updatedByUserTime;
    /**
     * 订单继承标识 为Y表示继承
     */
    @ApiModelProperty("订单继承标识 为Y表示继承")
    @ExcelProperty(value = "订单继承标识 为Y表示继承")  
    private String orderExtendsFlag;
}