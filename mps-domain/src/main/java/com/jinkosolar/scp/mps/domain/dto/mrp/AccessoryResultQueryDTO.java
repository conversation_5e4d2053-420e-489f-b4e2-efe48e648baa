package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import java.io.Serializable;
import java.time.LocalDateTime;


@ApiModel("材料搭配结果数据转换对象")
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessoryResultQueryDTO extends PageDTO implements Serializable {

    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * DP_ID
     */
    @ApiModelProperty(value = "DP_ID")
    private String dpId;
    /**
     * aps版本号
     */
    @ApiModelProperty(value = "aps版本号")
    private String versionNum;

    /**
     * 工作中心
     */
    @ApiModelProperty(value = "工作中心")
    private Long workCenterId;

    /**
     * 工厂代码
     */
    @ApiModelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 排产开始时间
     */
    @ApiModelProperty(value = "排产开始时间")
    private LocalDateTime scheduleStartDate;

}