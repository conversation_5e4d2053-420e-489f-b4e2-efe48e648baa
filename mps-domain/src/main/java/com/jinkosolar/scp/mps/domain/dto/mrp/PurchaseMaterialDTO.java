package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.component.BomItemCustomBean;
import com.ibm.scp.common.api.component.LovCustomBean;
import com.ibm.scp.common.api.component.TranslateCustomBean;
import com.ibm.scp.common.api.component.VendorCustomBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 采购基础数据 DTO
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "采购基础数据DTO对象", description = "DTO对象")
public class PurchaseMaterialDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @ExcelIgnore
    private Long id;

    /**
     * 工厂(Factory)
     */
    @ApiModelProperty(value = "工厂()")
    @ExcelIgnore
    private Long factoryId;

    @ApiModelProperty(value = "工厂名称")
    @ExcelIgnore
    private String factoryIdName;

    @ApiModelProperty(value = "工厂代码")
    @ExcelProperty(index = 0)
    private String factoryCode;

    /**
     * 物料分类(Item_Category)
     */
    @ApiModelProperty(value = "物料大类")
    @ExcelIgnore
    private Long itemCategoryMajId;


    @ApiModelProperty(value = "物料大类")
    @ExcelProperty(index = 2)
    private String itemCategoryMajIdName;

    @ApiModelProperty(value = "物料中类")
    @ExcelIgnore
    private Long itemCategorySubId;


    @ExcelIgnore

    @ApiModelProperty(value = "物料中类")
    @ExcelProperty(index = 3)
    @Translate(unTranslate = true,
            customBean = LovCustomBean.class, customMethod = "getItemCategoriesByNames",
            fields = {"itemCategorySubType", "itemCategorySubIdName", "itemCategoryMajId"}
            , to = {"itemCategorySubId"}, required = true)
    private String itemCategorySubIdName;

    /**
     * 规格(Specification)
     */
    @ApiModelProperty(value = "规格(Specification)")
    @ExcelIgnore
    @Translate(DictType = "Specification", to = {"materialSpecsIdName"})
    private Long materialSpecsId;

    @ApiModelProperty(value = "规格名称")
    @ExcelProperty(index = 4)
    private String materialSpecsIdName;

    /**
     * 物料编码(Item_Number)
     */
    @ApiModelProperty(value = "物料编码(Item_Number)")
    @ExcelIgnore
    @Translate(customBean = BomItemCustomBean.class, customMethod = "getBomItemsByFactoryIdAndItemId",
            from = {"itemCode", "itemDesc", "priUom"}, to = {"materialNumberCode", "materialNumberIdName", "priUom"}, fields = {"factoryId", "materialNumberId"}, queryColumns = {"factoryId", "itemId"})
    private Long materialNumberId;

    @ApiModelProperty(value = "物料名称")
    @ExcelIgnore
    private String materialNumberIdName;

    @ApiModelProperty(value = "物料编码")
    @ExcelProperty(index = 5)
    @Translate(unTranslate = true, customBean = BomItemCustomBean.class, customMethod = "getBomItemsByFactoryIdAndItemCode", required = true,
            from = {"itemId"}, to = {"materialNumberId"}, fields = {"factoryId", "materialNumberCode"}, queryColumns = {"factoryId", "itemCode"})
    private String materialNumberCode;

    @ApiModelProperty("物料单位")
    @ExcelIgnore
    private String priUom;

    /**
     * 供应商(Vendor_Name)
     */
    @ApiModelProperty(value = "供应商(Vendor_Name)")
    @ExcelIgnore
    @Translate(customBean = VendorCustomBean.class, customMethod = "getVendorByVendorId",
            from = {"code", "name"}, to = {"vendorCode", "vendorIdName"}, fields = {"vendorId"}, queryColumns = {"value"})
    private Long vendorId;

    @ApiModelProperty(value = "供应商名称")
    @ExcelIgnore
    private String vendorIdName;

    @ApiModelProperty("供应商编码")
    @ExcelProperty(index = 7)
    @Translate(unTranslate = true, customBean = VendorCustomBean.class, customMethod = "getVendorByVendorCode",
            from = {"value", "vendorBrand"}, to = {"vendorId", "vendorBrand"}, fields = {"vendorCode"}, queryColumns = {"code"}, required = true)
    private String vendorCode;

    /**
     * 采购提前期（天）
     */
    @ApiModelProperty(value = "采购提前期（天）")
    @ExcelProperty(index = 10)
    @Translate(unTranslate = true, required = true)
    private BigDecimal leadTime;

    /**
     * 最小下单量（天）
     */
    @ApiModelProperty(value = "最小下单量（天）")
    @ExcelProperty(index = 11)
    private BigDecimal minOrder;

    /**
     * 最小包装量（天）
     */
    @ApiModelProperty(value = "最小包装量（天）")
    @ExcelProperty(index = 12)
    @Translate(unTranslate = true, required = true)
    private BigDecimal minPack;

    /**
     * 整车数（天）
     */
    @ApiModelProperty(value = "整车数（天）")
    @ExcelProperty(index = 13)
    private BigDecimal carNum;

    @ApiModelProperty(value = "辅助单位")
    @ExcelProperty(index = 14)
    private String auxUom;

    /**
     * 辅助单位ID
     */
    @ApiModelProperty(value = "辅助单位ID")
    @ExcelIgnore
    private String auxUomId;

    /**
     * 租户号
     */
    @ApiModelProperty(value = "租户号")
    @ExcelIgnore
    private String tenantId;

    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁")
    @ExcelIgnore
    private Integer optCounter;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @ExcelIgnore
    private Integer isDeleted;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @ExcelIgnore
    @Translate(customBean = TranslateCustomBean.class, customMethod = "getUserNamesByIds", from = "userId", to = "createdByName")
    private String createdBy;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人")
    @ExcelIgnore
    private String createdByName;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @ExcelIgnore
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    @ExcelIgnore
    @Translate(customBean = TranslateCustomBean.class, customMethod = "getUserNamesByIds", from = "userId", to = "updatedByName")
    private String updatedBy;

    /**
     * 更新人名称
     */
    @ApiModelProperty(value = "创建人")
    @ExcelIgnore
    private String updatedByName;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @ExcelIgnore
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "翻译map")
    @ExcelIgnore
    private Map<String, Object> transMap = new HashMap<>();

    @ApiModelProperty(value = "身份标识")
    @ExcelIgnore
    private String token;

    /**
     * 是否合格供应商
     */
    @ApiModelProperty(value = "是否合格供应商")
    @ExcelProperty(index = 9)
    private String qualifiedVendorFlagName;

    @ApiModelProperty(value = "是否合格供应商")
    @ExcelIgnore
    private Long qualifiedVendorFlag;

    /**
     * 最后更新时间（接口传入）
     */
    @ApiModelProperty(value = "最后更新时间")
    @ExcelIgnore
    private LocalDateTime lastUpdatedTime;

    /**
     * 最后更新人（接口传入）
     */
    @ApiModelProperty(value = "最后更新人")
    @ExcelIgnore
    private String lastUpdatedBy;

    /**
     * 最后创建时间（接口传入）
     */
    @ApiModelProperty(value = "最后创建时间")
    @ExcelIgnore
    private LocalDateTime lastCreatedTime;

    /**
     * 最后创建人（接口传入）
     */
    @ApiModelProperty(value = "最后创建人")
    @ExcelIgnore
    private String lastCreatedBy;

    /**
     * 接口唯一标识（接口传入）
     */
    @ApiModelProperty(value = "接口唯一标识")
    @ExcelIgnore
    private String uniqueKey;

    @ApiModelProperty(value = "区域")
    @ExcelIgnore
    private Long areaId;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @ExcelIgnore
    private String areaIdName;

    @ExcelProperty(value = "序号")
    private Integer serialNo;

    /**
     * 供应商品牌
     */
    @ApiModelProperty(value = "供应商品牌")
    private String vendorBrand;
}
