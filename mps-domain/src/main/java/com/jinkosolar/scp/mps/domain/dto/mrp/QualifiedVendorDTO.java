package com.jinkosolar.scp.mps.domain.dto.mrp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ibm.scp.common.api.annotation.ExportExConvert;
import com.ibm.scp.common.api.annotation.Translate;
import com.ibm.scp.common.api.base.BaseDTO;
import com.ibm.scp.common.api.component.BomItemCustomBean;
import com.ibm.scp.common.api.component.VendorCustomBean;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * 合格供应商 DTO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "合格供应商DTO对象", description = "DTO对象")
public class QualifiedVendorDTO extends BaseDTO {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @Translate(DictType = LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE)
    private Long productFactoryId;
    @ApiModelProperty(value = "工厂")
    @ExcelProperty(value = "工厂")
    @Translate(unTranslate = true, DictType =LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE,required = true)
    private String productFactoryIdName;
    @ApiModelProperty(value = "工厂代码")
    @ExcelProperty(value = "工厂代码")
    private String productFactoryCode;
    /**
     * 物料分类
     */
    @ApiModelProperty(value = "物料大类")
    private Long itemCategoryMajId;
    @ApiModelProperty(value = "物料大类")
    @ExcelProperty(value = "物料大类")
    private String itemCategoryMajIdName;

    @ApiModelProperty(value = "物料中类")
    private Long itemCategorySubId;
    @ApiModelProperty(value = "物料中类")
    @ExcelProperty(value = "物料中类")
    private String itemCategorySubIdName;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @ExcelProperty(value = "物料编码")
    @Translate(customBean = BomItemCustomBean.class, customMethod = "getBomItemsByFactoryCodeAndItemCode",
            from = {"itemCode", "itemDesc"}, to = {"itemCategoryCode", "itemCategoryDesc"}, fields = {"productFactoryCode","itemCategoryCode"}, queryColumns = {"factoryCode","itemCode"})
    private String itemCategoryCode;

    @ApiModelProperty(value = "物料id")
    @ExcelProperty(value = "物料id")
    private Long itemId;

    /**
     * 物料描述
     */
    @ApiModelProperty(value = "物料描述")
    @ExcelProperty(value = "物料描述")
    private String itemCategoryDesc;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    @ExportExConvert(tableName = "sys_sap_supplier", fkColumnName = "id", valueColumnName = "suppliername")
    @Translate( customBean = VendorCustomBean.class, customMethod = "getVendorByVendorId",
            from = {"vendorBrand","code","name"},  fields = {"vendorId"}, queryColumns = {"value"}, to = {"vendorBrand","vendorCode","vendorIdName"})
    private Long vendorId;

    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商")
    @ExcelProperty(value = "供应商")
    private String vendorIdName;
    /**
     * 供应商品牌
     */
    @ApiModelProperty(value = "供应商品牌")
    @ExcelProperty(value = "供应商品牌")
    private String vendorBrand;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Long statusId;
    @ApiModelProperty(value = "状态名称")
    @ExcelProperty(value = "状态名称")
    private String statusIdName;
    /**
     * 多语言
     */
    @ApiModelProperty(value = "多语言")
    private String language;
    /**
     * 必须
     */
    @ApiModelProperty(value = "必须")
    private String isRequired;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    private String areaIdName;

    /** 创建人 */
    @ApiModelProperty(value = "创建人",notes = "")
    private String createdBy ;
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间",notes = "")
    private LocalDateTime createdTime ;
    /** 更新人 */
    @ApiModelProperty(value = "更新人",notes = "")
    private String updatedBy ;
    /** 更新时间 */
    @ApiModelProperty(value = "更新时间",notes = "")
    private LocalDateTime updatedTime ;
}
