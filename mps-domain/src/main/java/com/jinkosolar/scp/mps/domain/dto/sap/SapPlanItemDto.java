package com.jinkosolar.scp.mps.domain.dto.sap;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class SapPlanItemDto {
    /**
     * 物料清单项目编号
     */
    private String POSNR;
    /**
     * 物料编号
     */
    private String IDNRK;
    /**
     * 数量
     */
    private BigDecimal MENGE;
    /**
     * 基本计量单位
     */
    private String MEINS;
    /**
     * 替代项目：组
     */
    private String ALPGR;
    /**
     * 替代项目：策略
     */
    private String ALPST;
    /**
     * 替代项目：优先级
     */
    private BigDecimal ALPRF;
    /**
     * 以百分比表示的使用概率（备选项目）
     */
    private BigDecimal EWAHR;
}
