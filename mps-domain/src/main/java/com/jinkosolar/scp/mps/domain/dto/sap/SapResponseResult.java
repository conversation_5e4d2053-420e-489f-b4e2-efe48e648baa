package com.jinkosolar.scp.mps.domain.dto.sap;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SapResponseResult {
    @J<PERSON><PERSON>ield(name = "ZSCJY")
    private String  ZSCJY;
    @JSONField(name = "ZTYPE")
    private String  ZTYPE;
    @JSONField(name = "ZMSG")
    private String  ZMSG;
}
