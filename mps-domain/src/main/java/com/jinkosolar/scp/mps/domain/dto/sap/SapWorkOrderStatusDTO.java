package com.jinkosolar.scp.mps.domain.dto.sap;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SapWorkOrderStatusDTO {
    /**
     * 生产建议
     */
    @ApiModelProperty("生产建议")
    private String zscjy;
    /**
     * 组合号
     */
    @ApiModelProperty("组合号")
    private String zpkgnum;
    /**
     * 组合行
     */
    @ApiModelProperty("组合行")
    private Long zpkgitm;
    /**
     * 销售订单号
     */
    @ApiModelProperty("销售订单号")
    private String vbeln;
    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    private Long posnr;
    /**
     * 状态标识
     */
    @ApiModelProperty("状态标识")
    private String zbs;
}
