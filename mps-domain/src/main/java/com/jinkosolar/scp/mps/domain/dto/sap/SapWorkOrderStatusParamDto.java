package com.jinkosolar.scp.mps.domain.dto.sap;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.jinkosolar.scp.jip.api.dto.sap.IsBcInfo;
import com.jinkosolar.scp.mps.domain.dto.WorkOrderStatusDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SapWorkOrderStatusParamDto {
    @JSONField(name = "IS_BC_INFO")
    private IsBcInfo isBcInfo;
    @JSONField(name = "IT_DATA")
    private List<WorkOrderStatusDTO> itData= Lists.newArrayList();


}

