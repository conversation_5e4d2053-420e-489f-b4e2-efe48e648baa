package com.jinkosolar.scp.mps.domain.dto.sap;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import com.jinkosolar.scp.jip.api.dto.sap.IsBcInfo;
import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SapWorkOrderStatusResponseDto {

    @JSONField(name = "ES_BC_INFO")
    private EsBcInfo esBcInfo;
    @JSONField(name = "ET_RESULT")
    private List<SapResponseResult> etResult = Lists.newArrayList();

    @EqualsAndHashCode(callSuper = true)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EsBcInfo extends  IsBcInfo{

        @JSONField(name = "TYPE")
        private String type;

        @JSONField(name = "MSG")
        private String message;
    }

}
