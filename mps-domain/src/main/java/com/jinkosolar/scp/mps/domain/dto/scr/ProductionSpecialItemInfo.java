package com.jinkosolar.scp.mps.domain.dto.scr;

import com.ibm.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ProductionSpecialItemInfo", description = "")
@AllArgsConstructor
@NoArgsConstructor
public class ProductionSpecialItemInfo extends TokenDTO implements  Serializable {

    /** 特殊生产通知单号 */
    @ApiModelProperty(value = "特殊生产通知单号",notes = "")
    private String productionNoticeOrderNo ;

    /** 三级分类名称 */
    @ApiModelProperty(value = "三级分类名称",notes = "")
    private String thirdCategoryName ;

    /** 结构化内容 */
    @ApiModelProperty(value = "结构化内容",notes = "")
    private List<ProductionSpecialtemList> specialItemAttribute;
}
