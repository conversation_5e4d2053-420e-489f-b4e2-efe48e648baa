package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProductionSpecialtemList", description = "DTO对象")
public class ProductionSpecialtemList {

    @ApiModelProperty(value = "产品族",notes = "")
    private String productFamily;


    @ApiModelProperty(value = "结构化内容",notes = "")
    private List<String> specialItemAttributeValueList;
}
