package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrContractProductionNoticeOrderDTO参数", description = "生产通知单保存参数")
public class ScrContractProductionNoticeOrderDTO implements Serializable {

    private static final long serialVersionUID = 4938859848015941978L;

    /**
     * 特殊生产通知单ID
     */
    @ApiModelProperty(name = "特殊生产通知单ID")
    private Long productionNoticeOrderId;

    /**
     * 评审单ID
     */
    @ApiModelProperty(name = "评审单ID")
    private Long reviewOrderId;

    /**
     * 特殊生产通知单号
     */
    @ApiModelProperty(name = "特殊生产通知单号")
    private String productionNoticeOrderNo;

    /**
     * 特殊申请单ID
     */
    @ApiModelProperty(value = "特殊申请单ID")
    private Long reqOrderId;

    /**
     * 评审单号
     */
    @ApiModelProperty(value = "评审单号")
    private String reviewOrderNo;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String version;

    /**
     * 特殊申请单号
     */
    @ApiModelProperty(value = "特殊申请单号")
    private String reqOrderNo;

    /**
     * 地区
     */
    @ApiModelProperty(value = "地区")
    private String region;

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    private String nationality;

    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号")
    private String contractNo;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 销售员
     */
    @ApiModelProperty(value = "销售员")
    private String salesMan;

    /**
     * 合同量(WM)
     */
    @ApiModelProperty(value = "合同量(WM)")
    private String contractAmount;

    /**
     * 合同币种
     */
    @ApiModelProperty(value = "合同币种")
    private String contractCurrency;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    private String projectArea;

    /**
     * 功率需求
     */
    @ApiModelProperty(value = "功率需求")
    private String powerDemand;

    /**
     * 申请人
     */
    @ApiModelProperty(value = "申请人")
    private String reqName;

    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    private String reqTime;

    /**
     * 要求回复时间
     */
    @ApiModelProperty(value = "要求回复时间")
    private String askResponseTime;

    /**
     * 首评时间
     */
    @ApiModelProperty(value = "首评时间")
    private String firstReviewTime;

    /**
     * 终评时间
     */
    @ApiModelProperty(value = "终评时间")
    private String finalReviewTime;

    /**
     * 评审单版本;0待初评 1首评 2过程版 3终评版
     */
    @ApiModelProperty(value = "评审单版本")
    private Integer reviewVersion;

    /**
     * 是否监造验货
     */
    @ApiModelProperty(value = "是否监造验货")
    private String isSupervisionInspection;

    /**
     * 指定标片要求
     */
    @ApiModelProperty(value = "指定标片要求")
    private String isLabelRequire;

    /**
     * 是否排产
     */
    @ApiModelProperty(value = "是否排产")
    private String isSchedulingProduction;

    /**
     * 是否存在特殊项
     */
    @ApiModelProperty(value = "是否存在特殊项")
    private String isExistSpecialItem;

    /**
     * 量产信息
     */
    @ApiModelProperty(value = "量产信息;0都包含;1全部量产;2全部非量产")
    private Integer massProductionInfo;

    /**
     * 通知评审状态
     */
    @ApiModelProperty(value = "通知评审状态;0未通知;1已通知")
    private Integer noticeStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商机ID
     */
    @ApiModelProperty(value = "商机ID")
    private String businessOpportunityId;


    /**
     * 评审单产品信息
     */
    private List<ScrContractProductionProductInfoDTO> scrContractProductionProductInfoDTOList;

    /**
     * 评审单电池特殊项信息
     */
    // private List<ScrContractProductionSpecialItemDTO> scrContractProductionBattleSpecialItemDTOList;

    /**
     * 评审单材料特殊项信息
     */
    private List<ScrContractProductionSpecialItemDTO> scrContractProductionMaterialSpecialItemDTOList;

    /**
     * 评审单分类特殊项信息
     */
    private List<ScrContractProductionSpecialItemDTO> scrContractProductionCategorySpecialItemDTOList;

    /**
     * 评审单附加信息
     */
    // private List<ScrContractCommonFileDTO> scrContractCommonFileDTOList;

    /**
     * 材料搭配
     */
    private List<ScrMaterialCollocationDTO> scrMaterialCollocationDTOList;

    private Integer processType;

    private String bpmNo;

    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id")
    private String createdByUserId;
}
