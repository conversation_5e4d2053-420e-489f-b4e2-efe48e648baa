package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrContractProductionProductInfoDTO对象", description = "DTO对象")
public class ScrContractProductionProductInfoDTO implements Serializable {

    /**
     * 通知单产品信息ID
     */
    @ApiModelProperty(value = "通知单产品信息ID", notes = "")
    private Long productionProductId;
    /**
     * 关联通知单ID
     */
    @ApiModelProperty(value = "关联通知单ID", notes = "")
    private Long productionOrderId;
    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族", notes = "")
    private String productFamily;
    /**
     * 是否量产
     */
    @ApiModelProperty(value = "是否量产", notes = "")
    private String isMassProduction;
    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列", notes = "")
    private String productSeries;
    /**
     * 玻璃
     */
    @ApiModelProperty(value = "玻璃", notes = "")
    private String glass;
    /**
     * 边框
     */
    @ApiModelProperty(value = "边框", notes = "")
    private String frame;
    /**
     * 切半
     */
    @ApiModelProperty(value = "切半", notes = "")
    private String cut;
    /**
     * 预估总良损
     */
    @ApiModelProperty(value = "预估总良损", notes = "")
    private Double exceptGoodLoss;
    /**
     * 实际总良损
     */
    @ApiModelProperty(value = "实际总良损", notes = "")
    private Double realGoodLoss;
    /**
     * BOM套数
     */
    @ApiModelProperty(value = "BOM套数", notes = "")
    private String bomCount;
    /**
     * 最近交货日期
     */
    @ApiModelProperty(value = "最近交货日期", notes = "")
    private String recentDeliveryDate;
    /**
     * 是否固化
     */
    @ApiModelProperty(value = "是否固化", notes = "")
    private Integer isFixed;
}
