package com.jinkosolar.scp.mps.domain.dto.scr;

import com.ibm.scp.common.api.base.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 特殊生产通知单查询DTO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrContractProduction查询参数", description = "特殊生产通知单查询参数")
public class ScrContractProductionQueryDTO extends PageDTO implements Serializable {
    /**
     * 特殊通知单号
     */
    @ApiModelProperty(value = "评审单号", notes = "")
    private String productionNoticeNo;
    /**
     * 合同号
     */
    @ApiModelProperty(value = "合同号", notes = "")
    private String contractNo;
    /**
     * 特殊申请单号
     */
    @ApiModelProperty(value = "特殊申请单号", notes = "")
    private String reqOrderNo;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", notes = "")
    private String beginTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", notes = "")
    private String endTime;
    /**
     * 商机ID
     */
    @ApiModelProperty(value = "商机ID", notes = "")
    private String businessOpportunityId;

    @ApiModelProperty(value = "评审单号", notes = "")
    private String reviewOrderNo;
}
