package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrContractReviewSpecialItemAttributeDTO对象", description = "DTO对象")
public class ScrContractProductionSpecialItemAttributeDTO implements Serializable {

    /**
     * 通知单特殊项属性主键ID
     */
    @ApiModelProperty(value = "通知单特殊项属性主键ID")
    private Long productionSpecialItemAttributeId;
    /**
     * 通知单特殊项主键ID
     */
    @ApiModelProperty(value = "评审单特殊项主键ID", notes = "关联通知单材料主键ID")
    private Long productionSpecialItemId;
    /**
     * 特殊项属性
     */
    @ApiModelProperty(value = "特殊项属性")
    private String specialItemAttribute;
    /**
     * 特殊项属性名称
     */
    @ApiModelProperty(value = "特殊项属性名称")
    private String specialItemAttributeName;
    /**
     * 特殊项属性值
     */
    @ApiModelProperty(value = "特殊项属性值")
    private String specialItemAttributeValue;
    /**
     * 特殊项属性值名称
     */
    @ApiModelProperty(value = "特殊项属性值名称")
    private String specialItemAttributeValueName;
    /**
     * 其他值
     */
    @ApiModelProperty(value = "其他值")
    private String otherValue;
}
