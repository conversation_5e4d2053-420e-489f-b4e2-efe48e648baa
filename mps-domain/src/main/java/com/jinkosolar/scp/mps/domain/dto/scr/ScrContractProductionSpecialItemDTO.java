package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrContractProductionSpecialItemDTO参数", description = "生产通知单特殊项参数")
public class ScrContractProductionSpecialItemDTO implements Serializable {

    private static final long serialVersionUID = 9074446096739483706L;

    /**
     * 生产通知单特殊项表主键ID
     */
    @ApiModelProperty(name = "生产通知单特殊项表主键ID")
    private Long productionSpecialItemId;

    /**
     * 通知单ID
     */
    @ApiModelProperty(name = "通知单ID")
    private Long productionNoticeOrderId;

    /**
     * 通知单特殊项表主键ID集合
     */
    @ApiModelProperty(value = "通知单特殊项表主键ID集合")
    private List<Long> productionSpecialItemIds;

    /**
     * 评审单ID
     */
    @ApiModelProperty(value = "评审单ID")
    private Long reviewOrderId;

    /**
     * 产品信息ID
     */
    @ApiModelProperty(value = "产品信息ID")
    private List<String> productInfoIds;

    /**
     * 产品族列表
     */
    @ApiModelProperty(value = "产品族列表")
    private List<String> productFamilyList;

    /**
     * 产品信息
     */
    @ApiModelProperty(value = "产品信息")
    private String productInfo;

    /**
     * 一级分类ID
     */
    @ApiModelProperty(value = "一级分类ID")
    private String firstCategory;

    /**
     * 一级分类名称
     */
    @ApiModelProperty(value = "一级分类名称")
    private String firstCategoryName;

    /**
     * 二级分类ID
     */
    @ApiModelProperty(value = "二级分类ID")
    private String secondCategory;

    /**
     * 二级分类名称
     */
    @ApiModelProperty(value = "二级分类名称")
    private String secondCategoryName;

    /**
     * 三级分类
     */
    @ApiModelProperty(value = "三级分类")
    private String thirdCategory;

    /**
     * 三级分类名称
     */
    @ApiModelProperty(value = "三级分类名称")
    private String thirdCategoryName;

    /**
     * 调整后一级分类
     */
    @ApiModelProperty(value = "调整后一级分类")
    private String adjustFirstCategory;

    /**
     * 调整后二级分类
     */
    @ApiModelProperty(value = "调整后二级分类")
    private String adjustSecondCategory;

    /**
     * 调整后三级分类
     */
    @ApiModelProperty(value = "调整后三级分类")
    private String adjustThirdCategory;

    /**
     * 调整后一级分类名称
     */
    @ApiModelProperty(value = "调整后一级分类名称")
    private String adjustFirstCategoryName;

    /**
     * 调整后二级分类名称
     */
    @ApiModelProperty(value = "调整后二级分类名称")
    private String adjustSecondCategoryName;

    /**
     * 调整后三级分类名称
     */
    @ApiModelProperty(value = "调整后三级分类名称")
    private String adjustThirdCategoryName;

    /**
     * 天合指南
     */
    @ApiModelProperty(value = "天合指南")
    private String trinaGuide;

    /**
     * BOM结构
     */
    @ApiModelProperty(value = "BOM结构")
    private String bomStructure;

    /**
     * BOM结构名称
     */
    @ApiModelProperty(value = "BOM结构名称")
    private String bomStructureName;

    /**
     * 特殊条款号
     */
    @ApiModelProperty(value = "特殊条款号")
    private String specialClauseNo;

    /**
     * 客户要求
     */
    @ApiModelProperty(value = "客户要求")
    private String customerDemand;

    /**
     * 客户要求图片上传
     */
    @ApiModelProperty(value = "客户要求图片上传")
    private String customerDemandFile;

    /**
     * 天合标准
     */
    @ApiModelProperty(value = "天合标准")
    private String trinaStandard;

    /**
     * 应对方案
     */
    @ApiModelProperty(value = "应对方案")
    private String solutionPlan;

    /**
     * 评估结论
     */
    @ApiModelProperty(value = "评估结论")
    private String appraisalConclusion;

    /**
     * 所有者
     */
    @ApiModelProperty(value = "所有者")
    private String owner;

    /**
     * 良损率
     */
    @ApiModelProperty(value = "良损率")
    private Double goodLossRate;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String source;

    /**
     * 批次ID
     */
    @ApiModelProperty(value = "批次ID")
    private Long batchId;

    private ScrContractProductionSpecialItemAttributeDTO scrContractProductionSpecialItemAttributeDTO;

//    @ApiModelProperty(value = "产品族")
//    private List<String> productFamilyList;

    /**
     * 特殊项附件信息
     */
    // private List<ScrContractCommonFileDTO> scrContractCommonFileDTOList;

    /**
     * 良损信息实体
     */
    // private List<ScrContractReviewSpecialItemGoodLossDTO> scrContractSpecialItemGoodLossDTOS;
}
