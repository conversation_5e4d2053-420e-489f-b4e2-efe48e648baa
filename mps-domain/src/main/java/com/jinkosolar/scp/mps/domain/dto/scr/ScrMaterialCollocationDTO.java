package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 材料搭配
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrMaterialCollocationDTO对象", description = "DTO对象")
public class ScrMaterialCollocationDTO implements Serializable {

    /**
     * 材料搭配ID
     */
    @ApiModelProperty(name = "材料搭配ID", notes = "")
    private Long materialCollocationId;

    /**
     * 产品ID
     */
    @ApiModelProperty(name = "产品ID", notes = "")
    private List<String> productIds;

    /**
     * 材料搭配组详情
     */
    private List<ScrMaterialCollocationGroupDTO> scrMaterialCollocationGroupDTOList;
}
