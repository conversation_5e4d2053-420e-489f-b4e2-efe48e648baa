package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrMaterialCollocationDetailDTO对象", description = "DTO对象")
public class ScrMaterialCollocationDetailDTO implements Serializable {

    /**
     * 材料大类ID
     */
    @ApiModelProperty(name = "材料大类ID", notes = "")
    private String materialId;

    @ApiModelProperty(name = "材料大类名称", notes = "")
    private String materialName;

    /**
     * 材料搭配详情
     */
    private List<ScrMaterialCollocationTypeDetailDTO> scrMaterialCollocationTypeDetailDTOList;
}
