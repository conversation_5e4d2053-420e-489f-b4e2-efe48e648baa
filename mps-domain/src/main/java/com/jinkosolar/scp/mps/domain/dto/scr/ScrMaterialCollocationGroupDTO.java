package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 材料搭配组
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrMaterialCollocationGroupDTO对象", description = "DTO对象")
public class ScrMaterialCollocationGroupDTO {

    /**
     * 材料搭配详情
     */
    private List<ScrMaterialCollocationDetailDTO> scrMaterialCollocationDetailDTOList;
}
