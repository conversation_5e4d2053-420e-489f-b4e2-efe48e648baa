package com.jinkosolar.scp.mps.domain.dto.scr;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ScrMaterialCollocationTypeDetailDTO对象", description = "DTO对象")
public class ScrMaterialCollocationTypeDetailDTO implements Serializable {

    /**
     * 厂牌
     */
    @ApiModelProperty(name = "厂牌", notes = "")
    private String label;

    @ApiModelProperty(name = "厂牌名称", notes = "")
    private String labelName;

    /**
     * 型号一
     */
    @ApiModelProperty(name = "型号一", notes = "")
    private String typeOne;

    /**
     * 型号二
     */
    @ApiModelProperty(name = "型号二", notes = "")
    private String typeTwo;

    /**
     * 备注
     */
    @ApiModelProperty(name = "备注", notes = "")
    private String remark;

    /**
     * 关联ID
     */
    @ApiModelProperty(name = "关联ID", notes = "")
    private Long associatedId;

    private String collocationType;
}
