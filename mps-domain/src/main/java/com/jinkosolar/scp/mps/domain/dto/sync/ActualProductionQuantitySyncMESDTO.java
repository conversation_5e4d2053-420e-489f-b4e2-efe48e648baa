package com.jinkosolar.scp.mps.domain.dto.sync;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import io.swagger.annotations.ApiModel;
import lombok.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


@Data
@ApiModel("同步MES组件实投数量接口转换字段")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActualProductionQuantitySyncMESDTO implements Serializable {

    @JSONField(
            name = "IS_BC_INFO"
    )
    private Info info = new Info();

    @Getter
    @Setter
    public static class Info {
        @JSONField(
                name = "SENDER"
        )
        private String sender;
        @JSONField(
                name = "SEND_DT"
        )
        private String sendDt;
        @J<PERSON>NField(
                name = "RESEND_ID"
        )
        private String resendId;
        @J<PERSON>NField(
                name = "IFNAME"
        )
        private String ifname;
        @JSONField(
                name = "IFID"
        )
        private String ifId;
        @JSONField(
                name = "UUID"
        )
        private String uuid;
        @JSONField(
                name = "MSGID"
        )
        private String msgId;
        @JSONField(
                name = "RECEIVER"
        )
        private String receiver;
        @JSONField(
                name = "BUSTYP"
        )
        private String bustyp;
        @JSONField(
                name = "TEXT1"
        )
        private String text1;
        @JSONField(
                name = "TEXT2"
        )
        private String text2;
        @JSONField(
                name = "TEXT3"
        )
        private String text3;
        @JSONField(
                name = "TEXT4"
        )
        private String text4;
        @JSONField(
                name = "TEXT5"
        )
        private String text5;
    }

    @JSONField(
            name = "IT_DATA"
    )
    private List<ModuleActualProductionQuantitySyncDTO> itemDataList = new ArrayList<>();
}