package com.jinkosolar.scp.mps.domain.dto.sync;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.ibm.scp.common.api.util.ValidGroups;
import com.jinkosolar.scp.jip.api.dto.base.JipRequestData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;


@Data
@ApiModel("同步MES FI过账明细数据转换对象")
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FiPostingDetailSyncDTO extends JipRequestData implements Serializable {
    private static final long serialVersionUID = 104310174288755610L;
    /**
     * 工厂代码
     */
    @ApiModelProperty("工厂代码")
    @ExcelProperty(value = "工厂代码")
    @NotBlank(message = "工厂代码不能为空", groups = ValidGroups.Insert.class)
    private String factoryCode;
    /**
     * FI判定结果
     */
    @ApiModelProperty("FI判定结果")
    @ExcelProperty(value = "FI判定结果")
    @NotBlank(message = "FI判定结果不能为空", groups = ValidGroups.Insert.class)
    private String fiJudgementResult;
    /**
     * FI数量
     */
    @ApiModelProperty("FI数量")
    @ExcelProperty(value = "FI数量")
    @NotNull(message = "FI数量不能为空", groups = ValidGroups.Insert.class)
    private BigDecimal fiQuantity;
    /**
     * ID
     */
    @ApiModelProperty("ID")
    @ExcelProperty(value = "ID")
    @NotNull(message = "id不能为空", groups = ValidGroups.Update.class)
    private Long id;
    /**
     * 计划型号
     */
    @ApiModelProperty("计划型号")
    @ExcelProperty(value = "计划型号")
    //@NotBlank(message = "计划型号不能为空", groups = ValidGroups.Insert.class)
    private String planModel;
    /**
     * 产品型号
     */
    @ApiModelProperty("产品型号")
    @ExcelProperty(value = "产品型号")
    @NotBlank(message = "产品型号不能为空", groups = ValidGroups.Insert.class)
    private String productModel;
    /**
     * 销售订单行
     */
    @ApiModelProperty("销售订单行")
    @ExcelProperty(value = "销售订单行")
    @NotBlank(message = "销售订单行不能为空", groups = ValidGroups.Insert.class)
    private String salesOrderLineNo;
    /**
     * 销售订单
     */
    @ApiModelProperty("销售订单")
    @ExcelProperty(value = "销售订单")
    @NotBlank(message = "销售订单不能为空", groups = ValidGroups.Insert.class)
    private String salesOrderNo;
    /**
     * 工作中心
     */
    @ApiModelProperty("工作中心")
    @ExcelProperty(value = "工作中心")
    @NotBlank(message = "工作中心不能为空", groups = ValidGroups.Insert.class)
    private String workCenterCode;
    /**
     * 车间代码
     */
    @ApiModelProperty("车间代码")
    @ExcelProperty(value = "车间代码")
    @NotBlank(message = "车间代码不能为空", groups = ValidGroups.Insert.class)
    private String workshopCode;
}