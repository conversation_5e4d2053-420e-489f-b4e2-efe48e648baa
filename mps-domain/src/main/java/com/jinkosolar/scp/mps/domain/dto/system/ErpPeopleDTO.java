package com.jinkosolar.scp.mps.domain.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;


/**
 * erp人员信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 15:55:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpPeopleDTO对象", description = "DTO对象")
public class ErpPeopleDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 员工名
     */
    @ApiModelProperty(value = "员工名")
    private String fullName;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String employeeNumber;
    /**
     * 有效日期从
     */
    @ApiModelProperty(value = "有效日期从")
    private LocalDateTime effectiveDatesFrom;
    /**
     * 有效日期至
     */
    @ApiModelProperty(value = "有效日期至")
    private LocalDateTime effectiveDatesTo;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;
    /**
     * 传真
     */
    @ApiModelProperty(value = "传真")
    private String faxes;
    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    private String tczDept;
    /**
     * cc
     */
    @ApiModelProperty(value = "cc")
    private String tczCc;
    /**
     * cc名称
     */
    @ApiModelProperty(value = "cc名称")
    private String tczCcName;
    /**
     * 银行
     */
    @ApiModelProperty(value = "银行")
    private String tczBankNum;
    /**
     * 帐号
     */
    @ApiModelProperty(value = "帐号")
    private String tcBranchNum;
    /**
     * erp最后更新日期
     */
    @ApiModelProperty(value = "erp最后更新日期")
    private LocalDateTime lastUpdateDate;
}
