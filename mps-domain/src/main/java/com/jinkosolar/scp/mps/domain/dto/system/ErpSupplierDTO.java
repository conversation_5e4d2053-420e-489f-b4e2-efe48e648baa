package com.jinkosolar.scp.mps.domain.dto.system;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;


/**
 * erp供应商表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-22 09:10:40
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ErpSupplierDTO对象", description = "DTO对象")
public class ErpSupplierDTO {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 供应商ID
     */
    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    /**
     * 客户表ID
     */
    @ApiModelProperty(value = "客户表ID")
    private Long partyId;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    /**
     * 供应商简称
     */
    @ApiModelProperty(value = "供应商简称")
    private String vendorNameAlt;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String vendorNum;

    /**
     * 起始日期
     */
    @ApiModelProperty(value = "起始日期")
    private LocalDateTime startDateActive;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private LocalDateTime endDateActive;

    /**
     * 有效表示
     */
    @ApiModelProperty(value = "有效表示")
    private String enabledFlag;

    /**
     * 付款币种
     */
    @ApiModelProperty(value = "付款币种")
    private String paymentCurrencyCode;

    /**
     * 发票币种
     */
    @ApiModelProperty(value = "发票币种")
    private String invoiceCurrencyCode;

    /**
     * 统一授信代码
     */
    @ApiModelProperty(value = "统一授信代码")
    private String repRegistrationNumber;

    /**
     * 付款条件ID
     */
    @ApiModelProperty(value = "付款条件ID")
    private Long termsId;

    /**
     * 付款条件
     */
    @ApiModelProperty(value = "付款条件")
    private String termsName;

    /**
     * 供应商类型
     */
    @ApiModelProperty(value = "供应商类型")
    private String vendorTypeLookupCode;

    /**
     * 弹性域上下文
     */
    @ApiModelProperty(value = "弹性域上下文")
    private String attributeCategory;

    /**
     * attribute1
     */
    @ApiModelProperty(value = "attribute1")
    private String attribute1;

    /**
     * attribute2
     */
    @ApiModelProperty(value = "attribute2")
    private String attribute2;

    /**
     * attribute3
     */
    @ApiModelProperty(value = "attribute3")
    private String attribute3;

    /**
     * attribute4
     */
    @ApiModelProperty(value = "attribute4")
    private String attribute4;

    /**
     * attribute5
     */
    @ApiModelProperty(value = "attribute5")
    private String attribute5;

    /**
     * attribute6
     */
    @ApiModelProperty(value = "attribute6")
    private String attribute6;

    /**
     * attribute7
     */
    @ApiModelProperty(value = "attribute7")
    private String attribute7;

    /**
     * attribute8
     */
    @ApiModelProperty(value = "attribute8")
    private String attribute8;

    /**
     * attribute9
     */
    @ApiModelProperty(value = "attribute9")
    private String attribute9;

    /**
     * attribute10
     */
    @ApiModelProperty(value = "attribute10")
    private String attribute10;

    /**
     * attribute11
     */
    @ApiModelProperty(value = "attribute11")
    private String attribute11;

    /**
     * attribute12
     */
    @ApiModelProperty(value = "attribute12")
    private String attribute12;

    /**
     * attribute13
     */
    @ApiModelProperty(value = "attribute13")
    private String attribute13;

    /**
     * attribute14
     */
    @ApiModelProperty(value = "attribute14")
    private String attribute14;

    /**
     * attribute15
     */
    @ApiModelProperty(value = "attribute15")
    private String attribute15;
}
