package com.jinkosolar.scp.mps.domain.dto.system;

import com.ibm.scp.common.api.base.ImportResultDTO;

public class ImportResultExDTO extends ImportResultDTO {

    //数据分类ID
    private Long isOversea;

    //数据分类ID
    private Long dataTypeId;
    //版本号
    private String versionNumber;

    public Long getIsOversea() {
        return isOversea;
    }

    public void setIsOversea(Long isOversea) {
        this.isOversea = isOversea;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Long getDataTypeId() {
        return dataTypeId;
    }

    public void setDataTypeId(Long dataTypeId) {
        this.dataTypeId = dataTypeId;
    }
}
