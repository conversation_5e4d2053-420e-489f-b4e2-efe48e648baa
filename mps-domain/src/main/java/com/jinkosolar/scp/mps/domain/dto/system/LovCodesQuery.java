package com.jinkosolar.scp.mps.domain.dto.system;

import com.ibm.scp.common.api.base.LovLineQuery;
import com.ibm.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Data
@ApiModel(value = "LovQuery", description = "Lov查询条件多个Code")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class LovCodesQuery extends TokenDTO {
    @ApiModelProperty("多lov查询条件")
    List<LovLineQuery> lovQueries;

    //传输多个头编码，获取一个LovCodesQuery对象
    public static LovCodesQuery createLovCodesQuery(String... codes) {
        LovCodesQuery lovCodesQuery = new LovCodesQuery();
        ArrayList<LovLineQuery> codeList = Lists.newArrayList();
        for (String code : codes) {
            codeList.add(LovLineQuery.builder().code(code).build());
        }
        lovCodesQuery.setLovQueries(codeList);

        return lovCodesQuery;
    }

    public static LovCodesQuery createLovCodesQuery(List<String> codes) {
        ArrayList<LovLineQuery> codeList = Lists.newArrayList();
        LovCodesQuery lovCodesQuery = new LovCodesQuery();
        for (String code : codes) {
            codeList.add(LovLineQuery.builder().code(code).build());
        }
        lovCodesQuery.setLovQueries(codeList);

        return lovCodesQuery;
    }
}
