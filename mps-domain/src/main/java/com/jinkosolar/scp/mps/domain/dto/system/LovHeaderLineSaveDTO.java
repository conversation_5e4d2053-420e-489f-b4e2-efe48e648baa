package com.jinkosolar.scp.mps.domain.dto.system;

import com.ibm.scp.common.api.base.LovLineDTO;
import com.ibm.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/26
 */
@Data
@ApiModel(value = "LovHeaderLineSaveDTO", description = "LovHeaderLineSaveDTO" )
public class LovHeaderLineSaveDTO extends TokenDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * lov头id
     */
    @ApiModelProperty(value = "lov头id" )
    @NotNull(message = "lov头id 不可为空" )
    private Long lovHeaderId;

    @ApiModelProperty(value = "是否需要删除" )
    @NotNull(message = "是否需要删除" )
    private Boolean isHasDelete;

    @ApiModelProperty(value = "lov保存集合" )
    List<LovLineDTO> dtoList;
}
