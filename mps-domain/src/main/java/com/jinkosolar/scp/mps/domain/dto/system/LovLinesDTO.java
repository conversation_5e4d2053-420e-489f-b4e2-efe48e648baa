package com.jinkosolar.scp.mps.domain.dto.system;

import com.ibm.scp.common.api.base.LovLineDTO;
import com.ibm.scp.common.api.base.TokenDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * lov行表(SysLovLines)实体类
 *
 * <AUTHOR>
 * @since 2022-04-24 14:15:04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class LovLinesDTO extends TokenDTO implements Serializable {
    @ApiModelProperty(value = "lov编码")
    private String code;

    @ApiModelProperty(value = "lov行")
    private List<LovLineDTO> lineDTOS;
}
