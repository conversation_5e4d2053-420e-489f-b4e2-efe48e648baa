package com.jinkosolar.scp.mps.domain.dto.system;

import com.ibm.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDate;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@ToString
@Data
public class OrganizationDefinitions extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    /**
     * businessGroupId
     */
    @ApiModelProperty(value = "businessGroupId")
    private Long businessGroupId;

    /**
     * userDefinitionEnableDate
     */
    @ApiModelProperty(value = "userDefinitionEnableDate")
    private LocalDate userDefinitionEnableDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private LocalDate disableDate;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    private String organizationCode;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    /**
     * 账套ID
     */
    @ApiModelProperty(value = "账套ID")
    private Long setOfBooksId;

    /**
     * chartOfAccountsId
     */
    @ApiModelProperty(value = "chartOfAccountsId")
    private Long chartOfAccountsId;

    /**
     * inventoryEnabledFlag
     */
    @ApiModelProperty(value = "inventoryEnabledFlag")
    private String inventoryEnabledFlag;

    /**
     * 业务实体ID
     */
    @ApiModelProperty(value = "业务实体ID")
    private Long operatingUnit;

    /**
     * legalEntity
     */
    @ApiModelProperty(value = "legalEntity")
    private Integer legalEntity;


}
