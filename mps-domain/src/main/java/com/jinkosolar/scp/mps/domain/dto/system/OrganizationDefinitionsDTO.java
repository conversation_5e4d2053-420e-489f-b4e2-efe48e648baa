package com.jinkosolar.scp.mps.domain.dto.system;

import com.ibm.scp.common.api.base.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDate;


/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-04-28 11:38:58
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "OrganizationDefinitionsDTO对象", description = "DTO对象")
public class OrganizationDefinitionsDTO extends BaseDTO {

    private static final long serialVersionUID = -6045041388156273690L;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private Long organizationId;

    /**
     * businessGroupId
     */
    @ApiModelProperty(value = "businessGroupId")
    private Long businessGroupId;

    /**
     * userDefinitionEnableDate
     */
    @ApiModelProperty(value = "userDefinitionEnableDate")
    private LocalDate userDefinitionEnableDate;

    /**
     * 失效日期
     */
    @ApiModelProperty(value = "失效日期")
    private LocalDate disableDate;

    /**
     * 组织代码
     */
    @ApiModelProperty(value = "组织代码")
    private String organizationCode;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String organizationName;

    /**
     * 账套ID
     */
    @ApiModelProperty(value = "账套ID")
    private Long setOfBooksId;

    /**
     * chartOfAccountsId
     */
    @ApiModelProperty(value = "chartOfAccountsId")
    private Long chartOfAccountsId;

    /**
     * inventoryEnabledFlag
     */
    @ApiModelProperty(value = "inventoryEnabledFlag")
    private String inventoryEnabledFlag;

    /**
     * 业务实体ID
     */
    @ApiModelProperty(value = "业务实体ID")
    private Integer operatingUnit;

    @ApiModelProperty(value = "业务实体名称")
    private String operatingUnitName;

    /**
     * legalEntity
     */
    @ApiModelProperty(value = "legalEntity")
    private Integer legalEntity;

    @ApiModelProperty(value = "SCP使用标识（Y）")
    private String scpFlag;
}
