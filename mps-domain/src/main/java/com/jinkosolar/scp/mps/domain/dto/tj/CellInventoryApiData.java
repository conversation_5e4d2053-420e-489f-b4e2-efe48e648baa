package com.jinkosolar.scp.mps.domain.dto.tj;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: CellInventoryApiData
 * @date 2024/1/12 14:37
 */
@Data
public class CellInventoryApiData {

    /**
     * ERP代码
     */
    @JSONField(name = "erp_code")
    private String erpCode;

    /**
     * 库存组织
     */
    @JSONField(name = "org")
    private String org;

    /**
     * 国内/海外
     */
    @JSONField(name = "is_oversea")
    private String isOversea;

    /**
     * 电池料号
     */
    @JSONField(name = "cell_no")
    private String cellNo;

    /**
     * 仓库代码
     */
    @JSONField(name = "warehouse_code")
    private String warehouseCode;
    /**
     * 仓库代码
     */
    @JSONField(name = "location_code")
    private String subWarehouseCode;

    /**
     * 仓库名称
     */
    @JSONField(name = "warehouse_name")
    private String warehouseName;

    /**
     * 电池来源
     */
    @JSONField(name = "cell_from")
    private String cellFrom;

    /**
     * 电池尺寸
     */
    @JSONField(name = "cell_size")
    private BigDecimal cellSize;

    /**
     * 电池面积
     */
    @JSONField(name = "cell_area")
    private BigDecimal cellArea;

    /**
     * 电池等级
     */
    @JSONField(name = "cell_level")
    private String cellLevel;

    /**
     * 电池型号
     */
    @JSONField(name = "cell_spec")
    private String cellSpec;

    /**
     * 电池类型
     */
    @JSONField(name = "cell_type")
    private String cellType;

    /**
     * 电池单片功率（W）
     */
    @JSONField(name = "mc_power")
    private BigDecimal mcPower;

    /**
     * 电池数量
     */
    @JSONField(name = "quantity")
    private BigDecimal quantity;

    /**
     * 电池供应方
     */
    @JSONField(name = "supplier")
    private String supplier;

    /**
     * 电池系列
     */
    @JSONField(name = "cell_series")
    private String cellSeries;

    /**
     * 电池效率值
     */
    @JSONField(name = "efficiency")
    private BigDecimal efficiency;

    /**
     * 电池库存MW
     */
    @JSONField(name = "inventory_power")
    private BigDecimal inventoryPower;

    /**
     * 实际效率
     */
    @JSONField(name = "actual_efficiency")
    private BigDecimal actualEfficiency;

    /**
     * 分档标识
     */
    @JSONField(name = "characteristic")
    private String characteristic;

    /**
     * 库存日期
     */
    @JSONField(name = "inventory_date")
    private LocalDate inventoryDate;

    /**
     * 库存时点
     */
    @JSONField(name = "inventory_time")
    private String inventoryTime;

    /**
     * 批次号
     */
    @JSONField(name = "batch_no")
    private String batchNo;

    public boolean filter() {
        return StringUtils.isNotBlank(this.cellFrom) && StringUtils.isNotBlank(this.batchNo) && StringUtils.isNotBlank(this.cellType);
    }
}
