package com.jinkosolar.scp.mps.domain.dto.tj;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TjResponseDate
 * @date 2024/1/12 14:29
 */
@Data
public class TjResponse<T> {
    private final static String SUCCESS = "DPN-OLTP-COMMON-000";
    /**
     * code
     */
    @J<PERSON>NField(name = "code")
    private String code;
    /**
     * message
     */
    @JSONField(name = "message")
    private String message;
    /**
     * 结果集
     */
    @JSONField(name = "results")
    private List<T> results;

    public boolean isSuccess() {
        return SUCCESS.equals(code);
    }
}
