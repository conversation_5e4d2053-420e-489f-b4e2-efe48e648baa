package com.jinkosolar.scp.mps.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Maps;
import com.ibm.scp.common.api.base.BaseServiceImpl;
import com.ibm.scp.common.api.base.LovLineDTO;
import com.ibm.scp.common.api.util.*;
import com.jinkosolar.scp.mps.domain.constant.Constants;
import com.jinkosolar.scp.mps.domain.convert.NonModuleProductionPlanTzDEConvert;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanDTO;
import com.jinkosolar.scp.mps.domain.dto.NonModuleProductionPlanTzDTO;
import com.jinkosolar.scp.mps.domain.dto.PlanVerisonControllDTO;
import com.jinkosolar.scp.mps.domain.entity.CellTransferPlan;
import com.jinkosolar.scp.mps.domain.entity.NonModuleProductionPlanTz;
import com.jinkosolar.scp.mps.service.CellTransferPlanService;
import com.jinkosolar.scp.mps.domain.entity.QNonModuleProductionPlanTz;
import com.jinkosolar.scp.mps.domain.entity.QCellTransferPlan;
import com.jinkosolar.scp.mps.domain.query.NonModuleProductionPlanTzQuery;
import com.jinkosolar.scp.mps.domain.repository.NonModuleProductionPlanTzRepository;
import com.jinkosolar.scp.mps.domain.save.NonModuleProductionPlanTzEditDTO;
import com.jinkosolar.scp.mps.domain.util.Constant;
import com.jinkosolar.scp.mps.domain.util.DateUtil;
import com.jinkosolar.scp.mps.domain.util.LovHeaderCodeConstant;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanTempService;
import com.jinkosolar.scp.mps.service.NonModuleProductionPlanTzService;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPADeleteClause;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电池排产计划人工可调整本计划
 *
 * <AUTHOR> 2024-05-28 09:12:50
 */
@Slf4j
@Service("nonModuleProductionPlanTzService")
public class NonModuleProductionPlanTzServiceImpl extends BaseServiceImpl<NonModuleProductionPlanTz, Long> implements NonModuleProductionPlanTzService {


    @Autowired
    NonModuleProductionPlanTzRepository  nonModuleProductionPlanTzRepository;

    @Autowired
    NonModuleProductionPlanTzDEConvert nonModuleProductionPlanTzDEConvert;

    @Autowired
    NonModuleProductionPlanTempService  nonModuleProductionPlanTempService;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    @Autowired
    private CellTransferPlanService cellTransferPlanService;


    private static final QNonModuleProductionPlanTz qNonModuleProductionPlanTz = QNonModuleProductionPlanTz.nonModuleProductionPlanTz;


    @Override
    public Page<NonModuleProductionPlanTzDTO> queryOldBasePage(NonModuleProductionPlanTzQuery query) {
        BooleanBuilder booleanBuilder = buildWhere(query);
        booleanBuilder.and(qNonModuleProductionPlanTz.attribute8.isNotNull());
        booleanBuilder.and(qNonModuleProductionPlanTz.attribute8.isNotEmpty());
        booleanBuilder.and(qNonModuleProductionPlanTz.productType.isNotNull());
        Set<String> totalDaySet= new HashSet<>() ;



        JPAQuery<Tuple> jpaQuery= jpaQueryFactory.select(
                              qNonModuleProductionPlanTz.planVersion
                            , qNonModuleProductionPlanTz.apsPlanVersion
                            , qNonModuleProductionPlanTz.factoryCode
                            , qNonModuleProductionPlanTz.productType
                            , qNonModuleProductionPlanTz.directional
                            , qNonModuleProductionPlanTz.gridLine
                            , qNonModuleProductionPlanTz.processPlanType
                            , qNonModuleProductionPlanTz.siliconSupplier
                            , qNonModuleProductionPlanTz.thickness
                            , qNonModuleProductionPlanTz.workshopId
                            , qNonModuleProductionPlanTz.workCenterId
                            , qNonModuleProductionPlanTz.frenchCarbonLabel
                            , qNonModuleProductionPlanTz.zeroCarbonLabel
                            , qNonModuleProductionPlanTz.itemId
                            , qNonModuleProductionPlanTz.homemadeOrPurchase
                            , qNonModuleProductionPlanTz.electricalPerformance
                            , qNonModuleProductionPlanTz.specifySupplier
                            , qNonModuleProductionPlanTz.specifySupplierName)
                .from(qNonModuleProductionPlanTz)

                .where(booleanBuilder)

                .orderBy( qNonModuleProductionPlanTz.factoryCode.asc()
                        , qNonModuleProductionPlanTz.workshopId.asc()
                        , qNonModuleProductionPlanTz.productType.asc())
                .groupBy(
                          qNonModuleProductionPlanTz .apsPlanVersion
                         , qNonModuleProductionPlanTz.planVersion
                        , qNonModuleProductionPlanTz.factoryCode
                        , qNonModuleProductionPlanTz.workshopId
                        , qNonModuleProductionPlanTz.workCenterId
                        , qNonModuleProductionPlanTz.productType
                        , qNonModuleProductionPlanTz.directional
                        , qNonModuleProductionPlanTz.gridLine
                        , qNonModuleProductionPlanTz.processPlanType
                        , qNonModuleProductionPlanTz.siliconSupplier
                        , qNonModuleProductionPlanTz.thickness
                        , qNonModuleProductionPlanTz.homemadeOrPurchase
                        , qNonModuleProductionPlanTz.electricalPerformance
                        , qNonModuleProductionPlanTz.specifySupplier
                        , qNonModuleProductionPlanTz.specifySupplierName);

     //            String mainSelfKey = StringUtils.joinWith(":::", data.getFactoryId(),data.getWorkshopId(),data.getWorkCenterId(),data.getProductTypeId(),data.getThickness(),data.getHomemadeOrPurchase(),data.getGridLine(),data.getProcessPlanType(),data.getElectricalPerformance(),yearMonth);

        int count = jpaQuery.fetch().size();
        List<Tuple> fetch = jpaQuery.fetch();
        log.info("queryByPage sql: {}", count);
        log.info("queryByPage sql: {}", fetch);
        List<NonModuleProductionPlanTzDTO> list = fetch.stream().map(i -> {
            NonModuleProductionPlanTzDTO dto = new NonModuleProductionPlanTzDTO();
            dto.setApsPlanVersion(i.get(qNonModuleProductionPlanTz.apsPlanVersion));
            dto.setPlanVersion(i.get(qNonModuleProductionPlanTz.planVersion));
            dto.setFactoryCode(i.get(qNonModuleProductionPlanTz.factoryCode));
            dto.setWorkCenterId(i.get(qNonModuleProductionPlanTz.workCenterId));
            dto.setWorkshopId(i.get(qNonModuleProductionPlanTz.workshopId));
            dto.setProductType(i.get(qNonModuleProductionPlanTz.productType));
            dto.setDirectional(i.get(qNonModuleProductionPlanTz.directional));
            dto.setGridLine(i.get(qNonModuleProductionPlanTz.gridLine));
            dto.setProcessPlanType(i.get(qNonModuleProductionPlanTz.processPlanType));
            dto.setSiliconSupplier(i.get(qNonModuleProductionPlanTz.siliconSupplier));
            dto.setThickness(i.get(qNonModuleProductionPlanTz.thickness));
            dto.setHomemadeOrPurchase(i.get(qNonModuleProductionPlanTz.homemadeOrPurchase));
            dto.setElectricalPerformance(i.get(qNonModuleProductionPlanTz.electricalPerformance));
            dto.setSpecifySupplier(i.get(qNonModuleProductionPlanTz.specifySupplier));
            dto.setSpecifySupplierName(i.get(qNonModuleProductionPlanTz.specifySupplierName));
            dto.setCellItem("可用产出(万片)");

            NonModuleProductionPlanTz queryListEntity = nonModuleProductionPlanTzDEConvert.toEntity(dto);

            //查询同计划下不同天的数据
            List<NonModuleProductionPlanTz> itemList = nonModuleProductionPlanTzRepository.findAll(Example.of(queryListEntity));
            List<NonModuleProductionPlanTzDTO> itemDtoList = nonModuleProductionPlanTzDEConvert.toDto(itemList);
            // 处理日期，截取到年月日
            itemDtoList.forEach(s -> s.setAttribute19(s.getAttribute19().substring(0, 10)));
            Map<String, List<NonModuleProductionPlanTzDTO>> dayMap = itemDtoList.stream().collect(Collectors.groupingBy(NonModuleProductionPlanTzDTO::getAttribute19));

            List<String> infoDayList = itemDtoList.stream().sorted(Comparator.comparing(NonModuleProductionPlanTzDTO::getAttribute19)).map(NonModuleProductionPlanTzDTO::getAttribute19).collect(Collectors.toList());
            Map<String, Map<Object, Object>> dynamicColumnMap=new HashMap<>();

            // 按天汇总
            infoDayList.forEach(key -> {
                totalDaySet.add(key);
                List<NonModuleProductionPlanTzDTO> productionPlanDTOS = dayMap.get(key);
                // 可用产出(万片)
                BigDecimal attribute8 = productionPlanDTOS.stream().map(item -> new BigDecimal(item.getAttribute8())).reduce(BigDecimal.ZERO, BigDecimal::add);
                String ids = productionPlanDTOS.stream().map(NonModuleProductionPlanTzDTO::getId).map(String::valueOf).collect(Collectors.joining(","));
                Map<Object,Object> values=new HashMap<>();
                values.put("ids",ids);
                values.put("attribute8",attribute8);
                dynamicColumnMap.put(key,values);
            });
            dto.setDynamicColumnMap(dynamicColumnMap);
            return dto;

        }).collect(Collectors.toList());

        // 计算动态日期列,放第一行
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> totalDayList = new ArrayList<>(totalDaySet);
            Collections.sort(totalDayList);
            list.get(0).setDayList(totalDayList);
        }
        TranslateUtils.convert(list, false);
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize() == null ? 100 : query.getPageSize());
        return new PageImpl<>(list, pageable, list.size());

    }




    @Override
    public void cellOldBasePageExport(NonModuleProductionPlanTzQuery query, HttpServletResponse response) {


        List<NonModuleProductionPlanTzDTO> dtos = queryOldBasePage(query).getContent();

        OutputStream os = null;
        ExcelWriter excelWriter = null;
        try {
            ExcelUtils.setExportResponseHeader(response, "老基地电池排产调整计划");
            os = response.getOutputStream();
            excelWriter = EasyExcel.write(os).build();

            if(dtos.isEmpty()){
                return ;
            }
            // 动态列头
            List<String> colList = dtos.get(0).getDayList();
            List<List<Object>> excelData = org.apache.commons.compress.utils.Lists.newArrayList();

            List<List<String>> header = new ArrayList<>();
            header.add(Arrays.asList("计划版本"));
            header.add(Arrays.asList("APS计划版本"));
            header.add(Arrays.asList("工厂名称"));
            header.add(Arrays.asList("车间名称"));
            header.add(Arrays.asList("工作中心"));
            header.add(Arrays.asList("产品"));
            header.add(Arrays.asList("物料类型"));
            header.add(Arrays.asList("定向"));
            header.add(Arrays.asList("栅线数"));
            header.add(Arrays.asList("厚度"));
            header.add(Arrays.asList("工艺"));
            header.add(Arrays.asList("电性能"));
            header.add(Arrays.asList("采购方式"));
            header.add(Arrays.asList("指定硅料供应商"));


            colList.forEach(col -> {
                header.add(Arrays.asList(col));
            });
            dtos.forEach(val -> {
                List<Object> data = new ArrayList();
                data.add(val.getPlanVersion());
                data.add(val.getApsPlanVersion());
                data.add(val.getFactoryName());
                data.add(val.getWorkshopName());
                data.add(val.getWorkCenterName());
                data.add(val.getProductType());
                data.add(val.getCellItem());
                data.add(val.getDirectionalName());
                data.add(val.getGridLineName());
                data.add(val.getThicknessName());
                data.add(val.getProcessPlanTypeName());
                data.add(val.getElectricalPerformanceName());
                data.add(val.getHomemadeOrPurchaseName());
                data.add(val.getSpecifySupplierName());
                Map<String, Map<Object, Object>> colMap = val.getDynamicColumnMap();
                colList.forEach(col -> {
                    if (colMap.get(col) != null) {
                        Map<Object, Object> dataMap = (Map<Object, Object>) colMap.get(col);
                        data.add(dataMap.get("attribute8"));
                    } else {
                        data.add("");
                    }
                });
                excelData.add(data);
            });

            WriteSheet sheet = EasyExcel.writerSheet(0, "老基地电池排产调整计划").head(header).build();
            excelWriter.write(excelData, sheet);
            excelWriter.finish();
            response.flushBuffer();
        }catch (Exception ex) {
            log.error("export ie data error",ex);
        }finally {
            if (excelWriter != null) {
                try {
                    excelWriter.finish();
                } catch (Exception e) {
                    log.error("Failed to close ExcelWriter", e);
                }
            }
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("Failed to close output stream", e);
                }
            }

        }
    }

    @Override
    @Transactional
    public Boolean editRecord(NonModuleProductionPlanTzEditDTO planTzEditDTO) {

        BigDecimal totalQty= planTzEditDTO.getQty();
        int num=planTzEditDTO.getIds().size();
        List<Long> ids=planTzEditDTO.getIds();
        List<BigDecimal> qtyList=splitBigDecimal(totalQty,num);

        for(int i=0;i<ids.size();i++){
           NonModuleProductionPlanTz record= findById(ids.get(i));
           record.setAttribute8(qtyList.get(i).toString());
            save(record);
        }
        return Boolean.TRUE;
    }

    @Override
    public List<NonModuleProductionPlanDTO> cellForecast(PlanVerisonControllDTO planVersion) {

        JPAQuery<Tuple> jpaQuery = jpaQueryFactory.select(qNonModuleProductionPlanTz.planVersion
                        , qNonModuleProductionPlanTz.apsPlanVersion
                        , qNonModuleProductionPlanTz.factoryCode
                        , qNonModuleProductionPlanTz.productType
                        , qNonModuleProductionPlanTz.directional
                        , qNonModuleProductionPlanTz.attribute19
                        , qNonModuleProductionPlanTz.attribute8.castToNum(BigDecimal.class).sum().as("schedulingQty")
                        , qNonModuleProductionPlanTz.gridLine
                        , qNonModuleProductionPlanTz.processPlanType
                        , qNonModuleProductionPlanTz.siliconSupplier
                        , qNonModuleProductionPlanTz.thickness
                        , qNonModuleProductionPlanTz.workshopId
                        , qNonModuleProductionPlanTz.workCenterId
                        , qNonModuleProductionPlanTz.frenchCarbonLabel
                        , qNonModuleProductionPlanTz.zeroCarbonLabel
                        , qNonModuleProductionPlanTz.itemId
                        , qNonModuleProductionPlanTz.homemadeOrPurchase
                        , qNonModuleProductionPlanTz.electricalPerformance
                        , qNonModuleProductionPlanTz.specifySupplier
                        , qNonModuleProductionPlanTz.specifySupplierName)
                .from(qNonModuleProductionPlanTz)
                .where(qNonModuleProductionPlanTz.planVersion.eq(planVersion.getPlanVersion()), qNonModuleProductionPlanTz.productType.isNotNull()
                        , qNonModuleProductionPlanTz.attribute19.isNotNull())
                .orderBy(qNonModuleProductionPlanTz.factoryCode.asc()
                        ,qNonModuleProductionPlanTz.workshopId.asc()
                        , qNonModuleProductionPlanTz.productType.asc()
                        , qNonModuleProductionPlanTz.attribute19.asc())
                .groupBy(qNonModuleProductionPlanTz.planVersion
                        , qNonModuleProductionPlanTz.factoryCode
                        , qNonModuleProductionPlanTz.workshopId
                        , qNonModuleProductionPlanTz.workCenterId
                        , qNonModuleProductionPlanTz.productType
                        , qNonModuleProductionPlanTz.directional
                        , qNonModuleProductionPlanTz.gridLine
                        , qNonModuleProductionPlanTz.processPlanType
                        , qNonModuleProductionPlanTz.siliconSupplier
                        , qNonModuleProductionPlanTz.attribute19
                        , qNonModuleProductionPlanTz.thickness
                        , qNonModuleProductionPlanTz.homemadeOrPurchase
                        , qNonModuleProductionPlanTz.electricalPerformance
                        , qNonModuleProductionPlanTz.specifySupplier
                        , qNonModuleProductionPlanTz.specifySupplierName);
        List<Tuple> tupleList = jpaQuery.fetch();
        List<NonModuleProductionPlanDTO> dataList = convertTupleToDTO(tupleList);

        List<NonModuleProductionPlanDTO> resultList = nonModuleProductionPlanTempService.getNonModuleProductionPlanDTOS(dataList);

        return resultList;
    }

    @Override
    @Transactional
    public void saveLatestPlans(List<NonModuleProductionPlanTz> nonModuleProductionPlanTzs) {

        JPADeleteClause deleteExe = jpaQueryFactory.delete(qNonModuleProductionPlanTz);
        deleteExe.where(qNonModuleProductionPlanTz.modelType.eq(Constants.GNDC_MODEL_TYPE));
        deleteExe.execute();
        saveAll(nonModuleProductionPlanTzs);

    }

    @Override
    @Transactional
    public void executeTransferPlanCalculation() {
        log.info("开始执行转运计划计算...");

        try {
            // 获取自产的采购方式
            Long homemadeOrPurchase=LovUtils.get(LovHeaderCodeConstant.MPS_CELL_PROCUREMENT_METHOD,Constant.MPS_CELL_PROCUREMENT_METHOD_SELF).getLovLineId();

            // 1. 查询自产的调整计划数据
            List<NonModuleProductionPlanTz> selfMadePlans = nonModuleProductionPlanTzRepository
                    .findByHomemadeOrPurchase(homemadeOrPurchase);
            log.info("查询到自产调整计划数据 {} 条", selfMadePlans.size());

            if (selfMadePlans.isEmpty()) {
                log.warn("未找到自产的调整计划数据，无法执行转运计划计算");
                return;
            }

             // 2. 查询所有转运计划数据
             BooleanBuilder transferPlanBuilder = new BooleanBuilder();
             QCellTransferPlan qCellTransferPlan = QCellTransferPlan.cellTransferPlan;
             JPAQuery<CellTransferPlan> transferPlanQuery = jpaQueryFactory
                     .selectFrom(qCellTransferPlan)
                     .where(transferPlanBuilder);
             List<CellTransferPlan> allTransferPlans = transferPlanQuery.fetch();
            log.info("查询到转运计划数据 {} 条", allTransferPlans.size());

            if (allTransferPlans.isEmpty()) {
                log.warn("未找到转运计划数据，无法执行计算");
                return;
            }

            // 3. 分离入库计划和发货计划
            List<CellTransferPlan> inboundPlans = allTransferPlans.stream()
                    .filter(p -> Constant.INBOUND_PLAN.equals(p.getItem()))
                    .collect(Collectors.toList());

            List<CellTransferPlan> outboundPlans = allTransferPlans.stream()
                    .filter(p -> Constant.OUTBOUND_PLAN.equals(p.getItem()))
                    .collect(Collectors.toList());

            log.info("入库计划 {} 条，发货计划 {} 条", inboundPlans.size(), outboundPlans.size());

            // 4. 处理入库计划
            processInboundPlansNew(inboundPlans, selfMadePlans);

            // 5. 处理发货计划
            processOutboundPlansNew(outboundPlans, selfMadePlans);

            // 6. 批量更新数据
            batchUpdateAttribute8(selfMadePlans);

            log.info("转运计划计算执行完成");

        } catch (Exception e) {
            log.error("转运计划计算执行失败", e);
            throw new RuntimeException("转运计划计算执行失败", e);
        }
    }


    /**
     * 处理入库计划数据（新逻辑）
     * @param inboundPlans 入库计划列表
     * @param selfMadePlans 自产调整计划列表
     */
    private void processInboundPlansNew(List<CellTransferPlan> inboundPlans, List<NonModuleProductionPlanTz> selfMadePlans) {
        log.info("开始处理入库计划，共 {} 条", inboundPlans.size());

        for (CellTransferPlan inboundPlan : inboundPlans) {
            try {
                log.info("处理入库计划: transferId={}, transferDate={}, workcenterId={}, quantity={}",
                        inboundPlan.getId(), inboundPlan.getTransferDate(), inboundPlan.getWorkcenterId(), inboundPlan.getQuantity());

                // 1. 时间格式处理：转运计划时间转换为 yyyy/MM/dd 格式
                String transferDateStr = formatDateToYearMonthDay(inboundPlan.getTransferDate());
                log.info("转运计划时间格式化: {} -> {}", inboundPlan.getTransferDate(), transferDateStr);

                // 2. 判断是否有工作中心
                if (inboundPlan.getWorkcenterId() != null) {
                    // 3.3 有工作中心：基础匹配条件 + 工作中心匹配 + 时间大于等于转运计划时间
                    processInboundPlanWithWorkCenter(inboundPlan, selfMadePlans, transferDateStr);
                } else {
                    // 3.2 没有工作中心：只需基础匹配条件 + 时间大于等于转运计划时间
                    processInboundPlanWithoutWorkCenter(inboundPlan, selfMadePlans, transferDateStr);
                }

            } catch (Exception e) {
                log.error("处理入库计划失败: transferId={}", inboundPlan.getId(), e);
            }
        }
    }

    /**
     * 处理入库计划数据（原逻辑，保留备用）
     * @param inboundPlans 入库计划列表
     * @param selfMadePlans 自产调整计划列表
     */
    private void processInboundPlans(List<CellTransferPlan> inboundPlans, List<NonModuleProductionPlanTz> selfMadePlans) {
        log.info("开始处理入库计划，共 {} 条", inboundPlans.size());
        
        for (CellTransferPlan inboundPlan : inboundPlans) {
            try {
                // 1. 按照匹配条件查找对应的调整计划（attribute19 = transferDate）
                String transferDateStr = formatDateToString(inboundPlan.getTransferDate());
                List<NonModuleProductionPlanTz> matchedPlans = findMatchedPlans(inboundPlan, selfMadePlans, transferDateStr);
                
                if (!matchedPlans.isEmpty()) {
                    // 2. 有工作中心的优先匹配
                    if (inboundPlan.getWorkcenterId() != null) {
                        List<NonModuleProductionPlanTz> workCenterMatched = matchedPlans.stream()
                                .filter(p -> Objects.equals(p.getWorkCenterId(), inboundPlan.getWorkcenterId()))
                                .collect(Collectors.toList());
                        if (!workCenterMatched.isEmpty()) {
                            addQuantityToPlans(workCenterMatched, inboundPlan.getQuantity());
                            logMatchResult(inboundPlan, workCenterMatched, "入库计划-工作中心匹配");
                            continue;
                        }
                    }
                    
                    // 3. 没有工作中心或工作中心不匹配，直接加到匹配的计划上
                    addQuantityToPlans(matchedPlans, inboundPlan.getQuantity());
                    logMatchResult(inboundPlan, matchedPlans, "入库计划-基本匹配");
                } else if (inboundPlan.getWorkcenterId() == null) {
                    // 4. 工作中心为空且没有当天匹配的，查找后一天的数据
                    processInboundPlanWithoutWorkCenter(inboundPlan, selfMadePlans);
                } else {
                    log.warn("入库计划未找到匹配的调整计划: transferId={}, transferDate={}", 
                            inboundPlan.getId(), inboundPlan.getTransferDate());
                }
            } catch (Exception e) {
                log.error("处理入库计划失败: transferId={}", inboundPlan.getId(), e);
            }
        }
    }

    /**
     * 处理发货计划数据
     * @param outboundPlans 发货计划列表
     * @param selfMadePlans 自产调整计划列表
     */
    private void processOutboundPlans(List<CellTransferPlan> outboundPlans, List<NonModuleProductionPlanTz> selfMadePlans) {
        log.info("开始处理发货计划，共 {} 条", outboundPlans.size());
        
        for (CellTransferPlan outboundPlan : outboundPlans) {
            try {
                BigDecimal remainingQuantity = outboundPlan.getQuantity();
                
                // 1. 查找日期 <= 发货计划日期的数据，按日期倒序
                List<NonModuleProductionPlanTz> eligiblePlans = findEligiblePlansForOutbound(outboundPlan, selfMadePlans);
                
                // 2. 按日期分组
                Map<LocalDate, List<NonModuleProductionPlanTz>> plansByDate = groupPlansByDate(eligiblePlans);
                
                // 3. 按日期倒序依次扣减
                List<LocalDate> sortedDates = plansByDate.keySet().stream()
                        .sorted(Comparator.reverseOrder())
                        .collect(Collectors.toList());
                
                for (LocalDate date : sortedDates) {
                    if (remainingQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    
                    List<NonModuleProductionPlanTz> dayPlans = plansByDate.get(date);
                    // 同一天内按工作中心编码排序（如果工作中心为空，按工作中心顺序处理）
                    dayPlans = sortPlansByWorkCenter(dayPlans, outboundPlan.getWorkcenterId() == null);
                    
                    remainingQuantity = deductQuantityFromPlans(dayPlans, remainingQuantity);
                }
                
                if (remainingQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    log.warn("发货计划数量未完全扣减: transferId={}, 剩余数量={}", 
                            outboundPlan.getId(), remainingQuantity);
                }
                
                log.info("发货计划处理完成: transferId={}, 原始数量={}, 剩余数量={}", 
                        outboundPlan.getId(), outboundPlan.getQuantity(), remainingQuantity);
                
            } catch (Exception e) {
                log.error("处理发货计划失败: transferId={}", outboundPlan.getId(), e);
            }
        }
    }

    /**
     * 批量更新attribute8字段
     * @param plans 需要更新的计划列表
     */
    private void batchUpdateAttribute8(List<NonModuleProductionPlanTz> plans) {
        log.info("开始批量更新attribute8字段，共 {} 条记录", plans.size());
        
        List<NonModuleProductionPlanTz> updatedPlans = plans.stream()
                .filter(plan -> plan.getAttribute8() != null)
                .collect(Collectors.toList());
        
        // 分批更新，每批5条记录
        int batchSize = 5;
        for (int i = 0; i < updatedPlans.size(); i += batchSize) {
            List<NonModuleProductionPlanTz> batch = updatedPlans.subList(i, 
                    Math.min(i + batchSize, updatedPlans.size()));
            
            // 单个更新避免复杂的批量更新
            for (NonModuleProductionPlanTz plan : batch) {
                try {
                    nonModuleProductionPlanTzRepository.updateAttribute8ById(plan.getId(), plan.getAttribute8());
                } catch (Exception e) {
                    log.error("更新attribute8失败: planId={}, attribute8={}", plan.getId(), plan.getAttribute8(), e);
                }
            }
        }
        
        log.info("批量更新attribute8字段完成");
    }

    // ====================== 辅助方法 ======================

    /**
     * 日期格式化为字符串
     */
    private String formatDateToString(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    /**
     * 字符串日期转换为LocalDate
     */
    private LocalDate parseStringToDate(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.contains("/")) {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            } else if (dateStr.contains("-")) {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } else {
                return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
            }
        } catch (Exception e) {
            log.warn("日期解析失败: {}", dateStr);
            return null;
        }
    }

    /**
     * BigDecimal字符串转换
     */
    private BigDecimal stringToBigDecimal(String value) {
        if (StringUtils.isBlank(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            log.warn("数字转换失败: {}", value);
            return BigDecimal.ZERO;
        }
    }

    /**
     * BigDecimal转字符串
     */
    private String bigDecimalToString(BigDecimal value) {
        return value == null ? "0" : value.toString();
    }

    /**
     * 查找匹配的调整计划
     */
    private List<NonModuleProductionPlanTz> findMatchedPlans(CellTransferPlan transferPlan, 
                                                           List<NonModuleProductionPlanTz> selfMadePlans, 
                                                           String targetDate) {
        return selfMadePlans.stream()
                .filter(plan -> matchBasicConditions(transferPlan, plan))
                .filter(plan -> Objects.equals(plan.getAttribute19(), targetDate))
                .collect(Collectors.toList());
    }

    /**
     * 基本匹配条件判断
     */
    private boolean matchBasicConditions(CellTransferPlan transfer, NonModuleProductionPlanTz plan) {
        return Objects.equals(transfer.getFactoryId(), plan.getFactoryId()) &&
               Objects.equals(transfer.getWorkshopId(), plan.getWorkshopId()) &&
               Objects.equals(transfer.getProductType().toString(), plan.getProductType()) &&
               Objects.equals(transfer.getDirectional(), plan.getDirectional()) &&
               Objects.equals(transfer.getGridLine(), plan.getGridLine()) &&
               Objects.equals(transfer.getThickness(), plan.getThickness()) &&
               Objects.equals(transfer.getProcessType(), plan.getProcessType()) &&
               Objects.equals(transfer.getElectricalPerformance(), plan.getElectricalPerformance()) &&
               Objects.equals(transfer.getSpecifySupplier(), plan.getSpecifySupplier());
    }

    /**
     * 向计划中增加数量
     */
    private void addQuantityToPlans(List<NonModuleProductionPlanTz> plans, BigDecimal quantity) {
        if (plans.isEmpty() || quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        
        // 平均分配到所有匹配的计划中
        BigDecimal avgQuantity = quantity.divide(BigDecimal.valueOf(plans.size()), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal remainder = quantity.subtract(avgQuantity.multiply(BigDecimal.valueOf(plans.size())));
        
        for (int i = 0; i < plans.size(); i++) {
            NonModuleProductionPlanTz plan = plans.get(i);
            BigDecimal currentQty = stringToBigDecimal(plan.getAttribute8());
            BigDecimal addQty = avgQuantity;
            
            // 将余数加到第一个计划中
            if (i == 0) {
                addQty = addQty.add(remainder);
            }
            
            plan.setAttribute8(bigDecimalToString(currentQty.add(addQty)));
        }
    }

    /**
     * 记录匹配结果日志
     */
    private void logMatchResult(CellTransferPlan transfer, List<NonModuleProductionPlanTz> matched, String type) {
        log.info("{}: transferId={}, transferDate={}, quantity={}, matchedCount={}", 
                type, transfer.getId(), transfer.getTransferDate(), transfer.getQuantity(), matched.size());
    }

    /**
     * 处理工作中心为空的入库计划
     */
    private void processInboundPlanWithoutWorkCenter(CellTransferPlan inboundPlan, 
                                                   List<NonModuleProductionPlanTz> selfMadePlans) {
        LocalDate startDate = inboundPlan.getTransferDate();
        
        // 查找后续7天内的数据
        for (int i = 1; i <= 7; i++) {
            LocalDate nextDate = startDate.plusDays(i);
            String nextDateStr = formatDateToString(nextDate);
            
            List<NonModuleProductionPlanTz> nextDayPlans = findMatchedPlans(inboundPlan, selfMadePlans, nextDateStr);
            if (!nextDayPlans.isEmpty()) {
                // 按工作中心顺序排序
                nextDayPlans = nextDayPlans.stream()
                        .sorted(Comparator.comparing(plan -> plan.getWorkCenterId() == null ? 0L : plan.getWorkCenterId()))
                        .collect(Collectors.toList());
                
                addQuantityToPlans(Collections.singletonList(nextDayPlans.get(0)), inboundPlan.getQuantity());
                log.info("入库计划后续日期匹配: transferId={}, 原日期={}, 匹配日期={}", 
                        inboundPlan.getId(), startDate, nextDate);
                return;
            }
        }
        
        log.warn("入库计划后续7天内未找到匹配数据: transferId={}", inboundPlan.getId());
    }

    /**
     * 查找发货计划的候选调整计划
     */
    private List<NonModuleProductionPlanTz> findEligiblePlansForOutbound(CellTransferPlan outboundPlan, 
                                                                        List<NonModuleProductionPlanTz> selfMadePlans) {
        return selfMadePlans.stream()
                .filter(plan -> matchBasicConditions(outboundPlan, plan))
                .filter(plan -> {
                    LocalDate planDate = parseStringToDate(plan.getAttribute19());
                    return planDate != null && !planDate.isAfter(outboundPlan.getTransferDate());
                })
                .filter(plan -> stringToBigDecimal(plan.getAttribute8()).compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
    }

    /**
     * 按日期分组计划
     */
    private Map<LocalDate, List<NonModuleProductionPlanTz>> groupPlansByDate(List<NonModuleProductionPlanTz> plans) {
        return plans.stream()
                .filter(plan -> parseStringToDate(plan.getAttribute19()) != null)
                .collect(Collectors.groupingBy(plan -> parseStringToDate(plan.getAttribute19())));
    }

    /**
     * 按工作中心排序计划
     */
    private List<NonModuleProductionPlanTz> sortPlansByWorkCenter(List<NonModuleProductionPlanTz> plans, 
                                                                boolean nullWorkCenterFirst) {
        if (nullWorkCenterFirst) {
            return plans.stream()
                    .sorted(Comparator.comparing(plan -> plan.getWorkCenterId() == null ? 0L : plan.getWorkCenterId()))
                    .collect(Collectors.toList());
        } else {
            return plans.stream()
                    .sorted(Comparator.comparing(plan -> plan.getWorkCenterId() == null ? Long.MAX_VALUE : plan.getWorkCenterId()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * 从计划中扣减数量
     */
    private BigDecimal deductQuantityFromPlans(List<NonModuleProductionPlanTz> plans, BigDecimal remainingQuantity) {
        for (NonModuleProductionPlanTz plan : plans) {
            if (remainingQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            
            BigDecimal currentQty = stringToBigDecimal(plan.getAttribute8());
            if (currentQty.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            
            BigDecimal deductQty = remainingQuantity.min(currentQty);
            BigDecimal newQty = currentQty.subtract(deductQty);
            
            plan.setAttribute8(bigDecimalToString(newQty));
            remainingQuantity = remainingQuantity.subtract(deductQty);
            
            log.debug("扣减计划数量: planId={}, 原数量={}, 扣减数量={}, 新数量={}", 
                    plan.getId(), currentQty, deductQty, newQty);
        }
        
        return remainingQuantity;
    }




    private List<NonModuleProductionPlanDTO> convertTupleToDTO(List<Tuple> tupleList) {

        List<NonModuleProductionPlanDTO> dataList = new ArrayList<>();
        //获取工厂信息
        List<LovLineDTO> factoryLineList = LovUtils.getLineListByHeaderCode(LovHeaderCodeConstant.SYS_ORG_ARCHITECTURE);
        Map<String,Long>  factoryLineMap = factoryLineList.stream().collect(Collectors.toMap(LovLineDTO::getLovValue, LovLineDTO::getLovLineId,(k1,k2)->k2));
        //获取产品信息
        List<LovLineDTO> productTypeList = LovUtils.getLineListByHeaderCode(LovHeaderCodeConstant.ATTR_TYPE_006_ATTR_1000);
        Map<String,Long>  productTypeMap = productTypeList.stream().collect(Collectors.toMap(LovLineDTO::getLovValue, LovLineDTO::getLovLineId,(k1,k2)->k2));
        //获取是否定向信息
        List<LovLineDTO> directionalLineList = LovUtils.getLineListByHeaderCode(LovHeaderCodeConstant.SYS_IF_DIRECTIONAL);
        Map<Long,String>  directionalLineMap = directionalLineList.stream().collect(Collectors.toMap(LovLineDTO::getLovLineId, LovLineDTO::getLovName,(k1,k2)->k2));
        //获取主栅信息
        List<LovLineDTO> gridLineList = LovUtils.getLineListByHeaderCode(LovHeaderCodeConstant.SYS_MAIN_GRID);
        Map<Long,String>  gridLineMap = gridLineList.stream().collect(Collectors.toMap(LovLineDTO::getLovLineId, LovLineDTO::getLovName,(k1,k2)->k2));
        tupleList.forEach(tuple -> {
            int index = 0;
            String planVersion = tuple.get(index++, String.class);
            String apsPlanVersion = tuple.get(index++, String.class);
            String factoryCode = tuple.get(index++, String.class);
            String productType = tuple.get(index++, String.class);
            Long directional = tuple.get(index++, Long.class);
            String attribute19 = tuple.get(index++, String.class);
            String date = DateUtil.format(DateUtil.parse(attribute19), "yyyy/MM/dd");
            // attribute8
            BigDecimal qty = tuple.get(index++, BigDecimal.class);
            Long gridLine = tuple.get(index++, Long.class);
            Long processType = tuple.get(index++, Long.class);
            String siliconSupplier = tuple.get(index++, String.class);
            Long thickness = tuple.get(index++, Long.class);
            Long workshopId = tuple.get(index++, Long.class);
            Long workCenterId = tuple.get(index++, Long.class);
            String frenchCarbonLabel = tuple.get(index++, String.class);
            String zeroCarbonLabel = tuple.get(index++, String.class);
            Long itemId = tuple.get(index++, Long.class);
            Long homemadeOrPurchase = tuple.get(index++, Long.class);
            Long electricalPerformance = tuple.get(index++, Long.class);

            Long specifySupplier = tuple.get(index++, Long.class);
            String specifySupplierName = tuple.get(index++, String.class);
            NonModuleProductionPlanDTO    data = new NonModuleProductionPlanDTO();
            data.setPlanVersion(planVersion);
            data.setApsPlanVersion(apsPlanVersion);
            data.setFactoryCode(factoryCode);
            data.setFactoryId(factoryLineMap.get(factoryCode));
            data.setProductType(productType);
            data.setProductTypeId(productTypeMap.get(data.getProductType()));
            data.setDirectional(directional);
            data.setDirectionalName(directionalLineMap.get(directional));
            data.setGridLine(gridLine);
            data.setGridLineName(gridLineMap.get(gridLine));
            data.setProcessPlanType(processType);
            data.setSiliconSupplier(siliconSupplier);
            if (data.getSiliconSupplier() != null) {
                String siliconSupplierName = Arrays.stream(data.getSiliconSupplier().split(",")).map(LovUtils::getName).collect(Collectors.joining(","));
                data.setSiliconSupplierName(siliconSupplierName);
            }
            data.setThickness(thickness);
            data.setWorkshopId(workshopId);
            data.setWorkCenterId(workCenterId);
            data.setAttribute19(attribute19);
            data.setFrenchCarbonLabel(frenchCarbonLabel);
            data.setZeroCarbonLabel(zeroCarbonLabel);
            data.setItemId(itemId);
            data.setHomemadeOrPurchase(homemadeOrPurchase);
            data.setElectricalPerformance(electricalPerformance);
            data.setSchedulingQty(qty);

            data.setSpecifySupplierName(specifySupplierName);
            data.setSpecifySupplier(specifySupplier);
            data.setDynamicColumnList(org.apache.commons.compress.utils.Lists.newArrayList());
            data.setDynamicColumnMap(Maps.newHashMap());
            Map<Object, Object> dynamicMap = Maps.newHashMap();
            dynamicMap.put("id", "");
            dynamicMap.put("schedulingQty", qty);
            dynamicMap.put("property", "schedulingQty");
            data.getDynamicColumnMap().put(date, dynamicMap);
            dataList.add(data);
        });
        return dataList;
    }


    public static List<BigDecimal> splitBigDecimal(BigDecimal number, int parts) {

        List<BigDecimal> nums=new ArrayList<>();
        BigDecimal[] divideAndRemainder = number.divideAndRemainder(BigDecimal.valueOf(parts));
        BigDecimal quotient = divideAndRemainder[0];
        BigDecimal remainder = divideAndRemainder[1];

        for(int i=0;i<parts;i++){
            nums.add(quotient);
            if(i==parts-1 && remainder.compareTo(BigDecimal.ZERO) > 0){
                nums.set(i,quotient.add(remainder));
            }
        }

        return nums;
    }


    private BooleanBuilder buildWhere(NonModuleProductionPlanTzQuery query) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();


        if (query.getFactoryId() != null) {
            booleanBuilder.and(qNonModuleProductionPlanTz.factoryId.eq(query.getFactoryId()));
        }


        if (StringUtils.isNotBlank(query.getProductType())) {
            booleanBuilder.and(qNonModuleProductionPlanTz.productType.eq(query.getProductType()));
        }


        if (query.getWorkCenterId() != null) {
            booleanBuilder.and(qNonModuleProductionPlanTz.workCenterId.eq(query.getWorkCenterId()));
        }

        if (StringUtils.isNotBlank(query.getSchedulingStartTime())){
            LocalDateTime startTime=LocalDateTimeUtil.parse(query.getSchedulingStartTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime localDateTime= LocalDateTimeUtil.beginOfDay(startTime);
            booleanBuilder.and(qNonModuleProductionPlanTz.schedulingStartTime.goe(localDateTime));
        }
        if (query.getSchedulingEndTime() != null) {

            LocalDateTime endTime=LocalDateTimeUtil.parse(query.getSchedulingEndTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime localDateTime= LocalDateTimeUtil.endOfDay(endTime);
            booleanBuilder.and(qNonModuleProductionPlanTz.schedulingEndTime.loe(localDateTime));
        }

        return booleanBuilder;
    }




}
